package com.shuidihuzhu.cf.risk.admin;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RBitSet;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020-02-29
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class RedisTest extends BaseTest {

    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;

    @Test
    public void testB(){
        long index = Integer.MAX_VALUE;
        RBitSet bitSet = cfRiskRedissonHandler.getRedissonClient().getBitSet("risk-test");
        boolean setRes = bitSet.set(index);
        bitSet.expire(10, TimeUnit.SECONDS);
        System.out.println("setRes = " + setRes);
        boolean getRes = bitSet.get(index);
        System.out.println("getRes = " + getRes);
        long length = bitSet.length();
        System.out.println("length = " + length);
    }

}
