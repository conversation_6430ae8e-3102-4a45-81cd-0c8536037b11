package com.shuidihuzhu.cf.risk.admin.controller;
 
import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.admin.model.query.whiteList.WhiteListQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.whiteList.RiskWhiteListAddVo;
import com.shuidihuzhu.cf.risk.admin.service.whitelist.RiskWhiteListService;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.pf.common.test.MockMvcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
* MinaAnswerController Tester.
*
* <AUTHOR>
* @since <pre>Nov 15, 2019</pre>
* @version 1.0
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class WhiteListControllerTest {
 
    @Autowired
    private MockMvcUtil mockMvcUtil;
    @Autowired
    private RiskWhiteListService riskWhiteListService;
 
    @Before
    public void before() throws Exception {
    }
     
    @After
    public void after() throws Exception {
    }


    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }
     
    /**
     *
     * Method: save(RiskWhiteListAddVo riskWhiteListAddVo)
     *
     */
    @Test
    public void testSave() throws Exception {
        RiskWhiteListAddVo riskWhiteListAddVo = new RiskWhiteListAddVo();
        riskWhiteListAddVo.setAddReason("测试");
        riskWhiteListAddVo.setExpireTime("");
        riskWhiteListAddVo.setName("江南");
        riskWhiteListAddVo.setIdCard("142727199702243018");
        riskWhiteListAddVo.setPhoneNumber("18845897627");
        riskWhiteListService.add(riskWhiteListAddVo, 117);
    }
     
    /**
     *
     * Method: update(RiskWhiteListAddVo riskWhiteListAddVo)
     *
     */
    @Test
    public void testUpdate() throws Exception {
        RiskWhiteListAddVo riskWhiteListAddVo = new RiskWhiteListAddVo();
        riskWhiteListAddVo.setAddReason("测试更新");
        riskWhiteListAddVo.setExpireTime("3030-01-14 14:50:04");
        riskWhiteListAddVo.setName("江南2");
        riskWhiteListAddVo.setIdCard("142727199702243018");
        riskWhiteListAddVo.setPhoneNumber("18845897627");
        riskWhiteListAddVo.setId(2);
        riskWhiteListService.update(riskWhiteListAddVo, 117);
    }
     
    /**
     *
     * Method: get(@ApiParam("白名单序号")@RequestParam(value ="id" ) long id)
     *
     */
    @Test
    public void testGet() throws Exception {
        System.out.println(JSON.toJSONString(riskWhiteListService.get(2)));
    }
     
    /**
     *
     * Method: getLog(@ApiParam("白名单序号")@RequestParam(value ="id" ) long id)
     *
     */
    @Test
    public void testGetLog() throws Exception {
        System.out.println(JSON.toJSONString(riskWhiteListService.getLog(2)));
    }
     
    /**
     *
     * Method: getList(@RequestBody WhiteListQuery whiteListQuery)
     *
     */
    @Test
    public void testGetList() throws Exception {
        System.out.println(JSON.toJSONString(riskWhiteListService.getList(new WhiteListQuery())));
    }
     
    /**
     *
     * Method: getRuleVoList()
     *
     */
    @Test
    public void testGetRuleVoList() throws Exception {
        //TODO: Test goes here...
    }
     
     
}
