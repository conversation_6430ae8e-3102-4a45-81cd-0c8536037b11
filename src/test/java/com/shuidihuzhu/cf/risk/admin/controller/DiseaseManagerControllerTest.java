package com.shuidihuzhu.cf.risk.admin.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.kratos.client.feign.WxChatMessageFeignClient;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageSearchParam;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageSearchResult;
import com.shuidihuzhu.pf.common.test.MockMvcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Date;
import java.util.List;

/**
* MinaAnswerController Tester.
*
* <AUTHOR>
* @since <pre>Nov 15, 2019</pre>
* @version 1.0
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class DiseaseManagerControllerTest {
 
    @Autowired
    private MockMvcUtil mockMvcUtil;
    @Autowired
    private DiseaseClient diseaseClient;
    @Autowired
    private SeaAccountService seaAccountService;

    @Before
    public void before() throws Exception {
    }
     
    @After
    public void after() throws Exception {
    }
    
    private static final String TOKEN = "tQppJr2FiWvjinZ8CJi9oQ$0";

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }
    @Test
    public  void test1() {
        List<String> diseaseNameList = Lists.newArrayList();
        diseaseNameList.add("测试");
        diseaseNameList.add("类别名称测试");
        DecideReasonableInfo decideReasonableInfo = new DecideReasonableInfo(diseaseNameList, 2286688);
        System.out.println(JSON.toJSONString(diseaseClient.decideInfoAmountReasonable( decideReasonableInfo)));
    }
    /**
     *
     * Method: create(@ApiParam("疾病json字符串") @RequestParam("param") String param)
     *
     */
    @Test
    public void testCreate() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        params.add("param", "{\"diseaseClassName\":\"阿斯蒂\",\"medicalName\":\"阿斯顿法师\",\"diseaseMergeRule\":\"按地方撒的\",\"normalName\":\"阿士大夫撒\",\"treatMethodList\":[],\"raiseType\":2}");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/create", httpHeaders);

    }
     
    /**
     *
     * Method: edit(@ApiParam("疾病json字符串") @RequestParam("param") String param, @ApiParam("疾病序号")
                                      @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId)
     *
     */
    @Test
    public void testEdit() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        params.add("diseaseId", "382");
        params.add("param", "{\"id\":382,\"diseaseClassName\":\"脑出血\",\"medicalName\":\"\",\"diseaseMergeRule\":\"脑出血，脑内出血，脑室积血，脑溢血，脑+血管+破裂，脑/脑内/颅内/脑室/丘脑/额/颞/顶/枕/胼胝体/蛛网膜下腔/蛛网/基底/尾状核/脑叶/脑桥/脑干/硬膜外/硬膜下+出血/血肿，SAH/sah\",\"normalName\":\"\",\"raiseType\":1,\"treatMethodList\":[{\"customTreatment\":\"\",\"id\":670,\"maxTreatmentFee\":5,\"minTreatmentFee\":2,\"projectMergeRule\":\"\",\"projectName\":\"保守治疗\",\"raiseType\":1},{\"customTreatment\":\"\",\"id\":671,\"maxTreatmentFee\":15,\"minTreatmentFee\":10,\"projectMergeRule\":\"\",\"projectName\":\"手术治疗\",\"raiseType\":1},{\"customTreatment\":\"\",\"id\":672,\"maxTreatmentFee\":7,\"minTreatmentFee\":7,\"projectMergeRule\":\"\",\"projectName\":\"ICU治疗\",\"raiseType\":1},{\"customTreatment\":\"\",\"id\":673,\"maxTreatmentFee\":\"8\",\"minTreatmentFee\":\"5\",\"projectMergeRule\":\"\",\"projectName\":\"不知道治疗方案\",\"raiseType\":1},{\"customTreatment\":\"\",\"id\":1419,\"maxTreatmentFee\":30,\"minTreatmentFee\":15,\"projectMergeRule\":\"\",\"projectName\":\"康复治疗\",\"raiseType\":1}],\"diseaseId\":\"382\"}");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/edit", httpHeaders);

    }
     
    /**
     *
     * Method: delete(@ApiParam("疾病序号")
                                        @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId)
     *
     */
    @Test
    public void testDelete() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        params.add("diseaseId", "111");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/delete", httpHeaders);

    }
     
    /**
     *
     * Method: detail(@ApiParam("疾病序号")
                                    @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId)
     *
     */
    @Test
    public void testDetail() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        params.add("diseaseId", "483");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/disease/detail", httpHeaders);
    }

    @Test
    public void testNameList() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/treatment/name-list", httpHeaders);
    }
     
    /**
     *
     * Method: list(@ApiParam("疾病类别名称")@RequestParam(value = "diseaseClassName", required = false) String diseaseClassName,
     * @ApiParam("医学诊断名称")@RequestParam(value = "medicalName", required = false) String medicalName,
     * @ApiParam("常用名")@RequestParam(value = "normalName", required = false) String normalName,
     * @ApiParam("疾病类型")@RequestParam(value = "raiseType", defaultValue = "0") int raiseType,
     * @RequestParam(defaultValue = "pageJson") String pageJson)
     *
     */
    @Test
    public void testList() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        params.add("normalName", "常用名1");
        params.add("pageJson", "{   \"pageType\":1,   \"startId\":10,   \"endId\":0,   \"pageSize\":10 }");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/list", httpHeaders);

    }
     
    /**
     *
     * Method: getRaiseType()
     *
     */
    @Test
    public void testGet() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/raise-type/get", httpHeaders);

    }
     
    /**
     *
     * Method: listOperationRecord(@ApiParam("疾病序号")
                                                                   @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId, @RequestParam(defaultValue = "pageJson") String pageJson)
     *
     */
    @Test
    public void testListRecord() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        params.add("diseaseId", "4");
        params.add("pageJson", "{   \"pageType\":2,   \"startId\":0,   \"endId\":0,   \"pageSize\":10 }");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/operation-record/list", httpHeaders);

    }
     
    /**
     *
     * Method: listTreatment(@ApiParam("疾病序号")
                                                                           @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId)
     *
     */
    @Test
    public void testListDiseaseClassify() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", TOKEN);
        params.add("diseaseId", "4");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/treatment/list", httpHeaders);

    }
     

}
