package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotStrategyService;
import com.shuidihuzhu.cf.risk.client.admin.quality.sampling.QualitySpotClient;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotJobConfDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotWorkOrderWaitCheck;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;
import java.util.List;

/**
 * QualitySpotInnerController Tester.
 *
 * <AUTHOR>
 * @since <pre>6月 22, 2020</pre>
 * @version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class QualitySpotInnerControllerTest {

    @Autowired
    private QualitySpotClient qualitySpotClient;

    @Resource
    private QualitySpotStrategyService qualitySpotStrategyService;
    @Autowired
    protected MockMvc mockMvc;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    /**
     *
     * Method: listJobScopeConf() 
     *
     */
    @Test
    public void testListJobScopeConf() throws Exception {
        List<QualitySpotJobConfDto> listResponse =
                qualitySpotStrategyService.listJobScopeConf();
        System.out.println(listResponse);
    }

    @Test
    public void testDoQualitySpotStrategy() throws Exception {
        List<QualitySpotDto> qualitySpotJobConfDtos = Lists.newArrayList();
        QualitySpotDto qualitySpotDto = new QualitySpotDto();
        qualitySpotDto.setAdviserName("小米辣");
        qualitySpotDto.setAreaId(1);
        qualitySpotDto.setRecordingTime(1000L);
        qualitySpotDto.setValidQualitySpotCount(1L);

        qualitySpotJobConfDtos.add(qualitySpotDto);
        qualitySpotJobConfDtos.add(qualitySpotDto);


    /*    Response<List> listResponse =
                qualitySpotClient.doQualitySpotStrategy(new QualitySpotWorkOrderWaitCheck(84L, qualitySpotJobConfDtos, Lists.newArrayList()));
        System.out.println(listResponse);*/
    }

}
