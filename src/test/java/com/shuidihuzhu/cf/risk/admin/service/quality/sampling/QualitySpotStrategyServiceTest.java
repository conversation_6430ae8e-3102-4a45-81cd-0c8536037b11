package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcAppealProblemStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcAppealResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealProblemModel;
import com.shuidihuzhu.cf.risk.admin.rule.compile.GroovyCompile;
import com.shuidihuzhu.cf.risk.admin.rule.enums.RuleTypeEnum;
import com.shuidihuzhu.cf.risk.admin.rule.utils.GroovyScriptRunUtil;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotJobConfDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotOutBoundDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotResultIssueRequest;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.model.CfGwAppealMaterialModel;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestNoticeModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/** 
* QualitySpotStrategyService Tester. 
* 
* <AUTHOR> 
* @since <pre>6月 20, 2020</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class QualitySpotStrategyServiceTest {
    @Resource
    private GroovyCompile groovyCompile;

    @Autowired
    private QualitySpotStrategyService qualitySpotStrategyService;
    @Autowired
    protected MockMvc mockMvc;
    @Autowired
    private QualitySpotMaterialService qualitySpotMaterialService;

    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private QcAppealInnerService qcAppealInnerService;
    @Value("${qc.pass.style:''}")
    private String passStyle;
    @Value("${qc.reject.style:''}")
    private String rejectStyle;
    @Autowired
    private QualitySpotResultIssueInnerService qualitySpotResultIssueInnerService;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;


    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }
    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    } 
    
    /** 
     * 
     * Method: queryStrategyList(QualitySpotStrategyQuery qualitySpotStrategyQuery) 
     * 
     */ 
    @Test
    public void testQueryStrategyList() throws Exception {
        qcAppealInnerService.saveBuildLog(2, "niejiangnan",
                CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE, 64182, "一次申诉工单");
        qcAppealInnerService.saveBuildLog(3, "niejiangnan",
                CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE, 1, "二次申诉工单");
        qcAppealInnerService.saveBuildLog(4, "niejiangnan",
                CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.SERIOUS_ISSUE, 64182, "严重问题申诉工单");
        qcAppealInnerService.saveAgreeQcResultLog("niejiangnan", 64182, RiskQcOperationTypeEnum.ADVISER_AGREE_QC_RESULT);
        qcAppealInnerService.saveAgreeQcResultLog("niejiangnan", 64182, RiskQcOperationTypeEnum.ADVISER_AGREE_APPEAL_RESULT);
        qcAppealInnerService.saveAppealExpireLog(64182);
        qcAppealInnerService.saveManageAgreeLog("niejiangnan", 64182, RiskQcOperationTypeEnum.MANAGER_AGREE_QC_RESULT);
        qcAppealInnerService.saveManageNoSupportLog("niejiangnan", 64182, RiskQcOperationTypeEnum.MANAGER_NO_SUPPORT_AGAIN_APPEAL, "测试");

        saveLogResult(1);
        saveLogResult(2);
        saveLogResult(3);

    }

    private  void saveLogResult(int judgeResult){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("申诉判定结果：").append(QcAppealResultEnum.findOfCode(judgeResult));
        stringBuffer.append("\n");
        List<RiskQcAppealProblemModel> riskQcAppealProblemModels = Lists.newArrayList();
        RiskQcAppealProblemModel riskQcAppealProblemModel = new RiskQcAppealProblemModel();
        riskQcAppealProblemModel.setProperty("测试属性1");
        riskQcAppealProblemModel.setProblem("测试问题1");
        riskQcAppealProblemModel.setStatus(QcAppealProblemStatusEnum.PASS.getCode());
        riskQcAppealProblemModels.add(riskQcAppealProblemModel);
        RiskQcAppealProblemModel riskQcAppealProblemModel1 = new RiskQcAppealProblemModel();
        riskQcAppealProblemModel1.setProperty("测试属性2");
        riskQcAppealProblemModel1.setProblem("测试问题2");
        riskQcAppealProblemModel1.setStatus(QcAppealProblemStatusEnum.REJECTED.getCode());
        riskQcAppealProblemModels.add(riskQcAppealProblemModel1);
        riskQcAppealProblemModels.forEach(var -> {
            if (var.getStatus() == QcAppealProblemStatusEnum.PASS.getCode()) {
                stringBuffer.append(passStyle.replace("#status#", QcAppealProblemStatusEnum.PASS.getDesc()));
            } else {
                stringBuffer.append(rejectStyle.replace("#status#", QcAppealProblemStatusEnum.REJECTED.getDesc()));
            }
            stringBuffer.append(var.getProblem());
            stringBuffer.append("\n");
        });
        stringBuffer.append("添加评论:").append("\n");
        stringBuffer.append(StringUtils.trimToEmpty("测试评论"));
        riskQcLogService.addLog(RiskQcOperationTypeEnum.APPEAL_WORK_ORDER_RESULT, 64182, stringBuffer.toString());
    }
    
    /** 
     * 
     * Method: enableStrategy(Long id) 
     * 
     */ 
    @Test
    public void testEnableStrategy() throws Exception {
        RiskQcBaseInfo riskQcBaseInfo = JSON.parseObject("{\"caseId\":2297272,\"id\":1356,\"orderType\":45,\"qcByName\":\"王东伟\",\"qcType\":4,\"qcUniqueCode\":\"16216\",\"taskId\":0}",
                RiskQcBaseInfo.class);
        WorkOrderVO workOrderVO = new WorkOrderVO();
        workOrderVO.setCaseId(57179);
        workOrderVO.setHandleResult(8);
        //workOrderVO.setCaseUuid("996db603-2dda-4962-87cf-6d4caed181da");
        qualitySpotMaterialService.spotMaterial(57179L, riskQcBaseInfo, workOrderVO);
    } 
    
    /** 
     * 
     * Method: disableStrategy(Long id) 
     * 
     */ 
    @Test
    public void testDisableStrategy() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: saveStrategy(RiskQualitySpotStrategyDetailVo qualitySpotStrategyVo) 
     *  normalQualitySpotAppeal param:CfGwReplaceInputQualityTestFeedbackModel(reportId=,
     *                 workOrderId=, cfGwAppealMaterialModel=CfGwAppealMaterialModel(
     *                        ,  issueType=DEFAULT_ISSUE, appealNum=3, feedback=false, appealStatus=0,
     *                 expiredTime=, leaderAction=null,
     *                 leaderRemark=null, name=侯玉飞, mis=houyufei, leaderName=, leaderMis=, expired=false)
     */ 
    @Test
    public void testSaveStrategy() throws Exception {

        CfGwReplaceInputQualityTestFeedbackModel feedbackModel = new CfGwReplaceInputQualityTestFeedbackModel();
        feedbackModel.setReportId(4856L);
        feedbackModel.setWorkOrderId(47642L);
        feedbackModel.setIssueType(CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE);
        feedbackModel.setAppealStatus(0);
        feedbackModel.setName("侯玉飞");
        feedbackModel.setMis("houyufei");
        feedbackModel.setExpiredTime(new Date("Wed Nov 25 17:20:03 CST 2020"));
        CfGwAppealMaterialModel cfGwAppealMaterialModel = new CfGwAppealMaterialModel();
        feedbackModel.setCfGwAppealMaterialModel(cfGwAppealMaterialModel);
        cfGwAppealMaterialModel.setExplainIssue("我是来测试的");
        List<CfGwReplaceInputQualityTestNoticeModel.IssueInfo> issueInfos = Lists.newArrayList();
        CfGwReplaceInputQualityTestNoticeModel.IssueInfo issueInfo = new CfGwReplaceInputQualityTestNoticeModel.IssueInfo();
        issueInfo.setProperty("服务问题");
        issueInfo.setRemark("4-1 救助保障情况-【医保/农合、及报销比例】服务过程中未询问");
        issueInfo.setFeedbackStatus(2);
        issueInfos.add(issueInfo);
        CfGwReplaceInputQualityTestNoticeModel.IssueInfo issueInfo1 = new CfGwReplaceInputQualityTestNoticeModel.IssueInfo();
        issueInfo1.setProperty("服务问题");
        issueInfo1.setRemark("5-1 花费情况-【未来花费金额】服务过程中未询问");
        issueInfo1.setFeedbackStatus(1);
        issueInfos.add(issueInfo1);
        CfGwReplaceInputQualityTestNoticeModel.IssueInfo issueInfo2 = new CfGwReplaceInputQualityTestNoticeModel.IssueInfo();
        issueInfo2.setProperty("合规问题");
        issueInfo2.setRemark("6-1 合规问题-【病情信息】协助/诱导用户夸大病情情况（eg用户表示为良性肿瘤报备写成癌症等）");
        issueInfo2.setFeedbackStatus(2);
        issueInfos.add(issueInfo1);
        cfGwAppealMaterialModel.setIssueInfos(issueInfos);
        feedbackModel.setExpired(false);
        qcAppealInnerService.normalQualitySpotAppeal(feedbackModel);
    } 
    
    /** 
     * 
     * Method: queryStrategyLogList(Long strategyId) 
     * 
     */ 
    @Test
    public void testQueryStrategyLogList() throws Exception {
        WorkOrderVO workOrderVO = new WorkOrderVO();
        workOrderVO.setWorkOrderId(66509);
        workOrderVO.setCaseId(2300043);
        workOrderVO.setCreateTime(new Date("Fri Nov 20 11:58:55 CST 2020"));
        workOrderVO.setHandleTime(new Date("Fri Nov 20 11:58:55 CST 2020"));
        workOrderVO.setOrderType(49);
        QualitySpotResultIssueRequest qualitySpotResultIssueRequest = new QualitySpotResultIssueRequest();
        qualitySpotResultIssueRequest.setWorkOrderVOS(Lists.newArrayList(workOrderVO));
        qualitySpotResultIssueRequest.setNormal(true);
        qualitySpotResultIssueRequest.setAppealNum(1);
        //qualitySpotResultIssueInnerService.offlineIssue(qualitySpotResultIssueRequest);

        qualitySpotResultIssueInnerService.sendQcAppealResultMQ(Lists.newArrayList(workOrderVO),
                CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.SERIOUS_ISSUE, CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.SECOND_NOTICE);
    } 
    
    /** 
     * 
     * Method: listJobScopeConf() 
     * 
     */ 
    @Test
    public void testListJobScopeConf() throws Exception {
        List<QualitySpotJobConfDto> qualitySpotJobConfDtos = qualitySpotStrategyService.listJobScopeConf();
        Assert.assertTrue(CollectionUtils.isNotEmpty(qualitySpotJobConfDtos));
    }
    
    /** 
     * 
     * Method: doQualitySpotStrategy(@NotNull(message = "策略id不能为空") @Min(value = 1, message = "策略id不能小于1") Long strategyId, @Valid List<QualitySpotDto> qualitySpotDtos) 
     * 
     */ 
    @Test
    public void testDoQualitySpotStrategy() throws Exception {
        String script = "[{\n" +
                "\t\t\"name\": \"质检抽检规则01\",\n" +
                "\t\t\"priority\": 2,\n" +
                "\t\t\"status\": 0,\n" +
                "\t\t\"criterionGroup\": {\n" +
                "\t\t\t\"junctionType\": \"AND\",\n" +
                "\t\t\t\"criterions\": [{\n" +
                "\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\"op\": \"GREATER_THEN\",\n" +
                "\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\"fieldName\": \"recordingTime\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldLabel\": \"录音时长\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldType\": \"Long\",\n" +
                "\t\t\t\t\t\t\t\t\"sourcePath\": \"recordingTime\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\"content\": \"3\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\"op\": \"IN\",\n" +
                "\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\"content\": [\"1\",\"2\",\"3\"]\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\"fieldName\": \"areaId\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldLabel\": \"所在区域\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldType\": \"Long\",\n" +
                "\t\t\t\t\t\t\t\t\"sourcePath\": \"areaId\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\"op\": \"EQUALS\",\n" +
                "\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\"fieldName\": \"adviserName\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldLabel\": \"顾问姓名\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldType\": \"String\",\n" +
                "\t\t\t\t\t\t\t\t\"sourcePath\": \"adviserName\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\"content\": \"小米辣\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"criterionType\": \"CRITERION_GROUP\",\n" +
                "\t\t\t\t\t\"criterionGroup\": {\n" +
                "\t\t\t\t\t\t\"junctionType\": \"OR\",\n" +
                "\t\t\t\t\t\t\"criterions\": [{\n" +
                "\t\t\t\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\t\t\t\"op\": \"LESS_THEN\",\n" +
                "\t\t\t\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldName\": \"validQualitySpotCount\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldLabel\": \"顾问当月有效质检数\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldType\": \"Long\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"sourcePath\": \"validQualitySpotCount\"\n" +
                "\t\t\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\"content\": \"2\"\n" +
                "\t\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\t\t\t\"op\": \"GREATER_THEN\",\n" +
                "\t\t\t\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldName\": \"recordingTime\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldLabel\": \"录音时长\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldType\": \"Long\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"sourcePath\": \"recordingTime\"\n" +
                "\t\t\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\"content\": \"3\"\n" +
                "\t\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t]\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t}\n" +
                "\t\t\t]\n" +
                "\t\t},\n" +
                "\t\t\"thenAction\": {\n" +
                "\t\t\t\"actionType\": \"VARIABLE_ASSIGN\",\n" +
                "\t\t\t\"variableAssign\": {\n" +
                "\t\t\t\t\"fieldName\": \"isHit\",\n" +
                "\t\t\t\t\"fieldLabel\": \"是否命中策略规则\",\n" +
                "\t\t\t\t\"fieldType\": \"Boolean\",\n" +
                "\t\t\t\t\"sourcePath\": \"isHit\",\n" +
                "\t\t\t\t\"value\": {\n" +
                "\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\"content\": \"true\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t{\n" +
                "\t\t\"name\": \"质检抽检规则01\",\n" +
                "\t\t\"priority\": 2,\n" +
                "\t\t\"status\": 0,\n" +
                "\t\t\"criterionGroup\": {\n" +
                "\t\t\t\"junctionType\": \"AND\",\n" +
                "\t\t\t\"criterions\": [{\n" +
                "\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\"op\": \"GREATER_THEN\",\n" +
                "\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\"fieldName\": \"recordingTime\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldLabel\": \"录音时长\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldType\": \"Long\",\n" +
                "\t\t\t\t\t\t\t\t\"sourcePath\": \"recordingTime\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\"content\": \"3\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\"op\": \"IN\",\n" +
                "\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\"content\": [true, true, false]\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\"fieldName\": \"areaId\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldLabel\": \"所在区域\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldType\": \"Object\",\n" +
                "\t\t\t\t\t\t\t\t\"sourcePath\": \"areaId\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\"op\": \"EQUALS\",\n" +
                "\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\"fieldName\": \"adviserName\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldLabel\": \"顾问姓名\",\n" +
                "\t\t\t\t\t\t\t\t\"fieldType\": \"String\",\n" +
                "\t\t\t\t\t\t\t\t\"sourcePath\": \"adviserName\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\"content\": \"小米辣\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"criterionType\": \"CRITERION_GROUP\",\n" +
                "\t\t\t\t\t\"criterionGroup\": {\n" +
                "\t\t\t\t\t\t\"junctionType\": \"OR\",\n" +
                "\t\t\t\t\t\t\"criterions\": [{\n" +
                "\t\t\t\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\t\t\t\"op\": \"LESS_THEN\",\n" +
                "\t\t\t\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldName\": \"validQualitySpotCount\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldLabel\": \"顾问当月有效质检数\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldType\": \"Long\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"sourcePath\": \"validQualitySpotCount\"\n" +
                "\t\t\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\"content\": \"2\"\n" +
                "\t\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\t\t\"criterionType\": \"RELATIONAL\",\n" +
                "\t\t\t\t\t\t\t\t\"relational\": {\n" +
                "\t\t\t\t\t\t\t\t\t\"op\": \"GREATER_THEN\",\n" +
                "\t\t\t\t\t\t\t\t\t\"leftValue\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\"valueType\": \"VARIABLE\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\"variable\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldName\": \"recordingTime\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldLabel\": \"录音时长\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"fieldType\": \"Long\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\"sourcePath\": \"recordingTime\"\n" +
                "\t\t\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t\t\t\"rightValue\": {\n" +
                "\t\t\t\t\t\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\t\t\t\t\t\"content\": \"3\"\n" +
                "\t\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t]\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t}\n" +
                "\t\t\t]\n" +
                "\t\t},\n" +
                "\t\t\"thenAction\": {\n" +
                "\t\t\t\"actionType\": \"VARIABLE_ASSIGN\",\n" +
                "\t\t\t\"variableAssign\": {\n" +
                "\t\t\t\t\"fieldName\": \"isHit\",\n" +
                "\t\t\t\t\"fieldLabel\": \"是否命中策略规则\",\n" +
                "\t\t\t\t\"fieldType\": \"Boolean\",\n" +
                "\t\t\t\t\"sourcePath\": \"isHit\",\n" +
                "\t\t\t\t\"value\": {\n" +
                "\t\t\t\t\t\"valueType\": \"CONSTANT\",\n" +
                "\t\t\t\t\t\"content\": \"true\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t}\n" +
                "]";
        QualitySpotDto qualitySpotDto = new QualitySpotDto();
        qualitySpotDto.setAdviserName("小米辣");
        qualitySpotDto.setAreaId(1);
        qualitySpotDto.setRecordingTime(1000L);
        qualitySpotDto.setValidQualitySpotCount(1L);
        String groovyScript = groovyCompile.compileCriterion(script);

        Assert.assertTrue(StringUtils.isNotBlank(groovyScript));

        GroovyScriptRunUtil.executeCommon(RuleTypeEnum.RULE, 1, groovyScript, new Object[]{qualitySpotDto});

        Assert.assertFalse(qualitySpotDto.isHit());
    }
    
    
    /** 
     * 
     * Method: checkStrategyScopeValid(Integer scene, List<Integer> strategyScopes) 
     * 
     */ 
    @Test
    public void testCheckStrategyScopeValid() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = QualitySpotStrategyService.getClass().getMethod("checkStrategyScopeValid", Integer.class, List<Integer>.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: saveStrategyLog(Long strategyId, QualitySpotStrategyOperateTypeEnum operateTypeEnum) 
     * 
     */ 
    @Test
    public void testSaveStrategyLog() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = QualitySpotStrategyService.getClass().getMethod("saveStrategyLog", Long.class, QualitySpotStrategyOperateTypeEnum.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: selectCurrentStrategyForScene() 
     * 
     */ 
    @Test
    public void testSelectCurrentStrategyForScene() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = QualitySpotStrategyService.getClass().getMethod("selectCurrentStrategyForScene"); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
}
