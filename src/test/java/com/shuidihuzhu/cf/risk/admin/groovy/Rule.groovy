package com.shuidihuzhu.cf.risk.admin.groovy

import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotDto

class Rule {
    static def check(def data) {
        if ((data.recordingTime>3)&&([1,2,3].contains(data.areaId))&&(data.adviserName=="小米辣")&&((data.validQualitySpotCount<2)||(data.recordingTime>3))) {
            data.isHit=true
        } else {

        }
    }

    static void main(String[] args) {
        QualitySpotDto qualitySpotDto = new QualitySpotDto();
        qualitySpotDto.setAdviserName("小米辣")
        qualitySpotDto.setAreaId(List.of(1))
        qualitySpotDto.setRecordingTime(1000L)
        qualitySpotDto.setValidQualitySpotCount(1L)
        check(qualitySpotDto)
        println qualitySpotDto.isHit()

        println([1,2,3].contains([1]))
    }
}
