
package com.shuidihuzhu.cf.risk.admin.controller;
 
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotStrategyDetailVo;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotExecuteModelEnum;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.msg.util.DateUtil;
import com.shuidihuzhu.pf.common.test.MockMvcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.List;

/**
* MinaAnswerController Tester.
*
* <AUTHOR>
* @since <pre>Nov 15, 2019</pre>
* @version 1.0
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class QualitySpotControllerTest {
 
    @Autowired
    private MockMvcUtil mockMvcUtil;
 
    @Before
    public void before() throws Exception {
    }
     
    @After
    public void after() throws Exception {
    }

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    /**
     *
     * Method: strategySave(@Valid RiskQualitySpotStrategyDetailVo detailVo)
     *
     */
    @Test
    public void testStrategySave() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", "ZWEl6jbxBkdgPdcY-M6obw$0");
        RiskQualitySpotStrategyDetailVo detailVo  = new RiskQualitySpotStrategyDetailVo();
        detailVo.setStrategyName("微信质检1v1策略");
        detailVo.setFirstScene(2L);
        detailVo.setSecondScene(4L);
        detailVo.setStrategyParseTime(DateUtil.addDays(DateUtil.getCurrentDate(), 1).toString());
        detailVo.setStrategyScope(Lists.newArrayList(7));
        detailVo.setExecuteMode(QualitySpotExecuteModelEnum.PRIORITY.getCode());
        detailVo.setRuleDef("[{\"name\":\"兜底规则\",\"status\":0,\"extData\":{\"dataScope\":1},\"priority\":1000,\"criterionGroup\":{\"junctionType\":\"AND\",\"criterions\":[{\"criterionType\":\"RELATIONAL\",\"relational\":{\"op\":\"LESS_THEN\",\"leftValue\":{\"valueType\":\"VARIABLE\",\"variable\":{\"fieldName\":\"validQualitySpotCount\",\"fieldLabel\":\"顾问当月有效质检数\",\"fieldType\":\"Long\",\"sourcePath\":\"validQualitySpotCount\"}},\"rightValue\":{\"content\":\"1\",\"valueType\":\"CONSTANT\"}}}]},\"thenAction\":{\"actionType\":\"VARIABLE_ASSIGN\",\"variableAssign\":{\"fieldName\":\"isHit\",\"fieldLabel\":\"是否命中策略规则\",\"fieldType\":\"Boolean\",\"sourcePath\":\"isHit\",\"value\":{\"valueType\":\"CONSTANT\",\"content\":\"true\"}}}}]");
        params.add("detailVo", JSON.toJSONString(detailVo));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("VerifiedTokenDetail", "0,17527381,2,1");
//        mockMvcUtil.testPostController(params, "/api/cf-risk-admin/disease/manager/create", httpHeaders);
    }
     
    /**
     *
     * Method: strategyUpdate(@Valid RiskQualitySpotStrategyDetailVo detailVo)
     *
     */
    @Test
    public void testStrategyUpdate() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyGet(@ApiParam(value = "策略id", required = true)
            @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long id)
     *
     */
    @Test
    public void testStrategyGet() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyRemainingScope(@ApiParam(value = "适用场景id", required = true)
            @NotNull(message = "适用场景不能为空") @Min(value = 1, message = "适用场景不能小于1") Long scene)
     *
     */
    @Test
    public void testStrategyRemainingScope() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyList(@Valid QualitySpotStrategyQuery strategyQuery)
     *
     */
    @Test
    public void testStrategyList() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyEnable(@ApiParam(value = "策略id",required = true)
            @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long id)
     *
     */
    @Test
    public void testStrategyEnable() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyDisable(@ApiParam(value = "策略id",required = true)
            @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long id)
     *
     */
    @Test
    public void testStrategyDisable() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyLogList(@ApiParam(value = "策略id",required = true)
             @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long strategyId)
     *
     */
    @Test
    public void testStrategyLogList() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyLevelList(@NotNull(message = "质检对象不能为空")
                                                                            @ApiParam(value = "质检对象", defaultValue = "0") Long firstScene, @NotNull(message = "二级工单类型不能为空")
                                                                        @ApiParam(value = "二级工单类型", defaultValue = "0") Long secondScene)
     *
     */
    @Test
    public void testStrategyLevelList() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyLevelGetScene(@NotNull(message = "二级工单类型不能为空") @Min(value = 1, message = "二级工单类型不能小于1")
            @ApiParam(value = "二级工单类型", required = true) Long scene)
     *
     */
    @Test
    public void testStrategyLevelGetScene() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyLevelGet(@ApiParam(value = "抽检量级id", required = true)
            @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long id)
     *
     */
    @Test
    public void testStrategyLevelGet() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyLevelUpdate(@Valid RiskQualitySpotLevelConfUpdateVo confUpdateVo)
     *
     */
    @Test
    public void testStrategyLevelUpdate() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: strategyLevelHistoryList(@NotNull(message = "二级工单类型不能为空") @Min(value = 1, message = "二级工单类型不能小于1")
                    @ApiParam(value = "二级工单类型", required = true) Integer scene)
     *
     */
    @Test
    public void testStrategyLevelHistoryList() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: ruleRelationalList()
     *
     */
    @Test
    public void testRuleRelationalList() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: ruleEntityProp(@ApiParam(value = "质检对象id", defaultValue = "1", required = true) long scene)
     *
     */
    @Test
    public void testRuleEntityProp() throws Exception {
        //TODO: Test goes here...
    }
     
    /**
     *
     * Method: getTypeList(@ApiParam(value = "质检对象id", defaultValue = "0") Long parentId)
     *
     */
    @Test
    public void testGetTypeList() throws Exception {
        //TODO: Test goes here...
    }
     
     
}
