package com.shuidihuzhu.cf.risk.admin.service.list;

import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.model.admin.list.HospitalAuditPassDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

/** 
* ListDepartmentCaseService Tester. 
* 
* <AUTHOR> 
* @since <pre>8月 3, 2020</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class ListDepartmentCaseServiceTest { 

    @Autowired
    private ListDepartmentCaseService listDepartmentCaseService;
    @Autowired
    protected MockMvc mockMvc;

    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    } 
    
    /** 
     * 
     * Method: hospitalAuditPassHandle(@Valid HospitalAuditPassDto hospitalAuditPassDto) 
     * 
     */ 
    @Test
    public void testHospitalAuditPassHandle() throws Exception {
        HospitalAuditPassDto hospitalAuditPassDto = new HospitalAuditPassDto(
                2291126, 123L, 11, "430ff972-cc55-4008-bb18-08895d5a9887",
                "宣武医院" , "北京市", "北京市", "耳鼻喉科", List.of(
                        new HospitalAuditPassDto.LandlineNumber("010", "11112222", "123"),
                        new HospitalAuditPassDto.LandlineNumber("010", "22223333", "456")
        ));
        listDepartmentCaseService.hospitalAuditPassHandle(hospitalAuditPassDto);
    } 
    
    /** 
     * 
     * Method: queryLandlineNumberCases(ListDepartmentCaseQuery caseQuery) 
     * 
     */ 
    @Test
    public void testQueryLandlineNumberCases() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    
    /** 
     * 
     * Method: saveUpdate(HospitalAuditPassDto hospitalAuditPassDto, CfFirsApproveMaterial firstApprove, HospitalAuditPassDto.LandlineNumber landlineNumber) 
     * 
     */ 
    @Test
    public void testSaveUpdate() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = ListDepartmentCaseService.getClass().getMethod("saveUpdate", HospitalAuditPassDto.class, CfFirsApproveMaterial.class, HospitalAuditPassDto.LandlineNumber.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: assembleDepartmentTelCase(CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial firstApprove, HospitalAuditPassDto hospitalAuditPassDto, HospitalAuditPassDto.LandlineNumber landlineNumber, UserInfoModel userInfoModel) 
     * 
     */ 
    @Test
    public void testAssembleDepartmentTelCase() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = ListDepartmentCaseService.getClass().getMethod("assembleDepartmentTelCase", CrowdfundingInfo.class, CfFirsApproveMaterial.class, HospitalAuditPassDto.class, HospitalAuditPassDto.LandlineNumber.class, UserInfoModel.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
}
