package com.shuidihuzhu.cf.risk.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.cf.client.base.brand.BrandConst;
import com.shuidihuzhu.cf.enhancer.brand.BrandConfig;
import com.shuidihuzhu.cf.risk.admin.dao.WorkOrderRecordingMapper;
import com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio.AiTextValidForAudioDelegate;
import com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio.AiTextValidForAudioResp;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingDO;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import com.shuidihuzhu.cf.risk.admin.model.enums.BlackActionLimitTimeType;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeActionRef;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderParam;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.BlacklistDataQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistDataAddVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistTypeAddVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.RiskHitOperateVo;
import com.shuidihuzhu.cf.risk.admin.service.CfRepeatPayOrderService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcWorkOrderService;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListDataService;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListTypeService;
import com.shuidihuzhu.cf.risk.admin.service.hit.RiskHitService;
import com.shuidihuzhu.cf.risk.admin.service.mq.AsrCallbackConsumer;
import com.shuidihuzhu.cf.risk.admin.util.SDEncryptUtils;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyHitDto;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyRoleEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.IdentifySpotInitiatorHitEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class SimpleTest extends BaseTest{
    @Resource
    private RiskHitService riskHitService;

    @Resource
    private AiTextValidForAudioDelegate aiTextValidForAudioDelegate;

    @Resource
    private BlackListTypeService blackListTypeService;

    @Resource
    private BlackListDataService dataService;

    @Resource
    private CfRepeatPayOrderService cfRepeatPayOrderService;
    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private WorkOrderRecordingMapper workOrderRecordingMapper;

    @Autowired
    private RiskQcWorkOrderService riskQcWorkOrderService;

    @Autowired
    private CfSearchClient cfSearchClient;

    @Test
    public void test5(){
        final QcWorkOrderParam param = new QcWorkOrderParam();
        param.setPageNum(1);
        param.setPageSize(200);
        param.setOrderType(WorkOrderType.qc_hospital_dept.getType());
        param.setCaseId(2);
        param.setHandleResult(-1);
        CfWorkOrderV2IndexSearchParam searchParam = riskQcWorkOrderService.buildRiskQcSearchParam(param, null);

        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        log.info("{}", searchRpcResult);
    }
    @Test
    public void test4(){
        dataService.autoDeleteBlackList(1);
//        BlacklistDataQuery blacklistDataQuery = new BlacklistDataQuery();
//        blacklistDataQuery.setBeginTime(DateUtil.getStr2SDate("2022-03-17"));
//        blacklistDataQuery.setEndTime(new Date());
//        dataService.queryPage(blacklistDataQuery);
//        RiskHitOperateVo riskHitOperateVo = new RiskHitOperateVo();
//        riskHitOperateVo.setHandleResult(new Byte("3"));
//        riskHitOperateVo.setHandleActions(Arrays.asList(5, 6));
//        riskHitOperateVo.setRemark("擦擦撒打算打算");
//        riskHitOperateVo.setCaseId(2292700);
//        riskHitOperateVo.setHitPhaseCode(6);
//        riskHitOperateVo.setId(18335L);
//        List<BlacklistTypeActionRefDto> actionList = new ArrayList<>();
//        BlacklistTypeActionRefDto blacklistTypeActionRefDto = new BlacklistTypeActionRefDto();
//        blacklistTypeActionRefDto.setActionId(9);
//        blacklistTypeActionRefDto.setLimitTime(1647571338000L);
//        blacklistTypeActionRefDto.setLimitTimeType(BlackActionLimitTimeType.DAYS_180.getCode());
//        BlacklistTypeActionRefDto blacklistTypeActionRefDto1 = new BlacklistTypeActionRefDto();
//        blacklistTypeActionRefDto1.setActionId(8);
//        blacklistTypeActionRefDto1.setLimitTime(1647571338000L);
//        blacklistTypeActionRefDto1.setLimitTimeType(BlackActionLimitTimeType.DAYS_180.getCode());
//        actionList.add(blacklistTypeActionRefDto);
//        actionList.add(blacklistTypeActionRefDto1);
//        riskHitOperateVo.setActionList(actionList);
////        riskHitService.handleHitInfo(riskHitOperateVo);
////
//        dataService.autoAddBlacklist(riskHitOperateVo);
//        BlacklistTypeAddVo blacklistTypeAddVo = new BlacklistTypeAddVo();
//        blacklistTypeAddVo.setLevelIds(Arrays.asList(269L,270L,0L));
//        blacklistTypeAddVo.setLevelNames(Arrays.asList("1","2","12312221"));
//        blacklistTypeAddVo.setLevelType(Arrays.asList(1,1,2));
//        RiskBlacklistTypeActionRef riskBlacklistTypeActionRef = new RiskBlacklistTypeActionRef();
//        riskBlacklistTypeActionRef.setActionId(9L);
//        riskBlacklistTypeActionRef.setLimitTimeType(BlackActionLimitTimeType.DAYS_30.getCode());
//        blacklistTypeAddVo.setTypeActions(Collections.singletonList(riskBlacklistTypeActionRef));
//        blackListTypeService.saveType(blacklistTypeAddVo);
//
//        BlacklistDataAddVo blacklistDataAddVo = new BlacklistDataAddVo();
//        blacklistDataAddVo.setBornCard("123");
//        blacklistDataAddVo.setUserName("123");
//        blacklistDataAddVo.setOperateReason("asdasd");
//        blacklistDataAddVo.setTypeIds(Collections.singletonList(639L));
//        dataService.addData(blacklistDataAddVo, "新增黑名单");

//        cfRepeatPayOrderService.check(Arrays.asList(2959956, 2959067));
//
//        List<StrategyHitDto> strategyHitDtos = riskHitService.listRepeatPayOrderCaseByCaseId(2959067);
//        System.out.println(strategyHitDtos);
    }

    @Test
    public void test3(){
        String encrypt = SDEncryptUtils.encrypt("");
        System.out.println("encrypt = " + encrypt);
    }

    @Test
    public void test2(){
        //插入风控引擎策略
        StrategyHitDto strategyHitDto = new StrategyHitDto();
        strategyHitDto.setCaseId(123);
        strategyHitDto.setLaunchTime(new Date());
        strategyHitDto.setHitPhase(BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL.getCode());
        strategyHitDto.setRiskStrategy(RiskStrategyEnum.DISHONEST_PEOPLE_DISCERN.getCode());
        strategyHitDto.setSecondStrategy(RiskStrategySecondEnum.PATIENT_DISHONEST_DISCERN.getCode());
        strategyHitDto.setRiskType(IdentifySpotInitiatorHitEnum.FORTY.getCode());

        List<BlacklistVerifyDto> list = new ArrayList<>();
        BlacklistVerifyDto userBlack = new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PATIENT.getCode(), BlacklistVerifyTypeEnum.USER.getCode(),"roy-测试");
        userBlack.setHit(true);
        userBlack.setLimitActionIds(Lists.newArrayList());
        userBlack.setTypeIds(Lists.newArrayList());

        BlacklistVerifyDto paientBlack = new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PATIENT.getCode(),BlacklistVerifyTypeEnum.ID_CARD.getCode(),"uckw+UOzH0k6DPQF/+21tuCwXxc465bbvtqmhf0sMrY=");
        paientBlack.setHit(true);
        paientBlack.setLimitActionIds(Lists.newArrayList());
        paientBlack.setTypeIds(Lists.newArrayList());
        list.add(userBlack);
        list.add(paientBlack);
        String hitInfo = JSON.toJSONString(list);
        strategyHitDto.setHitInfo(hitInfo);

        RiskStrategyHitRecord strategyHitRecord = new RiskStrategyHitRecord();
        BeanUtils.copyProperties(strategyHitDto, strategyHitRecord);

        riskHitService.uniteSaveHitRecord(strategyHitRecord);

    }

    @Test
    public void test1(){

        String key = "brand.should-join-activity";
        String cf = BrandConfig.getConfig(BrandConst.Brand.sdc).getProperty(key, null);
        System.out.println("cf = " + cf);
        String jjb = BrandConfig.getConfig(BrandConst.Brand.jjb).getProperty(key, null);
        System.out.println("jjb = " + jjb);

        String three = BrandConfig.getConfig(3).getProperty(key, null);
        System.out.println("three = " + three);

    }
    @Autowired
    private AsrCallbackConsumer asrCallbackConsumer;
    @Test
    public void testAsrResult() {

        ConsumerMessage<OceanApiMQResponse> consumerMessage = new ConsumerMessage<>();
        OceanApiMQResponse oceanApiMQResponse = new OceanApiMQResponse();
        oceanApiMQResponse.setResultCode(0);
        oceanApiMQResponse.setBody("{\"callback_url\":\"http://ai-asr-bedin.shuidi.io/innerapi/ai/python/offline/callback\",\"result\":{\"length\":49896,\"runtime\":2.2584445476531982,\"stereo\":false,\"method\":\"speechXL\",\"model\":\"base\",\"sentences\":[{\"begin_time\":120,\"channel_id\":0,\"end_time\":1919,\"loudness\":-28.667660013492096,\"speaker_type\":\"s\",\"text\":\"我现在嗯嗯。\",\"prob\":0.6103718939933848},{\"begin_time\":5819,\"channel_id\":0,\"end_time\":7679,\"loudness\":-28.178381622746727,\"speaker_type\":\"s\",\"text\":\"唉这个能听到吗？\",\"prob\":0.8503409267048367},{\"begin_time\":7859,\"channel_id\":0,\"end_time\":11039,\"loudness\":-28.301587796843094,\"speaker_type\":\"s\",\"text\":\"啊能听到能听到，刚刚那个给你打电话！\",\"prob\":0.9222606993299095},{\"begin_time\":11640,\"channel_id\":0,\"end_time\":13439,\"loudness\":-28.560469558468643,\"speaker_type\":\"s\",\"text\":\"等一下等我回去！\",\"prob\":0.9615614263791225},{\"begin_time\":22979,\"channel_id\":0,\"end_time\":23579,\"loudness\":-19.9703625218341,\"speaker_type\":\"s\",\"text\":\"行不行？\",\"prob\":0.8269259666487478},{\"begin_time\":23579,\"channel_id\":1,\"end_time\":26339,\"loudness\":-27.825437589700854,\"speaker_type\":\"c\",\"text\":\"现在先找不到时间了，那个。\",\"prob\":0.7032879969095077},{\"begin_time\":26339,\"channel_id\":0,\"end_time\":31619,\"loudness\":-27.202277949893123,\"speaker_type\":\"s\",\"text\":\"有通话时间，有通话情况吗？现在没有。\",\"prob\":0.718039645048873},{\"begin_time\":47160,\"channel_id\":1,\"end_time\":49859,\"loudness\":-32.590471903165735,\"speaker_type\":\"c\",\"text\":\"唉等一分钟！\",\"prob\":0.7779208675447259}],\"speaking_rate\":{\"all\":243.2797717374981,\"c\":208.82945594431212,\"s\":256.2336409973825}},\"trace_id\":\"624ff6a3e3f003000efe2dec\",\"success\":true,\"input_url\":\"http://cf-growthtool-api-1254024480.cos.ap-beijing.myqcloud.com/call-audio/8002246-20220408111317-15901033661-13039595239--record-medias_5-1649387597.46707?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDkHaCYeaa9nVdYCogY9a7rNcaI3YnYTv7%26q-sign-time%3D1649407650%3B1649411250%26q-key-time%3D1649407650%3B1649411250%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D8934d9cf1f29836803e8eb8d5670dc2a01b032d2&materialId=67&handleType=5&workOrderId=1459881\",\"id\":\"462e8a35-ed89-4ce3-9907-4b9202fec435\"}");
        consumerMessage.setPayload(oceanApiMQResponse);
        asrCallbackConsumer.consumeMessage(consumerMessage);
    }
}
