package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfCaseInitalAuditConsumerTest {

    @Autowired
    private CfCaseInitalAuditConsumer cfCaseInitalAuditConsumer;
    @Autowired
    private QcWorkOrderCreateConsumer qcWorkOrderCreateConsumer;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testConsumeMessage() {
        ConsumerMessage<InitialAuditItem.InitialAuditOperation> mqMessage = new ConsumerMessage<>();
        InitialAuditItem.InitialAuditOperation initialAuditOperation = new InitialAuditItem.InitialAuditOperation();
        initialAuditOperation.setCaseId(2290285);
        mqMessage.setPayload(initialAuditOperation);
//        qcWorkOrderCreateConsumer.consumeMessage(mqMessage);
    }
}