package com.shuidihuzhu.cf.risk.admin.service.blacklist;

import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistDataDto;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.UpdateBatchDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;
import java.util.Set;

/** 
* BlackListDataService Tester. 
* 
* <AUTHOR> 
* @since <pre>7月 22, 2020</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class BlackListDataServiceTest { 

    @Autowired
    private BlackListDataService blackListDataService;
    @Autowired
    protected MockMvc mockMvc;

    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    }

    @Test
    public void queryNotBoundUidTest() {
        List<BlacklistDataDto> blacklistDataDtos = blackListDataService.queryNotBoundUid(0L, 10);
        System.out.println(blacklistDataDtos);
    }

    @Test
    public void updateBindUidByDataIdTest() {
        List<UpdateBatchDto> updateBatchDtos = List.of(
                new UpdateBatchDto(2L, 222L),
                new UpdateBatchDto(3L, 333L));
        blackListDataService.updateBindUidByDataId(updateBatchDtos);
    }

    @Test
    public void queryNotBoundMobileTest() {
        List<BlacklistDataDto> blacklistDataDtos = blackListDataService.queryNotBoundMobile(0L, 10);
        System.out.println(blacklistDataDtos);
    }

    @Test
    public void updateBindMobileByDataId() {
        List<UpdateBatchDto> updateBatchDtos = List.of(
                new UpdateBatchDto(4L, "ekIJgGZ/7upKmJWcgmRX1w=="));
        blackListDataService.updateBindMobileByDataId(updateBatchDtos);
    }

    @Test
    public void asyncDataActions() {
        blackListDataService.asyncDataActions(58L, Set.of(6L,7L), Set.of(1L));
    }


}
