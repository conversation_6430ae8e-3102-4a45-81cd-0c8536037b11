package com.shuidihuzhu.cf.risk.admin.util;


import com.google.common.collect.Lists;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import okhttp3.*;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-05
 **/
public class SoundTest {

    private static final String cosPrefix = "https://cf-growthtool-api-1254024480.cos.ap-beijing.myqcloud.com";

    public static void main(String[] args) {
        // 0:声音低的
        //
        String[] urlArr = new String[]{
                "https://cf-growthtool-api-1254024480.cos.ap-beijing.myqcloud.com/xjy-record/d093daab-d899-4c5a-b232-e49384ab46941594901031226.mp3",
//                "https://cf-growthtool-api-1254024480.cos.ap-beijing.myqcloud.com/xjy-record/1a79dda4-0f9d-4e95-8395-8e8ccfe1bac41596784480191.mp3",
//                "https://cf-growthtool-api-1254024480.cos.ap-beijing.myqcloud.com/xjy-record/8bcc799a-102e-4d8a-8ee9-37bc23199e511596783867030.mp3",
//                "https://cf-growthtool-api-1254024480.cos.ap-beijing.myqcloud.com/xjy-record/596c3668-57ed-4d32-a992-dfc40a1def121596789085206.mp3",
//                "https://cf-growthtool-api-1254024480.cos.ap-beijing.myqcloud.com/xjy-record/1641b9f9-7054-4593-94b0-f7ecb91899061596614504669.mp3"
        };

        for (int i = 0; i < urlArr.length; i++) {
            String url = CosUploadUtil.getCosSignWithUrl(urlArr[i]);
            System.out.println(i + " " + url);
            accessImageAndFile(url, i);
        }
    }

    private static void accessImageAndFile(String url, int i) {
        OkHttpClient okClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();
        Request request = new Request.Builder().url(url).build();
        try {
            okClient.newCall(request).enqueue(new Callback() {

                @Override
                public void onResponse(Call arg0, Response arg1) throws IOException {
                    BufferedInputStream buffInputStream = new BufferedInputStream(arg1.body().byteStream());
                    try {
//                        Sound sound = new Sound(buffInputStream);
//                        AudioFormat audioFormat = sound.getAudioFormat();
//                        System.out.println(audioFormat);
                        // 文件流
                        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(buffInputStream);
                        //将AudioInputStream MP3文件 转换为PCM
                        AudioFormat audioFormat = audioInputStream.getFormat();
                        System.out.println(audioFormat);
                        AudioFormat targetFormat = new AudioFormat(AudioFormat.Encoding.PCM_SIGNED, audioFormat.getSampleRate(), 16,
                                audioFormat.getChannels(), audioFormat.getChannels() * 2, audioFormat.getSampleRate(), false);

                        System.out.println(targetFormat);
                        AudioInputStream mp3audioStream = AudioSystem.getAudioInputStream(targetFormat, audioInputStream);
//                        byte[] pcmBytes = IOUtils.toByteArray(mp3audioStream);
//                        System.out.println(JSON.toJSONString(pcmBytes));
                        test1(targetFormat, mp3audioStream, i);
//                        mp3audioStream.reset();
//                        test2(targetFormat, mp3audioStream, i);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFailure(Call arg0, IOException arg1) {
                    // TODO Auto-generated method stub
                    System.out.println("获取服务器数据失败");
                }
            });
        } catch (Exception e) {
            // TODO: handle exception
            System.out.println(e);
        }
    }

    private static void test1(AudioFormat targetFormat, AudioInputStream mp3audioStream, int num) throws Exception {
        // 指定时间的样本数
        double sec = 1000; // 要表示的时长(s)
        int mount = (int) (targetFormat.getSampleRate() * sec);
        // 读取声音数据
        List<Integer> values = Lists.newArrayList();
        while (true) {
            // 1帧的大小
            int size = targetFormat.getFrameSize();
            byte[] data = new byte[size];
            int readedSize = mp3audioStream.read(data);
            // 读取失败时
            if (readedSize == -1) {
                break;
            }
            // SampleSizeInBits
            switch (targetFormat.getSampleSizeInBits()) {
                case 8:
                    values.add((int) data[0]);
                    break;
                case 16:
                    values.add((int) ByteBuffer.wrap(data).order(ByteOrder.LITTLE_ENDIAN).getShort());
                    break;
                default:
                    break;
            }
        }
//        List<Integer> value0 = values.stream().filter(value -> value == 0).collect(Collectors.toList());
//        System.out.println("test1-0" + num + " " + value0.size() + " " +
//                MoneyUtil.divide("" + value0.size(), "" + values.size(), 2, 5));

        int space = 1000;// 区间的间隔
        Map<String, Long> map1000 = values.stream().collect(Collectors.groupingBy((value) -> {
            return "区间:[" + value / space * space + "," + (value / space + 1) * space + ")";// 分组规则
        }, Collectors.counting()));
        map1000 = sortByValue(map1000);
        map1000.forEach((k, v) -> {
            System.out.println("test1-1000" + num + " " + k + " : " + v + " " +
                    MoneyUtil.divide("" + v, "" + values.size(), 3, BigDecimal.ROUND_HALF_UP));
        });
        List<Integer> value1000 = values.stream().filter(value -> value >= -1000 && value < 1000).collect(Collectors.toList());

        int space100 = 100;// 区间的间隔
        Map<String, Long> map100 = value1000.stream().collect(Collectors.groupingBy((value) -> {
            return "区间:[" + value / space100 * space100 + "," + (value / space100 + 1) * space100 + ")";// 分组规则
        }, Collectors.counting()));
        map100 = sortByValue(map100);
        map100.forEach((k, v) -> {
            System.out.println("test1-100" + num + " " + k + " : " + v + " " +
                    MoneyUtil.divide("" + v, "" + values.size(), 3, BigDecimal.ROUND_HALF_UP));
        });

        List<Integer> value100 = value1000.stream().filter(value -> value >= -100 && value < 100).collect(Collectors.toList());
        int space10 = 10;// 区间的间隔
        Map<String, Long> map10 = value100.stream().collect(Collectors.groupingBy((value) -> {
            return "区间:[" + value / space10 * space10 + "," + (value / space10 + 1) * space10 + ")";// 分组规则
        }, Collectors.counting()));
        map10 = sortByValue(map10);
        map10.forEach((k, v) -> {
            System.out.println("test1-10" + num + " " + k + " : " + v + " " +
                    MoneyUtil.divide("" + v, "" + values.size(), 3, BigDecimal.ROUND_HALF_UP));
        });
        long count = value100.stream().filter(value -> value == 0).count();
        System.out.println("test1-0 " + count + " " +
                MoneyUtil.divide("" + count, "" + values.size(), 3, BigDecimal.ROUND_HALF_UP));
    }

    public static void test2(AudioFormat targetFormat, AudioInputStream mp3audioStream, int num) throws IOException {
        List<Float> outputMusicList = new LinkedList<Float>();
        int frameSize = targetFormat.getFrameSize();

        boolean bigEndian = targetFormat.isBigEndian();
        boolean encodingUnsigned = targetFormat.getEncoding() == AudioFormat.Encoding.PCM_UNSIGNED;
        int bitsize = targetFormat.getSampleSizeInBits();
        int numChannels = targetFormat.getChannels();
//        outputMusicList = new SoundLinkedList(targetFormat.getSampleRate(), numChannels);

        if (encodingUnsigned && bitsize == 8) {
            while (mp3audioStream.available() > 0) {
                int nextInput = mp3audioStream.read();
                nextInput -= 128;
                outputMusicList.add(((float) nextInput) / 128f);
            }
        } else {
            while (true) {
                byte[] buff = new byte[frameSize];
                int bytesRead = mp3audioStream.read(buff, 0, frameSize);
                if (bytesRead != frameSize) {
                    System.out.println("Not Enough Bytes!");
                    break;
                }
                float[] values = new float[numChannels];
                for (int currentChannel = 0; currentChannel < numChannels; currentChannel++) {
                    byte highByte = buff[currentChannel * 2];
                    byte lowByte = buff[currentChannel * 2 + 1];

                    if (!bigEndian) {
                        byte tmp = lowByte;
                        lowByte = highByte;
                        highByte = tmp;
                    }
                    int highInt = highByte;
                    int lowInt = ((int) lowByte) & 0xFF;
                    highInt <<= 24;
                    highInt >>= 16;
                    int result = lowInt + highInt;
                    values[currentChannel] = ((float) result) / (float) 0x7FFF;
                }
                for (float value : values) {
                    outputMusicList.add(value);
                }
            }
        }
//        System.out.println(outputMusicList);
        float max = outputMusicList.stream().max(Float::compareTo).get();
        float min = outputMusicList.stream().min(Float::compareTo).get();
        System.out.println("test2:" + num + " " + min + " " + max);
    }

    public static <K, V extends Comparable<? super V>> Map<K, V> sortByValue(Map<K, V> map) {
        Map<K, V> result = new LinkedHashMap<>();
        map.entrySet().stream()
                .sorted(Map.Entry.<K, V>comparingByValue()
                        .reversed()).forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
        return result;
    }
}
