package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyRoleEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistExtendDto;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.supprt.RocketMQHeaders;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.messaging.MessageHeaders;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/** 
* BlacklistHitConsumer Tester. 
* 
* <AUTHOR> 
* @since <pre>8月 24, 2020</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class BlacklistHitConsumerTest { 

    @Autowired
    private BlacklistHitConsumer blacklistHitConsumer;
    @Autowired
    protected MockMvc mockMvc;

    /**
     * 
     * Method: consumeMessage(ConsumerMessage<BlacklistExtendDto> mqMessage) 
     * 
     */ 
    @Test
    public void testConsumeMessage() throws Exception {
        List<BlacklistVerifyDto> blacklistVerifyDtoList = new ArrayList<>();
        BlacklistVerifyDto blacklistVerifyDto1 = new BlacklistVerifyDto();
        blacklistVerifyDto1.setVerifyType(BlacklistVerifyTypeEnum.MOBILE.getCode());
        blacklistVerifyDto1.setVerifyData("13788883333");
        blacklistVerifyDto1.setTypeIds(List.of(53L,54L));
        blacklistVerifyDto1.setLimitActionIds(List.of(1L,3L));
        blacklistVerifyDto1.setVerifyRole(BlacklistVerifyRoleEnum.INITIATOR.getCode());
        blacklistVerifyDto1.setHit(true);
        blacklistVerifyDtoList.add(blacklistVerifyDto1);

        BlacklistVerifyDto blacklistVerifyDto2 = new BlacklistVerifyDto();
        blacklistVerifyDto2.setVerifyRole(BlacklistVerifyRoleEnum.PATIENT.getCode());
        blacklistVerifyDto2.setVerifyType(BlacklistVerifyTypeEnum.BORN_CARD.getCode());
        blacklistVerifyDto2.setVerifyData("2334234234534544");
        blacklistVerifyDtoList.add(blacklistVerifyDto2);

        BlacklistVerifyDto blacklistVerifyDto3 = new BlacklistVerifyDto();
        blacklistVerifyDto3.setVerifyRole(BlacklistVerifyRoleEnum.PAYEE.getCode());
        blacklistVerifyDto3.setVerifyType(BlacklistVerifyTypeEnum.ID_CARD.getCode());
        blacklistVerifyDto3.setVerifyData("23342342345345442");
        blacklistVerifyDto3.setHit(true);
        blacklistVerifyDto3.setTypeIds(List.of(55L));
        blacklistVerifyDto3.setLimitActionIds(List.of(2L,3L));
        blacklistVerifyDtoList.add(blacklistVerifyDto3);

        BlacklistVerifyDto blacklistVerifyDto4 = new BlacklistVerifyDto();
        blacklistVerifyDto3.setVerifyRole(BlacklistVerifyRoleEnum.INITIATOR.getCode());
        blacklistVerifyDto4.setVerifyType(BlacklistVerifyTypeEnum.USER_ID.getCode());
        blacklistVerifyDto4.setVerifyData("9828");
        blacklistVerifyDto4.setHit(true);
        blacklistVerifyDto4.setTypeIds(List.of(67L, 68L));
        blacklistVerifyDto4.setLimitActionIds(List.of(1L,2L,3L,4L));
        blacklistVerifyDtoList.add(blacklistVerifyDto4);

        BlacklistExtendDto blacklistExtendDto = new BlacklistExtendDto(123, "2020-08-01 12:23:33", 1, blacklistVerifyDtoList, true);

        ConsumerMessage consumerMessage = new ConsumerMessage(blacklistExtendDto,
                new MessageHeaders(Map.of(RocketMQHeaders.KEYS, UUID.randomUUID().toString())), null);
        ConsumeStatus consumeStatus = blacklistHitConsumer.consumeMessage(consumerMessage);
        Assert.assertTrue(consumeStatus == ConsumeStatus.CONSUME_SUCCESS);
    }
    
}
