package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * @author: cuikexiang
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class RiskQualitySpotStrategyTypeRelDaoTest {

    @Autowired
    private RiskQualitySpotStrategyTypeRelDao riskQualitySpotStrategyTypeRelDao;



    @Test
    public void test_deleteByStrategyId(){
        riskQualitySpotStrategyTypeRelDao.deleteByStrategyId(1);
    }
}