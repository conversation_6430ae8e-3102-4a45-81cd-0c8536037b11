package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.admin.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class GetInternalStaffAndEmergencyMobileDAOTest extends BaseTest {

    @Autowired
    private GetInternalStaffAndEmergencyMobileDAO getInternalStaffAndEmergencyMobileDAO;

    @Test
    public void testInfo() {
        //getInternalStaffAndEmergencyMobileDAO.insert("a1","1111","a2","1222","类型1");
        System.out.println(getInternalStaffAndEmergencyMobileDAO.selectByStaffMobile("1111"));
        System.out.println(getInternalStaffAndEmergencyMobileDAO.selectByEmergencyMobile("1222"));
    }

}