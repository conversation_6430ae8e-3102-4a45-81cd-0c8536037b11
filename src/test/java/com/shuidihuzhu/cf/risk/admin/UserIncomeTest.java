package com.shuidihuzhu.cf.risk.admin;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.client.rpc.UserIncomeClient;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2020-02-25
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class UserIncomeTest {

    private static final ExecutorService THREAD_POOL = Executors.newFixedThreadPool(10);

    @Autowired
    private UserIncomeClient userIncomeClient;
    @Resource
    private ShuidiCipher shuidiCipher;

    @Test
    public void exportExcel() {
        String fileName = "/Users/<USER>/doc/外部数据第二批数据样本0305.xlsx";
        String fileNameOut = "/Users/<USER>/doc/外部数据第二批数据样本0305-01.xlsx";

        try (InputStream fis = new FileInputStream(fileName)) {
            Workbook workbook = null;
            if (fileName.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(fis);
            } else if (fileName.endsWith(".xls") || fileName.endsWith(".et")) {
                workbook = new HSSFWorkbook(fis);
            }
            /* 读EXCEL文字内容 */
            // 获取第一个sheet表，也可使用sheet表名获取
            Sheet sheet = workbook.getSheetAt(0);
            // 获取行
            Iterator<Row> rows = sheet.rowIterator();
            Row title = rows.next();
            // 获取表头单元格
            List<String> titleValues = getCellValues(title);
            log.info("titleValues:{}", JSON.toJSONString(titleValues));
            CountDownLatch countDownLatch = new CountDownLatch(sheet.getPhysicalNumberOfRows() - 1);
            while (rows.hasNext()) {
                Row row = rows.next();
                // 获取单元格
                List<String> cellValues = getCellValues(row);
                getThirdData(cellValues, row, countDownLatch);
                log.info("cellValues:{}", JSON.toJSONString(cellValues));
            }
            countDownLatch.await();
            try (FileOutputStream out = new FileOutputStream(fileNameOut)) {
                workbook.write(out);
                out.flush();
            } catch (Exception e) {
                log.error("", e);
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    private List<String> getCellValues(Row row) {
        Cell cell;
        Iterator<Cell> cells = row.cellIterator();
        List<String> cellValues = Lists.newArrayList();
        while (cells.hasNext()) {
            cell = cells.next();
            String cellValue = POIUtil.getCellValue(cell);
            cellValues.add(cellValue);
        }
        return cellValues;
    }

    private void getThirdData(List<String> cellValues, Row row, CountDownLatch countDownLatch) {
        THREAD_POOL.submit(() -> {
            // 发起人
            if(cellValues.size() >= 4 && StringUtils.isNotBlank(cellValues.get(2)) && StringUtils.isNotBlank(cellValues.get(3))) {
                String name = cellValues.get(2);
                String idCard = shuidiCipher.decrypt(cellValues.get(3));
                cellValues.set(3, idCard);
                try {
                    Response<Map<String, String>> shangyongData = userIncomeClient.getShangyongData(idCard, "", name, "00");
                    row.createCell(10).setCellValue(shangyongData.getData().get("level"));
                } catch (Exception e) {
                    log.error("", e);
                }
//                try {
//                    Response<Map<String, String>> shangyongData1 = userIncomeClient.getShangyongData(idCard, "", name, "01");
//                    row.createCell(9).setCellValue(shangyongData1.getData().get("level"));
//                } catch (Exception e) {
//                    log.error("", e);
//                }
//                try {
//                    Response<Map<String, String>> bairongData = userIncomeClient.getBairongData(idCard, "", name);
//                    row.createCell(10).setCellValue(bairongData.getData().get("level"));
//                } catch (Exception e) {
//                    log.error("", e);
//                }
            }
            // 患者
            if(cellValues.size() >= 7 && StringUtils.isNotBlank(cellValues.get(5)) && StringUtils.isNotBlank(cellValues.get(6))) {
                String name = cellValues.get(5);
                String idCard = shuidiCipher.decrypt(cellValues.get(6));
                cellValues.set(6, idCard);
                try {
                    Response<Map<String, String>> shangyongData = userIncomeClient.getShangyongData(idCard, "", name, "00");
                    row.createCell(11).setCellValue(shangyongData.getData().get("level"));
                } catch (Exception e) {
                    log.error("", e);
                }
//                try {
//                    Response<Map<String, String>> shangyongData1 = userIncomeClient.getShangyongData(idCard, "", name, "01");
//                    row.createCell(12).setCellValue(shangyongData1.getData().get("level"));
//                } catch (Exception e) {
//                    log.error("", e);
//                }
//                try {
//                    Response<Map<String, String>> bairongData = userIncomeClient.getBairongData(idCard, "", name);
//                    row.createCell(13).setCellValue(bairongData.getData().get("level"));
//                } catch (Exception e) {
//                    log.error("", e);
//                }
            }
            countDownLatch.countDown();
            log.info("countDownLatch.count:{}", countDownLatch.getCount());
        });
    }
}
