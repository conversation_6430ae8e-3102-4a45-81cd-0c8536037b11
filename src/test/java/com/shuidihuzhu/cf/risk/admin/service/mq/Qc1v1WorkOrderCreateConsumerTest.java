package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.admin.biz.impl.RiskQcSearchIndexBizImpl;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoStatModel;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class Qc1v1WorkOrderCreateConsumerTest{

    @Autowired
    private Qc1v1WorkOrderCreateConsumer qc1v1WorkOrderCreateConsumer;
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private QcCallWorkOrderCreateConsumer qcCallWorkOrderCreateConsumer;
    @Autowired
    private QcMaterialWorkOrderCreateConsumer qcMaterialWorkOrderCreateConsumer;


    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Test
    public void test(){
        var cfClueInfo = cfClewtrackTaskFeignClient.listCfClueInfo(List.of(214161L));
        var cfClueInfoModel = cfClueInfo.getData().get(0);

        CfClewTaskDO cfClewTaskDO = new CfClewTaskDO();
        cfClewTaskDO.setId(cfClueInfoModel.getTaskId());
        cfClewTaskDO.setUserId(cfClueInfoModel.getUserId());
        cfClewTaskDO.setUserName("李飞飞");
        cfClewTaskDO.setWorkContentType(cfClueInfoModel.getWorkContentType());

        var build = CfClueInfoStatModel.builder().cfClewTaskDO(cfClewTaskDO).build();
        ConsumerMessage<CfClueInfoStatModel> mqMessage = new ConsumerMessage<>();
        mqMessage.setPayload(build);

        var consumeStatus = qc1v1WorkOrderCreateConsumer.consumeMessage(mqMessage);
    }

    @Test
    public void testCall(){

        var cfClueInfo = cfClewtrackTaskFeignClient.getClewTaskModel(List.of(254070L));
        var cfClueInfoModel = cfClueInfo.getData().get(0);

        CfClewTaskDO cfClewTaskDO = new CfClewTaskDO();
        cfClewTaskDO.setId(254070L);
        cfClewTaskDO.setUserId(cfClueInfoModel.getClewUserId());
        cfClewTaskDO.setUserName(cfClueInfoModel.getClewUserName());
        cfClewTaskDO.setWorkContentType(cfClueInfoModel.getWorkContentType());

        var build = CfClueInfoStatModel.builder().cfClewTaskDO(cfClewTaskDO).build();
        ConsumerMessage<CfClueInfoStatModel> mqMessage = new ConsumerMessage<>();
        mqMessage.setPayload(build);
        qcCallWorkOrderCreateConsumer.consumeMessage(mqMessage);
    }

    @Test
    public void testMaterial(){
        WorkOrderResultChangeEvent workOrderResultChangeEvent = new WorkOrderResultChangeEvent();
        workOrderResultChangeEvent.setWorkOrderId(46207);
        workOrderResultChangeEvent.setHandleResult(9);
        workOrderResultChangeEvent.setOrderType(26);
        ConsumerMessage<WorkOrderResultChangeEvent> mqMessage = new ConsumerMessage<>();
        mqMessage.setPayload(workOrderResultChangeEvent);
        qcMaterialWorkOrderCreateConsumer.consumeMessage(mqMessage);
    }

    @Test
    public void testll() {
//        qcWorkOrderCreate4RecordingConsumer.handleWorkOrderRecording(1460701L, 75);
    }
}
