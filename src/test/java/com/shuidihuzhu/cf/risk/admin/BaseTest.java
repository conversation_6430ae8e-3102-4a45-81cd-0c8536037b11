package com.shuidihuzhu.cf.risk.admin;

import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;

/**
 * <AUTHOR>
 * @date 2018-09-10  19:37
 */
@Slf4j
public class BaseTest {

    static {
        System.setProperty(StringConstants.SPRING_CONSUL_HOST, StringConstants.ZELDA_HOST);
    }

    @Before
    public void baseBefore(){
        System.out.println("BaseTest.before");
    }

    @After
    public void baseAfter(){
        System.out.println("BaseTest.after");
    }
}
