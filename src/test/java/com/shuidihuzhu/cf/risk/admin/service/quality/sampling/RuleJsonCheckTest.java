package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.everit.json.schema.Schema;
import org.everit.json.schema.ValidationException;
import org.everit.json.schema.loader.SchemaLoader;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2020/6/24 20:09
 */
@Slf4j
public class RuleJsonCheckTest {

    private static final Schema SCHEMA;

    static {
        try (InputStream inputStream = RuleJsonCheckTest.class.getResourceAsStream("/ruleJsonSchema.json")) {
            JSONObject schemaJson = new JSONObject(new JSONTokener(inputStream));
            SCHEMA = SchemaLoader.load(schema<PERSON>son);
        } catch (IOException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws IOException {
        String json = "" +
                "[\n" +
                "  {\n" +
                "    \"name\": \"质检抽检规则01\",\n" +
                "    \"priority\": 2,\n" +
                "    \"status\": 0,\n" +
                "    \"criterionGroup\": {\n" +
                "      \"junctionType\": \"AND\",\n" +
                "      \"criterions\": [\n" +
                "        {\n" +
                "          \"criterionType\": \"RELATIONAL\",\n" +
                "          \"relational\": {\n" +
                "            \"op\": \"GREATER_THEN\",\n" +
                "            \"leftValue\": {\n" +
                "              \"valueType\": \"VARIABLE\",\n" +
                "              \"variable\": {\n" +
                "                \"fieldName\": \"recordingTime\",\n" +
                "                \"fieldLabel\": \"录音时长\",\n" +
                "                \"fieldType\": \"Long\",\n" +
                "                \"sourcePath\": \"recordingTime\"\n" +
                "              }\n" +
                "            },\n" +
                "            \"rightValue\": {\n" +
                "              \"valueType\": \"CONSTANT\",\n" +
                "              \"content\": \"3\"\n" +
                "            }\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"criterionType\": \"RELATIONAL\",\n" +
                "          \"relational\": {\n" +
                "            \"op\": \"IN\",\n" +
                "            \"rightValue\": {\n" +
                "              \"valueType\": \"CONSTANT\",\n" +
                "              \"content\": \"[1,2,3]\"\n" +
                "            },\n" +
                "            \"leftValue\": {\n" +
                "              \"valueType\": \"VARIABLE\",\n" +
                "              \"variable\": {\n" +
                "                \"fieldName\": \"areaId\",\n" +
                "                \"fieldLabel\": \"所在区域\",\n" +
                "                \"fieldType\": \"Long\",\n" +
                "                \"sourcePath\": \"areaId\",\n" +
                "                \"aaa\": 1\n" +
                "              }\n" +
                "            }\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"criterionType\": \"RELATIONAL\",\n" +
                "          \"relational\": {\n" +
                "            \"op\": \"EQUALS\",\n" +
                "            \"leftValue\": {\n" +
                "              \"valueType\": \"VARIABLE\",\n" +
                "              \"variable\": {\n" +
                "                \"fieldName\": \"adviserName\",\n" +
                "                \"fieldLabel\": \"顾问姓名\",\n" +
                "                \"fieldType\": \"String\",\n" +
                "                \"sourcePath\": \"adviserName\"\n" +
                "              }\n" +
                "            },\n" +
                "            \"rightValue\": {\n" +
                "              \"valueType\": \"CONSTANT\",\n" +
                "              \"content\": \"小米辣\"\n" +
                "            }\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"criterionType\": \"CRITERION_GROUP\",\n" +
                "          \"criterionGroup\": {\n" +
                "            \"junctionType\": \"OR\",\n" +
                "            \"criterions\": [\n" +
                "              {\n" +
                "                \"criterionType\": \"RELATIONAL\",\n" +
                "                \"relational\": {\n" +
                "                  \"op\": \"LESS_THEN\",\n" +
                "                  \"leftValue\": {\n" +
                "                    \"valueType\": \"VARIABLE\",\n" +
                "                    \"variable\": {\n" +
                "                      \"fieldName\": \"validQualitySpotCount\",\n" +
                "                      \"fieldLabel\": \"顾问当月有效质检数\",\n" +
                "                      \"fieldType\": \"Long\",\n" +
                "                      \"sourcePath\": \"validQualitySpotCount\"\n" +
                "                    }\n" +
                "                  },\n" +
                "                  \"rightValue\": {\n" +
                "                    \"valueType\": \"CONSTANT\",\n" +
                "                    \"content\": \"2\"\n" +
                "                  }\n" +
                "                }\n" +
                "              },\n" +
                "              {\n" +
                "                \"criterionType\": \"RELATIONAL\",\n" +
                "                \"relational\": {\n" +
                "                  \"op\": \"GREATER_THEN\",\n" +
                "                  \"leftValue\": {\n" +
                "                    \"valueType\": \"VARIABLE\",\n" +
                "                    \"variable\": {\n" +
                "                      \"fieldName\": \"recordingTime\",\n" +
                "                      \"fieldLabel\": \"录音时长\",\n" +
                "                      \"fieldType\": \"Long\",\n" +
                "                      \"sourcePath\": \"recordingTime\"\n" +
                "                    }\n" +
                "                  },\n" +
                "                  \"rightValue\": {\n" +
                "                    \"valueType\": \"CONSTANT\",\n" +
                "                    \"content\": \"3\"\n" +
                "                  }\n" +
                "                }\n" +
                "              }\n" +
                "            ]\n" +
                "          }\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"thenAction\": {\n" +
                "      \"actionType\": \"VARIABLE_ASSIGN\",\n" +
                "      \"variableAssign\": {\n" +
                "        \"fieldName\": \"isHit\",\n" +
                "        \"fieldLabel\": \"是否命中策略规则\",\n" +
                "        \"fieldType\": \"Boolean\",\n" +
                "        \"sourcePath\": \"isHit\",\n" +
                "        \"value\": {\n" +
                "          \"valueType\": \"CONSTANT\",\n" +
                "          \"content\": \"true\"\n" +
                "        }\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"质检抽检规则01\",\n" +
                "    \"priority\": 2,\n" +
                "    \"status\": 0,\n" +
                "    \"criterionGroup\": {\n" +
                "      \"junctionType\": \"AND\",\n" +
                "      \"criterions\": [\n" +
                "        {\n" +
                "          \"criterionType\": \"RELATIONAL\",\n" +
                "          \"relational\": {\n" +
                "            \"op\": \"GREATER_THEN\",\n" +
                "            \"leftValue\": {\n" +
                "              \"valueType\": \"VARIABLE\",\n" +
                "              \"variable\": {\n" +
                "                \"fieldName\": \"recordingTime\",\n" +
                "                \"fieldLabel\": \"录音时长\",\n" +
                "                \"fieldType\": \"Long\",\n" +
                "                \"sourcePath\": \"recordingTime\"\n" +
                "              }\n" +
                "            },\n" +
                "            \"rightValue\": {\n" +
                "              \"valueType\": \"CONSTANT\",\n" +
                "              \"content\": \"3\"\n" +
                "            }\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"criterionType\": \"RELATIONAL\",\n" +
                "          \"relational\": {\n" +
                "            \"op\": \"IN\",\n" +
                "            \"rightValue\": {\n" +
                "              \"valueType\": \"CONSTANT\",\n" +
                "              \"content\": \"[1,2,3]\"\n" +
                "            },\n" +
                "            \"leftValue\": {\n" +
                "              \"valueType\": \"VARIABLE\",\n" +
                "              \"variable\": {\n" +
                "                \"fieldName\": \"areaId\",\n" +
                "                \"fieldLabel\": \"所在区域\",\n" +
                "                \"fieldType\": \"Long\",\n" +
                "                \"sourcePath\": \"areaId\"\n" +
                "              }\n" +
                "            }\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"criterionType\": \"RELATIONAL\",\n" +
                "          \"relational\": {\n" +
                "            \"op\": \"EQUALS\",\n" +
                "            \"leftValue\": {\n" +
                "              \"valueType\": \"VARIABLE\",\n" +
                "              \"variable\": {\n" +
                "                \"fieldName\": \"adviserName\",\n" +
                "                \"fieldLabel\": \"顾问姓名\",\n" +
                "                \"fieldType\": \"String\",\n" +
                "                \"sourcePath\": \"adviserName\"\n" +
                "              }\n" +
                "            },\n" +
                "            \"rightValue\": {\n" +
                "              \"valueType\": \"CONSTANT\",\n" +
                "              \"content\": \"小米辣\"\n" +
                "            }\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"criterionType\": \"CRITERION_GROUP\",\n" +
                "          \"criterionGroup\": {\n" +
                "            \"junctionType\": \"OR\",\n" +
                "            \"criterions\": [\n" +
                "              {\n" +
                "                \"criterionType\": \"RELATIONAL\",\n" +
                "                \"relational\": {\n" +
                "                  \"op\": \"LESS_THEN\",\n" +
                "                  \"leftValue\": {\n" +
                "                    \"valueType\": \"VARIABLE\",\n" +
                "                    \"variable\": {\n" +
                "                      \"fieldName\": \"validQualitySpotCount\",\n" +
                "                      \"fieldLabel\": \"顾问当月有效质检数\",\n" +
                "                      \"fieldType\": \"Long\",\n" +
                "                      \"sourcePath\": \"validQualitySpotCount\"\n" +
                "                    }\n" +
                "                  },\n" +
                "                  \"rightValue\": {\n" +
                "                    \"valueType\": \"CONSTANT\",\n" +
                "                    \"content\": \"2\"\n" +
                "                  }\n" +
                "                }\n" +
                "              },\n" +
                "              {\n" +
                "                \"criterionType\": \"RELATIONAL\",\n" +
                "                \"relational\": {\n" +
                "                  \"op\": \"GREATER_THEN\",\n" +
                "                  \"leftValue\": {\n" +
                "                    \"valueType\": \"VARIABLE\",\n" +
                "                    \"variable\": {\n" +
                "                      \"fieldName\": \"recordingTime\",\n" +
                "                      \"fieldLabel\": \"录音时长\",\n" +
                "                      \"fieldType\": \"Long\",\n" +
                "                      \"sourcePath\": \"recordingTime\"\n" +
                "                    }\n" +
                "                  },\n" +
                "                  \"rightValue\": {\n" +
                "                    \"valueType\": \"CONSTANT\",\n" +
                "                    \"content\": \"3\"\n" +
                "                  }\n" +
                "                }\n" +
                "              }\n" +
                "            ]\n" +
                "          }\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"thenAction\": {\n" +
                "      \"actionType\": \"VARIABLE_ASSIGN\",\n" +
                "      \"variableAssign\": {\n" +
                "        \"fieldName\": \"isHit\",\n" +
                "        \"fieldLabel\": \"是否命中策略规则\",\n" +
                "        \"fieldType\": \"Boolean\",\n" +
                "        \"sourcePath\": \"isHit\",\n" +
                "        \"value\": {\n" +
                "          \"valueType\": \"CONSTANT\",\n" +
                "          \"content\": \"true\"\n" +
                "        }\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "]";

        try {
            SCHEMA.validate(new JSONArray(json));
        } catch (ValidationException validationException) {
            throw new IllegalArgumentException(Joiner.on("\n").join(validationException.getAllMessages()));
        }
    }

}
