package com.shuidihuzhu.cf.risk.admin.util;

import com.shuidihuzhu.cf.risk.admin.Application;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CosUploadUtilTest {

    @Autowired
    CosUploadUtil cosUploadUtil;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testUploadText() throws IOException {
        RiskQcMaterialsInfo riskQcMaterialsInfo = riskQcMaterialsInfoBiz.getById(6664);
        var text = cosUploadUtil.uploadText(riskQcMaterialsInfo.getMaterialsValue(), null);
        System.out.println(text);

        var response1 = OkHttpClientUtil.getInstance().doGet(text);
        System.out.println(response1.body().string());
    }
}