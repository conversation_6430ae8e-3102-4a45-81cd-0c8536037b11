package com.shuidihuzhu.cf.risk.admin.model.vo.list;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/7/28 16:20
 */
@ApiModel(description = "科室电话实体")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListDepartmentVo extends ListDepartmentBase{

    @ApiModelProperty("id")
    private Long id;

}
