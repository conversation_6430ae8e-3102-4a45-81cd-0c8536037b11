package com.shuidihuzhu.cf.risk.admin.model.qcBesides;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Auther: subing
 * @Date: 2020/9/2
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskQcBesidesTaskDetail {
    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 手机号归属地
     */
    private String homeLocation;

    /**
     * 用户姓名
     */
    private String userName;

    @ApiModelProperty(value = "接听状态")
    private Integer phoneStatus;

    /**
     * 接通状态
     */
    @ApiModelProperty(value = "接听状态描述")
    private String phoneStatusCode;

    /**
     * 用户一级标签描述
     */
    private String firstTagDesc;

    /**
     * 用户二级标签描述
     */
    private String secondTagDesc;

    /**
     * 微信号
     */
    private String wechatId;

    @ApiModelProperty(value = "是否微信同手机号")
    private Boolean isSameWechatWithPhone;

    /**
     * 疾病名称
     */
    private String diseaseName;


    /**
     * 为谁筹款
     */
    private String fundraisingObject;


    /**
     * 服务备注
     */
    private String fuwuRemark;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("体能")
    private String physical;

    @ApiModelProperty("治疗医院")
    private String hosptialName;

    @ApiModelProperty("医院城市")
    private String cityName;

    private Integer cityId;


}
