package com.shuidihuzhu.cf.risk.admin.model.po.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.enums.BlackActionLimitTimeType;
import lombok.Data;

import java.util.Date;

@Data
public class RiskBlacklistTypeActionRef {
    /**
     * 主键
     */
    private Long id;

    /**
     * 黑名单类型id
     */
    private Long typeId;

    /**
     * 限制动作id
     */
    private Long actionId;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 限制动作的时长类型
     */
    private int limitTimeType = BlackActionLimitTimeType.FOREVER.getCode();
}