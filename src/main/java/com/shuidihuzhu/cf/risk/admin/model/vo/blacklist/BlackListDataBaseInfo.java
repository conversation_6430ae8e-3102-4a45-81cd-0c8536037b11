package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/20 15:29
 */
@Data
@ApiModel(description = "黑名单数据-基础用户信息")
public class BlackListDataBaseInfo {

    @ApiModelProperty("用户id")
    private Long userIdAlias;

    @ApiModelProperty("通过用户id查询的用户绑定手机号")
    private String mobileBind;

    @ApiModelProperty("用户身份证号")
    private String idCard;

    @ApiModelProperty("用户手机号")
    private String mobile;

    @ApiModelProperty("用户手机号反查到的用户id")
    private Long userIdBind;

    @ApiModelProperty("用户出生证")
    private String bornCard;

    @ApiModelProperty("用户姓名")
    private String userName;

}
