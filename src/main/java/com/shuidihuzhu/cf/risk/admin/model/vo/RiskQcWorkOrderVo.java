package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-17 18:06
 **/
@Data
public class RiskQcWorkOrderVo {


    //------------------------
    //-------质检通用字段-------
    //------------------------
    /**
     * 工单id
     */
    private long workOrderId;
    /**
     * 质检类型
     *
     * @see QcTypeEnum
     */
    private int qcType;
    /**
     * 质检人姓名
     */
    private String qcByName;
    /**
     * 案例id
     */
    private long caseId;
    /**
     * 案例infoUuid
     */
    private String infoUuid;
    /**
     * 案例标题
     */
    private String title;
    /**
     * 工单级别
     */
    private int orderLevel;
    /**
     * 处理结果
     */
    private int handleResult;
    /**
     * 处理时间
     */
    private Date updateTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 领取时间
     */
    private Date handleTime;
    /**
     * 处理人
     */
    private String operatorName;
    /**
     * 质检结果
     */
    private String qcResult;
    /**
     * 工单类型
     */
    private int orderType;
    /**
     * 分配类型
     */
    private int assignType;
    /**
     * 命中的规则名称
     */
    private String ruleName;


    //------------------------
    //-------普通质检字段-------
    //------------------------
    /**
     * 录音总时长
     */
    private int totalDuration;


    //--------------------------
    //-------微信1v1质检字段------
    //--------------------------
    /**
     * 登记手机号
     */
    private String registerMobile;
    /**
     * 登记手机号掩码（后台用）
     */
    private NumberMaskVo registerMobileMask;
    /**
     * 服务环节
     */
    private int serviceStage;
    /**
     * 工作内容
     */
    private int jobContent;
    /**
     * 换号发起手机号
     */
    private String createMobile;
    /**
     * 换号发起手机号掩码（后台用）
     */
    private NumberMaskVo createMobileMask;
    /**
     * 任务分配时间
     */
    private String assignTime;
    /**
     * 微信1v1任务id
     */
    private long taskId;
    /**
     * 已筹金额
     */
    private long amount;
    /**
     * 转发次数
     */
    private long shareCount;
    /**
     * 是否有企业微信聊天记录
     */
    private boolean hasWxRecord;
    /**
     * 是否有复检工单
     */
    private boolean hasRepeat;


    //--------------------------
    //--------外呼质检字段-------
    //--------------------------
    /**
     * 外呼服务一级标签
     */
    private long firstLevelLabel;
    /**
     * 外呼服务二级标签
     */
    private long twoLevelLabel;
    /**
     * 外呼服务线索渠道
     */
    private String callCluesChannel;
    /**
     * 外呼服务任务状态
     */
    private int callTaskStatus;


    //--------------------------
    //--------材审质检字段-------
    //--------------------------
    /**
     * 材审工单id
     */
    private long materialWorkOrderId;
    /**
     * 材审工单处理结果
     */
    private int materialHandleResult;
    /**
     * 材审工单操作人
     */
    private long materialOperatorId;

    /**
     * 材审工单类型
     */
    private long materialWorkOrderType;

    /**
     * 该质检工单是否操作过质检结果的修改
     */
    private boolean allowAgainQc;

    /**
     * 处理完成时间
     */
    private Date finishTime;

    /**
     * 来源工单id
     */
    private long sourceWorkOrderId;

    @ApiModelProperty("疑似无效质检工单 {0: 未确定, 1: 无效, 2: 有效}")
    private int seemInvalidOrder;

    public RiskQcWorkOrderVo buildCommonField(WorkOrderVO workOrderVO, int qcType, String qcByName,
                                              String operatorName, String qcResult, int assignType,
                                              String ruleName, boolean allowAgainQc) {
        if (Objects.nonNull(workOrderVO)) {
            this.workOrderId = workOrderVO.getWorkOrderId();
            this.caseId = workOrderVO.getCaseId();
            this.infoUuid = workOrderVO.getCaseUuid();
            this.title = workOrderVO.getTitle();
            this.orderLevel = workOrderVO.getOrderLevel();
            this.handleResult = workOrderVO.getHandleResult();
            this.updateTime = workOrderVO.getUpdateTime();
            this.createTime = workOrderVO.getCreateTime();
            this.handleTime = workOrderVO.getHandleTime();
            this.finishTime = workOrderVO.getFinishTime();
            this.orderType = workOrderVO.getOrderType();
        }
        this.qcType = qcType;
        this.qcByName = qcByName;
        if (StringUtils.isNotBlank(operatorName)) {
            this.operatorName = operatorName;
        }
        if (StringUtils.isNotBlank(qcResult)) {
            this.qcResult = qcResult;
        }
        this.assignType = assignType;
        if (StringUtils.isNotBlank(ruleName)) {
            this.ruleName = ruleName;
        }
        this.allowAgainQc = allowAgainQc;
        return this;
    }

    public RiskQcWorkOrderVo buildCommonQcField(int totalDuration) {
        this.totalDuration = totalDuration;
        return this;
    }

    public RiskQcWorkOrderVo buildWx1v1Field(RiskQcSearchIndex riskQcSearchIndex, CfClueInfoModel cfClueInfoModel, long taskId, ShuidiCipher shuidiCipher, MaskUtil maskUtil) {
        if (Objects.nonNull(riskQcSearchIndex)) {
            String mobile = StringUtils.isBlank(riskQcSearchIndex.getRegisterMobileEncrypt()) ?
                    StringUtils.EMPTY : shuidiCipher.decrypt(riskQcSearchIndex.getRegisterMobileEncrypt());
            this.registerMobileMask = maskUtil.buildByDecryptPhone(mobile);
            this.registerMobile = null;
            this.serviceStage = riskQcSearchIndex.getServiceStage();
            this.jobContent = riskQcSearchIndex.getJobContent();
        }
        if (Objects.nonNull(cfClueInfoModel)) {
            this.createMobileMask = maskUtil.buildByDecryptPhone(cfClueInfoModel.getClewSecondPhone());
            this.createMobile = null;
            this.assignTime = cfClueInfoModel.getAssignTime();
            this.hasWxRecord = cfClueInfoModel.isHasQywxRecord();
        }
        this.taskId = taskId;
        return this;
    }

    public RiskQcWorkOrderVo buildQcCallField(RiskQcSearchIndex riskQcSearchIndex, CfClewTaskModel cfClewTaskModel, long taskId, ShuidiCipher shuidiCipher, MaskUtil maskUtil) {
        if (Objects.nonNull(riskQcSearchIndex)) {
            String mobile = StringUtils.isBlank(riskQcSearchIndex.getRegisterMobileEncrypt()) ?
                    StringUtils.EMPTY : shuidiCipher.decrypt(riskQcSearchIndex.getRegisterMobileEncrypt());
            this.registerMobileMask = maskUtil.buildByDecryptPhone(mobile);
            this.registerMobile = null;
            this.jobContent = riskQcSearchIndex.getJobContent();
            this.callTaskStatus = riskQcSearchIndex.getCallTaskStatus();
            this.firstLevelLabel = riskQcSearchIndex.getFirstLevelLabel();
            this.twoLevelLabel = riskQcSearchIndex.getTwoLevelLabel();
        }
        if (Objects.nonNull(cfClewTaskModel)) {
            this.createMobileMask = maskUtil.buildByDecryptPhone(cfClewTaskModel.getMobile());
            this.createMobile = null;
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            this.assignTime = format.format(cfClewTaskModel.getAssignTime());
            this.callCluesChannel = cfClewTaskModel.getPrimaryChannelDesc();
        }
        this.taskId = taskId;
        return this;
    }


    public RiskQcWorkOrderVo buildMaterialField(RiskQcSearchIndex riskQcSearchIndex, long materialWorkOrderId, long materialOperatorId) {
        if (Objects.nonNull(riskQcSearchIndex)) {
            this.materialHandleResult = riskQcSearchIndex.getHandleResult();
        }
        this.materialWorkOrderId = materialWorkOrderId;
        this.materialOperatorId = materialOperatorId;
        return this;
    }
}
