package com.shuidihuzhu.cf.risk.admin.model.enums.hit;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 风控策略枚举
 * <AUTHOR>
 * @date 2020/8/20 17:32
 */
@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_strategy_hit_record", columnName = "result"),
        @DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_strategy_hit_operate", columnName = "result"),
        @DbInfo(dbName = "shuidi_cf_aegis_data", tableName = "risk_spot_hit_initiator", columnName = "result")})
@AllArgsConstructor
@Getter
public enum RiskHandleResultEnum {
    DEFAULT(0, ""),
    RISK_CONFIRMING(1, "风险确认中"),
    NO_RISK(2, "无风险"),
    HAMMER_RISK(3, "实锤风险"),
    SUSPECTED_RISK(4, "疑似风险"),
    TEST_CASE(5, "测试案例")
    ;

    public static RiskHandleResultEnum fromCode(int code){
        return Arrays.stream(values()).filter(riskHandleResultEnum -> riskHandleResultEnum.getCode() == code).findFirst().orElse(DEFAULT);
    }

    public static Map<Integer, String> usableKeyVal(){
        return Arrays.stream(values()).filter(riskHandleResultEnum -> riskHandleResultEnum.getCode()>0)
                .collect(Collectors.toMap(RiskHandleResultEnum::getCode, RiskHandleResultEnum::getDesc));
    }

    private int code;
    private String desc;

}
