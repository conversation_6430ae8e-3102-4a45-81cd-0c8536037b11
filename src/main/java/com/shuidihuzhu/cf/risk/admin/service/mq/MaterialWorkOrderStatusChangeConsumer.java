package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-10-27 17:00
 **/
@Service
@RocketMQListener(id = "cf_risk_admin_" + CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        group = "cf_risk_admin_" + CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        tags = CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        topic = MQTopicCons.CF)
@Slf4j
public class MaterialWorkOrderStatusChangeConsumer implements MessageListener<WorkOrderResultChangeEvent> {

    @Autowired
    private Producer producer;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WorkOrderResultChangeEvent> mqMessage) {
        log.info("MaterialWorkOrderStatusChangeConsumer workOrderResultChangeEvent:{}", JSON.toJSONString(mqMessage.getPayload()));
        //判断是否是材料审核工单
        WorkOrderResultChangeEvent workOrderResultChangeEvent = mqMessage.getPayload();
        if (WorkOrderType.cailiao_4.getType() != workOrderResultChangeEvent.getOrderType()
                && WorkOrderType.cailiao_5.getType() != workOrderResultChangeEvent.getOrderType()) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if (workOrderResultChangeEvent.getHandleResult() != HandleResultEnum.audit_pass.getType()
                && workOrderResultChangeEvent.getHandleResult() != HandleResultEnum.audit_reject.getType()) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        producer.send(new Message(MQTopicCons.CF, RiskMQTagCons.QC_MATERIAL_WORK_ORDER_CREATE,
                RiskMQTagCons.QC_MATERIAL_WORK_ORDER_CREATE + workOrderResultChangeEvent.getWorkOrderId(),
                workOrderResultChangeEvent, DelayLevel.S30));

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
