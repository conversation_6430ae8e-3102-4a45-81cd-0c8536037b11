package com.shuidihuzhu.cf.risk.admin.service.qc.workorder;

import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.delegate.ClewDelegate;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcHospitalDeptService;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentEditor;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.AssignTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QcOrderHospitalDeptCreateService {
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;

    @Autowired
    private ClewDelegate clewDelegate;

    @Autowired
    private QcHospitalDeptService qcHospitalDeptService;

    @Autowired
    private SeaAccountService seaAccountService;

    public Response<Void> create(int buildingId) {
        log.info("create hospital buildingId id {}", buildingId);
        //检查案例是否是线下bd发起
        DepartmentChangeDetailModel clewInfo = clewDelegate.getHospitalDeptInfoData(buildingId);
        if (clewInfo == null) {
            return NewResponseUtil.makeFail("楼宇查询失败 buildingId " + buildingId);
        }

        //创建质检基本信息
        RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
        riskQcBaseInfo.setCaseId(0);
        riskQcBaseInfo.setQcType(QcTypeEnum.BD.getCode());
        riskQcBaseInfo.setOrderType(WorkOrderType.qc_hospital_dept.getType());
        List<DepartmentEditor> departmentEditors = clewInfo.getDepartmentEditors();
        String qcByName = "";
        if (departmentEditors != null) {
            qcByName = departmentEditors.stream()
                    .map(v -> v.getOrgInfo() + "-" + v.getName())
                    .collect(Collectors.joining(","));
        }
        riskQcBaseInfo.setQcByName(qcByName);
        riskQcBaseInfo.setQcUniqueCode("");
        riskQcBaseInfoBiz.addQc(riskQcBaseInfo);

        //执行工单创建逻辑
        QcWorkOrder qcWorkOrder = new QcWorkOrder();
        qcWorkOrder.setCaseId(0);
        qcWorkOrder.setQcId(riskQcBaseInfo.getId());
        qcWorkOrder.setOrderType(WorkOrderType.qc_hospital_dept.getType());
        qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
        qcWorkOrder.setComment("生成医院科室质检工单");
        qcWorkOrder.setAssignStatus(AssignTypeEnum.MUST_ASSIGN.getCode());
        qcWorkOrder.setLoginUserId(0);
        Response<Long> clientQcWorkOrder = cfQcWorkOrderClient.createQcWorkOrder(qcWorkOrder);
        if (NewResponseUtil.isNotOk(clientQcWorkOrder)) {
            log.info("生成医院科室质检工单 resp {}", clientQcWorkOrder);
        }

        Preconditions.checkNotNull(clientQcWorkOrder);
        Preconditions.checkState(clientQcWorkOrder.ok());
        Long workOrderId = clientQcWorkOrder.getData();
        Preconditions.checkNotNull(workOrderId);
        log.info("createQcWorkOrder id:{}", workOrderId);

//        qcHospitalDeptService.saveSnapshot(workOrderId, clewInfo);

        // 添加搜索索引字段聚合表
        RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
        riskQcSearchIndex.setCaseId(buildingId);
        riskQcSearchIndex.setQcId(riskQcBaseInfo.getId());
        riskQcSearchIndex.setWorkOrderId(workOrderId);
        riskQcSearchIndex.setQcType(QcTypeEnum.BD.getCode());
        riskQcSearchIndex.setOrganization(getOrg(clewInfo));
        riskQcSearchIndex.setQcUniqueCode("");
        riskQcSearchIndex.setQcByName(qcByName);
        riskQcSearchIndex.setRegisterMobileEncrypt("");
        riskQcSearchIndex.setUserId(0);
        riskQcSearchIndex.setMaterialId(0);
        riskQcSearchIndex.setHospitalDeptId(buildingId);
        riskQcSearchIndexBiz.addSearchIndex(riskQcSearchIndex, qcWorkOrder.getOrderType());

        boolean deptClassifySuccess = qcHospitalDeptService.getDeptClassifySuccess(clewInfo);
        Von.extUpdate().saveByList(workOrderId, Lists.newArrayList(
                WorkOrderExt.create(workOrderId, QcConst.OrderExt.deptHospitalName, clewInfo.getHospitalName()),
                WorkOrderExt.create(workOrderId, QcConst.OrderExt.deptClassifySuccess, deptClassifySuccess)
                )
        );

//            cfQcWorkOrderClient.updateAssignType(Lists.newArrayList(clientQcWorkOrder.getData()), AssignTypeEnum.MUST_ASSIGN.getCode());

        //调用质检操作记录接口，记录操作记录
        String content = "生成医院科室质检工单,工单ID【" + workOrderId + "】";
        riskQcLogService.addLog(RiskQcOperationTypeEnum.CREATE_HOSPITAL_DEPT_WORK_ORDER, workOrderId, content);
        return result(true);
    }

    private String getOrg(DepartmentChangeDetailModel clewInfo) {
        final List<DepartmentEditor> departmentEditors = clewInfo.getDepartmentEditors();
        if (CollectionUtils.isEmpty(departmentEditors)) {
            return "";
        }
        final String res = departmentEditors.stream()
                .map(DepartmentEditor::getOrgInfo)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("#"));

        return StringUtils.trimToEmpty(res);
    }


    private Response<Void> result(boolean success) {
        return success ? NewResponseUtil.makeSuccess(null) : NewResponseUtil.makeFail("");
    }

}
