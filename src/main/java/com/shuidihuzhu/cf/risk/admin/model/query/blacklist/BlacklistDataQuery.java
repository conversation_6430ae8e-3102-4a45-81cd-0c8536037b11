package com.shuidihuzhu.cf.risk.admin.model.query.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.query.PageQuery;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/16 11:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(description = "黑名单数据查询")
public class BlacklistDataQuery extends PageQuery {

    private Long userIdAlias;
    private String mobile;
    private String idCard;
    private String userName;
    private String bornCard;
    private Long typeId;
    private Boolean isDelete;
    private String operateName;
    private String beginTime;
    private String endTime;

}
