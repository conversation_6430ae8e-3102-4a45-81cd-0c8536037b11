package com.shuidihuzhu.cf.risk.admin.dao.blacklist;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataActionRef;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskBlacklistDataActionRefDao {
    int insertSelective(RiskBlacklistDataActionRef record);

    RiskBlacklistDataActionRef selectByPrimaryKey(Long id);

    List<RiskBlacklistDataActionRef> listByDataId(Long dataId);

    List<RiskBlacklistDataActionRef> listByDataIdAndActionIds(@Param("dataIds") Collection<Long> dataIds, @Param("actionIds") Collection<Long> actionIds);

    int saveBatch(List<RiskBlacklistDataActionRef> refs);

    int deleteByIds(Collection<Long> ids);

    int updateActionRefById(RiskBlacklistDataActionRef riskBlacklistTypeActionRef);

    List<RiskBlacklistDataActionRef> getByLimitTime(@Param("limitTime") Long limitTime);


}