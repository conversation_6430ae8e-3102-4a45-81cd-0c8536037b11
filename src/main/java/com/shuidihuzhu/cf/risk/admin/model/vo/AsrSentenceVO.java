package com.shuidihuzhu.cf.risk.admin.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AsrSentenceVO {

    @ApiModelProperty("句子内容")
    private String text;
    @ApiModelProperty("句子开始时间")
    private int beginTime;
    @ApiModelProperty("句子结束时间")
    private int endTime;
    private String speakerType;

    /**
     * 老数据只有text
     */
    public AsrSentenceVO(String text) {
        this.text = text;
    }

    public static AsrSentenceVO createByAsrResult(QcAsrResultVO.Sentence sentence){
        AsrSentenceVO v = new AsrSentenceVO();
        v.setText(sentence.getText());
        v.setBeginTime(sentence.getBegin_time());
        v.setEndTime(sentence.getEnd_time());
        v.setSpeakerType(sentence.getSpeaker_type());
        return v;
    }
}
