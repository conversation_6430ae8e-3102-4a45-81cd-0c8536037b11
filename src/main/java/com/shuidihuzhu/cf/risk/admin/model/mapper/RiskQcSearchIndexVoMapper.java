package com.shuidihuzhu.cf.risk.admin.model.mapper;

import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.model.risk.RiskQcSearchIndexVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * copy
 *
 * <AUTHOR>
 * @since 2023-11-09 3:59 PM
 **/
@Mapper(componentModel = "spring", uses = {BeanMapperConvertHandler.class})
public interface RiskQcSearchIndexVoMapper {

    @Mappings(value = {
            @Mapping(source = "registerMobileEncrypt", target = "registerMobile", qualifiedByName = "decrypt")
    })
    RiskQcSearchIndexVO toVo(RiskQcSearchIndex riskQcSearchIndex);
}
