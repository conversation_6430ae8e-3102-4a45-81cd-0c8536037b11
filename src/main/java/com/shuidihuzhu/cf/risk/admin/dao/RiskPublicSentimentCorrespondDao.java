package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskSourceCorrespond;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/2/20
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskPublicSentimentCorrespondDao {

    List<RiskSourceCorrespond> getByInfoSource(@Param("infoSource")int infoSource);


    List<RiskSourceCorrespond> getByInfoSources(@Param("infoSources")List<Integer> infoSources);



    RiskSourceCorrespond getByInfoFeedBack(@Param("infoFeedback")int infoFeedback);

}
