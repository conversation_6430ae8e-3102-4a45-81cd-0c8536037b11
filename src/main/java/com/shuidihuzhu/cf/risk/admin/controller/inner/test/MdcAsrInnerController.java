package com.shuidihuzhu.cf.risk.admin.controller.inner.test;

import com.shuidihuzhu.cf.risk.admin.service.mdc.MdcAudioAsrService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/10/8 5:55 PM
 */

@RestController
@RequestMapping("/innerapi/cf-risk-admin/test/mdc")
@Slf4j
public class MdcAsrInnerController {

    @Autowired
    private MdcAudioAsrService audioAsrService;

    @PostMapping("asr-record")
    public Response<Void> asrRecord(@ApiParam(value = "开始时间") @RequestParam(value = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                    @ApiParam(value = "结束时间") @RequestParam(value = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate){
        audioAsrService.doHandleAsrRecord(startDate,endDate);
        return NewResponseUtil.makeSuccess();
    }
}
