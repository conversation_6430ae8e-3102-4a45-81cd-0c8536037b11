package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty;

import java.util.List;

public interface RiskQcStandardPropertyBiz {

    List<RiskQcStandardProperty> getAll();

    RiskQcStandardProperty getById(long id);

    List<RiskQcStandardProperty> getByIds(List<Long> ids);

    List<RiskQcStandardProperty> findByNameAndLevel(List<String> firstPerportyList, int level);
}
