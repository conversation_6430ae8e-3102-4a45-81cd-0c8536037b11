package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;

import java.util.List;

public interface RiskPsHandleRecordBiz {

    Response add(RiskPsHandleRecord handleRecord, long adminUserId);

    RiskPsHandleRecord getLastByPsId(long psId);


    List<RiskPsHandleRecord> listByPsId(long psId);

    PageResponse<RiskPsHandleRecord> listByPsIdOfPage(long psId, String pageJson);

    RiskPsHandleRecord getNoPushRecord(long psId);

    int getRecordCountByPsId(long psId);


}
