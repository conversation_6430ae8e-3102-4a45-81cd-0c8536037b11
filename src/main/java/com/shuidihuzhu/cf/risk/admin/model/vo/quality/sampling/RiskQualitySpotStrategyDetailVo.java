package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@ApiModel(description = "抽检策略配置详情")
public class RiskQualitySpotStrategyDetailVo {

    public RiskQualitySpotStrategyDetailVo(RiskQualitySpotStrategy riskQualitySpotStrategy){
        this.setStrategyName(riskQualitySpotStrategy.getStrategyName());
        this.setStrategyScope(Lists.newArrayList(Splitter.on(",").split(riskQualitySpotStrategy.getStrategyScope()))
                .stream().map(Integer::valueOf).collect(Collectors.toList()));
        this.setStrategyParseTime(DateUtil.getDate2LStr(riskQualitySpotStrategy.getStrategyParseTime()));
        String expireTimeStr = DateUtil.getDate2LStr(riskQualitySpotStrategy.getStrategyExpireTime());
        this.setStrategyExpireTime(Objects.equals(expireTimeStr, "3000-01-01 00:00:00") ? "" : expireTimeStr);
        this.setStatus(Objects.requireNonNull(QualitySpotStrategyStatusEnum.fromCode(riskQualitySpotStrategy.getStatus())).getDesc());
        this.setRuleDef(riskQualitySpotStrategy.getRuleDef());
        this.setId(riskQualitySpotStrategy.getId());
        this.setExecuteMode(riskQualitySpotStrategy.getExecuteMode());
    }

    @ApiModelProperty(value = "主键id", required = true)
    private long id;

    @NotBlank(message = "策略名称不能为空")
    @Length(max = 50, message = "策略名称长度不能超过50")
    @ApiModelProperty(value = "策略名称", required = true)
    private String strategyName;

    @NotNull(message = "适用场景不能为空")
    @Min(value = 1, message = "适用场景不能小于1")
    @ApiModelProperty(value = "1v1新增  一级适用场景", required = true)
    private Long firstScene;

    @NotNull(message = "适用场景不能为空")
    @Min(value = 1, message = "适用场景不能小于1")
    @ApiModelProperty(value = " 1v1新增 二级适用场景", required = true)
    private Long secondScene;

    @ApiModelProperty("每天抽取量级")
    private Integer samplingLevel;


    @NotEmpty(message = "策略适用范围不能为空")
    @ApiModelProperty(value = "策略适用范围，周日~周六：1-7，每天：0", required = true)
    private List<Integer> strategyScope;

    @NotBlank(message = "策略生效时间不能为空")
    @ApiModelProperty(value = "策略生效时间", required = true)
    private String strategyParseTime;

    @ApiModelProperty("策略失效时间：yyyy-MM-dd HH:mm:ss")
    private String strategyExpireTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("规则定义JSON：具体JSON定义找研发要")
    private String ruleDef;

    @NotNull(message = "策略执行方式不能为空")
    @Min(value = 0, message = "1v1新增  策略执行方式范围不能小于0")
    @ApiModelProperty("策略需要处理的有效最大数据日期范围(根据具体规则中的T或T-1计算得来，如果是T-1传1就行)，0 表示当天，1 表示前一天")
    private Integer executeMode;

}