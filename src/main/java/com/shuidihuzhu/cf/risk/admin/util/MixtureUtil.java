package com.shuidihuzhu.cf.risk.admin.util;

import org.apache.commons.lang3.StringUtils;

public class MixtureUtil {

    private static final int ID_CARD_MAX_LEN = 18;
    private static final int ID_CARD_MIN_LEN = 15;

    private static final int MOBILE_LEN = 11;

    private static final String placeholder = "******";

    /**
     * 加密手机号
     * @return 138****1234
     */
    public static String mixtureMobile(String mobile){
        if (StringUtils.isBlank(mobile) || mobile.length() != MOBILE_LEN){
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }


    /**
     * 加密身份证
     * @return 231121************051X
     */
    public static String mixtureIdCard(String idCard){
        if (StringUtils.isBlank(idCard) || idCard.length() != ID_CARD_MIN_LEN && idCard.length() != ID_CARD_MAX_LEN) {
            return idCard;
        }
        if (idCard.length() == ID_CARD_MIN_LEN) {
            return idCard.substring(0, 8)+placeholder+idCard.substring(14);
        }
        return idCard.substring(0, 10)+placeholder+idCard.substring(16);

    }

    public static void main(String[] args) {
        System.out.println(mixtureIdCard("632123198209270517"));
        System.out.println(mixtureIdCard("632123820927051"));
    }

}
