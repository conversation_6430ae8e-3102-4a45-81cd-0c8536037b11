package com.shuidihuzhu.cf.risk.admin.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.shuidihuzhu.cf.activity.enums.CaseSubsidyStatusEnum;
import com.shuidihuzhu.cf.activity.feign.CaseCountFeignClient;
import com.shuidihuzhu.cf.activity.feign.CfActivityFeignClient;
import com.shuidihuzhu.cf.activity.model.ActivityDetail;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-02-24
 **/
@Slf4j
@RestController
@RequestMapping("/api/cf-risk-admin/risk/cooperate")
public class RiskCooperateController {

    @Autowired
    private CfActivityFeignClient cfActivityFeignClient;
    @Autowired
    private CaseCountFeignClient caseCountFeignClient;
    @Autowired
    private SeaAccountService seaAccountService;

    @ApiOperation("获取案例配捐活动信息")
    @RequestMapping(path = "/get-activity-info", method = RequestMethod.POST)
    public Response getActivityInfo(@ApiParam("案例ID") @RequestParam int caseId) {
        log.info("caseId:{}", caseId);
        Response<ActivityDetail> response = getActivityDetail(caseId);
        if(response.getCode() != 0) {
            return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
        }
        ActivityDetail activityDetail = response.getData();
        CaseSubsidyStatusEnum caseSubsidyStatusEnum = CaseSubsidyStatusEnum.getSubsidyStatus(activityDetail);
        return NewResponseUtil.makeSuccess(ImmutableList.of(ImmutableMap.of("caseId", caseId,
                "status", caseSubsidyStatusEnum.getCode())));
    }

    @ApiOperation("更新案例配捐活动状态")
    @RequestMapping(path = "/update-status", method = RequestMethod.POST)
    @RequiresPermission("cooperate:updateStatus")
    public Response updateStatus(@ApiParam("案例ID") @RequestParam int caseId,
                                 @ApiParam("当前状态") @RequestParam int oldStatus,
                                 @ApiParam("新状态") @RequestParam int newStatus) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        String adminUserName = seaAccountService.getName(adminUserId);
        log.info("caseId:{}, oldStatus:{}, newStatus:{}, adminUserId:{}, adminUserName:{}",
                caseId, oldStatus, newStatus, adminUserId, adminUserName);
        Response<ActivityDetail> response = getActivityDetail(caseId);
        if(response.getCode() != 0) {
            return response;
        }
        ActivityDetail activityDetail = response.getData();
        int currentStatus = CaseSubsidyStatusEnum.getSubsidyStatus(activityDetail).getCode();
        if(currentStatus != oldStatus) {
            return NewResponseUtil.makeResponse(-1, "案例状态已过期，请刷新页面", null);
        }
        if(newStatus == CaseSubsidyStatusEnum.PAUSE.getCode()) {
            if(currentStatus != CaseSubsidyStatusEnum.APPROVE.getCode()) {
                return NewResponseUtil.makeResponse(-1, "非进行中的案例不能暂停", null);
            }
            log.info("rpcRequest caseId:{}, adminUserId:{}, adminUserName:{}", caseId, adminUserId, adminUserName);
            RpcResult<Void> rpcResult = this.caseCountFeignClient.pause(caseId, Math.toIntExact(adminUserId), adminUserName);
            log.info("rpcResult:{}", JSON.toJSONString(rpcResult));
        } else if (newStatus == CaseSubsidyStatusEnum.APPROVE.getCode()) {
            if(currentStatus != CaseSubsidyStatusEnum.PAUSE.getCode()
                    && currentStatus != CaseSubsidyStatusEnum.RISK_CONTROL.getCode()) {
                return NewResponseUtil.makeResponse(-1, "非暂停与结束（风险停止）的案例不能开启", null);
            }
            log.info("rpcRequest caseId:{}, adminUserId:{}, adminUserName:{}", caseId, adminUserId, adminUserName);
            RpcResult<Void> rpcResult = this.caseCountFeignClient.restart(caseId, Math.toIntExact(adminUserId), adminUserName);
            log.info("rpcResult:{}", JSON.toJSONString(rpcResult));
        } else {
            return NewResponseUtil.makeResponse(-1, "操作不正确", null);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    private Response<ActivityDetail> getActivityDetail(@RequestParam @ApiParam("案例ID") int caseId) {
        if(caseId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        RpcResult<ActivityDetail> rpcResult = this.cfActivityFeignClient.getActivityForRaiser(caseId);
        log.info("rpcResult : {}", JSON.toJSON(rpcResult));
        if(rpcResult.isFail()) {
            return NewResponseUtil.makeResponse(-1, "RPC调用失败", null);
        }
        ActivityDetail activityDetail = rpcResult.getData();
        if(activityDetail == null) {
            return NewResponseUtil.makeResponse(-1, "案例未参加活动", null);
        }
        Response<ActivityDetail> response = NewResponseUtil.makeSuccess(activityDetail);
        log.info("response:{}", JSON.toJSONString(response));
        return response;
    }
}
