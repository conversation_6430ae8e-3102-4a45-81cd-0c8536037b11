package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * 黑名单自动加入vo
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@NoArgsConstructor
@ApiModel(description = "黑名单数据")
public class BlacklistDataAutoVo {

    @ApiModelProperty("用户id")
    private Long userIdAlias;

    @ApiModelProperty("通过用户id查询的用户绑定手机号")
    private String mobileBind;

    @ApiModelProperty("用户身份证号")
    private String idCard;

    @ApiModelProperty("用户手机号")
    private String mobile;

    private NumberMaskVo mobileMask;

    @ApiModelProperty("用户手机号反查到的用户id")
    private Long userIdBind;

    @ApiModelProperty("用户出生证")
    private String bornCard;

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("黑名单类型")
    private List<Pair<Long, String>> typeIdNames;

    @ApiModelProperty("限制动作")
    private String action;

    @ApiModelProperty("限制动作详情")
    private List<BlacklistTypeActionRefDto> actionList;

    @ApiModelProperty("黑名单id")
    private long blackListId;

}
