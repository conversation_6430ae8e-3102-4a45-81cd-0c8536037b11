package com.shuidihuzhu.cf.risk.admin.biz.impl.quality.sampling;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotLevelConfBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotStrategyBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQualitySpotStrategyTypeRelBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQualitySpotTypeBiz;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotStrategyDao;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotStrategyLogDao;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategyLog;
import com.shuidihuzhu.cf.risk.admin.model.query.quality.sampling.QualitySpotStrategyQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotStrategyDetailVo;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/15 20:35
 */
@Service
@Slf4j
public class QualitySpotStrategyBizImpl implements QualitySpotStrategyBiz {

    @Resource
    private QualitySpotLevelConfBiz qualitySpotLevelConfBiz;
    @Resource
    private RiskQualitySpotStrategyDao riskQualitySpotStrategyDao;
    @Resource
    private RiskQualitySpotStrategyLogDao riskQualitySpotStrategyLogDao;
    @Resource
    private SeaAccountService seaAccountService;
    @Resource
    private RiskQualitySpotStrategyTypeRelBiz riskQualitySpotStrategyTypeRelBiz;
    @Resource
    private RiskQualitySpotTypeBiz spotTypeBiz;

    @Override
    public Page<RiskQualitySpotStrategy> listByQuery(QualitySpotStrategyQuery strategyVo) {
        dealScene(strategyVo);
        return PageHelper.startPage(strategyVo.getPageNo(), strategyVo.getPageSize())
                .doSelectPage(() -> riskQualitySpotStrategyDao.listByConditions(strategyVo));
    }

    private void dealScene(QualitySpotStrategyQuery qualitySpotStrategyQuery) {
        if (qualitySpotStrategyQuery.getScene() == null) {
            return;
        }
        List<Long> sceneList =  spotTypeBiz.getListByParentId(qualitySpotStrategyQuery.getScene())
                .stream().map(RiskQualitySpotType::getId).collect(Collectors.toList());
        qualitySpotStrategyQuery.setSceneList(CollectionUtils.isEmpty(sceneList)
                ? Lists.newArrayList(qualitySpotStrategyQuery.getScene()) : sceneList);
    }

    @Override
    public RiskQualitySpotStrategyDetailVo getById(Long id) {
        RiskQualitySpotStrategy spotStrategy = getModelById(id);
        RiskQualitySpotStrategyDetailVo spotStrategyVo = new RiskQualitySpotStrategyDetailVo(spotStrategy);
        setSceneInfo(id, spotStrategyVo);
        return spotStrategyVo;
    }

    private void setSceneInfo(Long id, RiskQualitySpotStrategyDetailVo spotStrategyVo) {
        List<RiskQualitySpotTypeRel> typeRelList = riskQualitySpotStrategyTypeRelBiz.findByStrategyId(id);
        if (CollectionUtils.isEmpty(typeRelList)){
            return;
        }
        List<RiskQualitySpotType> riskQualitySpotTypes = spotTypeBiz.findById(typeRelList.stream()
                .map(RiskQualitySpotTypeRel::getTypeId).distinct().collect(Collectors.toList()));
        //目前只支持两层
        for (RiskQualitySpotType type : riskQualitySpotTypes) {
            if (type.getParentId() == 0) {
                spotStrategyVo.setFirstScene(type.getId());
                continue;
            }
            spotStrategyVo.setSecondScene(type.getId());
            spotStrategyVo.setSamplingLevel(getCurrentLevelConf(type.getId()).getSamplingLevel());
        }
    }

    @Override
    public RiskQualitySpotStrategy getModelById(Long id) {
        return riskQualitySpotStrategyDao.selectByPrimaryKey(id);
    }

    @Override
    public RiskQualitySpotLevelConfVo getLatestLevelConf(Long scene){
        List<RiskQualitySpotLevelConfVo> spotLevelConfVos = qualitySpotLevelConfBiz.listWithAllLatestValidScene(0, scene);
        if (CollectionUtils.isEmpty(spotLevelConfVos)){
            return null;
        }
        return spotLevelConfVos.stream().filter(spotLevelConfVo ->
                Objects.equals(spotLevelConfVo.getScene(), scene)).findFirst().get();
    }

    @Override
    public RiskQualitySpotLevelConfVo getCurrentLevelConf(Long scene) {
        List<RiskQualitySpotLevelConfVo> spotLevelConfVos = qualitySpotLevelConfBiz.listWithAllCurrentValidScene(scene);
        Optional<RiskQualitySpotLevelConfVo> optional = spotLevelConfVos.stream().filter(spotLevelConfVo ->
                Objects.equals(spotLevelConfVo.getScene(), scene)).findFirst();
        if (optional.isEmpty()) {
            RuntimeException error = new RuntimeException("对应场景的抽检量级未配置");
            log.error("对应场景的抽检量级未配置 scene {}", scene, error);
            throw error;
        }
        return optional.get();
    }

    @Override
    public List<RiskQualitySpotStrategy> listByValidScene(Long scene) {
        return riskQualitySpotStrategyDao.listByScene(scene, QualitySpotStrategyStatusEnum.ENABLE.getCode());
    }

    @Override
    public int saveStrategy(RiskQualitySpotStrategy qualitySpotStrategyVo) {
        return riskQualitySpotStrategyDao.insertSelective(qualitySpotStrategyVo);
    }

    @Override
    public int updateStrategyStatus(Long id, QualitySpotStrategyStatusEnum statusEnum, Date expireTime, long adminUserId) {
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        return riskQualitySpotStrategyDao.updateStatusById(id, statusEnum.getCode(), expireTime,
                userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId(),
                userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg());
    }

    @Override
    public List<RiskQualitySpotStrategyLog> listLogByStrategyId(Long strategyId) {
        return riskQualitySpotStrategyLogDao.listByStrategyId(strategyId);
    }

    @Override
    public int saveStrategyLog(RiskQualitySpotStrategyLog riskQualitySpotStrategyLog) {
        return riskQualitySpotStrategyLogDao.insertSelective(riskQualitySpotStrategyLog);
    }

    @Override
    public List<RiskQualitySpotStrategy> listValidStrategy(Date currTime, Long startId, Integer limit) {
        return riskQualitySpotStrategyDao.listValidStrategy(startId, limit, currTime);
    }

    @Override
    public List<RiskQualitySpotStrategy> listById(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return riskQualitySpotStrategyDao.listById(ids);
    }

    @Override
    public int updateStrategy(RiskQualitySpotStrategy riskQualitySpotStrategy) {
        return riskQualitySpotStrategyDao.updateStrategy(riskQualitySpotStrategy);
    }
}
