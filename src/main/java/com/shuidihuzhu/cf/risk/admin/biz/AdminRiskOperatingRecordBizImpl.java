package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.dao.AdminRiskOperatingRecordDao;
import com.shuidihuzhu.cf.risk.admin.model.AdminRiskOperatingRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-05-15
 **/
@Slf4j
@Service
public class AdminRiskOperatingRecordBizImpl implements AdminRiskOperatingRecordBiz {

    @Autowired
    private AdminRiskOperatingRecordDao adminRiskOperatingRecordDao;

    @Override
    public int insert(AdminRiskOperatingRecord adminRiskOperatingRecord) {
        return this.adminRiskOperatingRecordDao.insert(adminRiskOperatingRecord);
    }
}
