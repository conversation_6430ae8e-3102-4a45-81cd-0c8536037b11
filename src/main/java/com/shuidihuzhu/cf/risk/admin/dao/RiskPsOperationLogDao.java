package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsOperationLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskPsOperationLogDao {

    int save(RiskPsOperationLog operationLog);

    List<RiskPsOperationLog> listByPsIdOfPage(@Param("psId") long psId, PageRequest pageRequest);
}
