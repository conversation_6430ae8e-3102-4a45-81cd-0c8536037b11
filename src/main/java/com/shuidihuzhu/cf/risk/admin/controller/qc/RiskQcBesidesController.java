package com.shuidihuzhu.cf.risk.admin.controller.qc;

import com.shuidihuzhu.cf.risk.admin.model.qcBesides.RiskQcBesidesTaskDetail;
import com.shuidihuzhu.cf.risk.admin.model.qcBesides.RiskQcBesidesTaskInfo;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcBesidesService;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewFollowUpTaskVO;
import com.shuidihuzhu.client.cf.clewtrack.param.ClewFollowUpTaskParam;
import com.shuidihuzhu.client.model.CommonPageModel;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Auther: subing
 * @Date: 2020/9/2
 */
@RestController
@RequestMapping(value = "/api/cf-risk-admin/qc-besides/detail", method = RequestMethod.POST)
public class RiskQcBesidesController {
    @Autowired
    private RiskQcBesidesService riskQcBesidesService;

    @Resource
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;

    @RequestMapping(path = "/get-task-info")
    public Response<RiskQcBesidesTaskInfo> getTaskInfo(@RequestParam long clewTaskId) {
        if (clewTaskId < 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        RiskQcBesidesTaskInfo riskQcBesidesTaskInfo = riskQcBesidesService.getTaskInfo(clewTaskId);
        return NewResponseUtil.makeSuccess(riskQcBesidesTaskInfo);
    }

    @RequestMapping(path = "/get-task-detail")
    public Response<RiskQcBesidesTaskDetail> getTaskDetail(@RequestParam long clewTaskId) {
        if (clewTaskId < 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        RiskQcBesidesTaskDetail riskQcBesidesTaskDetail = riskQcBesidesService.getTaskDetail(clewTaskId);
        return NewResponseUtil.makeSuccess(riskQcBesidesTaskDetail);
    }

    @RequestMapping(path = "/get-follow-up")
    public Response<CommonPageModel<ClewFollowUpTaskVO>> getFollowUpTask(@RequestBody ClewFollowUpTaskParam followUpTaskParam) {
        Response<CommonPageModel<ClewFollowUpTaskVO>> response = cfClewtrackTaskFeignClient.getFollowUpTask(followUpTaskParam);
        if (response.notOk()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(response.getData());
    }


}
