package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/15 20:56
 */
@Data
@ApiModel(description = "黑名单类型-详情")
public class BlacklistTypeDetailVo {

    @ApiModelProperty("层级类型，按照顺序排序")
    private List<String> levelName;

    @ApiModelProperty("限制动作")
    private List<Long> typeActions;

    @ApiModelProperty("限制动作详情")
    private List<BlacklistTypeActionRefDto> actionList;


}
