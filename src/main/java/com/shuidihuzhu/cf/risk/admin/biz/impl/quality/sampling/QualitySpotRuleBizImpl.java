package com.shuidihuzhu.cf.risk.admin.biz.impl.quality.sampling;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotRuleBiz;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotRuleDao;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/14 16:49
 */
@Slf4j
@Service
public class QualitySpotRuleBizImpl implements QualitySpotRuleBiz {

    @Resource
    private RiskQualitySpotRuleDao riskQualitySpotRuleDao;

    @Override
    public List<RiskQualitySpotRule> queryEnableRulesByStrategyIds(List<Long> strategyIds) {
        return riskQualitySpotRuleDao.listByStrategyIdsOptionsStatus(strategyIds, QualitySpotStrategyStatusEnum.ENABLE.getCode());
    }

    @Override
    public RiskQualitySpotRule getById(Long id) {
        return riskQualitySpotRuleDao.selectByPrimaryKey(id);
    }

    @Override
    public int saveBatch(List<RiskQualitySpotRule> riskQualitySpotRules) {
        return riskQualitySpotRuleDao.saveBatch(riskQualitySpotRules);
    }

    @Override
    public int deleteOldRuleByStrategyId(long strategyId) {
        if (strategyId <= 0 ){
            return 0;
        }
        return  riskQualitySpotRuleDao.deleteOldRuleByStrategyId(strategyId);
    }

    @Override
    public int closeOldRuleByStrategyId(long strategyId) {
        return riskQualitySpotRuleDao.closeOldRuleByStrategyId(strategyId);
    }

    @Override
    public int openOldRuleByStrategyId(long strategyId) {
        if (strategyId <= 0 ){
            return 0;
        }
        return  riskQualitySpotRuleDao.openOldRuleByStrategyId(strategyId);
    }

    @Override
    public List<RiskQualitySpotRule> findByStrategyId(long strategyId) {
        if (strategyId <= 0) {
            return Lists.newArrayList();
        }
        return riskQualitySpotRuleDao.findByStrategyId(strategyId);
    }

    @Override
    public List<RiskQualitySpotRule> findById( List<Long> ids) {
        return riskQualitySpotRuleDao.findById(ids);
    }
}
