package com.shuidihuzhu.cf.risk.admin.dao.hit;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskStrategyHitRecordDao {
    int insertSelective(RiskStrategyHitRecord record);

    RiskStrategyHitRecord selectByPrimaryKey(Long id);

    List<RiskStrategyHitRecord> listByIds(List<Long> ids);

    List<RiskStrategyHitRecord> listByCaseIdPhase(@Param("caseId") Integer caseId, @Param("hitPhase") Integer hitPhase);

    /**
     * 根据案例对应策略查询
     */
    RiskStrategyHitRecord oneByCaseStrategy(@Param("caseId") Integer caseId, @Param("hitPhases") List<Integer> hitPhases);

    int updateHandleInfo(@Param("id") Long id, @Param("status") Integer status, @Param("result") Byte result,
                         @Param("action") String action, @Param("lifting") Byte lifting,
                         @Param("operateId") Long operateId, @Param("operateName") String operateName,
                         @Param("incrementUpdateCount") boolean incrementUpdateCount);

    List<RiskStrategyHitRecord> tempWash();
}