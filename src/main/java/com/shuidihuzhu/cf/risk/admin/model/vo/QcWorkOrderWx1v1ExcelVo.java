package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.util.Date;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-07-10 13:35
 **/
@Data
public class QcWorkOrderWx1v1ExcelVo extends BaseRowModel {

    @ExcelProperty(value = "质检工单处理时间", index = 0)
    private Date updateTime;
    @ExcelProperty(value = "工单id", index = 1)
    private long workOrderId;
    @ExcelProperty(value = "质检同学姓名", index = 2)
    private String name;
    @ExcelProperty(value = "登记手机号", index = 3)
    private String registerMobile;
    @ExcelProperty(value = "新手机号", index = 4)
    private String newMobile;
    @ExcelProperty(value = "被质检人组织架构", index = 5)
    private String organization;
    @ExcelProperty(value = "被质检人姓名", index = 6)
    private String byName;
    @ExcelProperty(value = "被命中的抽检规则名称", index = 7)
    private String ruleName;
    @ExcelProperty(value = "工单结果", index = 8)
    private String workOrderResult;
    @ExcelProperty(value = "一级问题描述", index = 9)
    private String firstDesc;
    @ExcelProperty(value = "二级问题描述", index = 10)
    private String secondDesc;
    @ExcelProperty(value = "一级属性", index = 11)
    private String firstAttribute;
    @ExcelProperty(value = "二级属性", index = 12)
    private String secondAttribute;
    @ExcelProperty(value = "录音备注", index = 13)
    private String recordingNotes;
    @ExcelProperty(value = "其他备注", index = 14)
    private String otherNotes;
    @ExcelProperty(value = "录音时长", index = 15)
    private int totalDuration;
}
