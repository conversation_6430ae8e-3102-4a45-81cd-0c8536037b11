package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
public interface RiskQcStandBiz {
    List<RiskQcStandard> getAllByType(int isUse, int type, List<Integer> secondStandardTypes);

    int addStandard(RiskQcStandard riskQcStandard);


    int updateSort(int sort, long id);

    List<RiskQcStandard> getByIds(List<Long> ids);

    int updateUseById(int isUse, long id);

    List<RiskQcStandard> getByParentId(long patientId, int isUse);

    RiskQcStandard getLastByLevel(int level, Long parentId);

    int deleteInfo(long id);

    RiskQcStandard getById(long id);

    int updateSecondaryUseStatus(int useStatus, long id);


    RiskQcStandard getByName(String standardName, int standardType, int secondStandardType, int level);

    List<RiskQcStandard> fuzzyQuery(String standardName, int level);

}
