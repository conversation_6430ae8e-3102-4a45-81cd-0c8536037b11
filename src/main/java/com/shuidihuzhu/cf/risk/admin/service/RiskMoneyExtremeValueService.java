package com.shuidihuzhu.cf.risk.admin.service;

import com.shuidihuzhu.cf.risk.admin.model.RiskRuleModel;
import com.shuidihuzhu.cf.risk.admin.model.enums.OperatingRecordBizTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskRuleCondition;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskRuleResult;
import com.shuidihuzhu.cf.risk.admin.biz.AdminRiskOperatingRecordBiz;
import com.shuidihuzhu.cf.risk.admin.model.AdminRiskOperatingRecord;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.util.ContextUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-29 11:18
 **/
@Service
public class RiskMoneyExtremeValueService {

    private static final int THRESHOLD = 500000;

    @Autowired
    private AdminRiskOperatingRecordBiz adminRiskOperatingRecordBiz;


    public List<RiskRuleResult> ruleJudge(int hasCostAmount, int medicalDrugAmount,
                                          int nursingAmount, int totalGovReliefAmount,
                                          int caseId, long workOrderId) {

        List<RiskRuleModel> riskRuleModels = List.of(
                new RiskRuleModel(hasCostAmount, List.of(THRESHOLD), RiskRuleCondition.EQUAL_GREATER, RiskRuleResult.A_1),
                new RiskRuleModel(medicalDrugAmount, List.of(THRESHOLD), RiskRuleCondition.EQUAL_GREATER, RiskRuleResult.A_2),
                new RiskRuleModel(nursingAmount, List.of(THRESHOLD), RiskRuleCondition.EQUAL_GREATER, RiskRuleResult.A_3),
                new RiskRuleModel(totalGovReliefAmount, List.of(THRESHOLD), RiskRuleCondition.EQUAL_GREATER, RiskRuleResult.A_4)
        );

        List<RiskRuleResult> riskRuleResults = riskRuleModels.stream().map(riskRuleModel -> {
            RiskRuleResult riskRuleResult = null;
            boolean condition = RiskRuleCondition.condition(riskRuleModel.getCurrentVaule(),
                    riskRuleModel.getThreshold(), riskRuleModel.getRiskRuleCondition());
            if (condition) {
                riskRuleResult = riskRuleModel.getRiskRuleResult();
            }
            return riskRuleResult;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        AdminRiskOperatingRecord adminRiskOperatingRecord = new AdminRiskOperatingRecord();
        adminRiskOperatingRecord.setAdminUserId(Math.toIntExact(ContextUtil.getAdminLongUserId()));
        adminRiskOperatingRecord.setInfoId(caseId);
        adminRiskOperatingRecord.setWorkOrderId(workOrderId);
        adminRiskOperatingRecord.setBizType(OperatingRecordBizTypeEnum.TYPE_2.getCode());
        adminRiskOperatingRecord.setRiskLevel("");
        if (CollectionUtils.isNotEmpty(riskRuleResults)) {
            adminRiskOperatingRecord.setRisk(1);
            List<String> result = riskRuleResults.stream().map(riskRuleResult -> String.valueOf(riskRuleResult.getCode()))
                    .collect(Collectors.toList());
            String joinResult = String.join(",", result);
            adminRiskOperatingRecord.setRiskLevel(joinResult);
        }
        adminRiskOperatingRecordBiz.insert(adminRiskOperatingRecord);
        return riskRuleResults;
    }
}
