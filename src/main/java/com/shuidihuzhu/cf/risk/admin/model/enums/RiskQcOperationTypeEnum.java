package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_log", columnName = "operation_type")},
        valueName = "type", descName = "description")
@Getter
@AllArgsConstructor
public enum RiskQcOperationTypeEnum {
    //
    CREATE_COMMON_WORK_ORDER(1, "创建普通质检工单", true),
    AUTO_ALLOCATION_COMMON_ORDER(2, "自动分配普通质检工单", false),
    MANUAL_ALLOCATION_COMMON_ORDER(3, "手动分配普通质检工单", false),
    MANUAL_RECYCLE_COMMON_ORDER(4, "手动回收普通质检工单", false),
    DISPOSE_FINISH_COMMON(5, "处理完成普通质检工单", true),
    LATER_ON_DISPOSE_COMMON_ORDER(6, "稍后处理普通质检工单", false),
    CHANGE_QC_RESULT(7, "修改质检结果", true),
    CREATE_APPEAL_WORK_ORDER(8, "申诉工单的生成", true),
    APPEAL_WORK_ORDER_RESULT(9, "申诉工单判定结果", true),
    APPEAL_WORK_ORDER_EXPIRE(10, "申诉过期", true),
    ADVISER_AGREE_QC_RESULT(11, "顾问认可质检结果", true),
    ADVISER_AGREE_APPEAL_RESULT(12, "顾问认可申诉判定结果", true),
    MANAGER_NO_SUPPORT_AGAIN_APPEAL(13, "业务经理不支持二次申诉", true),
    MANAGER_AGREE_QC_RESULT(14, "业务经理认可质检结果", true),
    MANUAL_CLOSE(15, "手动关闭工单",true),
    ISSUE_QC_RESULT(16,"下发质检结果",true),
    ISSUE_APPEAL_RESULT(17,"下发申诉结果",true),
    CREATE_HOSPITAL_DEPT_WORK_ORDER(18, "创建医院科室质检工单", true),
    CREATE_ZHU_DONG_ORDER(19, "创建主动服务质检工单", true),
    CREATE_HIGH_RISK_ORDER(20, "创建高风险质检工单", true),
    HOSPITAL_DEPT_MODIFY(21, "科室质检-修改", true),
    UPDATE_BUILDING_INFO(22, "修改楼宇信息", true),
    ;


    int type;
    String description;
    /**
     * 是否在详情页展示
     */
    boolean detailShow;


    public static List<Integer> getTypeByEnum(List<RiskQcOperationTypeEnum> riskQcOperationTypeEnums) {
        if (CollectionUtils.isEmpty(riskQcOperationTypeEnums)) {
            return Lists.newArrayList();
        }
        List<Integer> types = Lists.newArrayList();
        riskQcOperationTypeEnums.forEach(riskQcOperationTypeEnum -> types.add(riskQcOperationTypeEnum.getType()));
        return types;
    }

    public static String findOfCode(int code) {
        for (RiskQcOperationTypeEnum value : values()) {
            if (value.getType() == code) {
                return value.getDescription();
            }
        }
        return "";
    }

    public static final List<RiskQcOperationTypeEnum> DETAIL_SEARCH_INFO =
            ImmutableList.copyOf(Arrays.stream(RiskQcOperationTypeEnum.values())
                    .filter(RiskQcOperationTypeEnum::isDetailShow).collect(Collectors.toList()));

}
