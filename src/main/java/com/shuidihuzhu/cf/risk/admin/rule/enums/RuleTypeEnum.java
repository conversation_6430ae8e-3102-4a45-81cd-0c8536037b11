package com.shuidihuzhu.cf.risk.admin.rule.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 规则类型枚举
 * <AUTHOR>
 * @date 2020/2/14 14:36
 */
@Getter
@AllArgsConstructor
public enum RuleTypeEnum {

    ABSTRACTION("abstraction_","特征提取规则"),
    ANALYZE_ABSTRACTION("analyze_abstraction_","分析结果提取规则"),
    RULE("rule_","风控规则"),
    ACTION_NOTICE("action_notice_","通知规则"),
    ;

    private String ruleType;
    private String desc;

}
