package com.shuidihuzhu.cf.risk.admin.controller;

import com.shuidihuzhu.cf.risk.admin.model.enums.RiskRuleResult;
import com.shuidihuzhu.cf.risk.admin.service.RiskMoneyExtremeValueService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-29 11:00
 **/
@RestController
@RequestMapping(path = "/api/cf-risk-admin/risk/money")
@Slf4j
@Api("资金风险")
public class RiskMoneyExtremeValueController {

    @Autowired
    private RiskMoneyExtremeValueService riskMoneyExtremeValueService;

    @RequestMapping(path = "rule-judge")
    @ApiOperation("资金极值判断")
    public Response<List<RiskRuleResult>> ruleJudge(@ApiParam("已花费金额") @RequestParam("hasCostAmount") int hasCostAmount,
                                                    @ApiParam("医疗药品费用") @RequestParam("medicalDrugAmount") int medicalDrugAmount,
                                                    @ApiParam("康复护理费用") @RequestParam("nursingAmount") int nursingAmount,
                                                    @ApiParam("政府补助") @RequestParam("totalGovReliefAmount") int totalGovReliefAmount,
                                                    @ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                    @ApiParam("工单id") @RequestParam("workOrderId") long workOrderId) {
        log.info("RiskMoneyExtremeValueController.rule hasCostAmount:{},medicalDrugAmount:{},nursingAmount:{}," +
                        "totalGovReliefAmount:{},caseId:{},workOrderId:{}",
                hasCostAmount, medicalDrugAmount, nursingAmount, totalGovReliefAmount, caseId, workOrderId);
        List<RiskRuleResult> results = riskMoneyExtremeValueService.ruleJudge(hasCostAmount, medicalDrugAmount, nursingAmount,
                totalGovReliefAmount, caseId, workOrderId);
        return NewResponseUtil.makeSuccess(results);
    }
}
