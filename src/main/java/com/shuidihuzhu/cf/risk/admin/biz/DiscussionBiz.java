package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.Discussion;
import com.shuidihuzhu.cf.risk.admin.model.vo.DiscussionInfoVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;

public interface DiscussionBiz {

    Response openDiscussion(int userId, Discussion discussion);

    Response closeDiscussion(int userId, int caseId, String infoUuid);

    String checkCase(int caseId, String infoUuid);

    int getStatus(int caseId);

    Discussion findByCaseId(int caseId);

    Response listByCaseId(int caseId);

    boolean hasDiscussion(int caseId);

    DiscussionInfoVO getInfoById(long id);
}
