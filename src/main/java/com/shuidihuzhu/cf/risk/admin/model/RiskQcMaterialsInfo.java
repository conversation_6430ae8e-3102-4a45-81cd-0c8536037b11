package com.shuidihuzhu.cf.risk.admin.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-15 14:25
 **/
@NoArgsConstructor
@Data
@ApiModel
public class RiskQcMaterialsInfo {
    @ApiModelProperty("id")
    private long id;
    @ApiModelProperty("质检id")
    private long qcId;
    @ApiModelProperty("材料key")
    private String materialsKey;
    @ApiModelProperty("材料值")
    private String materialsValue;

    public RiskQcMaterialsInfo(long qcId, String materialsKey, String materialsValue) {
        this.qcId = qcId;
        this.materialsKey = materialsKey;
        this.materialsValue = materialsValue;
    }
}
