package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.AsrSentenceVO;
import com.shuidihuzhu.cf.risk.admin.util.SDEncryptUtils;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.common.web.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2022/3/25 下午4:29
 * 质检工单&举报工单中录音信息
 */
@Data
@NoArgsConstructor
public class WorkOrderRecordingModel {
    @ApiModelProperty("手机号")
    private String mobile;
    private NumberMaskVo mobileMask;
    @ApiModelProperty("录音文件")
    private String videoUrl;
    @ApiModelProperty("接通状态")
    private Integer phoneStatus;
    @ApiModelProperty("座席开始打电话时间")
    private String startTime;
    @ApiModelProperty("座席挂断时间")
    private String endTime;
    @ApiModelProperty("客户接听时间")
    private String answerTime;
    @ApiModelProperty("总通话时长")
    private long duration;
    @ApiModelProperty("实际通话时间 坐席挂断时间 - 客户接听时间")
    private long totalDuration;
    @ApiModelProperty("句子详情列表")
    private List<AsrSentenceVO> sentenceInfoList;
    @ApiModelProperty("语音转译结果 {0: 无, 1: 有效录音, 2: 疑似无效录音}")
    private int audioAsrStatus;
    @ApiModelProperty("标红词检查结果")
    private List<CfBaseInfoRiskHitVO.ColourTag> hitWordColourTags;

    private String clientName;
    private String orgName;

    @ApiModelProperty("1001:客户挂机 1000:坐席挂机")
    private String endReason;
    @ApiModelProperty("0:天润,1:中通,2:企友")
    private int callSourceType;
    public WorkOrderRecordingModel(CallRecordModel callRecordModel, AsrResultModel asrResultModel, HitWordResultModel hitWordResultModel) {
        if (callRecordModel != null) {
            this.mobile = callRecordModel.getMobile();
            this.videoUrl = callRecordModel.getVideoUrl();
            this.phoneStatus = callRecordModel.getPhoneStatus();
            this.startTime = callRecordModel.getStartTime();
            this.endTime = callRecordModel.getEndTime();
            this.answerTime = callRecordModel.getAnswerTime();
            this.duration = callRecordModel.getDuration();
            this.totalDuration = callRecordModel.getTotalDuration();
            this.clientName = callRecordModel.getClientName();
            this.orgName = callRecordModel.getOrgName();
            this.endReason = callRecordModel.getEndReason();
            this.callSourceType = callRecordModel.getCallSourceType();
        }
        if (asrResultModel != null) {
            this.sentenceInfoList = asrResultModel.getSentenceInfoList();
            this.audioAsrStatus = asrResultModel.getAudioAsrStatus();
        }
        if (hitWordResultModel != null) {
            this.hitWordColourTags = hitWordResultModel.getHitWordColourTags();
        }
    }

    public String asrText() {
        if (CollectionUtils.isEmpty(this.getSentenceInfoList())) return null;
        return this.getSentenceInfoList().stream().map(AsrSentenceVO::getText).collect(Collectors.joining(""));
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CallRecordModel {
        @ApiModelProperty("手机号")
        private String mobile;
        @ApiModelProperty("录音文件")
        private String videoUrl;
        @ApiModelProperty("接通状态")
        private Integer phoneStatus;
        @ApiModelProperty("座席开始打电话时间")
        private String startTime;
        @ApiModelProperty("座席挂断时间")
        private String endTime;
        @ApiModelProperty("客户接听时间")
        private String answerTime;
        @ApiModelProperty("总通话时长")
        private long duration;
        @ApiModelProperty("实际通话时间 坐席挂断时间 - 客户接听时间")
        private long totalDuration;
        private String clientName;
        private String orgName;
        @ApiModelProperty("1001:客户挂机 1000:坐席挂机")
        private String endReason;
        @ApiModelProperty("0:天润,1:中通,2:企友")
        private int callSourceType;

        public static CallRecordModel createByClewCallRecordModel(ClewCallRecordModel item){
            long realTotalDuration = 0;
            if (item.getSipCause() == 200) {
                realTotalDuration = (item.getCnoEndTime().getTime() - item.getAnswerTime().getTime()) / 1000;
            }

            CallRecordModel callRecordModel = new CallRecordModel();
            callRecordModel.setMobile(SDEncryptUtils.decrypt(item.getEncryptCustomerNumber()));
            callRecordModel.setVideoUrl(item.getCosFile());
            callRecordModel.setPhoneStatus(item.getSipCause());
            callRecordModel.setStartTime(DateUtil.formatDateTime(item.getCnoStartTime()));
            callRecordModel.setEndTime(DateUtil.formatDateTime(item.getCnoEndTime()));
            callRecordModel.setAnswerTime(DateUtil.formatDateTime(item.getAnswerTime()));
            callRecordModel.setDuration(item.getTotalDuration());
            callRecordModel.setTotalDuration(realTotalDuration);
            callRecordModel.setClientName(item.getClientName());
            callRecordModel.setOrgName(item.getOrgName());
            callRecordModel.setEndReason(item.getEndReason());
            return callRecordModel;
        }

        public static CallRecordModel createByClewCallRecordDO(CfClewCallRecordsDO in) {
            if (Objects.isNull(in)) {
                return null;
            }
            // 真正通话时长 = (坐席挂断时间 - 客户接听时间) // 备注 没有客户挂断时间所以只能粗算
            Integer phoneStatus = in.getPhoneStatus();
            Date answerTime = in.getAnswerTime();
            long answerTimestamp = answerTime.getTime();
            long cnoEndTimestamp = in.getCnoEndTime().getTime();
            long realTotalDuration = 0;
            boolean callSuccess = phoneStatus == 200;
            if (callSuccess) {
                realTotalDuration = (cnoEndTimestamp - answerTimestamp) / 1000;
            }
            WorkOrderRecordingModel.CallRecordModel records = new WorkOrderRecordingModel.CallRecordModel();
            records.setAnswerTime(DateUtil.formatDateTime(callSuccess ? answerTime : null));
            records.setMobile(SDEncryptUtils.decrypt(in.getEncryptCustomerNumber()));
            records.setStartTime(DateUtil.formatDateTime(in.getCnoStartTime()));
            records.setEndTime(DateUtil.formatDateTime(in.getCnoEndTime()));
            records.setDuration(in.getTotalDuration());
            records.setTotalDuration((int) realTotalDuration);
            records.setVideoUrl(in.getCosFile());
            records.setPhoneStatus(phoneStatus);
            records.setClientName(in.getClientName());
            records.setOrgName(in.getOrgName());
            records.setEndReason(in.getEndReason());
            records.setCallSourceType(in.getCallSourceType());
            return records;
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AsrResultModel {
     @ApiModelProperty("句子详情列表")
     private List<AsrSentenceVO> sentenceInfoList;
     @ApiModelProperty("语音转译结果 {0: 无, 1: 有效录音, 2: 疑似无效录音}")
     private int audioAsrStatus;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HitWordResultModel {
        @ApiModelProperty("标红词检查结果")
        private List<CfBaseInfoRiskHitVO.ColourTag> hitWordColourTags;
    }


}
