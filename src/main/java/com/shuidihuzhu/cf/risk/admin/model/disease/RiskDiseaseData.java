package com.shuidihuzhu.cf.risk.admin.model.disease;

import com.shuidihuzhu.cf.risk.admin.model.vo.RiskDiseaseInfoVO;
import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Data
public class RiskDiseaseData implements PageHasId {

    private long id;

    private String diseaseClassName;

    private String medicalName;

    private String normalName;

    private String diseaseMergeRule;

    private int raiseType;

    private String raiseTypeDesc;

    private int choiceType;

    private int isDelete;



    public static RiskDiseaseData build(RiskDiseaseInfoVO riskDiseaseInfoVO) {
        RiskDiseaseData diseaseData = new RiskDiseaseData();
        diseaseData.setDiseaseClassName(riskDiseaseInfoVO.getDiseaseClassName());
        diseaseData.setDiseaseMergeRule(StringUtils.trimToEmpty(riskDiseaseInfoVO.getDiseaseMergeRule()));
        diseaseData.setMedicalName(StringUtils.trimToEmpty(riskDiseaseInfoVO.getMedicalName()));
        diseaseData.setNormalName(StringUtils.trimToEmpty(riskDiseaseInfoVO.getNormalName()));
        diseaseData.setRaiseType(riskDiseaseInfoVO.getRaiseType());
        diseaseData.setId(riskDiseaseInfoVO.getId());
        return diseaseData;
    }

    public static RiskDiseaseData build(String diseaseClassName,
                                        String medicalName,
                                        String normalName,
                                        String diseaseMergeRule,
                                        int raiseType) {
        RiskDiseaseData diseaseData = new RiskDiseaseData();
        diseaseData.diseaseClassName = diseaseClassName;
        diseaseData.medicalName = medicalName;
        diseaseData.normalName = normalName;
        diseaseData.diseaseMergeRule = diseaseMergeRule;
        diseaseData.raiseType = raiseType;
        return diseaseData;
    }

    /* @Getter
    public enum TreatmentProjectEnum {
        no_limit(1, "无要求"),
        need_operation(2, "做手术"),
        steel_needle(3, "取钢针"),
        custom(4, "自定义"),
        ;
        int code;
        String desc;

        TreatmentProjectEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static TreatmentProjectEnum findByCode(int code) {
            for (TreatmentProjectEnum value : TreatmentProjectEnum.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }*/
}
