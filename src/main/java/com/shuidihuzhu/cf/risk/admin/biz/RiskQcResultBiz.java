package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult;

import java.util.List;

public interface RiskQcResultBiz {

    List<RiskQcResult> getQcResultByTime(String qcUniqueCode, long startTime, long endTime);

    int add(long firstQcResultId, long secondQcResultId, String problemDescribe, long wordOrderId,
            String qcUniqueCode, int resultType, long qcId);

    RiskQcResult getByWorkOrderId(long wordOrderId);

    int updateInfo(long firstQcResultId, long secondQcResultId, String problemDescribe, long wordOrderId);

    List<RiskQcResult> findByWorkOrderIds(List<Long> wordOrderIds);

}
