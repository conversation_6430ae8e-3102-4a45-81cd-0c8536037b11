package com.shuidihuzhu.cf.risk.admin.aspect;

import com.alibaba.fastjson.JSON;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.shuidihuzhu.cf.risk.admin.service.mq.BlacklistHitConsumer;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 20190708
 */
@Slf4j
@Aspect
@Component
public class MQDeRepeatAspect {

    String KEY_PREFIX = "cf_risk_admin_";

    /**
     * 通过keys做MQ去重的consumer set
     */
    private static final Set<Class<?>> allowDeRepeatClassSet = Set.of(
            BlacklistHitConsumer.class
    );

    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;

    /**
     * MQ统一consumer分布式锁前缀
     */
    private String ROCKETMQ_CONSUMER_DISTRIBUTED_LOCK_PREFIX = KEY_PREFIX + "mq_lock_";
    /**
     * MQ consumer最大lock锁定时间 20分钟
     */
    private static final long ROCKETMQ_CONSUMER_LOCK_TIMEOUT_MILLISECOND = 20 * 60 * 1000;

    private static final long REDIS_KEY_TIMEOUT_MILLISECOND = 24 * 60 * 60 * 1000L;

    /**
     * 固定等待时长，单位：millisecond
     */
    private static final Integer FIXED_WAIT_DURATION = 500;
    /**
     * 延迟时长后停止重试，单位：second
     */
    private static final Integer STOP_AFTER_DELAY_DURATION = 6;

    /**
     * rocketmq consumer aop pointcut
     */
    @Pointcut("execution(* com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener.consumeMessage(com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage))")
    public void rocketMQConsumePointcut(){}

    @Around("rocketMQConsumePointcut()")
    public Object rocketMQAroundConsume(ProceedingJoinPoint joinPoint) {
        if (!mqAopHandle(joinPoint)) {
            try {
                return joinPoint.proceed();
            } catch (Throwable e) {
                log.error("", e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        }

        String identity = null;
        String lockKey = null;
        Object result;
        try {
            ConsumerMessage consumerMessage = checkAndGetConsumerMessage(joinPoint.getArgs());
            String keys = consumerMessage.getKeys();
            //1. 先进行分布式锁锁定
            lockKey = ROCKETMQ_CONSUMER_DISTRIBUTED_LOCK_PREFIX + consumerMessage.getTopic() + "_" +
                    consumerMessage.getTags() + "_" + keys;
            if (log.isDebugEnabled()) {
                log.debug("MQ deRepeat consumer redis lockKey:{}", lockKey);
            }
            //1.1 如果获取锁异常，直接让rocketMQ重发
            identity = redissonHandler.tryLock(lockKey, ROCKETMQ_CONSUMER_LOCK_TIMEOUT_MILLISECOND);
            //2. get lock
            //2.1 get lock 失败，直接返回延迟发送，不做任务处理
            if (StringUtils.isBlank(identity)) {
                log.info("rocketmq consumer aop获取Lock失败, keys:".concat(keys));
                return ConsumeStatus.RECONSUME_LATER;
            }
            //3. 调用具体的consumer处理
            result = joinPoint.proceed();
        } catch (Throwable e) {
            log.error("", e);
            return ConsumeStatus.RECONSUME_LATER;
        } finally {
            //4. 处理完成释放资源
            //4.1 释放redis锁：如果释放失败，最多需要等待20分钟后可以再次获取锁（依赖MQ重试机制）
            if (StringUtils.isNotBlank(identity)) {
                try {
                    redissonHandler.unLock(lockKey, identity);
                } catch (Throwable e) {
                    log.error("rocketMQ consumer aop redis lock release failed! redisKey:{}, errorMsg:{}", lockKey, e.getMessage());
                }
            }
        }

        printConsumerReturn(result, joinPoint.getArgs());
        return result;
    }

    /**
     * 环绕增强，处理实际消息订阅端，防止消息重复消费
     * 仅仅保证consumer消息在正常处理后，不再重复接收消息
     */
    @Around("rocketMQConsumePointcut()")
    public Object rocketMQRecordHandled(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!mqAopHandle(joinPoint)) {
            try {
                return joinPoint.proceed();
            } catch (Throwable e) {
                log.error("", e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        }

        ConsumerMessage consumerMessage = (ConsumerMessage) joinPoint.getArgs()[0];
        String keys = consumerMessage.getKeys();
        String simpleName = joinPoint.getSignature().getDeclaringType().getSimpleName();

        String redisKey = consumerMessage.getTopic() + "_" + consumerMessage.getTags() + "_" + keys + "_" + simpleName;
        if (log.isDebugEnabled()) {
            log.debug("MQ deRepeat consumer event redis key:{}", redisKey);
        }

        //查询是否处理过，如果异常直接抛出，让rocketmq进行重发
        String redisValue = redissonHandler.get(redisKey, String.class);
        //如果存在redis，说明已经接收过consumer请求，不做重复处理，直接返回
        if (redisValue != null) {
            log.info("发现rocketMQ已处理的重复消息, EventHandler:{}, keys:{}", simpleName, keys);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        ConsumeStatus res = (ConsumeStatus) joinPoint.proceed();

        //保存防重标识：keys + ClassName，如果失败进行重试(尽可能的设置)
        try {
            if (res == ConsumeStatus.CONSUME_SUCCESS) {
                RetryerBuilder.newBuilder()
                        .withWaitStrategy(WaitStrategies.fixedWait(FIXED_WAIT_DURATION, TimeUnit.MILLISECONDS))
                        .withStopStrategy(StopStrategies.stopAfterDelay(STOP_AFTER_DELAY_DURATION, TimeUnit.SECONDS))
                        .retryIfException()
                        .build()
                        .call(() -> {
                            redissonHandler.setEX(redisKey, "", REDIS_KEY_TIMEOUT_MILLISECOND);
                            return true;
                        });
            }
        } catch (Throwable e){
            log.error("事件处理完成后，设置redis失败！", e);
        }

        return res;
    }

    private ConsumerMessage checkAndGetConsumerMessage(Object[] args){
        //检验处理
        String errorMsg = "rocketMq consumer aop传入参数为空";
        if (args == null) {
            log.error(errorMsg);
            //发现MQ的内容为空，则直接抛出异常，终止后续逻辑
            throw new IllegalStateException(errorMsg);
        }
        ConsumerMessage consumerMessage = (ConsumerMessage) args[0];
        String keys = consumerMessage.getKeys();
        if (StringUtils.isBlank(keys)) {
            errorMsg = "rocketmq consumer aop传入keys为空，msgId:"
                    .concat(consumerMessage.getMsgId())
                    .concat(", payLoad:".concat(JSON.toJSONString(consumerMessage.getPayload())));
            log.error(errorMsg);
            //发现MQ的keys为空，则直接抛出异常，终止后续逻辑
            throw new IllegalStateException(errorMsg);
        }

        return consumerMessage;
    }

    /**
     * 打印消费结果 debug模式
     * <AUTHOR>
     * @date 2019/8/27 13:36
     * @param result 返回的结果
     */
    private void printConsumerReturn(Object result, Object[] args){
        if (log.isDebugEnabled()) {
            ConsumerMessage consumerMessage = (ConsumerMessage) args[0];
            log.debug("MQ deRepeat consumer keys:{}, return:{}", consumerMessage.getKeys(), result);
        }
    }

    private boolean mqAopHandle(ProceedingJoinPoint joinPoint) {
        return allowDeRepeatClassSet.contains(joinPoint.getSignature().getDeclaringType());
    }

}
