package com.shuidihuzhu.cf.risk.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 * <AUTHOR>
 * @date 2020/7/16 21:28
 */
@AllArgsConstructor
@Getter
public enum CommonStatusEnum {

    ENABLE(0, "启用"),
    DISABLE(1, "弃用"),
    ;

    public static CommonStatusEnum fromCode(int code){
        for (CommonStatusEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }

        return null;
    }

    private Byte code;
    private String desc;

    CommonStatusEnum(int code, String desc) {
        this.code = (byte)code;
        this.desc = desc;
    }

}
