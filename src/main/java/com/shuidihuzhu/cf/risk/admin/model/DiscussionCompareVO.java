package com.shuidihuzhu.cf.risk.admin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class DiscussionCompareVO implements PageHasId {
    private long id;
    private String insteadTitle;
    private String insteadDescription;
    private String insteadImages;
    private String operator;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Timestamp insteadTime;
    private String title;
    private String description;
    private String images;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Timestamp createTime;
    private int isDelete;
}

