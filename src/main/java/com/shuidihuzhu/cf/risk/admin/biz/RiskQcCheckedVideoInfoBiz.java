package com.shuidihuzhu.cf.risk.admin.biz;


import com.shuidihuzhu.cf.risk.admin.model.param.RiskQcVideoInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcCheckedVideoInfo;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/11/11
 */
public interface RiskQcCheckedVideoInfoBiz {
    int addInfo(long workOrderId, String checkedId);

    RiskQcCheckedVideoInfo getByWorkOrderId(long workOrderId);

    RiskQcVideoInfoModel getInfoByWorkOrderId(long workOrderId);

    int updateByWorkOrderId(String checkId, long workOrderId);

    List<RiskQcCheckedVideoInfo> findByWorkOrderId(List<Long> workOrderIdList);

    int add(RiskQcCheckedVideoInfo param);

    int update(RiskQcCheckedVideoInfo param);
}
