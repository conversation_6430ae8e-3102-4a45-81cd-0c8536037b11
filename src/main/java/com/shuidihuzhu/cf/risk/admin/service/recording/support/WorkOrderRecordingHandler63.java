package com.shuidihuzhu.cf.risk.admin.service.recording.support;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.service.recording.AbsWorkOrderRecordingHandler;
import com.shuidihuzhu.client.cf.workorder.config.WorkOrderHelper;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2022/3/28 下午4:43
 * qc_zhu_dong 材审主动服务质检工单 录音获取 处理 存储
 */
@Component
@Slf4j
public class WorkOrderRecordingHandler63 extends WorkOrderRecordingHandler45 {

    @Override
    public AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum() {
        return AiAsrDelegate.HandleTypeEnum.HANDLE_MATERIAL_ACTIVE_SERVICE_QC;
    }

    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.qc_zhu_dong;
    }
}
