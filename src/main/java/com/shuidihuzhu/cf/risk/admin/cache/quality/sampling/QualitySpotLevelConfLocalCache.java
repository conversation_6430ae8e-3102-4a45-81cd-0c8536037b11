package com.shuidihuzhu.cf.risk.admin.cache.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotLevelConfBiz;
import com.shuidihuzhu.cf.risk.admin.cache.AbstractCache;
import com.shuidihuzhu.cf.risk.admin.cache.constant.CacheConstant;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-18
 **/
@Slf4j
@Service
public class QualitySpotLevelConfLocalCache extends AbstractCache<String, List<RiskQualitySpotLevelConfVo>> {

    @Resource
    private QualitySpotLevelConfBiz qualitySpotLevelConfBiz;

    @Override
    protected List<RiskQualitySpotLevelConfVo> queryData(String name) {
        return qualitySpotLevelConfBiz.listWithAllLatestValidScene(0, 0);
    }

    @Override
    protected int getExpireAfterWriteSecond() {
        return CacheConstant.CONFIG_LOCAL_CACHE_EXPIRE_AFTER_WRITE_SECOND;
    }

    @Override
    protected int getRefreshAfterWriteSecond() {
        return CacheConstant.CONFIG_LOCAL_CACHE_REFRESH_AFTER_WRITE_SECOND;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }
}
