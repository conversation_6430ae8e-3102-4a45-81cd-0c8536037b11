package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/12
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQualitySpotTypeDao {



    RiskQualitySpotType getById(@Param("typeId") long typeId);


    List<RiskQualitySpotType> getByParentId(@Param("parentId") long parentId);


    List<RiskQualitySpotType> findById(@Param("ids") List<Long> ids);
}
