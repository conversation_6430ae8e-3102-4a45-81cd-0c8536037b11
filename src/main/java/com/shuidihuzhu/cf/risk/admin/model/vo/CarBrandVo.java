package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.po.RiskCarBrand;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/8/17 20:00
 */
@Data
public class CarBrandVo {

    public CarBrandVo(RiskCarBrand riskCarBrand) {
        this.name = riskCarBrand.getBrandName();
        this.smallPic = riskCarBrand.getSignPicture();
        this.type = riskCarBrand.getBrandType();
    }

    @ApiModelProperty("车品牌")
    private String name;
    @ApiModelProperty("车标")
    private String smallPic;
    @ApiModelProperty("车品牌类型")
    private int type;

}
