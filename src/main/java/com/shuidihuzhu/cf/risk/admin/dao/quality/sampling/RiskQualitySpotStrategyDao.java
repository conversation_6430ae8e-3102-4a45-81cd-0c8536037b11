package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.cf.risk.admin.model.query.quality.sampling.QualitySpotStrategyQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotStrategyVo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQualitySpotStrategyDao {
    int insertSelective(RiskQualitySpotStrategy record);

    RiskQualitySpotStrategy selectByPrimaryKey(Long id);

    List<RiskQualitySpotStrategy> listByConditions(QualitySpotStrategyQuery strategyQuery);

    List<RiskQualitySpotStrategy> listByScene(@Param("scene") Long scene, @Param("Status") Byte Status);

    int updateStatusById(@Param("id") Long id, @Param("status") Byte status, @Param("currTime") Date currTime,
                         @Param("operateId") Long operateId, @Param("operateName") String operateName);

    List<RiskQualitySpotStrategy> listValidStrategy(@Param("startId") Long startId, @Param("limit") Integer limit, @Param("currTime") Date currTime);

    List<RiskQualitySpotStrategy> listById(@Param("ids") List<Long> ids);

    int updateStrategy(RiskQualitySpotStrategy riskQualitySpotStrategy);
}