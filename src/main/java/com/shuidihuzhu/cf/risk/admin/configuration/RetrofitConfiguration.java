package com.shuidihuzhu.cf.risk.admin.configuration;

import com.shuidihuzhu.cf.risk.admin.feign.DiseaseSyncRetrofitClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Configuration
public class RetrofitConfiguration {

    private Retrofit riskAdminClient;

    @PostConstruct
    public void init(){
        riskAdminClient = new Retrofit.Builder()
                .baseUrl("https://api-bedin.shuiditech.com")
                .addConverterFactory(JacksonConverterFactory.create())
                .build();

    }

    @Bean
    public DiseaseSyncRetrofitClient diseaseSyncRetrofitClient() {
        return riskAdminClient.create(DiseaseSyncRetrofitClient.class);
    }
}
