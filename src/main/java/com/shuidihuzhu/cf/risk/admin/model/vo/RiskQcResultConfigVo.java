package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResultConfig;
import lombok.Data;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/29
 */
@Data
public class RiskQcResultConfigVo{
    private long id;
    private String qcResult;
    private List<RiskQcResultConfig> childrenConfig;




    public RiskQcResultConfigVo() {
    }

    public RiskQcResultConfigVo(long id, String qcResult, List<RiskQcResultConfig> childrenConfig) {
        this.id = id;
        this.qcResult = qcResult;
        this.childrenConfig = childrenConfig;
    }
}
