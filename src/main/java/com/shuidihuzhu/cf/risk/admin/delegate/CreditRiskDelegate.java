package com.shuidihuzhu.cf.risk.admin.delegate;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.client.highrisk.HighRiskClient;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDto;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class CreditRiskDelegate {

    @Autowired
    private HighRiskClient highRiskClient;

    public List<Integer> getRiskLabels(int caseId) {
        List<Integer> riskLabel = Lists.newArrayList();
        if (caseId <= 0) {
            return riskLabel;
        }
        //高风险案例手动调用规则码
        Response<List<HighRiskRecordDto>> response = highRiskClient.getListByCaseId(caseId);
        List<HighRiskRecordDto> highRiskRecordDtoList = Optional.ofNullable(response)
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(highRiskRecordDtoList)) {
            return riskLabel;
        }

        for (HighRiskRecordDto highRiskRecordDto : highRiskRecordDtoList) {
            riskLabel.addAll(highRiskRecordDto.getRedFieldCodes());
        }
        return riskLabel;
    }
}
