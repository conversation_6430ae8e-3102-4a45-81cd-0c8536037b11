package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/30 上午11:04
 */
@Data
public class BlacklistDataAutoAddVo {

    @NotNull
    @Min(value = 1)
    @ApiModelProperty(value = "案例id", required = true)
    private Integer caseId;

    @Length(min = 5, max = 200, message = "字数要求在5~200个字")
    @ApiModelProperty(value = "添加原因", required = true)
    private String operateReason;

    @NotNull(message = "限制动作不能为空")
    @ApiModelProperty("限制动作集合")
    private List<BlacklistTypeActionRefDto> actionList;

    @ApiModelProperty("黑名单类型Id")
    private long blackListTypeId;
}
