package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
public interface RiskDiseaseTreatmentProjectBiz {


    int deleteByDiseaseId(long diseaseId);

    int saveList(List<RiskDiseaseTreatmentProject> treatmentProjects);

    List<RiskDiseaseTreatmentProject> findByDiseaseId(long diseaseId);

    int deleteByIdList(List<Long> needdeleteIdList);

    int updateList(List<RiskDiseaseTreatmentProject> updateList);

    List<RiskDiseaseTreatmentProject> findByProjectName(String projectName);

}
