package com.shuidihuzhu.cf.risk.admin.model.enums;

public enum PublicSentimentInfoSourceEnum {
    //信息来源
    WEIBO(1, "微博"),
    WEIXIN(2, "微信"),
    TOUTIAO(3, "今日头条"),
    PIPIXIA(4, "皮皮虾"),
    TIEBA(5, "百度贴吧"),
    DOUYIN(6,"抖音"),
    OTHER(0,"其他"),
    ;

    int code;
    String description;

    PublicSentimentInfoSourceEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (PublicSentimentInfoSourceEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
