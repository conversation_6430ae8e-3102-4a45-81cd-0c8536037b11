package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;
import java.sql.Timestamp;

@Data
public class DiscussionCheckRecord implements PageHasId {

    private long id;
    private long discussionId;
    private int status;
    private String refuseReason;
    private String operator;
    private int refuseOption;
    private String title;
    private String description;
    private String images;
    //0代录入1不代录入
    private int insteadStatus;
    private String insteadTitle;
    private String insteadDescription;
    private String insteadImages;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public DiscussionCheckRecord(long discussionId, int status, String refuseReason, String operator) {
        this.discussionId = discussionId;
        this.status = status;
        this.refuseReason = refuseReason;
        this.operator = operator;
    }

    public void buildInfo(String title, String description, String images){
        this.title = title;
        this.description = description;
        this.images = images;
    }
}
