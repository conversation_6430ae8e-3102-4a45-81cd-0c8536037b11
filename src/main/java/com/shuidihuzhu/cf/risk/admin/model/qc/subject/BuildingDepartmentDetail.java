package com.shuidihuzhu.cf.risk.admin.model.qc.subject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BuildingDepartmentDetail{

    @ApiModelProperty("科室名称")
    private String departmentName;
    @ApiModelProperty("楼层信息")
    private String buildingFloor;
    @ApiModelProperty("床位信息")
    private String bedNum;
    @ApiModelProperty("是否修改标识,true:是")
    private boolean hasChange;
    @ApiModelProperty("ai科室归一信息")
    private String departmentAi;
}
