package com.shuidihuzhu.cf.risk.admin.rule.enums;

/**
 * <AUTHOR>
 * @date 2020-02-26
 **/
public enum JunctionType {
    AND("且", "&&"),
    OR("或", "||"),
    ;

    String name;
    String script;

    JunctionType(String name, String script) {
        this.name = name;
        this.script = script;
    }

    public String getName() {
        return name;
    }

    public String getScript() {
        return script;
    }

}
