package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseDataPortion;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
public interface RiskDiseaseDataBiz {

    int delete(long diseaseId);

    RiskDiseaseData getByClassName(String diseaseClassName);

    int save(RiskDiseaseData riskDiseaseData);

    int update(RiskDiseaseData riskDiseaseData);

    RiskDiseaseData getById(long diseaseId);

    List<RiskDiseaseData> findList(String diseaseClassName, String medicalName,
                                   String normalName, int raiseType, PageRequest pageRequest);

    List<RiskDiseaseData> findListV2(String diseaseClassName, int isDelete, String startCreateTime,
                                   String endCreateTime, int raiseType, List<Long> diseaseIds, int current, int pageSize);


    int deleteByName(String diseaseClassName);

    List<RiskDiseaseDataPortion> getAllDiseaseRule();

    List<RiskDiseaseData> findDiseaseNormList(String diseaseClassName);
}
