package com.shuidihuzhu.cf.risk.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Auther: subing
 * @Date: 2020/11/16
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum QcAppealResultEnum {
    PASS(6, "通过"),
    NO_PASS(7,"不通过"),
    PART_PASS(8,"部分通过");

    private int code;
    private String desc;

    public static String findOfCode(int code){
        for (QcAppealResultEnum value : values()) {
            if (value.getCode() == code){
                return value.getDesc();
            }
        }
        return "";
    }
}
