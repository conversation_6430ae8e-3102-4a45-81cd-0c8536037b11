package com.shuidihuzhu.cf.risk.admin.util;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import com.qcloud.cos.region.Region;
import com.shuidihuzhu.cf.store.plugins.CosPlugins;
import com.shuidihuzhu.client.cf.security.model.COSBucketEnum;
import com.shuidihuzhu.infra.starter.cos.configuration.CosClientWrapper;
import com.shuidihuzhu.pf.common.v2.cos.CosPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @author: subing
 * @create: 2020/06/22
 */
@Slf4j
@Component
@Validated
public class CosUploadUtil {

    private static final String BUCKET = "cf-risk";
    private static final String BASE_DIR = "cf-risk-admin/txt";

    @Autowired
    private CosPlugins cosPlugin;

    @Resource(name = "cos-cf-growthtool-api")
    private CosClientWrapper growthtoolCosClientWrapper;

    private static CosClientWrapper sGrowthtoolCosClientWrapper;

//    private static COSClient cosClient = null;
//    static {
//        COSBucketEnum bucketEnum = COSBucketEnum.CF_GROWTHTOOL;
//        COSCredentials cred = new BasicCOSCredentials(bucketEnum.getSecretId(), bucketEnum.getSecretKey());
//        Region region = new Region(bucketEnum.getRegion());
//        ClientConfig clientConfig = new ClientConfig(region);
//        cosClient = new COSClient(cred, clientConfig);
//    }

    @PostConstruct
    public void init(){
        sGrowthtoolCosClientWrapper = growthtoolCosClientWrapper;
    }

    /**
     * 获取cosSign
     * CosClient请求方式
     *
     * fileName 文件相对路径(文件夹+文件名)
     * @return
     */
    private static String getCosSign(String fileName){
        return getCosSignWithExpireTime(fileName, 0);
    }

    public static String getCosSignWithUrl(String url){
        URL urlObject;
        try {
            urlObject = new URL(url);
        } catch (MalformedURLException e) {
            log.error("getCosSignWithUrl MalformedURLException ", e);
            throw new RuntimeException(e);
        }
        String path = urlObject.getPath();
        return getCosSign(path);
    }

    /**
     * 为AI语音识别生成COS签名URL
     *
     * 该方法专门为AI语音识别服务生成长时效的COS签名URL：
     * 1. 解析原始URL获取文件路径
     * 2. 生成32小时有效期的签名URL
     * 3. 确保AI分析任务有足够时间完成文件访问
     *
     * @param url 原始的COS文件URL
     * @return 带有32小时有效期签名的COS访问URL
     * @throws RuntimeException 当URL格式不正确时抛出异常
     */
    public static String getAsrCosSignWithUrl(String url){
        URL urlObject;
        try {
            // 解析URL字符串为URL对象，用于提取文件路径
            urlObject = new URL(url);
        } catch (MalformedURLException e) {
            // URL格式错误时记录错误日志并抛出运行时异常
            log.error("getAsrCosSignWithUrl MalformedURLException for url: {}", url, e);
            throw new RuntimeException("Invalid URL format: " + url, e);
        }

        // 提取URL中的文件路径部分（不包含域名和查询参数）
        String path = urlObject.getPath();

        // 生成32小时有效期的COS签名URL
        // AI语音识别任务可能需要较长时间，因此设置较长的有效期
        return getCosSignWithExpireTime(path, TimeUnit.HOURS.toMillis(32));
    }

    /**
     * 生成指定过期时间的COS签名URL
     *
     * 该方法是COS签名生成的核心实现：
     * 1. 使用腾讯云COS SDK生成预签名URL
     * 2. 支持自定义过期时间，默认1小时
     * 3. 生成的URL可以在指定时间内安全访问COS文件
     *
     * @param fileName COS文件路径（相对于存储桶根目录）
     * @param expireTime 过期时间（毫秒），0表示使用默认1小时
     * @return 带签名的COS访问URL字符串，失败时返回null
     */
    public static String getCosSignWithExpireTime(String fileName, long expireTime){
        try {
            // 获取COS存储桶名称，格式为 BucketName-APPID
            String bucketName = COSBucketEnum.CF_GROWTHTOOL.getBucketName();

            // 创建预签名URL请求对象，指定存储桶、文件路径和HTTP方法（GET）
            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, fileName, HttpMethodName.GET);

            // 设置签名过期时间：0表示使用默认1小时，否则使用指定时间
            expireTime = expireTime == 0 ? TimeUnit.HOURS.toMillis(1) : expireTime;
            Date expirationDate = new Date(System.currentTimeMillis() + expireTime);
            req.setExpiration(expirationDate);

            // 使用COS客户端生成预签名URL
            URL url = sGrowthtoolCosClientWrapper.getOriginCosClient().generatePresignedUrl(req);

            log.debug("Generated COS signed URL for file: {}, expireTime: {}ms", fileName, expireTime);
            return url.toString();
        } catch (Exception e){
            // 签名生成失败时记录错误日志
            log.error("CosUploadUtil.getCosSignWithExpireTime error for fileName: {}, expireTime: {}", fileName, expireTime, e);
        }
        return null;
    }

    /**
     * 上传文本
     *
     * @param isContent 文本内容
     * @param directory 文本上传目录，首位需要'/' （可以为null）
     * @return cos 访问地址,如果返回为空串，说明没有上传成功
     */
    public String uploadText(@NotBlank(message = "文本内容不能为空") String isContent, @Nullable String directory) {
        try (InputStream is = IOUtils.toInputStream(isContent, StandardCharsets.UTF_8)) {
            String finalDir = BASE_DIR;
            if (StringUtils.isNotBlank(directory)) {
                finalDir = finalDir + directory;
            }
            return cosPlugin.uploadFile(is, BUCKET, finalDir, UUID.randomUUID().toString().replace("-",
                    "").toLowerCase() + ".txt");
        } catch (IOException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }
}
