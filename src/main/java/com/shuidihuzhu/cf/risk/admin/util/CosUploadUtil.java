package com.shuidihuzhu.cf.risk.admin.util;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import com.qcloud.cos.region.Region;
import com.shuidihuzhu.cf.store.plugins.CosPlugins;
import com.shuidihuzhu.client.cf.security.model.COSBucketEnum;
import com.shuidihuzhu.infra.starter.cos.configuration.CosClientWrapper;
import com.shuidihuzhu.pf.common.v2.cos.CosPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @author: subing
 * @create: 2020/06/22
 */
@Slf4j
@Component
@Validated
public class CosUploadUtil {

    private static final String BUCKET = "cf-risk";
    private static final String BASE_DIR = "cf-risk-admin/txt";

    @Autowired
    private CosPlugins cosPlugin;

    @Resource(name = "cos-cf-growthtool-api")
    private CosClientWrapper growthtoolCosClientWrapper;

    private static CosClientWrapper sGrowthtoolCosClientWrapper;

//    private static COSClient cosClient = null;
//    static {
//        COSBucketEnum bucketEnum = COSBucketEnum.CF_GROWTHTOOL;
//        COSCredentials cred = new BasicCOSCredentials(bucketEnum.getSecretId(), bucketEnum.getSecretKey());
//        Region region = new Region(bucketEnum.getRegion());
//        ClientConfig clientConfig = new ClientConfig(region);
//        cosClient = new COSClient(cred, clientConfig);
//    }

    @PostConstruct
    public void init(){
        sGrowthtoolCosClientWrapper = growthtoolCosClientWrapper;
    }

    /**
     * 获取cosSign
     * CosClient请求方式
     *
     * fileName 文件相对路径(文件夹+文件名)
     * @return
     */
    private static String getCosSign(String fileName){
        return getCosSignWithExpireTime(fileName, 0);
    }

    public static String getCosSignWithUrl(String url){
        URL urlObject;
        try {
            urlObject = new URL(url);
        } catch (MalformedURLException e) {
            log.error("getCosSignWithUrl MalformedURLException ", e);
            throw new RuntimeException(e);
        }
        String path = urlObject.getPath();
        return getCosSign(path);
    }

    public static String getAsrCosSignWithUrl(String url){
        URL urlObject;
        try {
            urlObject = new URL(url);
        } catch (MalformedURLException e) {
            log.error("getCosSignWithUrl MalformedURLException ", e);
            throw new RuntimeException(e);
        }
        String path = urlObject.getPath();
        return getCosSignWithExpireTime(path, TimeUnit.HOURS.toMillis(32));
    }

    public static String getCosSignWithExpireTime(String fileName, long expireTime){
        try {
            // 存储桶的命名格式为 BucketName-APPID，此处填写的存储桶名称必须为此格式
            String bucketName = COSBucketEnum.CF_GROWTHTOOL.getBucketName();
            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, fileName, HttpMethodName.GET);
            // 设置签名过期时间(可选), 若未进行设置, 则默认使用 ClientConfig 中的签名过期时间(1小时)
            expireTime = expireTime == 0 ? TimeUnit.HOURS.toMillis(1) : expireTime;
            Date expirationDate = new Date(System.currentTimeMillis() + expireTime);
            req.setExpiration(expirationDate);
            URL url = sGrowthtoolCosClientWrapper.getOriginCosClient().generatePresignedUrl(req);
            return url.toString();
        }catch (Exception e){
            log.error("CosUploadUtil.getCosSign error", e);
        }
        return null;
    }

    /**
     * 上传文本
     *
     * @param isContent 文本内容
     * @param directory 文本上传目录，首位需要'/' （可以为null）
     * @return cos 访问地址,如果返回为空串，说明没有上传成功
     */
    public String uploadText(@NotBlank(message = "文本内容不能为空") String isContent, @Nullable String directory) {
        try (InputStream is = IOUtils.toInputStream(isContent, StandardCharsets.UTF_8)) {
            String finalDir = BASE_DIR;
            if (StringUtils.isNotBlank(directory)) {
                finalDir = finalDir + directory;
            }
            return cosPlugin.uploadFile(is, BUCKET, finalDir, UUID.randomUUID().toString().replace("-",
                    "").toLowerCase() + ".txt");
        } catch (IOException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }
}
