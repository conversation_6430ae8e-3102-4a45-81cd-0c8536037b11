package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2022/3/25 下午4:18
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface WorkOrderRecordingMapper {
    int insertSelective(WorkOrderRecordingDO record);

    int batchInsert(@Param("records") List<WorkOrderRecordingDO> records);

    @DataSource(RiskAdminDS.CF_RISK_DATASOURCE)
    List<WorkOrderRecordingDO> listByWorkOrderId(Long workOrderId);

    WorkOrderRecordingDO selectById(Long id);

    int updateWorkOrderRecordingExt(WorkOrderRecordingDO workOrderRecordingDO);

}
