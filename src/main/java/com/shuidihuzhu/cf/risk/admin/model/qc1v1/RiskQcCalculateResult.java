package com.shuidihuzhu.cf.risk.admin.model.qc1v1;

import lombok.Data;

/**
 * @Auther: subing
 * @Date: 2020/8/7
 */
@Data
public class RiskQcCalculateResult {
    private int qcResult;
    private int criticalError;
    private int nonCriticalError;
    private int praiseCount;

    public RiskQcCalculateResult(int qcResult, int criticalError, int nonCriticalError, int praiseCount) {
        this.qcResult = qcResult;
        this.criticalError = criticalError;
        this.nonCriticalError = nonCriticalError;
        this.praiseCount = praiseCount;
    }

    public RiskQcCalculateResult() {
    }
}
