package com.shuidihuzhu.cf.risk.admin.model;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class DiscussionContentVO {
    private long id;
    private long discussionId;
    private int sourceNum;
    private String operator;
    private String title;
    private String description;
    private String images;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public DiscussionContentVO(long discussionId, int sourceNum, String operator) {
        this.discussionId = discussionId;
        this.sourceNum = sourceNum;
        this.operator = operator;
    }

    public void buildInfo(String title, String description, String images){
        this.title = title;
        this.description = description;
        this.images = images;
    }
}
