package com.shuidihuzhu.cf.risk.admin.feign;

import com.shuidihuzhu.cf.risk.admin.model.disease.DiseaseSyncDTO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 *
 * <AUTHOR>
 */
@FeignClient(name = "cf-risk-admin", url = "https://api-bedin.shuiditech.com",
        fallback = DiseaseSyncFeignClient.Fallback.class)
public interface DiseaseSyncFeignClient {

    String PATH = "/api/cf-risk-admin/disease-sync/";

    /**
     * 轻量读取案例信息 从缓存中
     */
    @PostMapping(PATH + "accept")
    Response<DiseaseSyncDTO> accept(@RequestBody DiseaseSyncDTO data);

    @Component
    class Fallback implements DiseaseSyncFeignClient {

        @Override
        public Response<DiseaseSyncDTO> accept(DiseaseSyncDTO data) {
            return NewResponseUtil.makeFail("rpc fall back");
        }
    }
}