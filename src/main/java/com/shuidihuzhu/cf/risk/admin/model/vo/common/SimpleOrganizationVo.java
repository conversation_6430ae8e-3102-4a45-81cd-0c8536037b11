package com.shuidihuzhu.cf.risk.admin.model.vo.common;


import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.feign.GroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleGroupVo;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/14
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Service
public class SimpleOrganizationVo {
    @ApiModelProperty("组织结构id")
    private long id;

    @ApiModelProperty("组织结构名称")
    private String organizationName;

    @ApiModelProperty("是否是内部组织")
    private boolean innerOrg;

    @ApiModelProperty("父类id")
    private long parentId;

    @ApiModelProperty("子类组织集合")
    private List<SimpleOrganizationVo> subOrg = Lists.newArrayList();

    @Autowired
    private  GroupFeignClient groupFeignClient;


    public SimpleOrganizationVo buildVo(SimpleGroupVo simpleGroupVo) {
        SimpleOrganizationVo vo = new SimpleOrganizationVo();
        Response<AuthGroupDto> response = groupFeignClient.selectById(simpleGroupVo.getGroupId());
        if (response.notOk() || response.getData() == null){
            vo.setId(0);
        }
        vo.setId(response.getData().getGroupBizId());
        vo.setOrganizationName(simpleGroupVo.getGroupName());
        return vo;
    }

    public  SimpleOrganizationVo buildVo(AuthGroupDto authGroupDto) {
        SimpleOrganizationVo vo = new SimpleOrganizationVo();
        vo.setId(authGroupDto.getGroupBizId());
        vo.setOrganizationName(authGroupDto.getGroupName());
        vo.setParentId(authGroupDto.getParentId());
        if (CollectionUtils.isEmpty(authGroupDto.getChidrenGroups())){
            return vo;
        }
        for (AuthGroupDto groupDto : authGroupDto.getChidrenGroups()) {
            vo.getSubOrg().add(buildVo(groupDto));
        }
        return vo;
    }
}
