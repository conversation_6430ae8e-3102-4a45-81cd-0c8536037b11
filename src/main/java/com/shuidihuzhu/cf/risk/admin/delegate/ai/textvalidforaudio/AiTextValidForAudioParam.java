package com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio;

import com.shuidihuzhu.cf.risk.admin.model.vo.AsrSentenceVO;
import lombok.Data;

import java.util.List;

/**
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=932950052
 * <AUTHOR>
 */
@Data
public class AiTextValidForAudioParam {

    private long workOrderId;

    private String type = "class";

    private List<Video> videos;

    @Data
    public static class Video {
        private long id;
        private String videoUrl;
        private String createTime;
        private String endTime;
        private List<AsrSentenceVO> sentences;
    }

}
