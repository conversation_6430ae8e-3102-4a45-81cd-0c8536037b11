package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcSearchIndexDao;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.client.cf.clewtrack.enums.CfClueInfoStatEnums;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoStatModel;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @author: lixiaoshuang
 * @create: 2020-08-10 16:20
 **/
@Service
@RocketMQListener(id = "sub_1v1_assign",
        group = "sub_1v1_assign_" + CfClientMQTagCons.CLUEOPERATION_NOTICE_OLAP,
        tags = CfClientMQTagCons.CLUEOPERATION_NOTICE_OLAP,
        topic = MQTopicCons.CF)
@Slf4j
public class Sub1v1AssignConsumer implements MessageListener<CfClueInfoStatModel> {

    @Autowired
    private Producer producer;
    @Autowired
    private RiskQcSearchIndexDao riskQcSearchIndexDao;

    private static final String KEY = "apollo.qc_1v1_order_create.delay-seconds";

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfClueInfoStatModel> mqMessage) {
        CfClueInfoStatModel cfClueInfoStatModel = mqMessage.getPayload();
        if (Objects.isNull(cfClueInfoStatModel)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.info("Sub1v1AssignConsumer cfClueInfoStatModel:{}", JSON.toJSONString(cfClueInfoStatModel));
        CfClueInfoStatEnums.ClueOperateEnum clueOperateEnum = cfClueInfoStatModel.getClueOperateEnum();
        if (Objects.isNull(clueOperateEnum)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Integer code = clueOperateEnum.getCode();
        if (code.equals(CfClueInfoStatEnums.ClueOperateEnum.CLUE_OPERATE_8.getCode())) {
            log.info("Sub1v1AssignConsumer 1v1");
            //1v1服务任务分配
            final Long delaySeconds = ConfigService.getAppConfig().getLongProperty(KEY, TimeUnit.MINUTES.toSeconds(30));

            MaliMQComponent.builder()
                    .setTags(RiskMQTagCons.QC_1v1_WORK_ORDER_CREATE)
                    .setDelayTime(delaySeconds * 1000L)
                    .addKey(RiskMQTagCons.QC_1v1_WORK_ORDER_CREATE + cfClueInfoStatModel.getCfClewTaskDO().getId())
                    .setPayload(cfClueInfoStatModel)
                    .send();
        } else if (code.equals(CfClueInfoStatEnums.ClueOperateEnum.CLUE_OPERATE_11.getCode())) {
            log.info("Sub1v1AssignConsumer call");
            //外呼任务分配
            producer.send(new Message(MQTopicCons.CF, RiskMQTagCons.QC_CALL_WORK_ORDER_CREATE,
                    RiskMQTagCons.QC_CALL_WORK_ORDER_CREATE + cfClueInfoStatModel.getCfClewTaskDO().getId(), cfClueInfoStatModel, DelayLevel.M30));
        } else if (code.equals(CfClueInfoStatEnums.ClueOperateEnum.CLUE_OPERATE_12.getCode())) {
            //外呼任务状态变更
            CfClewTaskDO cfClewTaskDO = cfClueInfoStatModel.getCfClewTaskDO();
            log.info("Sub1v1AssignConsumer cfClewTaskDO:{}", JSON.toJSONString(cfClewTaskDO));
            CfClewTaskDO.ShowPageNameEnum showPageNameEnum = cfClewTaskDO.getShowPageNameEnum();
            if (Objects.isNull(showPageNameEnum)){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            riskQcSearchIndexDao.updateCallTaskStatusByTaskId(showPageNameEnum.getCode(), cfClewTaskDO.getId(), QcTypeEnum.CALL.getCode());
        } else if (code.equals(CfClueInfoStatEnums.ClueOperateEnum.CLUE_OPERATE_13.getCode())) {
            log.info("外呼任务打标签 cfClueInfoStatModel:{}",JSON.toJSONString(cfClueInfoStatModel));
            //外呼任务打标签
            Long firstLevelLabel = cfClueInfoStatModel.getFirstUserTag();
            Long twoLevelLabel = cfClueInfoStatModel.getSecondUserTag();
            CfClewTaskDO cfClewTaskDO = cfClueInfoStatModel.getCfClewTaskDO();
            log.info("Sub1v1AssignConsumer firstLevelLabel:{},twoLevelLabel:{}", firstLevelLabel, twoLevelLabel);
            riskQcSearchIndexDao.updateLabelByTaskId(firstLevelLabel, twoLevelLabel, cfClewTaskDO.getId(), QcTypeEnum.CALL.getCode());
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
