package com.shuidihuzhu.cf.risk.admin.controller.inner.test;

import com.shuidihuzhu.cf.risk.admin.delegate.ai.deptclassify.AiDeptClassifyDelegate;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcHospitalDeptService;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcOrderCaiLiaoCreateService;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/innerapi/cf-risk-admin/test/order")
@Slf4j
public class OrderTestInnerController {

    @Autowired
    private QcOrderCaiLiaoCreateService qcOrderCaiLiaoCreateService;

    @Autowired
    private AiDeptClassifyDelegate aiDeptClassifyDelegate;

    @Autowired
    private QcHospitalDeptService qcHospitalDeptService;

    @PostMapping("create-cai-liao")
    public Response<ConsumeStatus> touchCreateCaiLiao(@RequestBody WorkOrderResultChangeEvent changeEvent){
        ConsumeStatus consumeStatus = qcOrderCaiLiaoCreateService.touchCreateCaiLiaoQcOrder(changeEvent);
        return NewResponseUtil.makeSuccess(consumeStatus);
    }

    @PostMapping("test-ai-dept-classify")
    public Response<Map<String, String>> testAiDeptClassify(@RequestParam List<String> depts){
        final Map<String, String> analyse = aiDeptClassifyDelegate.analyse(depts);
        return NewResponseUtil.makeSuccess(analyse);
    }

    @PostMapping("refresh-dept-search-data")
    public Response<Map<String, String>> refreshDeptSearchData(@RequestParam String key, @RequestParam int page, @RequestParam int size){
        if (!StringUtils.equals("refreshData", key)) {
            return NewResponseUtil.makeFail("key错误");
        }
        qcHospitalDeptService.refreshData(page, size);
        return NewResponseUtil.makeSuccess();
    }


}
