package com.shuidihuzhu.cf.risk.admin.model.vo.list;

import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartmentLog;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2020/8/3 13:23
 */
@Data
public class ListDepartmentLogVo {

    public ListDepartmentLogVo(RiskListDepartmentLog listDepartmentLog){
        BeanUtils.copyProperties(listDepartmentLog, this);
        this.setUpdateTime(DateUtil.getDate2LStr(listDepartmentLog.getUpdateTime()));
    }

    @ApiModelProperty("科室电话名单id")
    private Long listDepartmentId;

    @ApiModelProperty("修改内容")
    private String modifyContent;

    @ApiModelProperty("操作人id")
    private Long operateId;

    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("操作时间")
    private String updateTime;

}
