package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.client.cf.admin.model.CfAsrRecordModel;
import com.shuidihuzhu.client.cf.riskadmin.CfRiskAdminAsrClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class CfAdminAsrInnerController implements CfRiskAdminAsrClient {

    @Autowired
    private AiAsrDelegate aiAsrDelegate;

    @Override
    public Response<Void> doHandleAsrRecord(List<CfAsrRecordModel> cfAsrRecordModel) {
        for (CfAsrRecordModel asrRecordModel : cfAsrRecordModel) {
            aiAsrDelegate.analyseUsePriority(asrRecordModel.getId(), asrRecordModel.getId(), asrRecordModel.getCosFile(), AiAsrDelegate.HandleTypeEnum.HANDLE_QI_WORK_RECORD, 0);
        }
        return NewResponseUtil.makeSuccess();
    }
}
