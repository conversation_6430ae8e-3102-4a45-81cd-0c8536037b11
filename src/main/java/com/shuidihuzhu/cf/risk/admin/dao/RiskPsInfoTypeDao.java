package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsInfoType;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskPsInfoTypeDao {


    List<RiskPsInfoType> getAll();

    int updateById(@Param("path") String path, @Param("id") long id);

    List<RiskPsInfoType> findByIdsIn(List<Long> ids);


    String getById(@Param("id")long  id);
}
