package com.shuidihuzhu.cf.risk.admin.model.enums;


import lombok.Getter;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-29 11:23
 **/
@Getter
public enum RiskRuleCondition {


    EQUAL(1, "等于"),

    EQUAL_LESS(2, "小于等于"),

    EQUAL_GREATER(3, "大于等于"),

    LESS(4, "小于"),

    GREATER(5, "大于"),
    ;

    private int code;

    private String msg;

    RiskRuleCondition(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public static boolean condition(int value, List<Integer> thresholds, RiskRuleCondition condition) {

        if (thresholds.size() == 1) {
            int threshold = thresholds.get(0);
            switch (condition) {
                case LESS:
                    return value < threshold;
                case EQUAL:
                    return value == threshold;
                case GREATER:
                    return value > threshold;
                case EQUAL_LESS:
                    return value <= threshold;
                case EQUAL_GREATER:
                    return value >= threshold;
                default:
                    return false;
            }
        }

        if (thresholds.size() == 2) {
            int min = thresholds.get(0);
            int max = thresholds.get(1);
            switch (condition) {
                case EQUAL:
                    return value >= min && value <= max;
                case EQUAL_LESS:
                    return value > min && value <= max;
                case EQUAL_GREATER:
                    return value >= min && value < max;
                default:
                    return false;
            }
        }
        return false;
    }
}
