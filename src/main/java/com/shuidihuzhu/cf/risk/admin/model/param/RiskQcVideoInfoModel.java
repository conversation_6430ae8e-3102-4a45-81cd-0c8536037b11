package com.shuidihuzhu.cf.risk.admin.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RiskQcVideoInfoModel {
    private long id;
    private long workOrderId;

    @ApiModelProperty("选中录音ids")
    List<Long> checkedIds;

    @ApiModelProperty("语音转译准确的录音id")
    List<Long> asrCorrectIds;

    @ApiModelProperty("语音转译不准确的录音id")
    List<Long> asrIncorrectIds;

}
