package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultBiz;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult;
import com.shuidihuzhu.cf.risk.client.admin.RiskQcResultInnerClient;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/6/22
 */
@RestController
public class RiskQcResultInnerController implements RiskQcResultInnerClient {
    @Autowired
    private RiskQcResultBiz riskQcResultBiz;
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;

    @Override
    public Response<Integer> getQcResultCount(@RequestParam(value = "qcUniqueCode") String qcUniqueCode,
                                      @RequestParam(value = "startTime")long startTime,
                                      @RequestParam(value = "endTime")long endTime){
        if (startTime > endTime || StringUtils.isBlank(qcUniqueCode)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<RiskQcResult> riskQcResults = riskQcResultBiz.getQcResultByTime(qcUniqueCode, startTime, endTime);

        Response<List<WorkOrderVO>> workOrderVOResponse = cfQcWorkOrderClient.queryQcByIds(riskQcResults.stream().map(RiskQcResult::getWorkOrderId).collect(Collectors.toList()));
        List<WorkOrderVO> workOrderVOList = Optional.ofNullable(workOrderVOResponse).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(workOrderVOList)) {
            return NewResponseUtil.makeSuccess(0);
        }
        Map<Long, Date> map = workOrderVOList.stream().collect(Collectors.toMap(WorkOrderVO::getWorkOrderId, WorkOrderVO::getCreateTime));
        List<RiskQcResult> list = Lists.newArrayList();
        for (RiskQcResult result : riskQcResults) {
            if (Objects.isNull(result) || Objects.isNull(result.getCreateTime())) {
                continue;
            }
            String resultDate = this.getSharding(result.getCreateTime());
            String workOrderDate = this.getSharding(map.get(result.getWorkOrderId()));
            if (resultDate.equals(workOrderDate)) {
                list.add(result);
            }
        }
        return NewResponseUtil.makeSuccess(CollectionUtils.size(list));
    }

    private String getSharding(Date date) {
        String format = "yyyyMM";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }
}
