package com.shuidihuzhu.cf.risk.admin.dao.list;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskCityArea;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskCityAreaDao {
    int insertSelective(RiskCityArea record);

    RiskCityArea selectByPrimaryKey(Long id);

    RiskCityArea getByProvinceCity(@Param("province") String province, @Param("city") String city);

    RiskCityArea getByProvinceCityId(@Param("provinceId") Integer provinceId, @Param("cityId") Integer cityId);

    List<RiskCityArea> listAll();
}