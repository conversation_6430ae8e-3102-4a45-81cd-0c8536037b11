package com.shuidihuzhu.cf.risk.admin.model.po.blacklist;

import lombok.Data;

import java.util.Date;
@Data
public class RiskBlacklistData {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 通过用户id查询的用户绑定手机号
     */
    private String encryptMobileBind;

    /**
     * 用户身份证号
     */
    private String encryptIdCard;

    /**
     * 用户手机号
     */
    private String encryptMobile;

    /**
     * 用户手机号反查到的用户id
     */
    private Long userIdBind;

    /**
     * 用户出生证
     */
    private String encryptBornCard;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 操作原因
     */
    private String operateReason;

    /**
     * 操作人id
     */
    private Long operateId;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 限制时长
     */
    private Long limitTime;

}