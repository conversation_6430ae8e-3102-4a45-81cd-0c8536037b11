package com.shuidihuzhu.cf.risk.admin.delegate.ai;

import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.alps.feign.ocean.OceanAsynApiResponse;
import com.shuidihuzhu.common.web.model.Response;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 */
public interface AiAgent {

    Response<OceanApiResponse> agent(@NotNull OceanApiRequest request, Response<OceanApiResponse> fallbackResp);

    Response<OceanAsynApiResponse> agentAsync(@NotNull OceanApiRequest request, Response<OceanAsynApiResponse> fallbackResp);

}
