package com.shuidihuzhu.cf.risk.admin.model.enums;

public enum RiskQcStandardUseSceneEnum {
    //
    OFFLINE_STANDARD(1, "线下顾问质检"),
    CAI_SHEN(7, "材审质检"),
    HOSPITAL_DEPT(11, "医院科室质检"),
    CAI_SHEN_ZHU_DONG(12, "材审主动服务质检"),
    HIGH_RISK_QUALITY_INSPECTION(15, "高风险质检工单"),
    ;

    int code;
    String description;

    RiskQcStandardUseSceneEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code) {
        for (RiskQcStandardUseSceneEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return "";
    }
}
