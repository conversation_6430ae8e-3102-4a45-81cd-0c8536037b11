package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

@Data
public class RiskPublicSentimentInfo implements PageHasId {

  private long id;
  private java.sql.Timestamp publishTime;
  private int infoSource;
  private String infoSourceOther;
  private int infoFeedBack;
  private String infoFeedBackOther;
  private int nickNameType;
  private String nickName;
  private Integer infoClassify;
  private String infoClassifyOther;
  private int caseId;
  private java.sql.Timestamp createTime;

  private String lastOperator;
  private int status;
  private String publicSentimentInfoType;
  private String solution;
  private String solutionOther;
  private int reportId;

}
