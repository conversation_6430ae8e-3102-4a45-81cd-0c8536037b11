package com.shuidihuzhu.cf.risk.admin.controller;


import com.shuidihuzhu.cf.risk.admin.biz.RiskCommentBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Timestamp;

@RestController
@RequestMapping("/api/cf-risk-admin/ugc-manage")
public class UgcManageController {

    @Autowired
    RiskCommentBiz riskCommentBiz;

    @ApiOperation("删除")
    @RequestMapping("/del")
    @RequiresPermission("ugc-manage:del")
    public Response delUgc(long id) {
        return NewResponseUtil.makeSuccess(riskCommentBiz.delById(id));
    }

    @ApiOperation("操作日志")
    @RequestMapping("/operation-log")
    public Response operationLog(long id) {
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("ugc列表")
    @RequestMapping("/list")
    public Response list(@RequestParam int caseId,
                         @RequestParam(required = false) String keyword,
                         @RequestParam(required = false) Timestamp startTime,
                         @RequestParam(required = false) Timestamp endTime,
                         @RequestParam String pageJson) {
        return NewResponseUtil.makeSuccess(riskCommentBiz.listByParam(caseId, keyword, startTime, endTime, pageJson));
    }
}
