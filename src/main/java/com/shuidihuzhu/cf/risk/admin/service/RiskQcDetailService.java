package com.shuidihuzhu.cf.risk.admin.service;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.adminpure.feign.CaiLiaoRefuseReasonFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.refuse.RefuseDataTypeDetailVO;
import com.shuidihuzhu.cf.client.adminpure.model.refuse.RefuseInfoVO;
import com.shuidihuzhu.cf.client.adminpure.model.refuse.RefuseItemVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.risk.admin.biz.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.dao.CfRiskQcCorrectRefuseRecordDao;
import com.shuidihuzhu.cf.risk.admin.dao.CfRiskRecordingProblemsRecordDao;
import com.shuidihuzhu.cf.risk.admin.delegate.ClewDelegate;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.*;
import com.shuidihuzhu.cf.risk.admin.model.param.RiskQcVideoInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.qc.*;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcCalculateResult;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QcHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Auther: subing
 * @Date: 2020/6/16
 */
@Service
@Slf4j
public class RiskQcDetailService {

    @Autowired
    private RiskQcResultBiz riskQcResultBiz;
    @Autowired
    private RiskQcResultConfigBiz riskQcResultConfigBiz;
    @Resource
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private RiskQcStandBiz riskQcStandBiz;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private RiskQcStandExtBiz riskQcStandExtBiz;
    @Autowired
    private RiskQcStandardPropertyBiz riskQcStandardPropertyBiz;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcStandardService riskQcStandardService;
    @Autowired
    private Risk1v1QcDetailService risk1v1QcDetailService;
    @Autowired
    private RiskQcMaterialDetailService riskQcMaterialDetailService;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Autowired
    private RiskQcCheckedVideoInfoBiz riskQcCheckedVideoInfoBiz;
    @Autowired
    private SeaAccountService accountService;

    @Autowired
    private ClewDelegate clewDelegate;

    @Autowired
    private CaiLiaoRefuseReasonFeignClient caiLiaoRefuseReasonFeignClient;

    @Autowired
    private CfRiskQcCorrectRefuseRecordDao cfRiskQcCorrectRefuseRecordDao;

    @Autowired
    private CfRiskRecordingProblemsRecordDao cfRiskRecordingProblemsRecordDao;

    public List<RiskQcResultConfigVo> getQcResultConfig(boolean isDetail) {
        List<RiskQcResultConfig> riskQcResultConfigs = riskQcResultConfigBiz.getAll();
        List<RiskQcResultConfig> firstResults = riskQcResultConfigs.stream().filter(t -> t.getLevel() == RiskQcStandardLevelEnum.ONE_LEVEL.getCode()).collect(Collectors.toList());
        List<RiskQcResultConfig> secondResults = riskQcResultConfigs.stream().filter(t -> t.getLevel() == RiskQcStandardLevelEnum.TWO_LEVEL.getCode()).collect(Collectors.toList());
        List<RiskQcResultConfigVo> riskQcResultConfigVos = Lists.newArrayList();
        firstResults.forEach(firstResult -> {
            List<RiskQcResultConfig> second = secondResults.stream().filter(t -> t.getParentId() == firstResult.getId()).collect(Collectors.toList());
            riskQcResultConfigVos.add(new RiskQcResultConfigVo(firstResult.getId(), firstResult.getQcResult(), second));
        });
        return isDetail ? riskQcResultConfigVos.subList(0,3) : riskQcResultConfigVos;
    }

    public boolean judge(RiskQcResultVo riskQcResultVo,int disposeAction) {

        if (disposeAction == 2 || disposeAction == 3) {
            return true;
        }

        List<QcRecordingProblemsVo> qcRecordingProblemsVos = riskQcResultVo.getQcRecordingProblemsVos();
        if (CollectionUtils.isNotEmpty(qcRecordingProblemsVos)) {
            for (QcRecordingProblemsVo vo : qcRecordingProblemsVos) {
                if (StringUtils.isNotEmpty(vo.getProblemIds()) || StringUtils.isNotEmpty(vo.getComment())) {
                    return true;
                }
            }
        }

        if (Objects.isNull(riskQcResultVo)) {
            return false;
        }

        if (StringUtils.isNotEmpty(riskQcResultVo.getPreInfoRemark()) ||
                StringUtils.isNotEmpty(riskQcResultVo.getLowIncomeRemark()) ||
                StringUtils.isNotEmpty(riskQcResultVo.getAddCreditRemark()) ||
                StringUtils.isNotEmpty(riskQcResultVo.getImgWordRemark())) {
            return true;
        }

        Set<Boolean> set = Sets.newHashSet();

        List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos = RiskQcResultVo.getList(riskQcResultVo.getMaterialQcResultOption());
        for (RiskMaterialQcStandardVo riskMaterialQcStandardVo : riskMaterialQcStandardVos) {
            List<RiskQcStandardVo> riskQcStandardVoList = riskMaterialQcStandardVo.getRiskQcStandardVo();
            for (RiskQcStandardVo riskQcStandardVo : riskQcStandardVoList) {
                List<RiskQcStandardDetailVo> riskQcStandardDetailVoList = riskQcStandardVo.getRiskQcStandardSecondVos();
                Set<Boolean> isChecks = riskQcStandardDetailVoList.stream().map(RiskQcStandardDetailVo::isCheck).collect(Collectors.toSet());
                set.addAll(isChecks);
            }
        }
        return set.size() > 1 || set.stream().findFirst().orElse(true);
    }

    public int addInfo(long firstQcResult, String problemDescribe, long secondQcResult,
                       long workOrderId, int disposeAction, int caseId, RiskQcResultVo riskQcResultVo, int qcType,
                       WorkOrderType workOrderType, String correctRefuseIds, Integer scene) {
        if (disposeAction == RiskQcDisponseActionEnum.LATER_DISPOSE.getCode()){
            int orderType = workOrderType == null ? 0 : workOrderType.getType();
            return handleQc(caseId, workOrderId, HandleResultEnum.later_doing, "稍后处理", orderType);
        }
        return save(firstQcResult, problemDescribe, secondQcResult, workOrderId, caseId, riskQcResultVo, qcType,
                workOrderType, correctRefuseIds, scene);
    }

    private int save(long firstQcResult, String problemDescribe, long secondQcResult, long workOrderId, int caseId,
                     RiskQcResultVo riskQcResultVo, int qcType, WorkOrderType workOrderType, String correctRefuseIds,  Integer scene) {
        if (riskQcResultVo == null) {
            return 0;
        }
        int result = handleQc(caseId, workOrderId, HandleResultEnum.done, "结束处理", workOrderType.getType());
        if (result <= 0) {
            return result;
        }
        RiskQcCalculateResult riskQcCalculateResult = risk1v1QcDetailService.getResult(getIds(riskQcResultVo, qcType), scene);
        if (workOrderType == WorkOrderType.qc_wx_1v1_repeat) {
            firstQcResult = riskQcResultVo.getAgree();
        } else {
            firstQcResult = (qcType == QcTypeEnum.BD.getCode()) ? firstQcResult : riskQcCalculateResult.getQcResult();
        }
        long qcId = getQcId(workOrderId);
        riskQcResultBiz.add(firstQcResult, secondQcResult, problemDescribe, workOrderId,
                qcType == QcTypeEnum.BD.getCode() ? getName(workOrderId) : "", qcType, qcId);
        this.updateStatus(riskQcResultVo, qcType);
        this.addLog(riskQcResultVo, firstQcResult, secondQcResult, workOrderId, riskQcCalculateResult, qcType, false, workOrderType, correctRefuseIds);
        //保存录音问题信息
        this.saveQcRecordingProblems(riskQcResultVo, workOrderId);
        List<RiskQcStandardVo> riskQcStandardVos = Optional.ofNullable(riskQcResultVo.getQcResultOption()).orElse(Lists.newArrayList());
        List<String> idList = riskQcStandardVos.stream().map(r -> {
            long id = r.getId();
            List<RiskQcStandardDetailVo> vos = r.getRiskQcStandardSecondVos();
            if (CollectionUtils.isNotEmpty(vos)) {
                List<Long> ids = vos.stream().map(RiskQcStandardDetailVo::getId).collect(Collectors.toList());
                ids.add(id);
                return StringUtils.join(ids, ",");
            }
            return String.valueOf(id);
        }).collect(Collectors.toList());
        riskQcSearchIndexBiz.updateByWorkOrderId(workOrderId, (int) firstQcResult, StringUtils.join(idList, ","), getPropertyIds(riskQcResultVo), (int) secondQcResult);

        // 若科室质检合格需通知线索
        if (workOrderType == WorkOrderType.qc_hospital_dept && firstQcResult == 1) {
            notifyClewHospitalDeptApprove(workOrderId);
        }
        return result;
    }

    private void saveQcRecordingProblems(RiskQcResultVo riskQcResultVo, long workOrderId) {
        List<QcRecordingProblemsVo> qcRecordingProblemsVos = riskQcResultVo.getQcRecordingProblemsVos();
        if (CollectionUtils.isNotEmpty(qcRecordingProblemsVos)) {
            qcRecordingProblemsVos.forEach(v -> v.setWorkOrderId(workOrderId));
            cfRiskRecordingProblemsRecordDao.insertList(qcRecordingProblemsVos);
        }
    }

    private void notifyClewHospitalDeptApprove(long workOrderId) {
        RiskQcSearchIndex searchIndex = riskQcSearchIndexBiz.getByWorkOrderId(workOrderId);
        if (searchIndex == null) {
            log.info("notifyClewHospitalDeptApprove searchIndex null");
            return;
        }
        long deptId = searchIndex.getHospitalDeptId();
        if (deptId <= 0) {
            log.info("notifyClewHospitalDeptApprove deptId <= 0");
            return;
        }
        Response<DepartmentChangeDetailModel> hospitalDeptInfoResponse = clewDelegate.getHospitalDeptInfo((int) deptId);
        DepartmentChangeDetailModel data = hospitalDeptInfoResponse.getData();
        if (data == null) {
            log.info("notifyClewHospitalDeptApprove data null");
            return;
        }
        clewDelegate.notifyDepartmentCheck((int) deptId, data.getMaxChangeId());
    }

    private String getPropertyIds(RiskQcResultVo riskQcResultVo){
        Set<Long> allSecondIdSet = new HashSet<>();
        List<RiskQcStandardVo> qcResultOption = riskQcResultVo.getQcResultOption();
        if (CollectionUtils.isEmpty(qcResultOption)){
            return "";
        }
        qcResultOption.forEach(qc -> {
            List<Long> secondIdList = qc.getRiskQcStandardSecondVos().stream().map(RiskQcStandardDetailVo::getId).collect(Collectors.toList());
            allSecondIdSet.addAll(secondIdList);
        });
        if (CollectionUtils.isEmpty(allSecondIdSet)){
            return "";
        }
        List<RiskQcStandardExt> extList = riskQcStandExtBiz.getByStandardIds(new ArrayList<>(allSecondIdSet));
        if (CollectionUtils.isNotEmpty(extList)){
            return StringUtils.join(extList.stream().distinct().map(RiskQcStandardExt::getFirstPropertyId).collect(Collectors.toList()), ",");
        }
        return "";
    }

    /**
     * 根据复检工单id查询质检工单id
     */
    public long getQcOrderIdByRcOrderId(long rcOrderId){
        long rcId = riskQcDetailService.getQcId(rcOrderId);
        //查询被复检的质检id
        List<RiskQcMaterialsInfo> materials = riskQcMaterialsInfoBiz.getMaterials(rcId, QcMaterialsKeyEnum.ORIGINAL_WORK_ORDER.getKey());
        if (CollectionUtils.isEmpty(materials)) {
            return 0L;
        }
        //查询质检结果
        return Long.parseLong(materials.get(0).getMaterialsValue());
    }

    public RiskQcWorkOrderVo getQcWorkOrderVO(long rcOrderId){
        final long qcOrderId = riskQcDetailService.getQcOrderIdByRcOrderId(rcOrderId);
        Response<WorkOrderVO> getResponse = cfWorkOrderClient.getWorkOrderById(qcOrderId);
        if (getResponse.ok() && getResponse.getData() != null){
            WorkOrderVO vo = getResponse.getData();
            final long operatorId = vo.getOperatorId();
            RiskQcWorkOrderVo workOrderVo = new RiskQcWorkOrderVo();
            workOrderVo.setWorkOrderId(qcOrderId);
            workOrderVo.setOperatorName(operatorId > 0L ? accountService.getName(operatorId) : "");
            workOrderVo.setCreateTime(vo.getCreateTime());
            workOrderVo.setFinishTime(vo.getFinishTime());
            return workOrderVo;
        }
        return null;
    }

    public long getQcId(long workOrderId) {
        Response<List<WorkOrderExt>> response = cfWorkOrderClient.listExtInfos(Lists.newArrayList(workOrderId), OrderExtName.qcId.getName());
        long qcId = 0;
        if (response != null && CollectionUtils.isNotEmpty(response.getData())) {
            WorkOrderExt workOrderExt = response.getData().get(0);
            qcId = Long.parseLong(workOrderExt.getExtValue());
        }
        return qcId;
    }

    public List<Long> getIds(RiskQcResultVo riskQcResultVo, int qcType) {
        if (qcType == QcTypeEnum.MATERIAL.getCode() || qcType == QcTypeEnum.INTERNAL_AUDIT.getCode()) {
            return riskQcMaterialDetailService.getIds(RiskQcResultVo.getList(riskQcResultVo.getMaterialQcResultOption()));
        }
        if (riskQcResultVo == null || CollectionUtils.isEmpty(riskQcResultVo.getQcResultOption())) {
            return Lists.newArrayList();
        }
        List<Long> ids = Lists.newArrayList();
        List<RiskQcStandardVo> riskQcStandardVos = riskQcResultVo.getQcResultOption();
        riskQcStandardVos.forEach(riskQcStandardVo -> {
            List<RiskQcStandardDetailVo> riskQcStandardDetailVos = riskQcStandardVo.getRiskQcStandardSecondVos();
            if (CollectionUtils.isNotEmpty(riskQcStandardDetailVos)) {
                ids.addAll(riskQcStandardDetailVos.stream().map(RiskQcStandardDetailVo::getId).collect(Collectors.toList()));
            }
        });
        return ids;
    }

    private String getName(long workOrderId) {
        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (response == null || response.getData() == null) {
            return "";
        }
        long qcId = response.getData().getQcId();
        RiskQcBaseInfo riskQcBaseInfo = riskQcBaseInfoBiz.getById(qcId);
        return riskQcBaseInfo == null || StringUtils.isBlank(riskQcBaseInfo.getQcUniqueCode()) ? "" : riskQcBaseInfo.getQcUniqueCode();
    }

    public void addLog(RiskQcResultVo riskQcResultVo, long firstQcResult,
                       long secondQcResult, long workOrderId, RiskQcCalculateResult riskQcCalculateResult,
                       int qcType, boolean againQc, WorkOrderType workOrderType, String correctRefuseIds) {
        if (riskQcResultVo == null) {
            return;
        }
        List<RiskQcResultConfig> riskQcResultConfigs = riskQcResultConfigBiz.getAll();
        List<RiskQcResultConfig> firstQcResults = riskQcResultConfigs.stream().filter(t -> t.getId() == firstQcResult).collect(Collectors.toList());
        List<RiskQcResultConfig> secondQcResults = riskQcResultConfigs.stream().filter(t -> t.getId() == secondQcResult).collect(Collectors.toList());
        List<RiskQcStandardVo> riskQcStandardVos = qcType == QcTypeEnum.MATERIAL.getCode() ?
                riskQcMaterialDetailService.getRiskQcStandardVo(RiskQcResultVo.getList(riskQcResultVo.getMaterialQcResultOption())) :
                Optional.ofNullable(riskQcResultVo.getQcResultOption()).orElse(Lists.newArrayList());
        int sequence = 1;
        StringBuffer stringBuffer = new StringBuffer();
        Map<Long, Integer> result = Maps.newHashMap();
        int secondStandardType = 0;
        for (RiskQcStandardVo riskQcStandardVo : riskQcStandardVos) {
            List<RiskQcStandardDetailVo> riskQcStandardDetailVos =
                    Optional.ofNullable(riskQcStandardVo.getRiskQcStandardSecondVos()).orElse(Lists.newArrayList());
            List<Long> ids = riskQcStandardDetailVos.stream().map(RiskQcStandardDetailVo::getId).collect(Collectors.toList());
            List<RiskQcStandardExt> riskQcStandardExts = riskQcStandExtBiz.getByStandardIds(ids);
            for (RiskQcStandardDetailVo riskQcStandardDetailVo : riskQcStandardDetailVos) {
                if (qcType == QcTypeEnum.MATERIAL.getCode()) {
                    if (secondStandardType == 0 || secondStandardType != riskQcStandardVo.getSecondStandardType()) {
                        stringBuffer.append("【");
                        stringBuffer.append(RiskQcSecondStandardTypeEnum.findOfCode(riskQcStandardVo.getSecondStandardType()));
                        stringBuffer.append("】").append("\n");
                        secondStandardType = riskQcStandardVo.getSecondStandardType();
                    }
                    stringBuffer.append(sequence).append(".").append(riskQcStandardVo.getStandardName());
                    stringBuffer.append(":").append(riskQcStandardDetailVo.getStandardName());
                } else {
                    stringBuffer.append(sequence).append(".").append(riskQcStandardDetailVo.getStandardName());
                }
                Pair<String, String> propertyPair = getPropertyPair(riskQcStandardDetailVo);
                stringBuffer.append(String.format(" (%s：%s)", propertyPair.getLeft(), propertyPair.getRight()));
                stringBuffer.append("\n");
                sequence++;
                List<RiskQcStandardExt> riskQcStandardExtList = riskQcStandardExts.stream().filter(t -> t.getQcStandardId() == riskQcStandardDetailVo.getId()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(riskQcStandardExtList)) {
                    if (MapUtils.isEmpty(result) || result.get(riskQcStandardExtList.get(0).getSecondPropertyId()) == null) {
                        result.put(riskQcStandardExtList.get(0).getSecondPropertyId(), 1);
                    } else {
                        int count = result.get(riskQcStandardExtList.get(0).getSecondPropertyId()) + 1;
                        result.put(riskQcStandardExtList.get(0).getSecondPropertyId(), count);
                    }
                }
            }
        }

//        正确的驳回项
        log.info("correctRefuseIds {}", correctRefuseIds);
        String refuseMsg = getRefuseMsg(correctRefuseIds);
        if (StringUtils.isNotBlank(refuseMsg)) {
            stringBuffer.append("\n正确的驳回项：\n");
            stringBuffer.append(refuseMsg);
        }

        //存储正确的驳回项
        try {
            cfRiskQcCorrectRefuseRecordDao.insert(workOrderId, refuseMsg, againQc ? RiskQcOperationTypeEnum.CHANGE_QC_RESULT.getType() : RiskQcOperationTypeEnum.DISPOSE_FINISH_COMMON.getType());
        } catch (Exception e) {
            log.error("存储正确的驳回项失败 workOrderId:{}", workOrderId, e);
        }

        StringBuffer logInfo = new StringBuffer();
        switch (workOrderType) {
            case qc_wx_1v1_repeat:
                workOrderId = getQcOrderIdByRcOrderId(workOrderId);
                logInfo.append("复检结果");
                break;
            case qc_common_repeat:
                //如果是线下复检 处理完成日志不一样
                workOrderId = getQcOrderIdByRcOrderId(workOrderId);
                logInfo.append("复检结果:");
                logInfo.append(firstQcResults.get(0).getQcResult());
                logInfo.append("\n");
                if (StringUtils.isNotBlank(riskQcResultVo.getRemark())) {
                    logInfo.append("复检描述:");
                    logInfo.append(StringUtils.trimToEmpty(riskQcResultVo.getRemark()));
                    logInfo.append("\n");
                }
                if (StringUtils.isNotBlank(riskQcResultVo.getUnqualifiedReason())) {
                    logInfo.append("不合格原因:").append("\n");
                    List<String> reasonList = Stream.of(riskQcResultVo.getUnqualifiedReason().split(",")).collect(Collectors.toList());
                    for (String s : reasonList) {
                        logInfo.append(s).append("\n");
                    }
                }
                if (StringUtils.isNotBlank(riskQcResultVo.getDescribe())) {
                    logInfo.append("复检结果描述:");
                    logInfo.append(riskQcResultVo.getDescribe());
                }
                riskQcLogService.addLog(RiskQcOperationTypeEnum.DISPOSE_FINISH_COMMON, workOrderId, logInfo.toString());
                return;
            default:
                logInfo.append("质检结果: ");
                break;

        }
        if (!CollectionUtils.isEmpty(firstQcResults)) {
            if (workOrderType == WorkOrderType.qc_wx_1v1_repeat) {
                logInfo.append(riskQcResultVo.getAgree() == 9 ? "一致" : "不一致");
            } else {
                logInfo.append(firstQcResults.get(0).getQcResult());
            }
            if (qcType != QcTypeEnum.BD.getCode()) {
                logInfo.append("(").append("关键错误:").append(riskQcCalculateResult.getCriticalError()).append("个;");
                logInfo.append("非关键错误:").append(riskQcCalculateResult.getNonCriticalError()).append("个").append(")");
            }
        }
        if (!CollectionUtils.isEmpty(secondQcResults)) {
            logInfo.append(" ").append(secondQcResults.get(0).getQcResult());
        }
        logInfo.append("\n");
        logInfo.append("\n问题描述:").append("\n").append(stringBuffer);
        if (!StringUtils.isBlank(riskQcResultVo.getVoiceRemark())) {
            logInfo.append("\n");
            logInfo.append("\n").append("录音备注:").append("\n").append(riskQcResultVo.getVoiceRemark());
        }
        if (!StringUtils.isBlank(riskQcResultVo.getPreInfoRemark())) {
            logInfo.append("\n");
            logInfo.append("\n").append("前置信息备注:").append("\n").append(riskQcResultVo.getPreInfoRemark());
        }
        if (!StringUtils.isBlank(riskQcResultVo.getImgWordRemark())) {
            logInfo.append("\n");
            logInfo.append("\n").append("图文信息备注:").append("\n").append(riskQcResultVo.getImgWordRemark());
        }
        if (!StringUtils.isBlank(riskQcResultVo.getAddCreditRemark())) {
            logInfo.append("\n");
            logInfo.append("\n").append("增信信息备注:").append("\n").append(riskQcResultVo.getAddCreditRemark());
        }
        if (!StringUtils.isBlank(riskQcResultVo.getLowIncomeRemark())) {
            logInfo.append("\n");
            logInfo.append("\n").append("低保信息备注:").append("\n").append(riskQcResultVo.getLowIncomeRemark());
        }
        if (!StringUtils.isBlank(riskQcResultVo.getUserWriteRemark())) {
            logInfo.append("\n");
            logInfo.append("\n").append("其他备注:").append("\n")
                    .append(StringUtils.trimToEmpty(riskQcResultVo.getUserWriteRemark()));
        }
        if (StringUtils.isNotBlank(riskQcResultVo.getSuggest())) {
            logInfo.append("\n");
            logInfo.append("\n").append("改进意见及建议:").append("\n")
                    .append(StringUtils.trimToEmpty(riskQcResultVo.getSuggest()));
        }
        if (StringUtils.isNotBlank(riskQcResultVo.getRemark())) {
            logInfo.append("\n");
            logInfo.append("\n").append("备注信息:").append("\n")
                    .append(StringUtils.trimToEmpty(riskQcResultVo.getRemark()));
        }
        logInfo.append("\n");
//        logInfo.append("\n问题对应的属性:").append("\n");
//        List<RiskQcStandardProperty> riskQcStandardProperties = riskQcStandardPropertyBiz.getAll();
//        result.forEach((k, v) -> {
//            List<RiskQcStandardProperty> secondRiskQcStandardPropertyList =
//                    riskQcStandardProperties.stream().filter(t -> t.getId() == k).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(secondRiskQcStandardPropertyList)) {
//                List<RiskQcStandardProperty> firstProperty = riskQcStandardProperties.stream().filter(t -> t.getId() ==
//                        Optional.ofNullable(secondRiskQcStandardPropertyList.get(0)).orElseGet(RiskQcStandardProperty::new).getParentId()).collect(Collectors.toList());
//                String first = CollectionUtils.isEmpty(firstProperty) ? "" : Optional.ofNullable(firstProperty.get(0)).orElseGet(RiskQcStandardProperty::new).getProperty();
//                logInfo.append(first).append(":")
//                        .append(Optional.ofNullable(secondRiskQcStandardPropertyList.get(0)).orElseGet(RiskQcStandardProperty::new).getProperty()).append(":").append(v).append("\n");
//            }
//        });
        riskQcLogService.addLog(againQc ? RiskQcOperationTypeEnum.CHANGE_QC_RESULT : RiskQcOperationTypeEnum.DISPOSE_FINISH_COMMON, workOrderId, logInfo.toString());
    }

    private Pair<String, String> getPropertyPair(RiskQcStandardDetailVo riskQcStandardDetailVo) {
        String property = riskQcStandardDetailVo.getProperty();
        if (StringUtils.isEmpty(property)) {
            return Pair.of("", "");
        }
        String[] split = property.split("-");
        if (ArrayUtils.getLength(split) != 2){
            return Pair.of("", "");
        }
        return Pair.of(split[0], split[1]);
    }

    private String getRefuseMsg(String correctRefuseIdStr) {
        if (StringUtils.isBlank(correctRefuseIdStr)) {
            return "";
        }
        String[] arr = StringUtils.split(correctRefuseIdStr, ",");
        if (arr == null || arr.length == 0) {
            return "";
        }
        List<Integer> correctRefuseIds = Arrays.stream(arr)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        OperationResult<RefuseInfoVO> refuseResp = caiLiaoRefuseReasonFeignClient.selectRefuseInfoByReasonIds(correctRefuseIds);
        if (refuseResp.isFail()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        RefuseInfoVO refuseInfo = refuseResp.getData();
        List<RefuseDataTypeDetailVO> dataTypeList = refuseInfo.getDataTypeList();
        for (RefuseDataTypeDetailVO dataType : dataTypeList) {
            int sequence = 1;
            sb.append(String.format("【%s】\n", dataType.getContent()));
            List<RefuseItemVO> itemList = dataType.getItemList();
            for (RefuseItemVO i : itemList) {
                sb.append(String.format("%d. %s\n", sequence, i.getContent()));
                sequence ++;
            }
        }
        return sb.toString();
    }

    public void updateStatus(RiskQcResultVo riskQcResultVo, int qcType) {
        if (qcType == QcTypeEnum.MATERIAL.getCode() || qcType == QcTypeEnum.INTERNAL_AUDIT.getCode()) {
            riskQcMaterialDetailService.updateStatus(RiskQcResultVo.getList(riskQcResultVo.getMaterialQcResultOption()));
        }
        if (riskQcResultVo == null) {
            return;
        }
        List<RiskQcStandardVo> riskQcStandardVos = Optional.ofNullable(riskQcResultVo.getQcResultOption()).orElse(Lists.newArrayList());
        List<Long> ids = Lists.newArrayList();
        riskQcStandardVos.forEach(riskQcStandardVo -> {
            List<RiskQcStandardDetailVo> riskQcStandardDetailVos =
                    Optional.ofNullable(riskQcStandardVo.getRiskQcStandardSecondVos()).orElse(Lists.newArrayList());
            ids.addAll(riskQcStandardDetailVos.stream().map(RiskQcStandardDetailVo::getId).collect(Collectors.toList()));
        });
        ids.forEach(id -> {
            riskQcStandBiz.updateSecondaryUseStatus(RiskQcStandardUseEnum.IS_USE.getCode(), id);
        });
    }


    public int handleQc(int caseId, long workOrderId, HandleResultEnum handleResultEnum,
                        String orderExtName, int orderType) {
        Response workOrderRes = cfQcWorkOrderClient.handleQc(this.buildParam(caseId, workOrderId, handleResultEnum, orderExtName, orderType));
        if (workOrderRes.ok()) {
            return 1;
        }
        AtomicInteger result = new AtomicInteger();
        if (workOrderRes == null || ErrorCode.SUCCESS.getCode() != workOrderRes.getCode()) {
            try {
                RetryerBuilder.<Boolean>newBuilder()
                        .withWaitStrategy(WaitStrategies.fixedWait(2, TimeUnit.SECONDS))
                        .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                        .retryIfResult(BooleanUtils::isFalse)
                        .build()
                        .call(() -> {
                            Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
                            if (workOrderVOResponse.ok() && Objects.nonNull(workOrderVOResponse)
                                    && workOrderVOResponse.getData().getHandleResult() == handleResultEnum.getType()) {
                                result.set(1);
                                return true;
                            }
                            return false;
                        });
            } catch (Exception e) {
                log.error("handleQc 重试失败 workOrderId:" + workOrderId, e);
            }
        }
        return result.get();
    }


    private QcHandleOrderParam buildParam(int caseId, long workOrderId, HandleResultEnum handleResultEnum,
                                          String orderExtName, int workOrderType) {
        QcHandleOrderParam param = new QcHandleOrderParam();
        param.setCaseId(caseId);
        param.setWorkOrderId(workOrderId);
        param.setHandleResult(handleResultEnum.getType());
        param.setOperComment(orderExtName);
        param.setUserId(ContextUtil.getAdminLongUserId());
        param.setOrderType(workOrderType);
        return param;
    }

    public RiskQcResultVo getQcResult(long workOrderId, int useScene, int qcType, int materialWorkOrderId) {
        RiskQcResult riskQcResult = riskQcResultBiz.getByWorkOrderId(workOrderId);
        Date finishTime = null;
        if (useScene == RiskQcStandardUseSceneEnum.CAI_SHEN.getCode()
                || useScene == RiskQcStandardUseSceneEnum.CAI_SHEN_ZHU_DONG.getCode()) {
            Response<WorkOrderVO> workOrderVoResponse = cfWorkOrderClient.getWorkOrderById(materialWorkOrderId);
            if (workOrderVoResponse.ok() && Objects.nonNull(workOrderVoResponse.getData())) {
                WorkOrderVO workOrderVO = workOrderVoResponse.getData();
                finishTime = workOrderVO.getFinishTime();
            }
        }
        if (riskQcResult == null || StringUtils.isBlank(riskQcResult.getProblemDescribe())) {
            if (qcType == QcTypeEnum.MATERIAL.getCode()) {
                RiskQcResultVo riskQcResultVo = new RiskQcResultVo();
                riskQcResultVo.setAuditFinishTime(finishTime);
                return riskQcResultVo;
            } else {
                return null;
            }
        }
        RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<RiskQcResultVo>() {
        });
        if (qcType == QcTypeEnum.MATERIAL.getCode()) {
            riskQcMaterialDetailService.filterInfo(riskQcResultVo);
        }
        mergeInfo(riskQcResultVo, useScene, qcType);
        riskQcResultVo.setFirstResultId(riskQcResult.getFirstQcResultId());
        riskQcResultVo.setSecondResultId(riskQcResult.getSecondQcResultId());
        riskQcResultVo.setAuditFinishTime(finishTime);
        return riskQcResultVo;
    }

    /**
     * 返回全部标准配置加用户选中配置
     */
    public void mergeInfo(RiskQcResultVo riskQcResultVo, int useScene, int qcType) {
       /* if (CollectionUtils.isEmpty(riskQcResultVo.getQcResultOption()) && qcType != QcTypeEnum.MATERIAL.getCode()) {
            return;
        }*/
        if (MapUtils.isEmpty(riskQcResultVo.getMaterialQcResultOption()) && qcType == QcTypeEnum.MATERIAL.getCode()) {
            return;
        }
        int standardType = qcType == QcTypeEnum.MATERIAL.getCode() ?
                RiskQcStandardTypeEnum.MATERIAL.getCode() :
                RiskQcStandardTypeEnum.RECORDING.getCode();
        if (useScene == RiskQcStandardUseSceneEnum.HOSPITAL_DEPT.getCode()) {
            standardType = RiskQcStandardTypeEnum.HOSPITAL_DEPT.getCode();
        }

        List<RiskQcStandardVo> riskQcStandardVos =
                riskQcStandardService.getAllStandard(RiskQcStandardUseEnum.IS_USE.getCode(), standardType, 0, useScene, null);
        List<RiskQcStandardVo> userChecks = qcType == QcTypeEnum.MATERIAL.getCode() ?
                riskQcMaterialDetailService.getRiskQcStandardVo(RiskQcResultVo.getList(riskQcResultVo.getMaterialQcResultOption()))
                : riskQcResultVo.getQcResultOption();
        if (CollectionUtils.isEmpty(userChecks)){
            riskQcResultVo.setQcResultOption(riskQcStandardVos);
            return;
        }
        for (RiskQcStandardVo userCheckStandardVo : userChecks) {
            List<RiskQcStandardVo> allLabel = riskQcStandardVos.stream().filter(t -> t.getId() == userCheckStandardVo.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(allLabel)) {
                RiskQcStandardVo allStandard = allLabel.get(0);
                List<RiskQcStandardDetailVo> allDetail = Optional.ofNullable(allStandard.getRiskQcStandardSecondVos()).orElse(Lists.newArrayList());
                List<RiskQcStandardDetailVo> userDetail = Optional.ofNullable(userCheckStandardVo.getRiskQcStandardSecondVos()).orElse(Lists.newArrayList());
                for (RiskQcStandardDetailVo riskQcStandardDetailVo : userDetail) {
                    List<RiskQcStandardDetailVo> riskQcStandardDetailVos = allDetail.stream().filter(t -> t.getId() == riskQcStandardDetailVo.getId()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(riskQcStandardDetailVos)) {
                        Optional.ofNullable(riskQcStandardDetailVos.get(0)).orElseGet(RiskQcStandardDetailVo::new).setCheck(true);
                    } else {
                        riskQcStandardDetailVo.setCheck(true);
                        allDetail.add(riskQcStandardDetailVo);
                    }
                }
            } else {
                List<RiskQcStandardDetailVo> riskQcStandardDetailVos = Optional.ofNullable(userCheckStandardVo.getRiskQcStandardSecondVos()).orElse(Lists.newArrayList());
                riskQcStandardDetailVos.forEach(t -> t.setCheck(true));
                riskQcStandardVos.add(userCheckStandardVo);
            }
        }
        if (qcType == QcTypeEnum.MATERIAL.getCode()) {
            riskQcResultVo.setMaterialQcResultOption(riskQcStandardService.buildVo(riskQcStandardVos));
        } else {
            riskQcResultVo.setQcResultOption(riskQcStandardVos);
        }

    }


    public List<RiskQcLog> getLog(long workOrderId) {
        return riskQcLogService.getLogs(workOrderId, RiskQcOperationTypeEnum.DETAIL_SEARCH_INFO);
    }

    public int addCheckedVideo(long workOrderId, String checkedIds, boolean againQc) {
        if (againQc) {
            return riskQcCheckedVideoInfoBiz.updateByWorkOrderId(checkedIds, workOrderId);
        }
        RiskQcCheckedVideoInfo riskQcCheckedVideoInfo = riskQcCheckedVideoInfoBiz.getByWorkOrderId(workOrderId);
        if (riskQcCheckedVideoInfo != null) {
            return 0;
        }
        return riskQcCheckedVideoInfoBiz.addInfo(workOrderId, StringUtils.trimToEmpty(checkedIds));
    }

    public int addCheckedVideoV2(RiskQcVideoInfoModel param, boolean againQc) {
        RiskQcCheckedVideoInfo d = new RiskQcCheckedVideoInfo();
        d.setId(param.getId());
        d.setWorkOrderId(param.getWorkOrderId());
        String checkId = CollectionUtils.isEmpty(param.getCheckedIds()) ? "" : StringUtils.join(param.getCheckedIds(), ",");
        d.setCheckedId(checkId);

        RiskQcCheckedVideoInfo.ExtInfo ext = new RiskQcCheckedVideoInfo.ExtInfo();
        ext.setAsrCorrectIds(param.getAsrCorrectIds());
        ext.setAsrIncorrectIds(param.getAsrIncorrectIds());
        d.setExtInfo(JSON.toJSONString(ext));

        if (againQc) {
            return riskQcCheckedVideoInfoBiz.update(d);
        }
        RiskQcCheckedVideoInfo riskQcCheckedVideoInfo = riskQcCheckedVideoInfoBiz.getByWorkOrderId(param.getWorkOrderId());
        if (riskQcCheckedVideoInfo != null) {
            return 0;
        }
        return riskQcCheckedVideoInfoBiz.add(d);
    }

    public Response<RiskQcResultVo> judgeRc(RiskQcResultVo rcVo, long workOrderId) {
        long rcId = riskQcDetailService.getQcId(workOrderId);
        //查询被复检的质检id
        List<RiskQcMaterialsInfo> materials = riskQcMaterialsInfoBiz.getMaterials(rcId, QcMaterialsKeyEnum.ORIGINAL_WORK_ORDER.getKey());
        if (CollectionUtils.isEmpty(materials)) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_WORK_ORDER_LIST_NOT_EXIST);
        }
        //查询质检结果
        long qcId = Long.parseLong(materials.get(0).getMaterialsValue());
        RiskQcResult qc = riskQcResultBiz.getByWorkOrderId(qcId);
        if (qc == null){
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_WORK_ORDER_RESULT_NOT_EXIST);
        }
        RiskQcResultVo qcVo = JSON.parseObject(qc.getProblemDescribe(), new TypeReference<RiskQcResultVo>() {
        });
        //复检和质检对比
        qcVo.judgeRc1v1(rcVo);
        return NewResponseUtil.makeSuccess(rcVo);
    }

    public Response<List<QcRecordingProblemsVo>> getRecordingProblems(long workOrderId) {
        return NewResponseUtil.makeSuccess(cfRiskRecordingProblemsRecordDao.getListByWorkOrderId(workOrderId));
    }

    public List<QcRecordingProblemsVo> getRecordingProblemsByWorkOrderIds(List<Long> workOrderIds) {
        return cfRiskRecordingProblemsRecordDao.getListByWorkOrderIds(workOrderIds);
    }
}
