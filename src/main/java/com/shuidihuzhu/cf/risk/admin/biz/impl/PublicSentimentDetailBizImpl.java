package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.risk.admin.biz.*;
import com.shuidihuzhu.cf.risk.admin.model.*;
import com.shuidihuzhu.cf.risk.admin.model.enums.*;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskPublicSentimentParticularsVo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.risk.admin.service.ReportService;
import com.shuidihuzhu.client.cf.admin.model.AdminMarkReport;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/2/23
 */
@Service
public class PublicSentimentDetailBizImpl implements PublicSentimentDetailBiz {
    @Autowired
    private RiskPublicSentimentInfoBiz riskPublicSentimentInfoBiz;
    @Autowired
    private RiskPublicSentimentDetailBiz riskPublicSentimentDetailBiz;
    @Autowired
    private RiskPsProgressBiz riskPsProgressBiz;
    @Autowired
    private RiskPsHandleRecordBiz riskPsHandleRecordBiz;
    @Autowired
    private RiskPublicSentimentCorrespondBiz riskPublicSentimentCorrespondBiz;
    @Autowired
    private RiskPsInfoTypeBiz riskPsInfoTypeBiz;
    @Autowired
    private ReportService reportService;
    @Autowired
    private UploadBiz uploadBiz;
    @Autowired
    private MaskUtil maskUtil;


    @Override
    public RiskPublicSentimentParticularsVo getDetail(long psId) {
        RiskPublicSentimentInfo riskPublicSentimentInfo = Optional.ofNullable(riskPublicSentimentInfoBiz.getInfoById(psId)).orElse(new RiskPublicSentimentInfo());
        AdminMarkReport report = null;
        if (riskPublicSentimentInfo.getReportId() != 0 && riskPublicSentimentInfo.getCaseId() != 0) {
            report = reportService.getReport(riskPublicSentimentInfo.getCaseId(), riskPublicSentimentInfo.getReportId());
        }
        RiskPublicSentimentDetail riskPublicSentimentDetail = Optional.ofNullable(riskPublicSentimentDetailBiz.getByPublicSentimentId(psId)).orElse(new RiskPublicSentimentDetail());
        RiskSourceCorrespond riskSourceCorrespond = Optional.ofNullable(riskPublicSentimentCorrespondBiz.getByInfoFeedBack(riskPublicSentimentInfo.getInfoFeedBack())).orElse(new RiskSourceCorrespond());
        String infoFeedBack = riskPublicSentimentInfo.getInfoFeedBack() == 0 ? riskPublicSentimentInfo.getInfoFeedBackOther() : riskSourceCorrespond.getInfoFeedBackContent();
        String infoType = riskPsInfoTypeBiz.getByPath(riskPublicSentimentInfo.getPublicSentimentInfoType());
        List<RiskPsHandleRecord> riskPsHandleRecords = riskPsHandleRecordBiz.listByPsId(psId);
        riskPsHandleRecords =
                riskPsHandleRecords.stream().sorted(Comparator.comparing(RiskPsHandleRecord::getId).reversed()).collect(Collectors.toList());
        RiskPsHandleRecord riskPsHandleRecord = CollectionUtils.isEmpty(riskPsHandleRecords) ? new RiskPsHandleRecord() : riskPsHandleRecords.get(0);
        return this.buildVo(riskPublicSentimentInfo, riskPublicSentimentDetail,
                 infoFeedBack, infoType, riskPsHandleRecord, CollectionUtils.size(riskPsHandleRecords), report);
    }

    private RiskPublicSentimentParticularsVo buildVo(RiskPublicSentimentInfo riskPublicSentimentInfo,
                                                     RiskPublicSentimentDetail riskPublicSentimentDetail,
                                                     String infoFeedBack,
                                                     String infoType, RiskPsHandleRecord riskPsHandleRecord,
                                                     int disposeCount, AdminMarkReport report) {

        RiskPublicSentimentParticularsVo riskPublicSentimentParticularsVo = new RiskPublicSentimentParticularsVo();
        this.buildInfo(riskPublicSentimentInfo, riskPublicSentimentParticularsVo, infoFeedBack);
        Map<String, Object> supplementInfo = JSONObject.parseObject(riskPublicSentimentDetail.getSupplementInfo());
        String mobile = null;
        if(supplementInfo.get("mobile") != null){
            mobile = supplementInfo.get("mobile").toString();
        }
        if(MapUtils.isNotEmpty(supplementInfo) && StringUtils.isNotBlank(mobile)){
            supplementInfo.put("mobileMask",maskUtil.buildByDecryptPhone(mobile));
            supplementInfo.put("mobile", null);
        }
        riskPublicSentimentParticularsVo.setSupplementInfo(supplementInfo);
        riskPublicSentimentParticularsVo.setDisposeName(riskPublicSentimentInfo.getLastOperator());
        if (riskPublicSentimentInfo.getStatus() != 0) {
            riskPublicSentimentParticularsVo.setDisposeTime(riskPsHandleRecord.getCreateTime());
        }else {
            RiskPsProgress psProgresses = riskPsProgressBiz.getLastProgress(riskPublicSentimentInfo.getId());
            riskPublicSentimentParticularsVo.setDisposeTime(psProgresses.getCreateTime());
        }
        riskPublicSentimentParticularsVo.setInfoType(infoType);
        riskPublicSentimentParticularsVo.setReplySituation(PsFeedBackEnum.findOfCode(riskPsHandleRecord.getReplyType()));
        riskPublicSentimentParticularsVo.setReplyTime(riskPsHandleRecord.getReplyTime());
        riskPublicSentimentParticularsVo.setReplyContent(JSON.parse(riskPsHandleRecord.getReplyContent()));
        riskPublicSentimentParticularsVo.setSatisfaction(riskPsHandleRecord.getSatisfaction() == PsSatisfactionEnum.OTHER.getCode() ? riskPsHandleRecord.getSatisfactionExt() : PsSatisfactionEnum.findOfCode(riskPsHandleRecord.getSatisfaction()));
        riskPublicSentimentParticularsVo.setDisposeCount(disposeCount);
        riskPublicSentimentParticularsVo.setTitle(riskPublicSentimentDetail.getTitle());
        riskPublicSentimentParticularsVo.setUrl(riskPublicSentimentDetail.getUrl());
        riskPublicSentimentParticularsVo.setVideoUrl(riskPublicSentimentDetail.getVideoUrl());
        riskPublicSentimentParticularsVo.setImgUrl(riskPublicSentimentDetail.getImgUrl());
        riskPublicSentimentParticularsVo.setContent(riskPublicSentimentDetail.getContent());
        riskPublicSentimentParticularsVo.setDepartment(riskPsHandleRecord.getDepartment());
        riskPublicSentimentParticularsVo.setImages(uploadBiz.getTemporalUrlByUrl(riskPsHandleRecord.getImages()));
        riskPublicSentimentParticularsVo.setOtherExt(riskPsHandleRecord.getOtherExt());
        if (report != null) {
            riskPublicSentimentParticularsVo.setWorkOrderId(report.getWorkOrderId());
            riskPublicSentimentParticularsVo.setReportId(report.getReportId());
            if (report.isNew()) {
                HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(report.getHandleStatus());
                riskPublicSentimentParticularsVo.setSchedule(handleResultEnum == null ? "" : handleResultEnum.getShowMsg());
            } else {
                riskPublicSentimentParticularsVo.setSchedule(OldReportHandleStatusEnum.findOfCode(report.getHandleStatus()));
            }
        }
        return riskPublicSentimentParticularsVo;
    }

    public void buildInfo(RiskPublicSentimentInfo riskPublicSentimentInfo, RiskPublicSentimentParticularsVo riskPublicSentimentParticularsVo, String infoFeedBack) {
        int infoSource = riskPublicSentimentInfo.getInfoSource();
        riskPublicSentimentParticularsVo.setInfoSource(
                infoSource == PublicSentimentInfoSourceEnum.OTHER.getCode() ?
                        PublicSentimentInfoSourceEnum.OTHER.getDescription() + ": " + riskPublicSentimentInfo.getInfoSourceOther()
                        : PublicSentimentInfoSourceEnum.findOfCode(infoSource));
        riskPublicSentimentParticularsVo.setPublishTime(riskPublicSentimentInfo.getPublishTime());
        riskPublicSentimentParticularsVo.setNickName(riskPublicSentimentInfo.getNickName());
        riskPublicSentimentParticularsVo.setUserType(PublicSentimentUserTypeEnum.findOfCode(riskPublicSentimentInfo.getNickNameType()));
        riskPublicSentimentParticularsVo.setInfoFeedBack(riskPublicSentimentInfo.getInfoFeedBack() == 0 ?
                PublicSentimentInfoSourceEnum.OTHER.getDescription() + ": " + riskPublicSentimentInfo.getInfoFeedBackOther() :
                infoFeedBack);
        riskPublicSentimentParticularsVo.setInfoClassify(riskPublicSentimentInfo.getInfoClassify());
        if (riskPublicSentimentInfo.getInfoClassify() == PublicSentimentInfoClassifyEnum.OTHER.getCode()) {
            riskPublicSentimentParticularsVo.setInfoClassifyOther(riskPublicSentimentInfo.getInfoClassifyOther());
        }
        riskPublicSentimentParticularsVo.setCaseId(riskPublicSentimentInfo.getCaseId());
        if (riskPublicSentimentInfo.getSolution() == null || StringUtils.isBlank(riskPublicSentimentInfo.getSolution())) {
            riskPublicSentimentParticularsVo.setSolution("");
            riskPublicSentimentParticularsVo.setSolutionOther(StringUtils.trimToEmpty(riskPublicSentimentInfo.getSolutionOther()));
        } else {
            List<String> solutions = Arrays.asList(riskPublicSentimentInfo.getSolution().split(","));
            StringBuffer stringBuffer = new StringBuffer();
            solutions.forEach(solution -> {
                stringBuffer.append(PsSolutionEnum.findOfCode(Integer.parseInt(solution))).append("/");
            });
            riskPublicSentimentParticularsVo.setSolution(riskPublicSentimentInfo.getSolution() == null ? "" : stringBuffer.toString().trim());
            riskPublicSentimentParticularsVo.setSolutionOther(riskPublicSentimentInfo.getSolutionOther());
        }
        riskPublicSentimentParticularsVo.setDisposeStatus(riskPublicSentimentInfo.getStatus());
    }
}
