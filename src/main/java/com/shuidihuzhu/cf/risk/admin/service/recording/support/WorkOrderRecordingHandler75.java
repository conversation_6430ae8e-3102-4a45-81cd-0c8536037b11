package com.shuidihuzhu.cf.risk.admin.service.recording.support;

import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * @author: wanghui
 * @create: 2022/3/28 下午4:43
 * qc_high_risk_quality_inspection 高风险质检工单 录音获取 处理 存储
 */
@Component
@Slf4j
public class WorkOrderRecordingHandler75 extends WorkOrderRecordingHandler45 {

    @Override
    public AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum() {
        return AiAsrDelegate.HandleTypeEnum.HANDLE_INTERNAL_AUDIT_HIGH_RISK_QC;
    }

    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.qc_high_risk_quality_inspection;
    }
}
