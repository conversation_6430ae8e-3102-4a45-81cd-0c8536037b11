package com.shuidihuzhu.cf.risk.admin.controller;

import com.shuidihuzhu.cf.risk.admin.model.RiskPsProgress;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPsProgressBiz;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/cf-risk-admin/progress", method = {RequestMethod.POST})
public class PsProgressController {

    @Autowired
    private RiskPsProgressBiz progressService;

    @ApiOperation("舆情发酵跟进")
    @RequestMapping(path = "/add-progress")
    public Response addProgress(@RequestBody RiskPsProgress riskPsProgress){
        long adminUserId = ContextUtil.getAdminLongUserId();
        return NewResponseUtil.makeSuccess(progressService.add(riskPsProgress, adminUserId));
    }

    @ApiOperation("舆情发展记录")
    @RequestMapping(path = "/progress-list")
    public Response progressList(long psId){
        return NewResponseUtil.makeSuccess(progressService.listByPsId(psId));
    }


}
