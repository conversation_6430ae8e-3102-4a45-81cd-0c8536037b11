package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskPsHandleRecordDao {

    int save(RiskPsHandleRecord riskPsHandleRecord);


    List<RiskPsHandleRecord> listByPsId(@Param("psId")long psId);


    List<RiskPsHandleRecord> listByPsIdOfPage(@Param("psId")long psId, PageRequest pageRequest);


    RiskPsHandleRecord  getLastByPsId(@Param("psId")long psId);


    RiskPsHandleRecord  getByPsIdAndStatus(@Param("psId")long psId);


    int  countRecordSumByPsId(@Param("psId")long psId);
}
