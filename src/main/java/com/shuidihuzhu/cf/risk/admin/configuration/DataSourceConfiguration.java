package com.shuidihuzhu.cf.risk.admin.configuration;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.shuidihuzhu.infra.starter.cos.configuration.CosClientWrapper;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
public class DataSourceConfiguration {


//    @Bean(RiskAdminDS.CF_RISK_DATASOURCE)
//    @ConfigurationProperties("spring.datasource.druid.shuidi-cf-risk")
//    public DataSource riskDatasource() {
//        return DruidDataSourceBuilder.create().build();
//    }
//
//    @Bean(RiskAdminDS.CF_RISK_SLAVE_DATASOURCE)
//    @ConfigurationProperties("spring.datasource.druid.shuidi-cf-risk-slave")
//    public DataSource riskSlaveDatasource() {
//        return DruidDataSourceBuilder.create().build();
//    }
//
//    @Bean(DS.CF_SLAVE)
//    @ConfigurationProperties("spring.datasource.druid.crowdfunding-slave")
//    public DataSource crowdfundingSlaveDataSource() {
//        return DruidDataSourceBuilder.create().build();
//    }




    @Bean(RiskAdminDS.CF_RISK_DATASOURCE)
    @ConfigurationProperties("spring.datasource.druid.cf-risk-admin-risk.cf-risk-admin")
    public DataSource riskDatasource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(RiskAdminDS.CF_RISK_SLAVE_DATASOURCE)
    @ConfigurationProperties("spring.datasource.druid.cf-risk-admin-risk-slave.cf-risk-admin")
    public DataSource riskSlaveDatasource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("cos-cf-risk")
    @ConfigurationProperties("rms.cos.cf-risk.cf-risk-admin")
    public CosClientWrapper riskCosClientWrapper() {
        return new CosClientWrapper();
    }

    @Bean("cos-cf-growthtool-api")
    @ConfigurationProperties("rms.cos.cf-growthtool-api.cf-risk-admin")
    public CosClientWrapper growthtoolCosClientWrapper() {
        return new CosClientWrapper();
    }

}
