package com.shuidihuzhu.cf.risk.admin.controller.inner.qc;

import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.model.QcCreateWorkOrderModel;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcStandardService;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcOrderHospitalDeptCreateService;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcOrderZhuDongCreateService;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTopicCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.UUID;

@RestController
@Slf4j
@RequestMapping("/innerapi/cf-risk-admin/qc/standard")
public class QcStandardInnerController {

    @Autowired
    private RiskQcStandardService riskQcStandService;
    @Autowired
    private Producer producer;

    @ApiOperation("批量添加场景")
    @PostMapping("multi-add-scene")
    public Response<Integer> multiAddScene(String standardIds, String useScene) {
        return riskQcStandService.multiAddScene(standardIds, useScene);
    }

    @ApiOperation("生成质检工单的录音转义数据")
    @PostMapping("gen-recording-qc-work-order")
    public Response<String> genRecordingQcWorkOrder(Integer orderType,
                                                    Long workOrderId,
                                                    Long sourceWorkOrderId) {
        MessageResult send = producer.send(Message.of(CfRiskMQTopicCons.CF_RISK_TOPIC, QcConst.QC_WORK_ORDER_CREATE_4_RECODING_MSG, UUID.randomUUID().toString(), new QcCreateWorkOrderModel(orderType, workOrderId, sourceWorkOrderId)));
        return NewResponseUtil.makeSuccess(send.getMsgId());
    }
}
