package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskPsOperationLogBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPsProgressBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPsProgressDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsOperationLog;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsProgress;
import com.shuidihuzhu.cf.risk.admin.model.enums.PsProgressActionEnum;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class RiskPsProgressBizImpl implements RiskPsProgressBiz {
    @Autowired
    private RiskPsProgressDao riskPsProgressDao;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private RiskPsOperationLogBiz operationLogService;

    @Override
    public int add(RiskPsProgress riskPsProgress, long adminUserId) {
        String operator = "";
        if (adminUserId > 0) {
            operator = seaAccountService.getOrganization(adminUserId) + seaAccountService.getName(adminUserId);
        }
        riskPsProgress.setAction(PsProgressActionEnum.FOLLOW.getCode());
        riskPsProgress.setOperator(operator);
        operationLogService.add(new RiskPsOperationLog(riskPsProgress.getPsId(), operator, "舆情发酵跟进"));
        return riskPsProgressDao.save(riskPsProgress);
    }

    @Override
    public List<RiskPsProgress> listByPsId(long psId) {
        return riskPsProgressDao.listByPsId(psId);
    }

    @Override
    public RiskPsProgress getLastProgress(long psIds) {
        if (psIds < 0){
            return null;
        }
        return riskPsProgressDao.getLastProgress(psIds);
    }

    @Override
    public List<RiskPsProgress> getByPsId(long psId) {
        if (psId < 0){
            return null;
        }
        return riskPsProgressDao.getByPsId(psId);
    }
}
