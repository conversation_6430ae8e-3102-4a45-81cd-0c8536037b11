package com.shuidihuzhu.cf.risk.admin.configuration;

import brave.Tracing;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2019-06-05 10:20
 */
@Configuration
@Slf4j
public class ThreadPoolConfiguration {

    private static final int WAIT_TERMINAL_SECOND = 120;
    private static final int KEEP_ALIVE_SECONDS = 1800;

    /**
     * async task thread pool config
     */
    private static final int TASK_CORE_POOL_SIZE = 5;
    public static final int TASK_MAX_POOL_SIZE = 100;
    public static final int TASK_QUEUE_CAPACITY = 1000;

    @Autowired
    private Tracing tracing;

    /**
     * localcache thread pool config
     */
    @Bean(value = "asyncTaskThreadPool",destroyMethod = "shutdown")
    public ThreadPoolTaskExecutor asyncTaskThreadPoolTaskExecutor(){
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(TASK_CORE_POOL_SIZE);
        threadPoolTaskExecutor.setMaxPoolSize(TASK_MAX_POOL_SIZE);
        threadPoolTaskExecutor.setQueueCapacity(TASK_QUEUE_CAPACITY);
        threadPoolTaskExecutor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        //允许核心线程空闲时退出
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("asyncTaskThreadPool reject executing");
                super.rejectedExecution(r, e);
            }
        });
        threadPoolTaskExecutor.setThreadNamePrefix("localcacheExecutor-");
        //设置优雅关闭
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(WAIT_TERMINAL_SECOND);
        return threadPoolTaskExecutor;
    }

    /**
     * localcache thread pool config
     */
    @Bean("asyncSaveSnapshot")
    public Executor asyncSaveSnapshot(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("asyncSaveSnapshot" + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(2, 10, 600,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(3000), threadFactory, new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("The asyncSaveSnapshot is rejectedExecution");
                super.rejectedExecution(r, e);
            }
        });
        poolExecutor.allowCoreThreadTimeOut(true);
        return tracing.currentTraceContext().executorService(poolExecutor);
    }



}
