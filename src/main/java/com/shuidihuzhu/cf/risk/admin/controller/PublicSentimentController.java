package com.shuidihuzhu.cf.risk.admin.controller;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.biz.*;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord;
import com.shuidihuzhu.cf.risk.admin.model.enums.PsFeedBackEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.PsSolutionEnum;
import com.shuidihuzhu.cf.risk.admin.model.vo.CaseReportVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.EnumVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskPublicSentimentDetailVo;
import com.shuidihuzhu.cf.risk.admin.service.CaseInfoService;
import com.shuidihuzhu.cf.risk.admin.service.ReportService;
import com.shuidihuzhu.client.auth.saas.annotation.NoLoginRequired;
import com.shuidihuzhu.client.cf.admin.model.AdminMarkReport;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/2/19
 */
@RestController
@Slf4j
@RequestMapping(value = "/api/cf-risk-admin/public-sentiment", method = {RequestMethod.POST}, produces = "application/json;charset=UTF-8")
public class PublicSentimentController {


    @Autowired
    private RiskPsHandleRecordBiz handleRecordService;
    @Autowired
    private RiskPsInfoTypeBiz riskPsInfoTypeBiz;
    @Autowired
    private RiskPublicSentimentInfoBiz riskPublicSentimentInfoBiz;
    @Autowired
    private RiskPublicSentimentDetailBiz riskPublicSentimentDetailBiz;
    @Autowired
    private RiskPublicSentimentCorrespondBiz riskPublicSentimentCorrespondBiz;
    @Autowired
    private UploadBiz uploadBiz;
    @Autowired
    private PublicSentimentDetailBiz publicSentimentDetailBiz;
    @Autowired
    private CaseInfoService caseInfoService;
    @Autowired
    private RiskPsOperationLogBiz operationLogService;
    @Autowired
    private ReportService reportService;


    @ApiOperation("信息录入或信息编辑")
    @RequestMapping(path = "/add-or-update")
    public Response addOrUpdate(@RequestParam(required = false) String basicInformationJson, @RequestParam(required = false) String detailInformationJson, int status, @RequestParam(required = false) Long id) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (status == 0) {
            riskPublicSentimentInfoBiz.add(basicInformationJson, detailInformationJson, adminUserId);
        } else {
            riskPublicSentimentInfoBiz.updateById(basicInformationJson, id, detailInformationJson, adminUserId);
        }
        return NewResponseUtil.makeSuccess(null);
    }



    @ApiOperation("返回编辑信息")
    @RequestMapping(path = "/get-edit-info")
    public Response getInfo(Long id) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("basicInformation", riskPublicSentimentInfoBiz.getInfoById(id));
        result.put("detailInformation", RiskPublicSentimentDetailVo.buildVo(riskPublicSentimentDetailBiz.getByPublicSentimentId(id)));
        return NewResponseUtil.makeSuccess(result);
    }

    @ApiOperation("舆情列表")
    @RequestMapping(path = "/get-list")
    public Response getList(@RequestParam(required = false, defaultValue = "-1") int infoSource,
                            @RequestParam(required = false, defaultValue = "-1") int infoFeedBack,
                            @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime,
                            @RequestParam(required = false, defaultValue = "-1") int infoClassify,
                            @RequestParam(required = false, defaultValue = "") String infoType,
                            @RequestParam(required = false, defaultValue = "-1") int solution,
                            @RequestParam(required = false, defaultValue = "-1") int caseId,
                            @RequestParam(required = false) String operationName, String disposeStatus,
                            @RequestParam(required = false) String infoFeedBackOther,
                            @RequestParam(required = false, defaultValue = "1") int pageNo,
                            @RequestParam(required = false, defaultValue = "10")int pageSize) {
        return NewResponseUtil.makeSuccess(riskPublicSentimentInfoBiz.getInfoList(infoSource, infoFeedBack, infoClassify, startTime, endTime, infoType, solution, caseId, pageNo, pageSize, operationName, disposeStatus, infoFeedBackOther));
    }
    @ApiOperation("舆情详情")
    @RequestMapping(path = "/get-detail")
    public Response getDetail(long id) {
        return NewResponseUtil.makeSuccess(publicSentimentDetailBiz.getDetail(id));
    }


    @NoLoginRequired
    @ApiOperation("信息来源与反馈形式对应关系")
    @RequestMapping(path = "/info-type-get")
    public Response infoTypeGet(int id, @RequestParam(value =  "isShowOther", defaultValue = "false")boolean isShowOther) {
        return NewResponseUtil.makeSuccess(riskPublicSentimentCorrespondBiz.getByInfoSource(id, isShowOther));
    }


    @ApiOperation("舆情操作日志")
    @RequestMapping(path = "/operation-log")
    public Response operationUp(long psId, String pageJson) {
        return NewResponseUtil.makeSuccess(operationLogService.listByPsIdOfPage(psId, pageJson));
    }

    @NoLoginRequired
    @ApiOperation("舆情信息类型枚举列表")
    @RequestMapping(path = "/info-type-list", method = RequestMethod.GET)
    public Response operationUp() {
        return NewResponseUtil.makeSuccess(riskPsInfoTypeBiz.getAll());
    }

    @NoLoginRequired
    @ApiOperation("舆情解决类型枚举列表")
    @RequestMapping(path = "/solution-list", method = RequestMethod.GET)
    public Response solutionList() {
        return NewResponseUtil.makeSuccess(Arrays.stream(PsSolutionEnum.values()).map(t -> new EnumVO(t.getDescription(), t.getCode())).collect(Collectors.toList()));
    }

    @NoLoginRequired
    @ApiOperation("舆情回复类型枚举列表")
    @RequestMapping(path = "/feed-back-list", method = RequestMethod.GET)
    public Response feedBackList() {
        return NewResponseUtil.makeSuccess(Arrays.stream(PsFeedBackEnum.values()).map(t -> new EnumVO(t.getDescription(), t.getCode())).collect(Collectors.toList()));
    }

    @ApiOperation("舆情处理")
    @RequestMapping(path = "/ps-handle")
    public Response psHandle(@RequestBody RiskPsHandleRecord handleRecord) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        return NewResponseUtil.makeSuccess(handleRecordService.add(handleRecord,adminUserId));
    }

    @ApiOperation("获取上一次未提交处理情况")
    @RequestMapping(path = "/get-no-push")
    public Response getNoPushRecord(long psId){
        return NewResponseUtil.makeSuccess(handleRecordService.getNoPushRecord(psId));
    }

    @ApiOperation("舆情处理历史")
    @RequestMapping(path = "/ps-handle-list")
    public Response psHandleList(long psId, String pageJson) {
        return NewResponseUtil.makeSuccess(handleRecordService.listByPsIdOfPage(psId, pageJson));
    }

    @ApiOperation("查看案例关联舆情信息")
    @RequestMapping(path = "/ps-info-by-case")
    public Response psInfoByList(int caseId) {
        return NewResponseUtil.makeSuccess(riskPublicSentimentInfoBiz.getInfoByCaseId(caseId));
    }

    @ApiOperation("查看舆情案例相关信息")
    @RequestMapping(path = "/get-case-info")
    public Response getCaseInfo(int isNewPage, int caseId) {
        return NewResponseUtil.makeSuccess(caseInfoService.getCaseInfoVO(isNewPage, caseId));
    }

    @ApiOperation("查看舆情举报相关信息")
    @RequestMapping(path = "/get-case-report-info")
    public Response getCaseReportInfo(long psId) {
        AdminMarkReport report = reportService.getReport(psId);
        if (report != null) {
            return NewResponseUtil.makeSuccess(new CaseReportVO(report));
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("获取舆情处理历史数量")
    @RequestMapping(path = "/get-record-count")
    public Response getRecordCount(long psId){
        return NewResponseUtil.makeSuccess(handleRecordService.getRecordCountByPsId(psId));
    }


}
