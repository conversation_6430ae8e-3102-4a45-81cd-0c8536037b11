package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcWorkOrderCreateService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-07-05 11:03
 **/
@Slf4j
@Service
@RocketMQListener(id = RiskMQTagCons.QC_WORK_ORDER_CREATE,
        tags = RiskMQTagCons.QC_WORK_ORDER_CREATE,
        topic = MQTopicCons.CF)
public class QcWorkOrderCreateConsumer implements MessageListener<CrowdfundingInfo> {

    @Autowired
    private QcWorkOrderCreateService qcWorkOrderCreateService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingInfo> mqMessage) {
        CrowdfundingInfo payload = mqMessage.getPayload();
        int caseId = payload.getId();
        log.info("QcWorkOrderCreateConsumer caseId:{}", caseId);
        int reconsumeTimes = mqMessage.getReconsumeTimes();
        if (reconsumeTimes > 20) {
            log.error("QcWorkOrderCreateConsumer reconsumeTimes out 重试超过最大次数 {}", payload);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        OperationResult<Void> result = null;
        try {
            result = qcWorkOrderCreateService.promoteQcWorkOrderCreate(caseId, 0);
            log.info("普通质检工单生成结果 result {}", result);
        } catch (Exception e) {
            log.error("QcWorkOrderCreateConsumer consumeMessage error", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
