package com.shuidihuzhu.cf.risk.admin.model.po.hit;

import lombok.Data;

import java.util.Date;
@Data
public class RiskStrategyHitLog {
    /**
     * 主键
     */
    private Long id;

    /**
     * 风控策略命中记录id
     */
    private Long hitRecordId;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private Long operateId;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}