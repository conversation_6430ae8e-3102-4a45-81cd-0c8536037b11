package com.shuidihuzhu.cf.risk.admin.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @Author: lianghongchao
 * @Date: 2019-05-06 11:14
 * excel 文件上传或则下载基本功能
 */
@Slf4j
@Service
public class ExcelFileUtil {
    public static final String xls = "xls";
    public static final String xlsx = "xlsx";

    /**
     * 检查文件内容是否符合要求
     * 格式要求为 xlsx 或者 xls
     *
     * @param ins
     * @param fileName
     * @return
     */
    public Response<List<Sheet>> checkAndGetSheet(InputStream ins, String fileName, List<String> sheetNameList) {
        if (null == ins) {
            return NewResponseUtil.makeFail("文件流为空");
        }
        if (StringUtils.isEmpty(fileName)) {
            return NewResponseUtil.makeFail("文件名为空");
        }
        if (CollectionUtils.isEmpty(sheetNameList)) {
            return NewResponseUtil.makeFail("sheet 名字为空");
        }
        String oriName = fileName.substring(0, fileName.lastIndexOf("."));
        String extensionName = fileName.substring(fileName.lastIndexOf(".") + 1);
        log.info("originFileName:{};oriName:{};extensionName:{}", fileName, oriName, extensionName);

        if (StringUtils.isEmpty(extensionName)) {
            return NewResponseUtil.makeFail("文件扩展名错误");
        }
        if (!xlsx.equals(extensionName) && !xls.equals(extensionName)) {
            return NewResponseUtil.makeFail("上传文件格式:." + extensionName + ",只能识别xlsx和xls格式");
        }

        List<Sheet> sheetList = Lists.newArrayList();
        Workbook workbook;
        if (xlsx.equals(extensionName)) {
            try (XSSFWorkbook hssfWorkbook = new XSSFWorkbook(ins)) {
                workbook = hssfWorkbook;
            } catch (IOException e) {
                log.error("IO流错误", e);
                return NewResponseUtil.makeFail("文件读取异常");
            }
        } else {
            try (HSSFWorkbook hssfWorkbook = new HSSFWorkbook(ins)) {
                workbook = hssfWorkbook;
            } catch (IOException e) {
                log.error("IO流错误", e);
                return NewResponseUtil.makeFail("文件读取异常");
            }
        }
        for (String sheetName : sheetNameList) {
            if (StringUtils.isBlank(sheetName)) {
                continue;
            }
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                log.warn("没有在文件中找到sheetName:{}的记录", sheetName);
            } else {
                sheetList.add(sheet);
            }
        }
        if (CollectionUtils.isEmpty(sheetList)) {
            return NewResponseUtil.makeFail("没有在文件中获取的sheet为空");
        }
        return NewResponseUtil.makeSuccess(sheetList);
    }

    /**
     * 得到单元格的内容字符串
     *
     * @param cell
     * @return
     */
    public String getCellValue(Cell cell) {
        String cellValue = "";
        if (null == cell) {
            return "";
        }
        CellType cellType = cell.getCellTypeEnum();
        switch (cellType) {
            case NUMERIC:
                /**
                 * 可能是numbers, fractional numbers, dates
                 */
                cell.setCellType(CellType.STRING);
                cellValue = cell.getStringCellValue();
                break;
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                /**
                 * 公式
                 */
                cellValue = String.valueOf(cell.getCellFormula());
                break;
            case BLANK:
            case ERROR:
                cellValue = "";
                break;
            default:
                cellValue = cell.toString().trim();
                break;
        }
        return cellValue.trim();
    }

}
