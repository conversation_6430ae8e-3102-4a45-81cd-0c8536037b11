package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcStandardDao;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
@Service
public class RiskQcStandardBizImpl implements RiskQcStandBiz {
    @Autowired
    private RiskQcStandardDao riskQcStandDao;

    @Override
    public List<RiskQcStandard> getAllByType(int isUse, int type, List<Integer> secondStandardTypes) {
        if (isUse < 0 || type < 0) {
            return Lists.newArrayList();
        }
        return riskQcStandDao.getAllByType(isUse, type, secondStandardTypes);
    }

    @Override
    public int addStandard(RiskQcStandard riskQcStandard) {
        if (riskQcStandard == null) {
            return 0;
        }
        return riskQcStandDao.addStandard(riskQcStandard);
    }

    @Override
    public int updateSort(int sort, long id) {
        if (sort < 0 || id < 0) {
            return 0;
        }
        return riskQcStandDao.updateSort(sort, id);
    }

    @Override
    public List<RiskQcStandard> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return riskQcStandDao.getByIds(ids);
    }

    @Override
    public int updateUseById(int isUse, long id) {
        if (isUse < 0 || id < 0) {
            return 0;
        }
        return riskQcStandDao.updateUseById(isUse, id);
    }

    @Override
    public List<RiskQcStandard> getByParentId(long patientId, int isUse) {
        if (patientId < 0 || isUse < 0) {
            return Lists.newArrayList();
        }
        return riskQcStandDao.getByParentId(patientId, isUse);
    }

    @Override
    public RiskQcStandard getLastByLevel(int level, Long parentId) {
        if (level < 0) {
            return null;
        }
        return riskQcStandDao.getLastByLevel(level, parentId);
    }

    @Override
    public int deleteInfo(long id) {
        if (id < 0) {
            return 0;
        }
        return riskQcStandDao.deleteInfo(id);
    }

    @Override
    public RiskQcStandard getById(long id) {
        if (id < 0) {
            return null;
        }
        return riskQcStandDao.getById(id);
    }

    @Override
    public int updateSecondaryUseStatus(int useStatus, long id) {
        if (id < 0 || useStatus < 0) {
            return 0;
        }
        return riskQcStandDao.updateSecondaryUseStatus(useStatus, id);
    }

    @Override
    public RiskQcStandard getByName(String standardName, int standardType, int secondStandardType, int level) {
        if (StringUtils.isBlank(standardName)){
            return null;
        }
        return riskQcStandDao.getByName(standardName, standardType, secondStandardType, level);
    }

    @Override
    public List<RiskQcStandard> fuzzyQuery(String standardName, int level) {
        return riskQcStandDao.fuzzyQuery(standardName, level);
    }
}
