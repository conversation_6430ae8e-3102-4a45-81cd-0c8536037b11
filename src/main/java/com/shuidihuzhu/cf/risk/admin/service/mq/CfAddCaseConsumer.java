package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.client.feign.CaseInfoFeignClient;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.service.env.EnvService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = "new_qc_work_order",
        group = "new_qc_work_order_" + com.shuidihuzhu.cf.constants.MQTagCons.CF_CASE_ID_CREATE,
        tags = com.shuidihuzhu.cf.constants.MQTagCons.CF_CASE_ID_CREATE,
        topic = MQTopicCons.CF)
@Slf4j
public class CfAddCaseConsumer implements MessageListener<CrowdfundingInfo> {

    @Autowired
    private Producer producer;
    @Autowired
    private CaseInfoFeignClient caseInfoFeignClient;
    @Autowired
    private EnvService envService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingInfo> mqMessage) {

        CrowdfundingInfo payload = mqMessage.getPayload();
        if(payload == null){
            return ConsumeStatus.RECONSUME_LATER;
        }
        log.info("CfAddCaseConsumer accept success, payload:{}", payload);

        int caseId = payload.getId();
        if(caseId <= 0){
            log.info("caseId不存在");
            return ConsumeStatus.RECONSUME_LATER;
        }

        // 延时6h发送创建线下质检工单mq
        // 测试环境5s
        DelayLevel delayLevel = envService.isProduction() ? DelayLevel.H6 : DelayLevel.S5;
        producer.send(new Message(MQTopicCons.CF, RiskMQTagCons.QC_WORK_ORDER_CREATE,
                RiskMQTagCons.QC_WORK_ORDER_CREATE + caseId, payload, delayLevel));

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}

