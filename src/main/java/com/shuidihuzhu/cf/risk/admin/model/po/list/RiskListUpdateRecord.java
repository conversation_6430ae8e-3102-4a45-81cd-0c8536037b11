package com.shuidihuzhu.cf.risk.admin.model.po.list;

import lombok.Data;

import java.util.Date;
@Data
public class RiskListUpdateRecord {
    /**
     * 主键
     */
    private Long id;

    /**
     * 规则引擎调用流水号
     */
    private String eventNo;

    /**
     * 座机区号
     */
    private String areaCode;

    /**
     * 座机号
     */
    private String landline;

    /**
     * 座机分机号
     */
    private String extension;

    /**
     * 案例id
     */
    private Integer caseId;

    /**
     * 工单id
     */
    private Long workOrderId;

    /**
     * 医院code码
     */
    private String hospitalCode;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 医院所在省份
     */
    private String province;

    /**
     * 医院所在城市
     */
    private String city;

    /**
     * 科室
     */
    private String departments;

    /**
     * 医院所属城市对应的区号
     */
    private String cityAreaCode;

    /**
     * 医院所属城市对应的区号曾用区号
     */
    private String cityAreaCodeFormer;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 调用结果：与已有黑白名单匹配：1；与已有灰名单匹配：3；没有匹配的：2
     */
    private String result;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}