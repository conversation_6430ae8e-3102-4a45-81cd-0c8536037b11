package com.shuidihuzhu.cf.risk.admin.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandBiz;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcStandardTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcStandardUseEnum;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskMaterialQcStandardVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcResultVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcStandardDetailVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcStandardVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/9/25
 */
@Service
public class RiskQcMaterialDetailService {
    @Autowired
    private RiskQcStandBiz riskQcStandBiz;
    @Autowired
    private RiskQcStandardService riskQcStandardService;
    @Autowired
    private RiskQcDetailService riskQcDetailService;

    public void updateStatus(List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos) {
        if (CollectionUtils.isEmpty(riskMaterialQcStandardVos)) {
            return;
        }
        List<RiskQcStandardVo> riskQcStandardVos = getRiskQcStandardVo(riskMaterialQcStandardVos);
        List<Long> ids = Lists.newArrayList();
        riskQcStandardVos.forEach(riskQcStandardVo -> {
            List<RiskQcStandardDetailVo> riskQcStandardDetailVos = riskQcStandardVo.getRiskQcStandardSecondVos();
            if (CollectionUtils.isNotEmpty(riskMaterialQcStandardVos)) {
                ids.addAll(riskQcStandardDetailVos.stream().map(RiskQcStandardDetailVo::getId).collect(Collectors.toList()));
            }
        });
        ids.forEach(id -> {
            riskQcStandBiz.updateSecondaryUseStatus(RiskQcStandardUseEnum.IS_USE.getCode(), id);
        });
    }


    public List<Long> getIds(List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos) {
        List<Long> ids = Lists.newArrayList();
        if (CollectionUtils.isEmpty(riskMaterialQcStandardVos)) {
            return ids;
        }
        List<RiskQcStandardVo> riskQcStandardVos = getRiskQcStandardVo(riskMaterialQcStandardVos);
        riskQcStandardVos.forEach(riskQcStandardVo -> {
            List<RiskQcStandardDetailVo> riskQcStandardDetailVos = riskQcStandardVo.getRiskQcStandardSecondVos();
            if (CollectionUtils.isNotEmpty(riskQcStandardDetailVos)) {
                ids.addAll(riskQcStandardDetailVos.stream().map(RiskQcStandardDetailVo::getId).collect(Collectors.toList()));
            }
        });
        return ids;
    }

    public List<RiskQcStandardVo> getRiskQcStandardVo(List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos) {
        if (CollectionUtils.isEmpty(riskMaterialQcStandardVos)) {
            return Lists.newArrayList();
        }
        List<RiskQcStandardVo> riskQcStandardVos = Lists.newArrayList();
        riskMaterialQcStandardVos.forEach(riskMaterialQcStandardVo -> {
            List<RiskQcStandardVo> riskQcStandardVoList = riskMaterialQcStandardVo.getRiskQcStandardVo();
            for (RiskQcStandardVo riskQcStandardVo : riskQcStandardVoList) {
                List<RiskQcStandardDetailVo> riskQcStandardDetailVos = riskQcStandardVo.getRiskQcStandardSecondVos();
                riskQcStandardDetailVos = riskQcStandardDetailVos.stream().filter(RiskQcStandardDetailVo::isCheck).collect(Collectors.toList());
                riskQcStandardVo.setRiskQcStandardSecondVos(riskQcStandardDetailVos);
            }
            riskQcStandardVos.addAll(Optional.ofNullable(riskMaterialQcStandardVo.getRiskQcStandardVo())
                    .orElse(Lists.newArrayList()));
        });
        return riskQcStandardVos;
    }

    public void filterInfo(RiskQcResultVo riskQcResultVo) {
        if (MapUtils.isEmpty(riskQcResultVo.getMaterialQcResultOption())) {
            return;
        }
        Map<String, RiskMaterialQcStandardVo> materialQcResultOption = riskQcResultVo.getMaterialQcResultOption();
        if (CollectionUtils.isEmpty(materialQcResultOption.values())) {
            return;
        }
        List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos =
                new ArrayList<>(materialQcResultOption.values());
        List<RiskQcStandardVo> riskQcStandardVos = getRiskQcStandardVo(riskMaterialQcStandardVos);
        riskQcResultVo.setMaterialQcResultOption(riskQcStandardService.buildVo(riskQcStandardVos));
    }
}
