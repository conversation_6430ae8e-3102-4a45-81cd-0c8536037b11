package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-15 14:42
 **/
public interface RiskQcBaseInfoBiz {

    int addQc(RiskQcBaseInfo riskQcBaseInfo);

    RiskQcBaseInfo getById(long id);

    RiskQcBaseInfo getByOrderType(long id, int orderType);

    List<RiskQcBaseInfo> getByIds(List<Long> ids);

    int updateCaseId(long taskId, long caseId, List<Integer> orderTypes);
}
