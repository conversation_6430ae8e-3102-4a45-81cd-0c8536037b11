package com.shuidihuzhu.cf.risk.admin.service.list;

import com.shuidihuzhu.cf.risk.admin.dao.list.RiskCityAreaDao;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskCityArea;
import com.shuidihuzhu.cf.risk.model.RiskCityAreaDto;
import com.shuidihuzhu.common.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 省市区域code表
 * <AUTHOR>
 * @date 2020/7/28 20:00
 */
@Service
@Slf4j
public class RiskCityAreaService {

    @Resource
    private RiskCityAreaDao riskCityAreaDao;

    public RiskCityAreaDto getByProvenceAndCity(String provence, String city) {
        RiskCityArea area = riskCityAreaDao.getByProvinceCity(provence, city);
        if (area == null) {
            return null;
        }
        RiskCityAreaDto riskCityAreaDto = new RiskCityAreaDto();
        BeanUtils.copyProperties(area, riskCityAreaDto);
        return riskCityAreaDto;
    }

    public RiskCityAreaDto getByProvinceAndCityId(Integer provenceId, Integer cityId){
        RiskCityArea area = riskCityAreaDao.getByProvinceCityId(provenceId, cityId);
        if (area == null) {
            return null;
        }
        RiskCityAreaDto riskCityAreaDto = new RiskCityAreaDto();
        BeanUtils.copyProperties(area, riskCityAreaDto);
        return riskCityAreaDto;
    }

}
