package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.UploadBiz;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.store.plugins.CosPlugins;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.cos.CosPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/2/21
 */
@Service
@Slf4j
@RefreshScope
public class UploadBizImpl implements UploadBiz {
    @Autowired
    private CosPlugins cosPlugin;

    private static final String BUCKET = "cf-risk";

    @Override
    public String getTemporalUrlByUrl(String url) {
        return cosPlugin.getTemporalUrlByUrl(BUCKET, url);
    }
}
