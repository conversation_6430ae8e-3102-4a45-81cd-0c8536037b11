package com.shuidihuzhu.cf.risk.admin.model.vo;

import lombok.Data;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
@Data
public class RiskQcStandardDetailVo {
    private long id;
    private String standardName;
    private String property;
    private int useScene;
    private List<Integer> useSceneList;
    private int sort;
    private boolean isCheck;



    public RiskQcStandardDetailVo(long id, String standardName, String property, int useScene, int sort) {
        this.id = id;
        this.standardName = standardName;
        this.property = property;
        this.useScene = useScene;
        this.sort = sort;
    }

    public RiskQcStandardDetailVo() {
    }



}
