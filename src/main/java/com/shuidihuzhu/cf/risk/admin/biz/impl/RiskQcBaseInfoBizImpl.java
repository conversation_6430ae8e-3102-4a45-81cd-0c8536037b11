package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcBaseInfoDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-15 14:42
 **/
@Service
public class RiskQcBaseInfoBizImpl implements RiskQcBaseInfoBiz {
    @Autowired
    private RiskQcBaseInfoDao riskQcBaseInfoDao;

    @Override
    public int addQc(RiskQcBaseInfo riskQcBaseInfo) {
        RiskQcBaseInfo baseInfo;
        int orderType = riskQcBaseInfo.getOrderType();
        if (orderType == WorkOrderType.qc_hospital_dept.getType()
                || orderType == WorkOrderType.qc_material_audit.getType()){
            return riskQcBaseInfoDao.insertOne(riskQcBaseInfo);
        }
        if (orderType == WorkOrderType.qc_wx_1v1.getType()
                || orderType == WorkOrderType.qc_call.getType()
                || orderType == WorkOrderType.qc_wx_1v1_repeat.getType()) {
            baseInfo = riskQcBaseInfoDao.getByTaskIdAndOrderTypeAndQcUniqueCode(riskQcBaseInfo.getTaskId(),
                    orderType, riskQcBaseInfo.getQcUniqueCode());
        } else {
            baseInfo = riskQcBaseInfoDao.getByCaseIdAndOrderTypeAndQcUniqueCode(riskQcBaseInfo.getCaseId(),
                    orderType, riskQcBaseInfo.getQcUniqueCode());
        }
        if (Objects.nonNull(baseInfo)) {
            riskQcBaseInfo.setId(baseInfo.getId());
            return 0;
        }
        return riskQcBaseInfoDao.insertOne(riskQcBaseInfo);
    }

    @Override
    public RiskQcBaseInfo getById(long id) {
        return riskQcBaseInfoDao.getById(id);
    }

    @Override
    public RiskQcBaseInfo getByOrderType(long id, int orderType) {
        if (orderType == WorkOrderType.qc_wx_1v1.getType()) {
            return riskQcBaseInfoDao.getByTaskIdAndOrderType(id, orderType);
        }
        return riskQcBaseInfoDao.getByCaseIdAndOrderType(id, orderType);
    }

    @Override
    public List<RiskQcBaseInfo> getByIds(List<Long> ids) {
        return riskQcBaseInfoDao.getByIds(ids);
    }

    @Override
    public int updateCaseId(long taskId, long caseId, List<Integer> orderTypes) {
        return riskQcBaseInfoDao.updateCaseId(taskId, caseId, orderTypes);
    }

}
