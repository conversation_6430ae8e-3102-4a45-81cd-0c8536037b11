package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandardLogBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcStandardLogDao;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardLog;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/13
 */
@Service
public class RiskQcStandardLogBizImpl implements RiskQcStandardLogBiz {
    @Autowired
    private RiskQcStandardLogDao riskQcStandardLogDao;

    @Override
    public int addInfo(long operationId, String operationName, String operationLog, long qcStandardId, int operationType) {
        return riskQcStandardLogDao.addInfo(operationId, operationName, operationLog, qcStandardId, operationType);
    }

    @Override
    public List<RiskQcStandardLog> getLogByStandardId(long standardId) {
        if (standardId < 0){
            return Lists.newArrayList();
        }
        return riskQcStandardLogDao.getLogByStandardId(standardId);
    }
}
