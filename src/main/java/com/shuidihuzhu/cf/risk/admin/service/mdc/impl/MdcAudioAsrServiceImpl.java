package com.shuidihuzhu.cf.risk.admin.service.mdc.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.vo.AsrSentenceVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcAsrResultVO;
import com.shuidihuzhu.cf.risk.admin.service.mdc.MdcAudioAsrService;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.mdc.client.feign.MdcFeignClient;
import com.shuidihuzhu.mdc.client.model.SdMedicalCallRecordModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class MdcAudioAsrServiceImpl implements MdcAudioAsrService {

    @Autowired
    private AiAsrDelegate aiAsrDelegate;
    @Autowired
    private MdcFeignClient mdcFeignClient;
    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    @Override
    public void doHandleAsrRecord(Date startDate, Date endDate) {
        Response<List<SdMedicalCallRecordModel>> callRecordResponse = mdcFeignClient.listNeedAsrCallRecord(DateUtil.formatDate(startDate),DateUtil.formatDate(endDate));
        log.info("mdcFeignClient.listNeedAsrCallRecord response:{}",callRecordResponse);
        if (callRecordResponse.notOk() || CollectionUtils.isEmpty(callRecordResponse.getData())){
            return;
        }

        List<SdMedicalCallRecordModel> callRecordList = callRecordResponse.getData();
        List<String> uniqueIds = callRecordList.stream().map(SdMedicalCallRecordModel::getCallUid).collect(Collectors.toList());
        Response<List<ClewCallRecordModel>> callRecordsByUniqueIds = cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(uniqueIds);
        List<ClewCallRecordModel> callRecordsByUniqueIdList = Lists.newArrayList();
        log.info("cfClewtrackFeignClient.getClewCallRecordsByUniqueIds response:{}", callRecordsByUniqueIds);
        if (callRecordsByUniqueIds.ok() && CollectionUtils.isNotEmpty(callRecordsByUniqueIds.getData())) {
            callRecordsByUniqueIdList=callRecordsByUniqueIds.getData();
        }
        Map<String, ClewCallRecordModel> recordModelMap = callRecordsByUniqueIdList.stream().collect(Collectors.toMap(ClewCallRecordModel::getUniqueId, Function.identity(), (oldVal, newVal) -> newVal));
        for (SdMedicalCallRecordModel model : callRecordList){
            if(model.getWhPlatformType()==0) {
                ClewCallRecordModel clewCallRecordModel = recordModelMap.get(model.getCallUid());
                if (Objects.isNull(clewCallRecordModel)) {
                    continue;
                }
                aiAsrDelegate.analyseUsePriority(model.getId(), model.getId(), clewCallRecordModel.getCosFile(), AiAsrDelegate.HandleTypeEnum.HANDLE_MDC_RECORD, 1);
            }
            if(model.getWhPlatformType()==1 && StringUtils.isNotBlank(model.getCallRecording())){
                aiAsrDelegate.analyseUsePriority(model.getId(), model.getId(), model.getCallRecording(), AiAsrDelegate.HandleTypeEnum.HANDLE_MDC_RECORD, 1);
            }

        }
        mdcFeignClient.updateAsrDoing(callRecordList);
    }


    public static void main(String args[]){
        List<ClewCallRecordModel> callRecordsByUniqueIds = Lists.newArrayList();
        Map<String, ClewCallRecordModel> recordModelMap = callRecordsByUniqueIds.stream().collect(Collectors.toMap(ClewCallRecordModel::getUniqueId, Function.identity(), (oldVal, newVal) -> newVal));
        System.out.println(JSON.toJSONString(recordModelMap));
    }

    @Override
    public Response<Void> onAudioAsrCallback(QcAsrResultVO resultVO) {
        log.info("mdcAudioAsrServiceImpl onAudioAsrCallback result {}", resultVO);
        if (resultVO == null) {
            return NewResponseUtil.makeSuccess();
        }
        long materialId = resultVO.getMaterialId();
        SdMedicalCallRecordModel sdMedicalCallRecordModel = processWithResult(resultVO);
        if (Objects.isNull(sdMedicalCallRecordModel)){
            sdMedicalCallRecordModel = new SdMedicalCallRecordModel();
            sdMedicalCallRecordModel.setId(materialId);
            sdMedicalCallRecordModel.setAsrStatus(SdMedicalCallRecordModel.AsrStatusEnum.ASR_FAIL.getCode());
            sdMedicalCallRecordModel.setAsrResult(StringUtils.EMPTY);
        }else{
            sdMedicalCallRecordModel.setId(materialId);
        }
        mdcFeignClient.updateAsrResult(Lists.newArrayList(sdMedicalCallRecordModel));
        log.info("mdcAudioAsrServiceImpl update success id {}", materialId);
        return NewResponseUtil.makeSuccess();
    }


    private SdMedicalCallRecordModel processWithResult(QcAsrResultVO resultVO) {
        QcAsrResultVO.Result result = resultVO.getResult();
        if (result == null) {
            return null;
        }
        SdMedicalCallRecordModel sdMedicalCallRecordModel = new SdMedicalCallRecordModel();
        List<AsrSentenceVO> vos = getSentenceIntercept(result.getSentences());
        sdMedicalCallRecordModel.setAsrResult(JSONObject.toJSONString(vos));
        sdMedicalCallRecordModel.setAsrStatus(SdMedicalCallRecordModel.AsrStatusEnum.ASR_DONE.getCode());
        return sdMedicalCallRecordModel;
    }

    private List<AsrSentenceVO> getSentenceIntercept(List<QcAsrResultVO.Sentence> sentences) {
        List<AsrSentenceVO> vos = Lists.newArrayList();
        int maxLength = 5_0000;
        int currentLength = 0;
        for (QcAsrResultVO.Sentence sentence : sentences) {
            final int length = StringUtils.length(sentence.getText());
            currentLength += length;
            if (currentLength > maxLength) {
                return vos;
            }
            vos.add(AsrSentenceVO.createByAsrResult(sentence));
        }
        return vos;
    }

}
