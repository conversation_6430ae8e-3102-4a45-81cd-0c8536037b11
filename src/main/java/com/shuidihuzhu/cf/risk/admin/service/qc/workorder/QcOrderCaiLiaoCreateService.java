package com.shuidihuzhu.cf.risk.admin.service.qc.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcSearchIndexDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcmaterialService;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.env.EnvService;
import com.shuidihuzhu.cf.risk.admin.service.qc.CallService;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotMaterialService;
import com.shuidihuzhu.cf.risk.admin.util.CosUploadUtil;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.cf.admin.client.CfRecordClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QcOrderCaiLiaoCreateService {

    @Autowired
    private RiskQcSearchIndexDao riskQcSearchIndexDao;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;
    @Autowired
    private Producer producer;
    @Autowired
    private CfRecordClient cfRecordClient;
    @Autowired
    private QualitySpotMaterialService qualitySpotMaterialService;
    @Autowired
    private CosUploadUtil cosUploadUtil;

    @Autowired
    private CallService callService;

    @Autowired
    private RiskQcmaterialService riskQcmaterialService;

    @Autowired
    private EnvService envService;
    @Autowired
    private UserGroupFeignClient userGroupFeignClient;

    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;

    public ConsumeStatus touchCreateCaiLiaoQcOrder(WorkOrderResultChangeEvent workOrderResultChangeEvent) {
        log.info("QcOrderCaiLiaoCreateService workOrderResultChangeEvent:{}", JSON.toJSONString(workOrderResultChangeEvent));

        //判断处理人是否是石家庄材料审核组
        long workOrderId = workOrderResultChangeEvent.getWorkOrderId();
        Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (workOrderVOResponse.notOk()) {
            return ConsumeStatus.RECONSUME_LATER;
        }
        WorkOrderVO workOrderVO = workOrderVOResponse.getData();
        var operatorId = workOrderVO.getOperatorId();
        var caseId = workOrderVO.getCaseId();
        Response<AuthGroupDto> adminOrganizationAuthRpcResponse = userGroupFeignClient.selectByUserId(operatorId);
        if (!adminOrganizationAuthRpcResponse.ok()) {
            return ConsumeStatus.RECONSUME_LATER;
        }
        AuthGroupDto authGroupDto = adminOrganizationAuthRpcResponse.getData();
        final Long groupBizId = Optional.ofNullable(authGroupDto)
                .map(AuthGroupDto::getGroupBizId).orElse(0L);

        //生成工单
        String lockName = "qcLock_" + QcTypeEnum.MATERIAL.getCode() + "_" + workOrderResultChangeEvent.getWorkOrderId();
        String identifier = "";
        try {
            identifier = cfRiskRedissonHandler.tryLock(lockName, 3 * 1000L, 30 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                return ConsumeStatus.RECONSUME_LATER;
            }

            //创建质检基本信息
            var qcByName = seaAccountService.getName(operatorId);
            RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
            riskQcBaseInfo.setCaseId(caseId);
            riskQcBaseInfo.setQcType(QcTypeEnum.MATERIAL.getCode());
            riskQcBaseInfo.setOrderType(WorkOrderType.qc_material_audit.getType());
            riskQcBaseInfo.setQcUniqueCode(Long.toString(workOrderVO.getOperatorId()));
            riskQcBaseInfo.setQcByName(qcByName);
            riskQcBaseInfoBiz.addQc(riskQcBaseInfo);

//            String snapshot = "";
//            Response<String> stringResponse = cfRecordClient.selectHandleSnapshot(caseId, workOrderId);
//            if (stringResponse.ok()) {
//                snapshot = stringResponse.getData();
//            }
//            if (StringUtils.isEmpty(snapshot)) {
//                log.info("get snapshot empty !!!");
//                workOrderVO.setQcId(riskQcBaseInfo.getId());
//                producer.send(new Message(MQTopicCons.CF, RiskMQTagCons.QC_MATERIAL_UPDATE_SNAPSHOT,
//                        RiskMQTagCons.QC_MATERIAL_UPDATE_SNAPSHOT + workOrderId, workOrderVO, DelayLevel.S5));
//            } else {
//                snapshot = cosUploadUtil.uploadText(snapshot, null);
//            }

            //记录组织结构id、材审工单id
            List<RiskQcMaterialsInfo> riskQcMaterialsInfos = List.of(
                    new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.ORG_ID.getKey(), Long.toString(groupBizId)),
                    new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.ORDER_ID.getKey(), Long.toString(workOrderId)));
//                    new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.DETAIL_SNAPSHOT.getKey(), snapshot));
            riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfos);

            //执行工单创建逻辑
            QcWorkOrder qcWorkOrder = new QcWorkOrder();
            qcWorkOrder.setCaseId(caseId);
            qcWorkOrder.setQcId(riskQcBaseInfo.getId());
            qcWorkOrder.setOrderType(WorkOrderType.qc_material_audit.getType());
            qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
            qcWorkOrder.setComment("生成材审质检工单");
            Response<Long> clientQcWorkOrder = cfQcWorkOrderClient.createQcWorkOrder(qcWorkOrder);

            if (clientQcWorkOrder.ok() && Objects.nonNull(clientQcWorkOrder.getData())) {
                log.info("QcMaterialWorkOrderCreate id:{}", clientQcWorkOrder.getData());
                //调用质检操作记录接口，记录操作记录
                String content = "生成材审质检工单,工单ID【" + clientQcWorkOrder.getData() + "】";
                riskQcLogService.addLog(RiskQcOperationTypeEnum.CREATE_COMMON_WORK_ORDER, clientQcWorkOrder.getData(), content);

                // 保存快照
//                riskQcmaterialService.saveSnapshotWithRetry(clientQcWorkOrder.getData(), riskQcBaseInfo.getId(), caseId, workOrderId);
                boolean snapshotSuccess = riskQcmaterialService.saveSnapshotWithRetryV2(clientQcWorkOrder.getData(), riskQcBaseInfo.getId(), workOrderId, 0);

                //获取通话状态
                int callStatus = callService.getCallStatus(workOrderId);

                // 添加搜索索引字段聚合表
                var organization = getOrganization(operatorId);
                RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
                riskQcSearchIndex.setCaseId(caseId);
                riskQcSearchIndex.setQcId(riskQcBaseInfo.getId());
                riskQcSearchIndex.setWorkOrderId(clientQcWorkOrder.getData());
                riskQcSearchIndex.setQcType(QcTypeEnum.MATERIAL.getCode());
                riskQcSearchIndex.setOrganization(organization);
                riskQcSearchIndex.setQcUniqueCode(Long.toString(operatorId));
                riskQcSearchIndex.setQcByName(qcByName);
                riskQcSearchIndex.setCallStatus(callStatus);
                riskQcSearchIndex.setHandleResult(workOrderVO.getHandleResult());
                riskQcSearchIndex.setSourceWorkOrderId(workOrderId);
                riskQcSearchIndex.setSourceWorkOrderType(workOrderVO.getOrderType());

                FeignResponse<CrowdfundingInfo> infoFeignResponse = crowdfundingFeignClient.getCaseInfoById(caseId);
                CrowdfundingInfo crowdfundingInfo = Optional.ofNullable(infoFeignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
                if (Objects.nonNull(crowdfundingInfo)) {
                    riskQcSearchIndex.setUserId(crowdfundingInfo.getUserId());
                }

                riskQcSearchIndexBiz.addMaterialRiskQcSearchIndex(qcWorkOrder.getOrderType(), riskQcSearchIndex);
                //记录分配工单要用的字段
                cfQcWorkOrderClient.addExtValue(clientQcWorkOrder.getData(), OrderExtName.qcUserName.getName(), qcByName);
                //10分钟后在同步一次通话状态
                Map<String, Object> map = Maps.newHashMap();
                map.put("workOrderVO", workOrderVO);
                map.put("newWorkOrderId", clientQcWorkOrder.getData());
                producer.send(new Message(MQTopicCons.CF, RiskMQTagCons.QC_MATERIAL_WORK_ORDER_CALL_STATUS,
                        RiskMQTagCons.QC_MATERIAL_WORK_ORDER_CALL_STATUS + workOrderId, map, DelayLevel.M10));
                //材审工单质检
                if (snapshotSuccess) {
                    qualitySpotMaterialService.spotMaterial(clientQcWorkOrder.getData(), riskQcBaseInfo, workOrderVO);
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        } catch (Exception e) {
            log.error("QcCallWorkOrderCreateConsumer.consumeMessage error", e);
        } finally {
            if (StringUtils.isNotEmpty(identifier)) {
                cfRiskRedissonHandler.unLock(lockName, identifier);
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }


    public String getOrganization(long userId) {
        //内部
        Response<String> authRpcResponse = userGroupFeignClient.getGroupNameByUserId(userId);
        if (authRpcResponse.ok()) {
            return authRpcResponse.getData();
        }
        return "";
    }
}
