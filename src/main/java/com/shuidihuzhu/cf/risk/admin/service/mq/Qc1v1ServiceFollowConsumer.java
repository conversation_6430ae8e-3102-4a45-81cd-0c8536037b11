package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueFollowUpTaskModel;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: lixiaoshuang
 * @create: 2020-08-15 13:32
 **/
@Service
@RocketMQListener(id = CfClientMQTagCons.CF_CLUE_FOLLOWUP_TASK_HANDLE_MSG,
        tags = CfClientMQTagCons.CF_CLUE_FOLLOWUP_TASK_HANDLE_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class Qc1v1ServiceFollowConsumer implements MessageListener<CfClueFollowUpTaskModel> {

    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfClueFollowUpTaskModel> mqMessage) {
        CfClueFollowUpTaskModel payload = mqMessage.getPayload();
        log.info("Qc1v1ServiceFollowConsumer payload:{}", payload);
        //同步1v1质检工单服务状态
        var taskId = payload.getTaskId().intValue();
        Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(taskId, WorkOrderType.qc_wx_1v1.getType());
        if (lastWorkOrder.notOk()) {
            return ConsumeStatus.RECONSUME_LATER;
        }
        if (Objects.isNull(lastWorkOrder.getData())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        var result = riskQcSearchIndexBiz.updateServiceStageByTaskId(taskId,
                lastWorkOrder.getData().getWorkOrderId(), payload.getServicePhase());

        if (result > 0) {
            //同步1v1复检工单服务状态
            Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getLastWorkOrder(taskId, WorkOrderType.qc_wx_1v1_repeat.getType());
            if (workOrderVOResponse.ok() && Objects.nonNull(workOrderVOResponse.getData())) {
                riskQcSearchIndexBiz.updateServiceStageByTaskId(taskId,
                        workOrderVOResponse.getData().getWorkOrderId(), payload.getServicePhase());
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
