package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.DiscussionRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface DiscussionRecordDao {


    DiscussionRecord getByDiscussionIdAndUserId(@Param("discussionId") long discussionId,
                                                @Param("userId") long userId);

    int save(DiscussionRecord discussionRecord);


    DiscussionRecord getByCaseIdAndUserId(@Param("caseId") int caseId, @Param("userId") long userId);


    DiscussionRecord getByDiscussionIdAndUserID(@Param("discussionId") long discussionId, @Param("userId") long userId);

    List<DiscussionRecord> getListByDiscussionId(@Param("discussionId") long discussionId);
}
