package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.QualitySpotStrategyLevelConfigTypeRel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface QualitySpotStrategyLevelConfigTypeRelDao {

    int save(QualitySpotStrategyLevelConfigTypeRel typeRel);


    List<QualitySpotStrategyLevelConfigTypeRel> findByTypeId(@Param("typeId") long typeId);

    int deleteByLevelConfidId(@Param("strategyId") long strategyId);
}
