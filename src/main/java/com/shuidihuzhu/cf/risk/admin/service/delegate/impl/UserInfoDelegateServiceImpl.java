package com.shuidihuzhu.cf.risk.admin.service.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserInfoServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/26 11:00 AM
 */
@Slf4j
@Service
public class UserInfoDelegateServiceImpl implements UserInfoDelegateService {

    @Resource
    private UserInfoServiceClient userInfoServiceClient;

    @Override
    public List<UserInfoModel> getUserInfoByUserIdBatch(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }

        List<UserInfoModel> userModelResult = Lists.newArrayList();

        List<List<Long>> lists = Lists.partition(userIds, 1000);
        for (List<Long> list : lists) {
            userModelResult.addAll(userInfoServiceClient.getUserInfoByUserIdBatchV2(list));
        }

        return userModelResult;
    }

    @Override
    public UserInfoModel getUserInfoByUserId(long userId) {
        return userInfoServiceClient.getUserInfoByUserId(userId);
    }

    @Override
    public UserInfoModel getUserInfoByCryptoMobile(String cryptoMobile) {
        return userInfoServiceClient.getUserInfoByCryptoMobile(cryptoMobile);
    }

    @Override
    public UserInfoModel getUserInfoByMobile(String mobile) {
        return userInfoServiceClient.getUserInfoByMobile(mobile);
    }
}
