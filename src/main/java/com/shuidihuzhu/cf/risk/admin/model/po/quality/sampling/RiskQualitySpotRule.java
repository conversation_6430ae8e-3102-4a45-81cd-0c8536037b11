package com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class RiskQualitySpotRule {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 策略优先级
     */
    private Integer priority;

    /**
     * 启用停用状态：0 启用，1 停用
     */
    private Byte status;

    /**
     * 是否删除0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 规则脚本
     */
    private String ruleScript;

    /**
     * 执行方式的值
     */
    private String excuteModeValue;

    /**
     * 规则执行时间
     */
    private String dataScopeString;


    /**
     * 策略处理的数据日期范围，0 表示当天，1 表示前一天
     */
    private List<Integer> dataScopeList;

    public void setDataScopeString(String dataScopeString) {
        this.dataScopeString = dataScopeString;
        this.dataScopeList = Lists.newArrayList();
        if (StringUtils.isBlank(dataScopeString)){
            return;
        }
        this.dataScopeList = Lists.newArrayList(Integer.parseInt(dataScopeString));
    }

}