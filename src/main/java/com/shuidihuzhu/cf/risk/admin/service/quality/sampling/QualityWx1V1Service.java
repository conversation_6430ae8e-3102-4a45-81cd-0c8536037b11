package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcAssignRecordBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotLevelConfBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;
import com.shuidihuzhu.cf.risk.client.admin.RiskQcBaseClient;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.*;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotExecuteModelEnum;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.cf.risk.model.risk.RiskQcBaseInfoVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueCallRecordSumModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.AssignTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/15
 */
@Service
@Slf4j
public class QualityWx1V1Service {

    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private QualitySpotStrategyService qualitySpotStrategyService;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private QualitySpotLevelConfBiz qualitySpotLevelConfBiz;
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private RiskQcBaseClient riskQcBaseClient;
    @Autowired
    private RiskQcAssignRecordBiz assignRecordBiz;

    public void spotMaterial(WorkOrderVO workOrderVO) {
        //获取工单规则
        List<QualitySpotJobConfDto> qualitySpotJobConfDtoList =
                qualitySpotStrategyService.listJobScopeConf(QualitySpotSceneEnum.OFFLINE_Wx_1V1_WORK_ORDER.getCode());
        if (CollectionUtils.isEmpty(qualitySpotJobConfDtoList)) {
            return;
        }

        QualitySpotJobConfDto qualitySpotJobConfDto = qualitySpotJobConfDtoList.get(0);
        //检查当天的质检数量是否已经到达上限
        if (checkStrategyCount(qualitySpotJobConfDto)) {
            return;
        }

        List<QualitySpotRuleInfo> qualitySpotRuleInfos = qualitySpotJobConfDto.getRuleInfoList();

        for (QualitySpotRuleInfo ruleInfo : qualitySpotRuleInfos) {
            if (checkRuleSampling(ruleInfo, qualitySpotJobConfDto)) {
                continue;
            }
            QualitySpotWxDto qualitySpotWxDto = this.wx1v1SpotCheck(ruleInfo.getRuleId(), workOrderVO, qualitySpotJobConfDto.getScene()).get(0);
            //分配工单
            if (!qualitySpotWxDto.isHit()) {
                continue;
            }
            //记录命中规则名称,命中规则id
            riskQcSearchIndexBiz.updateRuleNameByWorkOrderId(qualitySpotWxDto.getWorkOrderId(), ruleInfo.getRuleName());
            cfQcWorkOrderClient.addExtValue(qualitySpotWxDto.getWorkOrderId(),
                    OrderExtName.ruleId.getName(), Long.toString(ruleInfo.getRuleId()));
            //更新工单分配类型
            cfQcWorkOrderClient.updateAssignType(Lists.newArrayList(qualitySpotWxDto.getWorkOrderId()), AssignTypeEnum.MUST_ASSIGN.getCode());
            //统计工单信息
            updateCountRedis(qualitySpotJobConfDto.getScene());
            break;
        }

    }

    private boolean checkRuleSampling(QualitySpotRuleInfo ruleInfo, QualitySpotJobConfDto qualitySpotJobConfDto) {
        if (qualitySpotJobConfDto.getExecuteMode() == QualitySpotExecuteModelEnum.PRIORITY.getCode()) {
            return false;
        }
        int count = getCountByKey(getRuleCountKey(ruleInfo.getRuleId()));
        QualitySpotExecuteModelEnum executeModelEnum = QualitySpotExecuteModelEnum.fromCode(qualitySpotJobConfDto.getExecuteMode());
        switch (executeModelEnum) {
            case PRIORITY:
                return false;
            case PERCENTAGE:
            case FINAL_COUNT:
                return count >= ruleInfo.getRuleSamplingLevel();
            default:
                return true;
        }

    }

    private int getCountByKey(String key) {
        if (log.isDebugEnabled()) {
            log.debug("key:{}", key);
        }
        Integer count = redissonHandler.get(key, Integer.class);
        if (count == null) {
            return 0;
        }
        return count;
    }

    private boolean checkStrategyCount(QualitySpotJobConfDto confDto) {
        StrategyCount strategyCount =
                redissonHandler.get(getStrategyCountKey(confDto.getScene()), StrategyCount.class);
        if (strategyCount == null) {
            List<RiskQualitySpotLevelConfVo> riskQualitySpotLevelConfVos =
                    qualitySpotLevelConfBiz.listWithAllCurrentValidScene(confDto.getScene());
            if (CollectionUtils.isEmpty(riskQualitySpotLevelConfVos)) {
                log.error("riskQualitySpotLevelConfVos is empty, scene:{}", confDto.getScene());
                return true;
            }
            //查询数量
            strategyCount = new StrategyCount(0, riskQualitySpotLevelConfVos.get(0).getSamplingLevel(),
                    DateUtil.getYMDStringByDate(new Date()));
            redissonHandler.setEX(getStrategyCountKey(confDto.getScene()), strategyCount, RedissonHandler.ONE_DAY);
        }
        return strategyCount.getCurrentCount() >= strategyCount.getTargetCount();
    }

    /**
     * 任务策略的key
     */
    private String getStrategyCountKey(long scene) {
        return DateUtil.getYMDStringByDate(new Date()) + "_" + scene;
    }

    /**
     * 规则每天的工单的key
     */
    private String getRuleCountKey(long ruleId) {
        return DateUtil.getYMDStringByDate(new Date()) + "_" + ruleId;
    }

    private List<QualitySpotWxDto> wx1v1SpotCheck(long ruleId, WorkOrderVO workOrderVo, long scene) {
        long taskId = workOrderVo.getCaseId();
        Response<List<CfClueInfoModel>> cfClueInfo = cfClewtrackTaskFeignClient.listCfClueInfo(Lists.newArrayList(taskId));
        Map<Long, CfClueInfoModel> cfClueInfoModelMap = cfClueInfo.getData().stream()
                .collect(Collectors.toMap(CfClueInfoModel::getTaskId, Function.identity(), (k1, k2) -> k2));

        //一周的时间
        WeekFields week = WeekFields.of(DayOfWeek.MONDAY, 7);
        LocalDate startDate = LocalDate.now().with(week.getFirstDayOfWeek());
        LocalDate endDate = startDate.plusDays(7);
        String startStr = startDate.toString() + " 00:00:00";
        String endStr = endDate.toString() + " 00:00:00";


        RiskQcSearchIndex riskQcSearchIndex = riskQcSearchIndexBiz.getByWorkOrderId(workOrderVo.getWorkOrderId());

        QualitySpotWxDto qualitySpotWxDto = new QualitySpotWxDto();
        qualitySpotWxDto.setWorkOrderId(workOrderVo.getWorkOrderId());
        if (Objects.nonNull(riskQcSearchIndex)) {
            qualitySpotWxDto.setServiceStage(riskQcSearchIndex.getServiceStage());
            if (riskQcSearchIndex.getCaseId() > 0) {
                //查询已筹金额
                FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = crowdfundingFeignClient.getCaseInfoById((int) riskQcSearchIndex.getCaseId());
                CrowdfundingInfo crowdfundingInfo = crowdfundingInfoFeignResponse.getData();
                if (Objects.nonNull(crowdfundingInfo)) {
                    qualitySpotWxDto.setHasAmount((long) crowdfundingInfo.getAmount() / 100);
                }
                //查询初审状态
                FeignResponse<CfInfoExt> cfInfoExtFeignResponse = crowdfundingFeignClient.getCfInfoExtByCaseId((int) riskQcSearchIndex.getCaseId());
                CfInfoExt cfInfoExt = cfInfoExtFeignResponse.getData();
                if (Objects.nonNull(cfInfoExt)) {
                    qualitySpotWxDto.setFirstAuitStatus(cfInfoExt.getFirstApproveStatus());
                }
            }
        }
        CfClueInfoModel cfClueInfoModel = cfClueInfoModelMap.get(taskId);
        qualitySpotWxDto.setPersonnelJobContent(0);
        if (Objects.nonNull(cfClueInfoModel)) {
            qualitySpotWxDto.setWxPassStatus(cfClueInfoModel.getWechatPass());
            qualitySpotWxDto.setFirstTypeInStatus(cfClueInfoModel.getPreMsgStatus());
            qualitySpotWxDto.setChangeNumberLaunch(cfClueInfoModel.getClewSecondPhone() != null);
            qualitySpotWxDto.setPersonnelJobContent(cfClueInfoModel.getWorkContentType());

            String mis = cfClueInfoModel.getUserId();
            String encryptPhone = oldShuidiCipher.aesEncrypt(cfClueInfoModel.getClewPhone());
            String encryptSecondPhone = oldShuidiCipher.aesEncrypt(cfClueInfoModel.getClewSecondPhone());
            Date time = DateUtil.parseDateTime(cfClueInfoModel.getAssignTime());
            CfClueCallRecordSumModel cfClueCallRecordSumModel = this.getCfClueCallRecordSumModel(mis, encryptPhone, encryptSecondPhone, time);
            if (Objects.nonNull(cfClueCallRecordSumModel)) {
                qualitySpotWxDto.setTotalDuration(cfClueCallRecordSumModel.getTotalDuration());
            }
        }
        //获取质检信息
        Response<RiskQcBaseInfoVo> riskQcBaseInfoVoResponse = riskQcBaseClient.getQcInfo(workOrderVo.getQcId());
        RiskQcBaseInfoVo riskQcBaseInfoVo = riskQcBaseInfoVoResponse.getData();
        if (Objects.nonNull(riskQcBaseInfoVo)) {
            qualitySpotWxDto.setInnerOrgId(riskQcBaseInfoVo.getAreaId());
            qualitySpotWxDto.setWithoutOrgId(riskQcBaseInfoVo.getAreaId());
            qualitySpotWxDto.setUniqueCode(riskQcBaseInfoVo.getQcUniqueCode());
            StrategyCount strategyCount =
                    redissonHandler.get(getStrategyCountKey(scene), StrategyCount.class);
            if (Objects.nonNull(strategyCount)) {
                qualitySpotWxDto.setDayAllotWorkOrderCount(strategyCount.getCurrentCount());
            } else {
                qualitySpotWxDto.setDayAllotWorkOrderCount(0);
            }
            //查询一周数量
            int weekHitCount = assignRecordBiz.countByUniqueCode(
                    riskQcBaseInfoVo.getQcUniqueCode(),
                    WorkOrderType.qc_wx_1v1.getType(),
                    startStr,
                    endStr);
            qualitySpotWxDto.setWeekAllotWorkOrderCount(weekHitCount);
        }
        return qualitySpotStrategyService.doQualitySpotWxStrategy(ruleId, Lists.newArrayList(qualitySpotWxDto));
    }


    public CfClueCallRecordSumModel getCfClueCallRecordSumModel(String mis, String encryptPhone,
                                                                String encryptSecondPhone, Date time) {
        Response<CfClueCallRecordSumModel> response = cfClewtrackTaskFeignClient.getCallRecordSummaryByMisAndPhone(
                mis, encryptPhone, encryptSecondPhone, time == null ? null : time.getTime());
        if (response != null && response.ok() && response.getData() != null) {
            return response.getData();
        }
        return null;
    }

    private void updateCountRedis(long scene) {
        //查询数量
        QualitySpotMaterialZhuDongService.StrategyCount strategyCount = redissonHandler.get(getStrategyCountKey(scene), QualitySpotMaterialZhuDongService.StrategyCount.class);
        if (strategyCount == null) {
            List<RiskQualitySpotLevelConfVo> riskQualitySpotLevelConfVos =
                    qualitySpotLevelConfBiz.listWithAllCurrentValidScene(scene);
            if (CollectionUtils.isEmpty(riskQualitySpotLevelConfVos)) {
                log.error("riskQualitySpotLevelConfVos is empty, scene:{}", scene);
                return;
            }
            strategyCount = new QualitySpotMaterialZhuDongService.StrategyCount(0, riskQualitySpotLevelConfVos.get(0).getSamplingLevel(),
                    DateUtil.getYMDStringByDate(new Date()));
        }
        strategyCount.setCurrentCount(strategyCount.getCurrentCount() + 1);
        redissonHandler.setEX(getStrategyCountKey(scene),
                strategyCount, RedissonHandler.ONE_DAY);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class StrategyCount {
        private int currentCount;
        private int targetCount;
        private String currentDate;
    }
}
