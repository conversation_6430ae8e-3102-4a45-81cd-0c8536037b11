package com.shuidihuzhu.cf.risk.admin.service.recording.support;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListDataService;
import com.shuidihuzhu.cf.risk.admin.service.recording.AbsWorkOrderRecordingHandler;
import com.shuidihuzhu.cf.risk.admin.util.SpringUtil;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingChaiFenV2FeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: wanghui
 * @create: 2022/3/28 下午4:43
 * qc_material_audit 材审质检工单 录音获取 处理 存储
 */
@Component
@Slf4j
public class WorkOrderRecordingHandler45 extends AbsWorkOrderRecordingHandler {
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private BlackListDataService dataService;
    @Resource
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;
    @Resource
    private CrowdfundingChaiFenV2FeignClient crowdfundingChaiFenFeignClient;
    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Resource
    private CfClewtrackTaskFeignClient clewtrackTaskClient;
    @Resource
    private UserFeignClient userFeignClient;
    @Resource
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;


    @Override
    public List<WorkOrderRecordingModel.CallRecordModel> listCallRecordModels(long workOrderId) {
        RiskQcSearchIndex riskQcSearchIndex = riskQcSearchIndexBiz.getByWorkOrderId(workOrderId);
        if (riskQcSearchIndex==null) {
            log.warn("riskQcSearchIndexBiz.getByWorkOrderId workOrderId:{} result is null", workOrderId);
            return Lists.newArrayList();
        }
        Response<List<WorkOrderVO>> listResponse = handleFeignResponse("cfQcWorkOrderClient.queryQcByIds",cfQcWorkOrderClient.queryQcByIds(Lists.newArrayList(riskQcSearchIndex.getSourceWorkOrderId())));
        if (CollectionUtils.isEmpty(listResponse.getData())) {
            return Lists.newArrayList();
        }

        WorkOrderVO workOrderVO = listResponse.getData().get(0);
        int caseId = workOrderVO.getCaseId();
        int userId = (int) workOrderVO.getOperatorId();

        FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = handleFeignResponse("crowdfundingFeignClient.getCaseInfoById",crowdfundingFeignClient.getCaseInfoById(caseId));
        if (crowdfundingInfoFeignResponse.getData() == null) {
            return Lists.newArrayList();
        }
        CrowdfundingInfo info = crowdfundingInfoFeignResponse.getData();
        FeignResponse<CfInfoExt> cfInfoExtFeignResponse = handleFeignResponse("crowdfundingFeignClient.getCfInfoExtByCaseId",crowdfundingFeignClient.getCfInfoExtByCaseId(caseId));
        if (cfInfoExtFeignResponse == null) {
            return Lists.newArrayList();
        }
        CfInfoExt ext = cfInfoExtFeignResponse.getData();
        HashSet<String> mobiles = Sets.newHashSet();
        // 收款人手机号
        if (StringUtils.isNotBlank(info.getPayeeMobile())) {
            mobiles.add(info.getPayeeMobile());
        }
        // 发起人手机号
        UserInfoModel userInfoModel = dataService.getUserInfoByUserId(info.getUserId());
        if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile()) &&
                !Objects.equals(userInfoModel.getCryptoMobile(), info.getPayeeMobile())) {
            mobiles.add(userInfoModel.getCryptoMobile());
        }

        // 线下顾问
        String volunteerUniqueCode = ext.getVolunteerUniqueCode();
        if (StringUtils.isNotBlank(volunteerUniqueCode)) {
            Response<CrowdfundingVolunteer> cfVolunteerDoByUniqueCode = handleFeignResponse("cfGrowthtoolVolunteerFeignClient.getCfVolunteerDOByUniqueCode",
                    cfGrowthtoolVolunteerFeignClient.getCfVolunteerDOByUniqueCode(volunteerUniqueCode));
            if (cfVolunteerDoByUniqueCode.getData()!=null) {
                mobiles.add(cfVolunteerDoByUniqueCode.getData().getMobile());
            }
        }
        // 紧急联系人
        Response<String> payee = handleFeignResponse("crowdfundingChaiFenFeignClient.getCrowdfundingInfoPayeeByInfoUuid",
                crowdfundingChaiFenFeignClient.getCrowdfundingInfoPayeeByInfoUuid(info.getInfoId()));
        if (payee != null && StringUtils.isNotBlank(payee.getData())) {
            CrowdfundingInfoPayee crowdfundingInfoPayee = JSON.parseObject(payee.getData(), CrowdfundingInfoPayee.class);
            if (crowdfundingInfoPayee!=null) mobiles.add(crowdfundingInfoPayee.getEmergencyPhone());
        }

        // 代录入手机号
        Response<PreposeMaterialModel.MaterialInfoVo> response = handleFeignResponse("clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC",
                clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(caseId));
        if (response.getData() != null){
            mobiles.add(SpringUtil.getBean(OldShuidiCipher.class).aesEncrypt(response.getData().getRaiseMobile()));
        }
        return selectCallRecords(workOrderVO, userId, workOrderId, caseId, Lists.newArrayList(mobiles));
    }

    public List<WorkOrderRecordingModel.CallRecordModel> selectCallRecords(WorkOrderVO workOrder,int userId, long workOrderId, int caseId, List<String> mobiles) {

        List<WorkOrderRecordingModel.CallRecordModel> callRecordModels = Lists.newArrayList();
        Response<AuthUserDto> validAuthUserById = handleFeignResponse("userFeignClient.getValidAuthUserById",userFeignClient.getValidAuthUserById(Long.valueOf(userId)));
        String misName = Optional.ofNullable(validAuthUserById.getData()).map(AuthUserDto::getLoginName).orElse("");
        long taskAssignTime = workOrder.getHandleTime().getTime();
        Date finishTime = new DateTime(workOrder.getFinishTime() == null ? workOrder.getUpdateTime() : workOrder.getFinishTime())
                .plusMinutes(10).toDate();
        List<CfClewCallRecordsDO> recordDos = selectCallRecords(misName, taskAssignTime, mobiles);
        for (CfClewCallRecordsDO recordsDO : recordDos) {
            if (Math.toIntExact(recordsDO.getCaseId()) != caseId || !isInRange(finishTime, recordsDO.getCreateTime())) {
                continue;
            }
//            Integer phoneStatus = recordsDO.getPhoneStatus();
//            long realTotalDuration = 0;
//            if (phoneStatus == 200) {
//                realTotalDuration = (recordsDO.getCnoEndTime().getTime() - recordsDO.getAnswerTime().getTime()) / 1000;
//            }
            callRecordModels.add(WorkOrderRecordingModel.CallRecordModel.createByClewCallRecordDO(recordsDO));
        }
        return callRecordModels;
    }

    private List<CfClewCallRecordsDO> selectCallRecords(String mis, long taskAssignTime, List<String> mobiles) {

        List<CfClewCallRecordsDO> allResult = Lists.newArrayList();
        if (taskAssignTime == 0) {
            return allResult;
        }

        for (String mobile : mobiles) {
            Response<List<CfClewCallRecordsDO>> raiseRecords = clewtrackTaskClient.listCallRecordByMisAndPhone(mis,
                    mobile, null, taskAssignTime);
            if (raiseRecords != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(raiseRecords.getData())) {
                allResult.addAll(raiseRecords.getData());
            }
        }

        return allResult;
    }
    private boolean isInRange(Date finishTime, Date phoneTime) {
        return finishTime == null || phoneTime == null || finishTime.after(phoneTime);
    }

    @Override
    public AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum() {
        return AiAsrDelegate.HandleTypeEnum.HANDLE_MATERIAL_QC;
    }

    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.qc_material_audit;
    }
}
