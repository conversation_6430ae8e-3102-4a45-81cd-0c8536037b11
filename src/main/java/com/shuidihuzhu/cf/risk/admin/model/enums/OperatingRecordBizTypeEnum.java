package com.shuidihuzhu.cf.risk.admin.model.enums;

import lombok.Getter;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-29 15:33
 **/
@Getter
public enum OperatingRecordBizTypeEnum {
    TYPE_1(1, "增信填写极值判断"),
    TYPE_2(2, "资金用途极值判断"),
    ;
    private int code;
    private String desc;

    OperatingRecordBizTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
