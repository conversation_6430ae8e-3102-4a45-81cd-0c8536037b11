package com.shuidihuzhu.cf.risk.admin.model.vo.hit;

import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleActionEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleStatusEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/21 14:34
 */
@Data
public class StrategyHitRecordVo {

    public StrategyHitRecordVo(RiskStrategyHitRecord riskStrategyHitRecord, List<BlacklistVerifyDto> verifyDtos, String hitInfo){
        this.id = riskStrategyHitRecord.getId();
        this.caseId = riskStrategyHitRecord.getCaseId();
        this.operateName = riskStrategyHitRecord.getOperateName();
        this.hitPhase = BlacklistCallPhaseEnum.fromCode(riskStrategyHitRecord.getHitPhase()).getDesc();
        this.hitPhaseCode = riskStrategyHitRecord.getHitPhase();
        this.riskStrategy = RiskStrategyEnum.fromCode(riskStrategyHitRecord.getRiskStrategy()).getDesc();
        this.riskStrategyCode = riskStrategyHitRecord.getRiskStrategy();
        this.secondStrategy = RiskStrategySecondEnum.fromCode(riskStrategyHitRecord.getSecondStrategy()).getDesc();
        this.secondStrategyCode = riskStrategyHitRecord.getSecondStrategy();
        this.launchTime = DateUtil.getDate2LStr(riskStrategyHitRecord.getLaunchTime());
        this.status = RiskHandleStatusEnum.fromCode(riskStrategyHitRecord.getStatus()).getDesc();
        this.statusCode = riskStrategyHitRecord.getStatus();
        this.result = RiskHandleResultEnum.fromCode(riskStrategyHitRecord.getResult()).getDesc();
        this.handleCount = riskStrategyHitRecord.getHandleCount();
        this.updateCount = riskStrategyHitRecord.getUpdateCount();
        this.action = Joiner.on("、").join(RiskHandleActionEnum.codes2Names(riskStrategyHitRecord.getAction()));
        this.hitInfo = hitInfo;
        this.createTime = DateUtil.getDate2LStr(riskStrategyHitRecord.getCreateTime());
        this.updateTime = "";
        if (riskStrategyHitRecord.getStatus() != RiskHandleStatusEnum.PENDING.getCode()) {
            this.updateTime = DateUtil.getDate2LStr(riskStrategyHitRecord.getUpdateTime());
        }
        this.limitAction = Joiner.on("、").join(
                LimitActionEnum.ids2Names(verifyDtos.stream().flatMap(bv -> bv.getLimitActionIds().stream())
                        .collect(Collectors.toSet()))
        );
    }

    /**
     * 主键
     */
    private Long id;

    /**
     * 案例id
     */
    private Integer caseId;

    /**
     * 案例发起时间
     */
    private String launchTime;

    /**
     * 命中时机,BlacklistCallPhaseEnum
     */
    private String hitPhase;

    /**
     * 命中时机code
     */
    private Integer hitPhaseCode;

    /**
     * 命中策略,RiskStrategyEnum
     */
    private String riskStrategy;

    /**
     * 命中策略,RiskStrategyEnum
     */
    private Integer riskStrategyCode;

    /**
     * 命中二级策略，RiskStrategySecondEnum
     */
    private String secondStrategy;

    /**
     * 命中二级策略，RiskStrategySecondEnum
     */
    private Integer secondStrategyCode;

    /**
     * 限制动作
     */
    private String limitAction;

    /**
     * 处理状态，RiskHandleStatusEnum
     */
    private String status;

    /**
     * 处理状态code
     */
    @ApiModelProperty("处理状态，2 已完成")
    private Integer statusCode;

    /**
     * 风险核实结果，RiskHandleResultEnum
     */
    private String result;

    /**
     * 处理动作，RiskHandleActionEnum
     */
    private String action;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 处理次数
     */
    private Integer handleCount;

    @ApiModelProperty("风控处理结果修改次数")
    private Byte updateCount;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 命中详情
     */
    @ApiModelProperty("被命中的信息类型及详细")
    private String hitInfo;

}
