package com.shuidihuzhu.cf.risk.admin.service;

import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcLogBiz;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcLog;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/17
 */
@Service
@Slf4j
public class RiskQcLogService {

    @Autowired
    private RiskQcLogBiz riskQcLogBiz;
    @Autowired
    private SeaAccountService seaAccountService;


    public void addLog(RiskQcOperationTypeEnum riskQcOperationTypeEnum, long workOrderId, String operationLog) {
        long adminUserId  = ContextUtil.getAdminLongUserId();
        String operation = seaAccountService.getOrganization(adminUserId) + seaAccountService.getName(adminUserId);
        riskQcLogBiz.addLog(new RiskQcLog(adminUserId, operation, operationLog, riskQcOperationTypeEnum.getType(), workOrderId));
    }

    public List<RiskQcLog> getLogs(long workOrderId, List<RiskQcOperationTypeEnum> riskQcOperationTypeEnums) {
        List<Integer> types = RiskQcOperationTypeEnum.getTypeByEnum(riskQcOperationTypeEnums);
        return riskQcLogBiz.getByWorkOrderId(workOrderId, types);
    }

    public void addAppealLog(RiskQcOperationTypeEnum riskQcOperationTypeEnum, long workOrderId,
                             String operation, String operationLog) {
        riskQcLogBiz.addLog(new RiskQcLog(0, operation, operationLog, riskQcOperationTypeEnum.getType(), workOrderId));
    }


}
