package com.shuidihuzhu.cf.risk.admin.service.list;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.cf.risk.admin.dao.list.RiskCityAreaDao;
import com.shuidihuzhu.cf.risk.admin.dao.list.RiskListDepartmentCaseDao;
import com.shuidihuzhu.cf.risk.admin.dao.list.RiskListDepartmentDao;
import com.shuidihuzhu.cf.risk.admin.dao.list.RiskListUpdateRecordDao;
import com.shuidihuzhu.cf.risk.admin.model.enums.list.ListTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskCityArea;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartment;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartmentCase;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListUpdateRecord;
import com.shuidihuzhu.cf.risk.admin.model.query.list.ListDepartmentCaseQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.list.ListDepartmentBase;
import com.shuidihuzhu.cf.risk.admin.model.vo.list.ListDepartmentCaseInfo;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.cf.risk.model.admin.list.HospitalAuditPassDto;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingChaiFenV2FeignClient;
import com.shuidihuzhu.common.util.BeanUtils;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/28 20:00
 */
@Validated
@Service
@Slf4j
public class ListDepartmentCaseService {

    private static final String UPDATE_LEVEL_CONF_KEY = "cf_risk_admin_list_department_case_";
    private static final long UPDATE_LEVEL_CONF_KEY_LEAVE_TIME = 10 * 1000;

    @Resource
    private RiskListDepartmentCaseDao departmentCaseDao;
    @Resource
    private RiskListDepartmentDao listDepartmentDao;
    @Resource
    private RiskListUpdateRecordDao listUpdateRecordDao;
    @Resource
    private RiskCityAreaDao riskCityAreaDao;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private CfFirstApproveFeignClient firstApproveFeignClient;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Resource
    private ListDepartmentService listDepartmentService;
    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private CrowdfundingChaiFenV2FeignClient chaiFenV2FeignClient;

    public void hospitalAuditPassHandle(@Valid HospitalAuditPassDto hospitalAuditPassDto) {
        Integer caseId = hospitalAuditPassDto.getCaseId();
        FeignResponse<CrowdfundingInfo> crowdfundingResp = crowdfundingFeignClient.getCaseInfoById(caseId);
        if (crowdfundingResp.notOk()) {
            throw new RuntimeException("query crowdfunding fail, caseId:"+caseId+", resp:"+crowdfundingResp);
        }
        FeignResponse<CfFirsApproveMaterial> firstApproveResp = firstApproveFeignClient.getCfFirstApproveMaterialByCaseId(caseId);
        if (firstApproveResp.notOk()) {
            throw new RuntimeException("query first approve fail, caseId:"+caseId+", resp:"+firstApproveResp);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingResp.getData();
        CfFirsApproveMaterial firstApprove = firstApproveResp.getData();
        CrowdfundingInfoHospitalPayee hospitalPayee = null;

        if (crowdfundingInfo.getRelationType() == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT) {
            Response<String> resp = chaiFenV2FeignClient.getCrowdfundingInfoHospitalPayeeByInfoUuid(crowdfundingInfo.getInfoId());
            if (resp.notOk()) {
                throw new RuntimeException("查询案例("+crowdfundingInfo.getInfoId()+")对公打款信息失败");
            }
            hospitalPayee = JSON.parseObject(resp.getData(), CrowdfundingInfoHospitalPayee.class);
        }

        UserInfoModel userInfoModel = userInfoDelegateService.getUserInfoByUserId(crowdfundingInfo.getUserId());
        if (userInfoModel == null) {
            throw new RuntimeException("query userInfoModel fail, userId:"+crowdfundingInfo.getUserId()+", resp:null");
        }

        String key;
        String identify = null;
        for (HospitalAuditPassDto.LandlineNumber landlineNumber : hospitalAuditPassDto.getLandlineNumber()) {
            String areaCode = landlineNumber.getAreaCode();
            String landline = landlineNumber.getLandline();
            String extension = landlineNumber.getExtension();
            key = UPDATE_LEVEL_CONF_KEY + "_" + areaCode + "_" + landline + "_" + extension;
            try {
                identify = redissonHandler.tryLock(key, UPDATE_LEVEL_CONF_KEY_LEAVE_TIME);
                if (StringUtils.isBlank(identify)) {
                    throw new RuntimeException("get lock fail");
                }
                //1. 座机号和名单匹配，更新名单+保存调用记录
                saveUpdate(hospitalAuditPassDto, firstApprove, landlineNumber);
                //2. 查询是否已经添加，否，记录座机号提交过的案例信息
                RiskListDepartmentCase departmentCase = departmentCaseDao.getByUniqueTelAndCaseId(
                        areaCode, landline, extension, caseId);
                if (departmentCase == null) {
                    departmentCaseDao.insertSelective(assembleDepartmentTelCase(crowdfundingInfo, firstApprove,
                            hospitalAuditPassDto, landlineNumber, userInfoModel, hospitalPayee));
                }
            } catch (InterruptedException e) {
                log.error("", e);
            } finally {
                if (StringUtils.isNotBlank(identify)) {
                    try {
                        redissonHandler.unLock(key, identify);
                    } catch (Exception e) {
                        log.error("", e);
                    }
                }
            }
        }
    }

    public List<ListDepartmentCaseInfo> queryLandlineNumberCases(ListDepartmentCaseQuery caseQuery){
        caseQuery.setExtension(Strings.nullToEmpty(caseQuery.getExtension()));
        List<RiskListDepartmentCase> departmentCases = departmentCaseDao.listByUniqueTelLimit100(caseQuery.getAreaCode(),
                caseQuery.getLandline(), caseQuery.getExtension());
        return departmentCases.stream().map(listDepartmentCase -> {
            ListDepartmentCaseInfo listDepartmentCaseInfo = new ListDepartmentCaseInfo();
            BeanUtils.copyProperties(listDepartmentCase, listDepartmentCaseInfo);
            listDepartmentCaseInfo.setLaunchTime(DateUtil.getDate2LStr(listDepartmentCase.getLaunchTime()));
            listDepartmentCaseInfo.setMobile(StringUtils.isNotBlank(listDepartmentCase.getMobile())
                    ? shuidiCipher.decryptAndDesensitize(listDepartmentCase.getMobile(), DesensitizeEnum.MOBILE)
                    : "");
            listDepartmentCaseInfo.setIdCard(shuidiCipher.decryptAndDesensitize(listDepartmentCase.getIdCard(), DesensitizeEnum.IDCARD));
            listDepartmentCaseInfo.setPatientIdType(listDepartmentCase.getPatientIdType() == 1 ? "身份证号" : "出生证号");
            listDepartmentCaseInfo.setPatientIdNumber(StringUtils.isNotBlank(listDepartmentCase.getPatientIdNumber())
                    ? listDepartmentCase.getPatientIdType() == 1
                    ? shuidiCipher.decryptAndDesensitize(listDepartmentCase.getPatientIdNumber(), DesensitizeEnum.IDCARD)
                    : shuidiCipher.decryptAndDesensitize(shuidiCipher.encrypt(listDepartmentCase.getPatientIdNumber()), DesensitizeEnum.IDCARD)
                    : "");
            listDepartmentCaseInfo.setPayeePhone(StringUtils.isNotBlank(listDepartmentCase.getPayeePhone())
                            ? shuidiCipher.decryptAndDesensitize(listDepartmentCase.getPayeePhone(), DesensitizeEnum.MOBILE)
                            : "");
            listDepartmentCaseInfo.setPayeeIdCard(StringUtils.isNotBlank(listDepartmentCase.getPayeeIdCard())
                    ? shuidiCipher.decryptAndDesensitize(listDepartmentCase.getPayeeIdCard(), DesensitizeEnum.IDCARD)
                    : "");
            listDepartmentCaseInfo.setHospitalBankCard(StringUtils.isNotBlank(listDepartmentCase.getHospitalBankCard())
                    ? shuidiCipher.decryptAndDesensitize(listDepartmentCase.getHospitalBankCard(), DesensitizeEnum.BANKNO)
                    : "");
            return listDepartmentCaseInfo;
        }).collect(Collectors.toList());
    }

    private void saveUpdate(HospitalAuditPassDto hospitalAuditPassDto, CfFirsApproveMaterial firstApprove,
                            HospitalAuditPassDto.LandlineNumber landlineNumber){
        RiskListDepartment listDepartment = listDepartmentDao.getByUniqueTel(
                landlineNumber.getAreaCode(), landlineNumber.getLandline(), landlineNumber.getExtension());
        if (listDepartment == null) {
            ListDepartmentBase listDepartmentBase = new ListDepartmentBase();
            BeanUtils.copyProperties(hospitalAuditPassDto, listDepartmentBase);
            BeanUtils.copyProperties(landlineNumber, listDepartmentBase);
            listDepartmentBase.setListType(ListTypeEnum.GREYLIST.getCode());
            listDepartmentService.save(listDepartmentBase, 0L, "系统");
        }
        //保存调用记录
        RiskCityArea area = riskCityAreaDao.getByProvinceCity(hospitalAuditPassDto.getProvince(), hospitalAuditPassDto.getCity());
        RiskListUpdateRecord listUpdateRecord = new RiskListUpdateRecord();
        BeanUtils.copyProperties(hospitalAuditPassDto, listUpdateRecord);
        BeanUtils.copyProperties(landlineNumber, listUpdateRecord);
        listUpdateRecord.setCityAreaCode(area == null ? "" : area.getAreaCode());
        listUpdateRecord.setCityAreaCodeFormer(area == null ? "" : area.getAreaCodeFormer());
        listUpdateRecord.setPatientName(firstApprove.getPatientRealName());
        //与已有黑白名单匹配：1；与已有灰名单匹配：3；没有匹配的：2
        listUpdateRecord.setResult(listDepartment != null
                ? listDepartment.getListType() == ListTypeEnum.GREYLIST.getCode() ? "3" : "1"
                : "2");
        listUpdateRecordDao.insertSelective(listUpdateRecord);
    }

    private RiskListDepartmentCase assembleDepartmentTelCase(CrowdfundingInfo crowdfundingInfo,
                                                             CfFirsApproveMaterial firstApprove, HospitalAuditPassDto hospitalAuditPassDto,
                                                             HospitalAuditPassDto.LandlineNumber landlineNumber, UserInfoModel userInfoModel,
                                                             CrowdfundingInfoHospitalPayee hospitalPayee){
        RiskListDepartmentCase riskListDepartmentCase = new RiskListDepartmentCase();
        BeanUtils.copyProperties(hospitalAuditPassDto, riskListDepartmentCase);
        BeanUtils.copyProperties(landlineNumber, riskListDepartmentCase);
        riskListDepartmentCase.setLaunchTime(crowdfundingInfo.getCreateTime());
        riskListDepartmentCase.setRelationType((byte) CrowdfundingRelationType.getCode(crowdfundingInfo.getRelationType()));
        riskListDepartmentCase.setMobile(userInfoModel.getCryptoMobile());
        riskListDepartmentCase.setName(firstApprove.getUserRelationType() == 1
                ? firstApprove.getPatientRealName()
                : firstApprove.getSelfRealName());
        riskListDepartmentCase.setIdCard(firstApprove.getUserRelationType() == 1
                ? firstApprove.getPatientCryptoIdcard()
                : firstApprove.getSelfCryptoIdcard());
        riskListDepartmentCase.setPatientIdType(firstApprove.getPatientIdType());
        riskListDepartmentCase.setPatientName(firstApprove.getPatientRealName());
        riskListDepartmentCase.setPatientIdNumber(
                firstApprove.getPatientIdType() == PreposeMaterialModel.PatientIdCardTypeEnum.IDENTITY_CARD.getCode()
                        ? firstApprove.getPatientCryptoIdcard()
                        : firstApprove.getPatientBornCard());
        riskListDepartmentCase.setPayeePhone(crowdfundingInfo.getPayeeMobile());
        riskListDepartmentCase.setPayeeName(crowdfundingInfo.getPayeeName());
        riskListDepartmentCase.setPayeeIdCard(crowdfundingInfo.getPayeeIdCard());
        if (hospitalPayee != null) {
            riskListDepartmentCase.setHospitalNum(hospitalPayee.getHospitalizationNum());
            riskListDepartmentCase.setHospitalAccountName(hospitalPayee.getHospitalAccountName());
            riskListDepartmentCase.setHospitalBankBranch(hospitalPayee.getHospitalBankBranchName());
            riskListDepartmentCase.setBedNum(hospitalPayee.getBedNum());
            riskListDepartmentCase.setHospitalBankCard(StringUtils.isNotBlank(hospitalPayee.getHospitalBankCard())
                    ? oldShuidiCipher.aesEncrypt(hospitalPayee.getHospitalBankCard())
                    : "");
            riskListDepartmentCase.setPayeeDepartment(hospitalPayee.getDepartment());
        }
        return riskListDepartmentCase;
    }

}
