package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;

import java.util.List;

public interface RiskQcMaterialsInfoBiz {

    int addMaterials(RiskQcMaterialsInfo riskQcMaterialsInfo);

    int addMaterials(List<RiskQcMaterialsInfo> riskQcMaterialsInfos);

    List<RiskQcMaterialsInfo> getMaterials(long qcId, String materialsKey);

    RiskQcMaterialsInfo getById(long id);

    int updateById(long id, String value);

    int updateSnapshot(long qcId, String materialsKey, String materialsValue);

}
