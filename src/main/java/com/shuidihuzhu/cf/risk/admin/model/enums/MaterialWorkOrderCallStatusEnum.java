package com.shuidihuzhu.cf.risk.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum MaterialWorkOrderCallStatusEnum {

    NOT_CALL(1, "无通话记录"),
    EFFECTIVE_CALL(2, "有有效通话记录"),
    INVALID_CALL(3, "仅包含无效通话记录"),
    ;

    @Getter
    private int code;
    @Getter
    private String desc;

    public static String findDesc(int code) {
        for (MaterialWorkOrderCallStatusEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return "";
    }
}
