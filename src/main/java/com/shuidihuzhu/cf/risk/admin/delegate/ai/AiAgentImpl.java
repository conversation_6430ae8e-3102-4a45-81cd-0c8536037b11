package com.shuidihuzhu.cf.risk.admin.delegate.ai;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.alps.feign.ocean.OceanAsynApiResponse;
import com.shuidihuzhu.common.web.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class AiAgentImpl implements AiAgent {

    @Autowired
    private OceanApiClient oceanApiClient;

    private AiConfig mAiConfig;

    private Map<String, Tag> mTagConfigMap;

    @Override
    public Response<OceanApiResponse> agent(@NotNull OceanApiRequest request, Response<OceanApiResponse> fallbackResp) {
        final AiHystrixCommand command = new AiHystrixCommand(Groups.AI_GROUP,
                getTimeout(request),
                () -> oceanApiClient.agent(request),
                () -> {
                    log.warn("ai ocean 降级 tag {}", request.getTag());
                    return fallbackResp;
                }
        );
        return command.execute();
    }

    @Override
    public Response<OceanAsynApiResponse> agentAsync(@NotNull OceanApiRequest request, Response<OceanAsynApiResponse> fallbackResp) {
        final AiAsyncHystrixCommand command = new AiAsyncHystrixCommand(Groups.AI_GROUP_ASYNC,
                getTimeout(request),
                () -> oceanApiClient.agentAsyn(request),
                () -> {
                    log.warn("ai ocean 降级 tag {}", request.getTag());
                    return fallbackResp;
                }
        );
        return command.execute();
    }


    @Value("${apollo.ai.ocean.hystrix.config:}")
    public void setAiConfig(String configJson) {
        log.info("AI请求配置变更 {}", configJson);
        mTagConfigMap = Maps.newHashMap();
        if (StringUtils.isEmpty(configJson)) {
            mAiConfig = new AiConfig();
            mAiConfig.setDefaultTimeoutInMilliseconds(2000);
            return;
        }
        mAiConfig = JSON.parseObject(configJson, AiConfig.class);
        if (mAiConfig == null) {
            return;
        }
        final List<Tag> tags = mAiConfig.getTags();
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }
        for (Tag tag : tags) {
            mTagConfigMap.put(tag.getName(), tag);
        }
    }

    @Data
    public static class AiConfig {
        private List<Tag> tags;
        private int defaultTimeoutInMilliseconds;
    }

    @Data
    public static class Tag {
        private String name;
        private int timeoutInMilliseconds;
    }

    public enum Groups implements HystrixCommandGroupKey {
        AI_GROUP,
        AI_GROUP_ASYNC,

    }

    public static class AiHystrixCommand extends AbstractAiHystrixCommand<Response<OceanApiResponse>> {
        protected AiHystrixCommand(HystrixCommandGroupKey group,
                                   int executionIsolationThreadTimeoutInMilliseconds,
                                   Supplier<Response<OceanApiResponse>> supplier,
                                   Supplier<Response<OceanApiResponse>> fallbackSupplier) {
            super(group, executionIsolationThreadTimeoutInMilliseconds, supplier, fallbackSupplier);
        }
    }

    public static class AiAsyncHystrixCommand extends AbstractAiHystrixCommand<Response<OceanAsynApiResponse>> {
        protected AiAsyncHystrixCommand(HystrixCommandGroupKey group,
                                        int executionIsolationThreadTimeoutInMilliseconds,
                                        Supplier<Response<OceanAsynApiResponse>> supplier,
                                        Supplier<Response<OceanAsynApiResponse>> fallbackSupplier) {
            super(group, executionIsolationThreadTimeoutInMilliseconds, supplier, fallbackSupplier);
        }
    }

    public static class AbstractAiHystrixCommand<T> extends HystrixCommand<T> {

        private final Supplier<T> supplier;
        private final Supplier<T> fallbackSupplier;

        protected AbstractAiHystrixCommand(HystrixCommandGroupKey group,
                                           int executionIsolationThreadTimeoutInMilliseconds,
                                           @NonNull Supplier<T> supplier,
                                           @NonNull Supplier<T> fallbackSupplier
        ) {
            super(group, executionIsolationThreadTimeoutInMilliseconds);
            this.supplier = supplier;
            this.fallbackSupplier = fallbackSupplier;
        }

        @Override
        protected T run() throws Exception {
            return supplier.get();
        }

        @Override
        protected T getFallback() {
            return fallbackSupplier.get();
        }
    }

    private int getTimeout(OceanApiRequest request) {
        final String tag = request.getTag();
        final Tag config = mTagConfigMap.get(tag);
        if (config == null) {
            return mAiConfig.getDefaultTimeoutInMilliseconds();
        }
        final int timeoutInMilliseconds = config.getTimeoutInMilliseconds();
        log.debug("获取到指定超时时间 {}", timeoutInMilliseconds);
        return timeoutInMilliseconds;
    }
}
