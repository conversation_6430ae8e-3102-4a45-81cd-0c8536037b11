package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;

import java.util.List;

public interface RiskQcSearchIndexBiz {

    int addSearchIndex(RiskQcSearchIndex riskQcSearchIndex, int workOrderType);

    int updateByWorkOrderId(long workOrderId, int qcResultId, String questionTypeIds, String firstPropertyIds, int qcResultSecond);

    RiskQcSearchIndex getByWorkOrderId(long workOrderId);

    List<RiskQcSearchIndex> getByWorkOrderIds(List<Long> workOrderIds);

    int updateServiceStageByTaskId(int taskId, long workOrderId, int serviceStage);

    int updateCaseIdAndUserId(long taskId, int qcType, long caseId, long userId);

    int updateRuleNameByWorkOrderId(long workOrderId, String ruleName);

    int addCallRiskQcSearchIndex(int orderType, RiskQcSearchIndex riskQcSearchIndex);

    int addMaterialRiskQcSearchIndex(int orderType, RiskQcSearchIndex riskQcSearchIndex);

    int updateOrgByWorkOrderId(long workOrderId, String org);

}
