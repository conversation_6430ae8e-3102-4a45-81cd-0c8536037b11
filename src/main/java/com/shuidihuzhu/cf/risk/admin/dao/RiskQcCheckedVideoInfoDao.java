package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcCheckedVideoInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/11/11
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcCheckedVideoInfoDao {
    int addInfo(@Param("workOrderId") long workOrderId, @Param("checkedId") String checkedId);


    RiskQcCheckedVideoInfo getByWorkOrderId(@Param("workOrderId") long workOrderId);

    int updateByWorkOrderId(@Param("checkId") String checkId, @Param("workOrderId") long workOrderId);


    List<RiskQcCheckedVideoInfo> findByWorkOrderId(@Param("workOrderIdList") List<Long> workOrderIdList);

    int add(RiskQcCheckedVideoInfo param);

    int update(RiskQcCheckedVideoInfo param);
}
