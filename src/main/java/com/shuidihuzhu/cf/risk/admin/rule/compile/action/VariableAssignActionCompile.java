package com.shuidihuzhu.cf.risk.admin.rule.compile.action;

import com.shuidihuzhu.cf.risk.admin.rule.compile.value.AbstractCompileChain;
import com.shuidihuzhu.cf.risk.admin.rule.compile.value.ValueCompileContext;
import com.shuidihuzhu.cf.risk.admin.rule.enums.ActionType;
import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionAction;
import com.shuidihuzhu.cf.risk.admin.rule.model.VariableAssign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/18 22:35
 */
@Primary
@Component
@Slf4j
public class VariableAssignActionCompile extends AbstractCompileChain<CriterionAction> {

    @Resource
    private ValueCompileContext valueCompileContext;

    @Override
    public String compileValue(CriterionAction action) {
        if (action == null || action.getActionType() == null) {
            return "";
        }
        if (action.getActionType() == ActionType.VARIABLE_ASSIGN) {
            VariableAssign variableAssign = action.getVariableAssign();
            return METHOD_VARIABLE_NAME + "." + variableAssign.getSourcePath() + "=" + valueCompileContext.spliceArithmetic(variableAssign.getValue());
        }

        return valueCompile.compileValue(action);
    }

    @Resource(name = "returnValActionCompile")
    @Override
    public void setValueCompile(AbstractCompileChain valueCompile) {
        this.valueCompile = valueCompile;
    }
}
