package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.alps.feign.config.OceanApiMqConfig;
import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcAsrResultVO;
import com.shuidihuzhu.cf.risk.admin.service.mdc.MdcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.recording.WorkOrderRecordingExtHandler;
import com.shuidihuzhu.cf.risk.admin.service.recording.WorkOrderRecordingHandlerRegister;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate.HandleTypeEnum.HANDLE_QC_BD;

/**
 * <AUTHOR>
 */
@Component
@RocketMQListener(id = "ai-asr_30007", tags = "ai-asr_30007", topic = OceanApiMqConfig.TOPIC)
@Slf4j
public class AsrShuidiCallbackConsumer extends MaliBaseMQConsumer<OceanApiMQResponse> implements MessageListener<OceanApiMQResponse> {

    @Autowired
    private QcAudioAsrService qcAudioAsrService;

    @Autowired
    private AiAsrDelegate aiAsrDelegate;

    @Autowired
    private WorkOrderRecordingHandlerRegister workOrderRecordingHandlerRegister;
    @Autowired
    private MdcAudioAsrService mdcAudioAsrService;

    @Override
    protected boolean handle(ConsumerMessage<OceanApiMQResponse> consumerMessage) {
        OceanApiMQResponse payload = consumerMessage.getPayload();
        log.info("AsrShuidiCallbackConsumer payload:{}", payload);
        QcAsrResultVO result = aiAsrDelegate.getResultByCallback(payload);
        if (result == null) {
            log.warn("AsrShuidiCallbackConsumer result null");
            return true;
        }
        if(result.getHandleTypeEnum() == null || !result.getHandleTypeEnum().equals(HANDLE_QC_BD)){
            log.info("AsrShuidiCallbackConsumer handleTypeEnum is null or not HANDLE_QC_BD");
            return true;
        }
        qcAudioAsrService.onAudioAsrShuidiCallback(result);
        return true;
    }


    @Override
    protected Logger getLogger() {
        return log;
    }
}
