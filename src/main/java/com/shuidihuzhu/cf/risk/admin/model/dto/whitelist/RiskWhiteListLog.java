package com.shuidihuzhu.cf.risk.admin.model.dto.whitelist;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskWhiteListLog {

    private long id;

    private long whiteListId;

    /**
     * @see com.shuidihuzhu.cf.risk.admin.model.enums.list.WhiteOperateTypeEnum
     */
    private int operateType;

    private long operateId;

    private String operateName;

    private String otherInfo;

    private Date createTime;


}
