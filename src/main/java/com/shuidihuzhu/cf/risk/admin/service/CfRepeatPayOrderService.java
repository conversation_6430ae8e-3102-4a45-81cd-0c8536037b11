package com.shuidihuzhu.cf.risk.admin.service;

import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleActionEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.RiskRecordRecordQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.StrategyHitContentVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.StrategyHitRecordVo;
import com.shuidihuzhu.cf.risk.admin.service.hit.RiskHitService;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyHitDto;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @Author: wangpeng
 * @Date: 2022/3/10 15:56
 * @Description:
 */
@Slf4j
@Service
public class CfRepeatPayOrderService {

    @Resource
    private RiskHitService riskHitService;

    public void check(List<Integer> repeatCaseIdList) {
        RiskRecordRecordQuery riskRecordRecordQuery = new RiskRecordRecordQuery();
        riskRecordRecordQuery.setHitPhase(BlacklistCallPhaseEnum.REPEAT_PAY_ORDER.getCode());
        Integer currentCaseId = repeatCaseIdList.get(repeatCaseIdList.size() - 1);
        for (Integer caseId : repeatCaseIdList) {
            riskRecordRecordQuery.setCaseId(caseId);
            if (caseId.equals(currentCaseId)) {
                continue;
            }
            try {
                StrategyHitContentVo strategyHitContentVo = riskHitService.queryRiskHitRecord(riskRecordRecordQuery);
                List<StrategyHitRecordVo> strategyHitRecordVos = Optional.ofNullable(strategyHitContentVo)
                        .map(StrategyHitContentVo::getPageResult)
                        .map(PageResult::getList)
                        .orElse(Collections.emptyList());
                if (CollectionUtils.isNotEmpty(strategyHitRecordVos)) {
                    StrategyHitRecordVo strategyHitRecordVo = strategyHitRecordVos.get(0);
                    StrategyHitDto strategyHitDto = new StrategyHitDto();
                    strategyHitDto.setCaseId(currentCaseId);
                    strategyHitDto.setHitPhase(strategyHitRecordVo.getHitPhaseCode());
                    strategyHitDto.setRiskStrategy(strategyHitRecordVo.getRiskStrategyCode());
                    strategyHitDto.setSecondStrategy(strategyHitRecordVo.getSecondStrategyCode());
                    strategyHitDto.setHitInfo(strategyHitRecordVo.getHitInfo());
                    strategyHitDto.setRepeatCaseId(caseId);
                    strategyHitDto.setAction(strategyHitRecordVo.getAction());
                    riskHitService.insertRepeatPayOrder(strategyHitDto);
                }
            } catch (Exception e) {
                log.info("CfRepeatPayOrderService check error {} {}", caseId, e);
            }
        }

    }
}
