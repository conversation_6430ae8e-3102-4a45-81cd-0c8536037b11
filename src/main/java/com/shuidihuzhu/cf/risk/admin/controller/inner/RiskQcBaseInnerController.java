package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcAssignRecordBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcAssignRecord;
import com.shuidihuzhu.cf.risk.admin.model.mapper.RiskQcSearchIndexVoMapper;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcVideoVo;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.client.admin.RiskQcBaseClient;
import com.shuidihuzhu.cf.risk.model.risk.RiskQcAssignRecordDto;
import com.shuidihuzhu.cf.risk.model.risk.RiskQcBaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.RiskQcSearchIndexVO;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.enums.MyErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-06-23 17:45
 **/

@RestController
public class RiskQcBaseInnerController implements RiskQcBaseClient {

    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private RiskQcAssignRecordBiz assignRecordBiz;

    @Override
    public Response<RiskQcBaseInfoVo> getQcInfo(long qcId) {
        if (qcId <= 0) {
            NewResponseUtil.makeSuccess(null);
        }
        RiskQcBaseInfo riskQcBaseInfo = riskQcBaseInfoBiz.getById(qcId);
        //获取组织id
        List<RiskQcMaterialsInfo> areaMaterialsInfoList = riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.ORG_ID.getKey());
        RiskQcMaterialsInfo areaMaterialsInfo = areaMaterialsInfoList.get(0);

        //获取录音
        List<RiskQcMaterialsInfo> materialsInfoList = riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.RECORDING.getKey());
        int sumDuration = materialsInfoList.stream().mapToInt(riskQcMaterialsInfo -> {
            String materialsValue = riskQcMaterialsInfo.getMaterialsValue();
            RiskQcVideoVo riskQcVideoVo = JSONObject.parseObject(materialsValue, RiskQcVideoVo.class);
            return riskQcVideoVo.getDuration();
        }).sum();

        RiskQcBaseInfoVo riskQcBaseInfoVo = new RiskQcBaseInfoVo();
        riskQcBaseInfoVo.setAreaId(Integer.parseInt(areaMaterialsInfo.getMaterialsValue()));
        riskQcBaseInfoVo.setQcByName(riskQcBaseInfo.getQcByName());
        riskQcBaseInfoVo.setQcId(riskQcBaseInfo.getId());
        riskQcBaseInfoVo.setDuration(sumDuration);
        riskQcBaseInfoVo.setQcUniqueCode(riskQcBaseInfo.getQcUniqueCode());
        return NewResponseUtil.makeSuccess(riskQcBaseInfoVo);
    }

    @Override
    public Response<Void> updateRuleName(long workOrderId, String ruleName) {
        riskQcSearchIndexBiz.updateRuleNameByWorkOrderId(workOrderId, ruleName);
        return NewResponseUtil.makeSuccess(null);
    }

    @Autowired
    private RiskQcSearchIndexVoMapper riskQcSearchIndexVoMapper;

    @Override
    public Response<RiskQcSearchIndexVO> getRiskQcSearchIndex(long workOrderId) {
        var riskQcSearchIndex = riskQcSearchIndexBiz.getByWorkOrderId(workOrderId);
        RiskQcSearchIndexVO riskQcSearchIndexVO = riskQcSearchIndexVoMapper.toVo(riskQcSearchIndex);
        return NewResponseUtil.makeSuccess(riskQcSearchIndexVO);
    }

    @Override
    public Response<Void> assignRecordAddList(List<RiskQcAssignRecordDto> assignRecordList) {
        if (CollectionUtils.isEmpty(assignRecordList)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        assignRecordBiz.addList(assignRecordList.stream().map(a -> {
            RiskQcAssignRecord record = new RiskQcAssignRecord();
            BeanUtils.copyProperties(a, record);
            return record;
        }).collect(Collectors.toList()));
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Integer> countAssignRecord(String uniqueCode, int orderType, String startTime, String endTime) {
        return NewResponseUtil.makeSuccess(assignRecordBiz.countByUniqueCode(uniqueCode, orderType, startTime, endTime));
    }

}
