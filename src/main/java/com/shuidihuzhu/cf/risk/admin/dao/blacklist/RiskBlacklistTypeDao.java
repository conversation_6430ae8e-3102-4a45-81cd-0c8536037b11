package com.shuidihuzhu.cf.risk.admin.dao.blacklist;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistType;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.BlacklistTypeQuery;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskBlacklistTypeDao {
    int insertSelective(RiskBlacklistType record);

    RiskBlacklistType selectByPrimaryKey(Long id);

    List<RiskBlacklistType> listByOptions(BlacklistTypeQuery typeQuery);

    List<RiskBlacklistType> listByIds(Collection<Long> ids);

    List<RiskBlacklistType> listByClassifyIds(List<Long> classifyId);

    List<RiskBlacklistType> listByEnabledTypeNames(Collection<String> typeNames);

    int updateStatusById(@Param("id") Long id, @Param("status") Byte status);
}