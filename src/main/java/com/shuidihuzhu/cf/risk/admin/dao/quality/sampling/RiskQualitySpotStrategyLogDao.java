package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategyLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQualitySpotStrategyLogDao {
    int insertSelective(RiskQualitySpotStrategyLog record);

    RiskQualitySpotStrategyLog selectByPrimaryKey(Long id);

    List<RiskQualitySpotStrategyLog> listByStrategyId(Long strategyId);
}