package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseOperationRecordBiz;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseOperationRecordDao;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseOperationRecord;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Service
public class RiskDiseaseOperationRecordBizImpl implements RiskDiseaseOperationRecordBiz {

    @Autowired
    private RiskDiseaseOperationRecordDao riskDiseaseOperationRecordDao;

    @Override
    public int save(RiskDiseaseOperationRecord operationRecord) {
        if (operationRecord == null ) {
            return 0;
        }
        return riskDiseaseOperationRecordDao.save(operationRecord);
    }

    @Override
    public List<RiskDiseaseOperationRecord> findListByDiseaseId(long diseaseId) {
        if (diseaseId <= 0) {
            return Lists.newArrayList();
        }
        return riskDiseaseOperationRecordDao.findListByDiseaseId(diseaseId);
    }

    @Override
    public RiskDiseaseOperationRecord getLastOneByDiseaseId(long diseaseId) {
        if (diseaseId <= 0) {
            return null;
        }
        return riskDiseaseOperationRecordDao.getLastOneByDiseaseId(diseaseId);
    }
}
