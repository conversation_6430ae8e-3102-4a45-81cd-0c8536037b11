package com.shuidihuzhu.cf.risk.admin.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/9/9
 */
public class DataFormatUtil {

    public static String dealSensitivePhone(String phone){
        if (StringUtils.isBlank(phone)){
            return "";
        }
        return StringUtils.substring(phone,0, 3) + "****" +  StringUtils.substring(phone, phone.length()-4, phone.length()) ;
    }

    public static String dealSensitiveIdCard(String idCard){
        if (StringUtils.isBlank(idCard)){
            return "";
        }
        return StringUtils.substring(idCard, 0, idCard.length() - 4) + "****";
    }
}
