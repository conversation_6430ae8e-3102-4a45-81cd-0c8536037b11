package com.shuidihuzhu.cf.risk.admin.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class GetInternalStaffAndEmergencyMobileDO {

    @ExcelIgnore
    @ApiModelProperty("主键")
    private long id;

    @ExcelProperty(index = 0)
    @ApiModelProperty("员工姓名")
    private String selfName;

    @ExcelProperty(index = 1)
    @ApiModelProperty("员工手机号")
    private String selfMobile;

    @ExcelProperty(index = 2)
    @ApiModelProperty("紧急联系人姓名")
    private String emergencyName;

    @ExcelProperty(index = 3)
    @ApiModelProperty("紧急联系人手机号")
    private String emergencyMobile;

    @ExcelProperty(index = 4)
    @ApiModelProperty("员工类型")
    private String type;

    @ExcelIgnore
    private Timestamp createTime;

    @ExcelIgnore
    private Timestamp updateTime;

    @ExcelIgnore
    private int isDelete;
}
