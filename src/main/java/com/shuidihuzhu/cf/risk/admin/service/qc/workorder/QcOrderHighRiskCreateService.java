package com.shuidihuzhu.cf.risk.admin.service.qc.workorder;

import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcmaterialService;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.qc.CallService;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotHighRiskService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QcOrderHighRiskCreateService {

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;
    @Resource
    private SeaAccountService seaAccountService;
    @Resource
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Resource
    private RiskQcLogService riskQcLogService;
    @Resource
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Resource
    private CallService callService;
    @Resource
    private RiskQcmaterialService riskQcmaterialService;
    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;
    @Resource
    private QualitySpotHighRiskService qualitySpotHighRiskService;
    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;

    public ConsumeStatus createHighRiskWorkOrder(QcWorkOrderCreateParam qcWorkOrderCreateParam) {

        Long oldWorkOrderId = qcWorkOrderCreateParam.getWorkOrderId();

        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(oldWorkOrderId);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(workOrderVO)) {
            return ConsumeStatus.RECONSUME_LATER;
        }

        long operatorId = workOrderVO.getOperatorId();
        int caseId = workOrderVO.getCaseId();

        //生成高风险质检工单
        String lockName = "qcLock_" + QcTypeEnum.INTERNAL_AUDIT.getCode() + "_" + oldWorkOrderId;
        String identifier = "";
        try {
            identifier = cfRiskRedissonHandler.tryLock(lockName, TimeUnit.SECONDS.toMillis(3), TimeUnit.SECONDS.toMillis(30));
            if (StringUtils.isBlank(identifier)) {
                return ConsumeStatus.RECONSUME_LATER;
            }

            //创建质检基本信息
            String qcByName = seaAccountService.getName(operatorId);
            RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
            riskQcBaseInfo.setCaseId(caseId);
            riskQcBaseInfo.setQcType(QcTypeEnum.INTERNAL_AUDIT.getCode());
            riskQcBaseInfo.setOrderType(WorkOrderType.qc_high_risk_quality_inspection.getType());
            riskQcBaseInfo.setQcUniqueCode(String.valueOf(operatorId));
            riskQcBaseInfo.setQcByName(qcByName);
            riskQcBaseInfoBiz.addQc(riskQcBaseInfo);

            WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
            workOrderCreateParam.setOrderType(WorkOrderType.qc_high_risk_quality_inspection.getType());
            workOrderCreateParam.setCaseId(caseId);
            workOrderCreateParam.setHandleResult(HandleResultEnum.not_auto_assign.getType());
            workOrderCreateParam.setOrderlevel(OrderLevel.C.getType());
            workOrderCreateParam.addExt(OrderExtName.qcId, riskQcBaseInfo.getId());
            workOrderCreateParam.addExt(OrderExtName.qcUserName, qcByName);
            workOrderCreateParam.addExt(OrderExtName.qcAssignType, AssignTypeEnum.ASSIGN.getCode());
            Response<Long> newResponse = workOrderCoreFeignClient.create(workOrderCreateParam);
            long workOrderId = Optional.ofNullable(newResponse).filter(Response::ok).map(Response::getData).orElse(0L);
            if (workOrderId != 0L) {
                //调用质检操作记录接口，记录操作记录
                String content = "生成高风险质检工单,工单ID【" + workOrderId + "】";
                riskQcLogService.addLog(RiskQcOperationTypeEnum.CREATE_HIGH_RISK_ORDER, workOrderId, content);

                // 保存快照
//                boolean snapshotSuccess = riskQcmaterialService.saveSnapshotWithRetryV2(workOrderId, riskQcBaseInfo.getId(), oldWorkOrderId, 0);

                //获取通话状态
                int callStatus = callService.getCallStatus(oldWorkOrderId);

                // 添加搜索索引字段聚合表
                String organization = seaAccountService.getOrganization(operatorId);
                RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
                riskQcSearchIndex.setCaseId(caseId);
                riskQcSearchIndex.setQcId(riskQcBaseInfo.getId());
                riskQcSearchIndex.setWorkOrderId(workOrderId);
                riskQcSearchIndex.setQcType(QcTypeEnum.INTERNAL_AUDIT.getCode());
                riskQcSearchIndex.setOrganization(organization);
                riskQcSearchIndex.setQcUniqueCode(String.valueOf(operatorId));
                riskQcSearchIndex.setQcByName(qcByName);
                riskQcSearchIndex.setCallStatus(callStatus);
                riskQcSearchIndex.setHandleResult(workOrderVO.getHandleResult());
                riskQcSearchIndex.setSourceWorkOrderId(oldWorkOrderId);
                riskQcSearchIndex.setSourceWorkOrderType(workOrderVO.getOrderType());

                FeignResponse<CrowdfundingInfo> infoFeignResponse = crowdfundingFeignClient.getCaseInfoById(caseId);
                CrowdfundingInfo crowdfundingInfo = Optional.ofNullable(infoFeignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
                if (Objects.nonNull(crowdfundingInfo)) {
                    riskQcSearchIndex.setUserId(crowdfundingInfo.getUserId());
                }

                riskQcSearchIndexBiz.addMaterialRiskQcSearchIndex(riskQcBaseInfo.getOrderType(), riskQcSearchIndex);

//                if (snapshotSuccess) {
                    qualitySpotHighRiskService.spotHighRisk(workOrderId, riskQcBaseInfo, workOrderVO, callStatus);
//                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        } catch (Exception e) {
            log.error("QcCallWorkOrderCreateConsumer.consumeMessage error", e);
        } finally {
            if (StringUtils.isNotEmpty(identifier)) {
                cfRiskRedissonHandler.unLock(lockName, identifier);
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
