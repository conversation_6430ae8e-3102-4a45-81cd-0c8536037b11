package com.shuidihuzhu.cf.risk.admin.controller;

import com.shuidihuzhu.cf.risk.admin.model.vo.MarkReportVO;
import com.shuidihuzhu.cf.risk.admin.service.ReportService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/cf-risk-admin/report", method = {RequestMethod.POST})
public class PsReportController {

    @Autowired
    private ReportService reportService;

    @RequestMapping("/mark-report")
    public Response markReport(@RequestBody MarkReportVO markReportVO){
        return NewResponseUtil.makeSuccess(reportService.markReport(markReportVO));
    }
}
