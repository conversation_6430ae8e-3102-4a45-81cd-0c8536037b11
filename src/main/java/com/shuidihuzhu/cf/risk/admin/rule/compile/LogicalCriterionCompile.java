package com.shuidihuzhu.cf.risk.admin.rule.compile;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.rule.model.Criterion;
import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/18 23:21
 */
@Service
@Slf4j
public class LogicalCriterionCompile implements ICriterionCompile<CriterionGroup>{

    @Resource
    private RelationalCriterionCompile relationalCriterionCompile;

    @Override
    public String compileCriterion(CriterionGroup criterionGroup) {
        List<String> logicalResult = Lists.newArrayList();
        for (Criterion criterion : criterionGroup.getCriterions()) {
            logicalResult.add("(" + relationalCriterionCompile.compileCriterion(criterion) + ")");
        }
        return Joiner.on(criterionGroup.getJunctionType().getScript()).join(logicalResult);
    }
}
