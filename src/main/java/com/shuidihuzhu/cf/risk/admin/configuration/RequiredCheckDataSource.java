package com.shuidihuzhu.cf.risk.admin.configuration;

import com.google.common.collect.Sets;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.IRequiredCheckDataSourceService;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class RequiredCheckDataSource implements IRequiredCheckDataSourceService {

    private static final String RISK_REDIS = "cfRiskRedissonHandler";

    public static Set<String> requiredDBList(){
        return Sets.newHashSet(RiskAdminDS.CF_RISK_DATASOURCE);
    }
    public static Set<String> requiredRedisList(){
        return Sets.newHashSet(RISK_REDIS);
    }

    @Override
    public Set<String> requiredDbDataSource() {
        return requiredDBList();
    }

    @Override
    public Set<String> requiredRedissonDataSource() {
        return requiredRedisList();
    }
}
