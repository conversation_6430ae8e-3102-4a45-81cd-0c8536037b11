package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.util.CosUploadUtil;
import com.shuidihuzhu.client.cf.admin.client.CfRecordClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: lixiaoshuang
 * @create: 2020-08-10 16:20
 **/
@Service
@RocketMQListener(id = RiskMQTagCons.QC_MATERIAL_UPDATE_SNAPSHOT,
        tags = RiskMQTagCons.QC_MATERIAL_UPDATE_SNAPSHOT,
        topic = MQTopicCons.CF)
@Slf4j
public class QcMaterialUpdateSnapshotConsumer implements MessageListener<WorkOrderVO> {


    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private CfRecordClient cfRecordClient;
    @Autowired
    private CosUploadUtil cosUploadUtil;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WorkOrderVO> mqMessage) {
        WorkOrderVO workOrderVO = mqMessage.getPayload();
        log.info("QcMaterialUpdateSnapshotConsumer workOrderVO:{}", JSONObject.toJSONString(workOrderVO.toString()));
        if (Objects.isNull(workOrderVO)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Response<String> stringResponse = cfRecordClient.selectHandleSnapshot(workOrderVO.getCaseId(), workOrderVO.getWorkOrderId());
        if (stringResponse.notOk() || StringUtils.isEmpty(stringResponse.getData())) {
            return ConsumeStatus.RECONSUME_LATER;
        }
        String snapshot = stringResponse.getData();
        if (StringUtils.isNotBlank(snapshot)) {
            snapshot = cosUploadUtil.uploadText(snapshot, null);
        }
        riskQcMaterialsInfoBiz.updateSnapshot(workOrderVO.getQcId(), QcMaterialsKeyEnum.DETAIL_SNAPSHOT.getKey(), snapshot);
        return ConsumeStatus.CONSUME_SUCCESS;
    }


}
