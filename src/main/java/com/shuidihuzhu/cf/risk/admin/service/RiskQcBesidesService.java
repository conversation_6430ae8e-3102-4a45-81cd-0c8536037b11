package com.shuidihuzhu.cf.risk.admin.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.model.qcBesides.RiskQcBesidesTaskDetail;
import com.shuidihuzhu.cf.risk.admin.model.qcBesides.RiskQcBesidesTaskInfo;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskModel;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Auther: subing
 * @Date: 2020/9/10
 */
@Service
@Slf4j
public class RiskQcBesidesService {
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private MaskUtil maskUtil;

    public RiskQcBesidesTaskInfo getTaskInfo(long clewTaskId) {
        Response<List<CfClewTaskModel>> response = cfClewtrackTaskFeignClient.getClewTaskModel(Lists.newArrayList(clewTaskId));
        log.info("cfClewtrackTaskFeignClient getClewTaskModel response:{}", response);
        if (response != null && response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            CfClewTaskModel cfClewTaskModel = Optional.ofNullable(response.getData().get(0)).orElseGet(CfClewTaskModel::new);
            RiskQcBesidesTaskInfo riskQcBesidesTaskInfo = RiskQcBesidesTaskInfo.builder()
                    .allotDate(DateUtil.formatDateTime(cfClewTaskModel.getAssignTime()))
                    .channelName(cfClewTaskModel.getPrimaryChannelDesc())
                    .phoneMask(maskUtil.buildByDecryptPhone(cfClewTaskModel.getMobile()))
                    .clewId(cfClewTaskModel.getClewId() == null ? null : String.valueOf(cfClewTaskModel.getClewId()))
                    .status(cfClewTaskModel.getShowPageNameEnum() == null ? null : cfClewTaskModel.getShowPageNameEnum().getDesc())
                    .name(seaAccountService.getInfoByMis(cfClewTaskModel.getClewUserId()))
                    .workContent(cfClewTaskModel.getWorkContentTypeDesc())
                    .build();
            return riskQcBesidesTaskInfo;
        }
        return new RiskQcBesidesTaskInfo();
    }


    public RiskQcBesidesTaskDetail getTaskDetail(long clewTaskId) {
        Response<List<CfClewTaskModel>> response = cfClewtrackTaskFeignClient.getClewTaskModel(Lists.newArrayList(clewTaskId));
        log.info("cfClewtrackTaskFeignClient getClewTaskModel response:{}", response);
        RiskQcBesidesTaskDetail riskQcBesidesTaskDetail = new RiskQcBesidesTaskDetail();
        if (response != null && response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            CfClewTaskModel cfClewTaskModel = Optional.ofNullable(response.getData().get(0)).orElseGet(CfClewTaskModel::new);
            riskQcBesidesTaskDetail = RiskQcBesidesTaskDetail.builder()
                    .taskType(cfClewTaskModel.getTaskModeDesc())
                    .homeLocation(cfClewTaskModel.getPhoneAddress())
                    .userName(cfClewTaskModel.getClewUserName())
                    .phoneStatusCode(cfClewTaskModel.getPhoneStatusDesc())
                    .phoneStatus(cfClewTaskModel.getPhoneStatus())
                    .firstTagDesc(cfClewTaskModel.getFirstTagDesc())
                    .secondTagDesc(cfClewTaskModel.getSecondTagDesc())
                    .wechatId(cfClewTaskModel.getWechat())
                    .isSameWechatWithPhone(cfClewTaskModel.getIsSameWechatWithPhone())
                    .diseaseName(cfClewTaskModel.getDiseaseName())
                    .fundraisingObject(cfClewTaskModel.getFundraisingObject())
                    .fuwuRemark(cfClewTaskModel.getRemark())
                    .age(cfClewTaskModel.getAge())
                    .physical(cfClewTaskModel.getPhysical())
                    .hosptialName(cfClewTaskModel.getHosptialName())
                    .cityName(cfClewTaskModel.getCityName())
                    .build();
        }
        return riskQcBesidesTaskDetail;
    }


}
