package com.shuidihuzhu.cf.risk.admin.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcChangeResultLogBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.dao.CfRiskRecordingProblemsRecordDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcCalculateResult;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType.*;


/**
 * @Auther: subing
 * @Date: 2020/10/28
 */
@Service
@Slf4j
public class RiskAgainQcService {
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcResultBiz riskQcResultBiz;
    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Autowired
    private Risk1v1QcDetailService risk1v1QcDetailService;
    @Autowired
    private RiskQcChangeResultLogBiz riskQcChangeResultLogBiz;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private CfRiskRecordingProblemsRecordDao cfRiskRecordingProblemsRecordDao;

    public static final ImmutableList<Integer> APPEAL_QC_WORK_ORDER_LIST =
            ImmutableList.of(qc_complaint.getType(), qc_second_complaint.getType(), qc_serious_complaint.getType(),
                    qc_wx_1v1_repeat.getType(), qc_common_repeat.getType());

    public Response<Integer> againQc(String problemDescribeJson, long workOrderId,
                                     int firstQcResult, int secondQcResult, RiskQcResultVo riskQcResultVo, int qcType,
                                     WorkOrderType workOrderType, String correctRefuseIds, Integer scene) {
        Response<Integer> response = cfQcWorkOrderClient.changeQcWorkOrderStatus(workOrderId, ContextUtil.getAdminLongUserId());
        log.info("againQc cfQcWorkOrderClient response:{}", JSON.toJSONString(response));
        if (response == null || response.notOk()) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_HANDEL_WORK_ORDER_ERROR);
        }
        RiskQcCalculateResult riskQcCalculateResult =
                risk1v1QcDetailService.getResult(riskQcDetailService.getIds(riskQcResultVo, qcType), scene);
        firstQcResult = (qcType == QcTypeEnum.BD.getCode()) ? firstQcResult : riskQcCalculateResult.getQcResult();
        riskQcDetailService.updateStatus(riskQcResultVo, qcType);
        riskQcDetailService.addLog(riskQcResultVo, firstQcResult, secondQcResult, workOrderId, riskQcCalculateResult,
                qcType, true, workOrderType, correctRefuseIds);
        int result = riskQcResultBiz.updateInfo(firstQcResult, secondQcResult, problemDescribeJson, workOrderId);
        updateQcResult(riskQcResultVo, workOrderId, firstQcResult);
        return NewResponseUtil.makeSuccess(result);
    }

    private void updateQcResult(RiskQcResultVo riskQcResultVo, long workOrderId, int firstQcResult){
        List<RiskQcStandardVo> riskQcStandardVos = Optional.ofNullable(riskQcResultVo.getQcResultOption()).orElse(Lists.newArrayList());
        String ids = riskQcStandardVos.stream().map(RiskQcStandardVo::getId).map(String::valueOf).collect(Collectors.joining(","));
        riskQcSearchIndexBiz.updateByWorkOrderId(workOrderId, firstQcResult, ids, "", 0);
    }

    public int againAssign(long workOrderId, String reason, int assignId, String qcName){
        List<Long> orderIds = filterInfo(List.of(workOrderId));
        if (CollectionUtils.isEmpty(orderIds) || !orderIds.contains(workOrderId)){
            return 0;
        }
        Response<Long> response =
        cfQcWorkOrderClient.qcAgainAssignWorkOrder(workOrderId, ContextUtil.getAdminLongUserId(), assignId, reason);
        log.info("againQc cfQcWorkOrderClient response:{}", JSON.toJSONString(response));
        if (response == null || response.notOk()){
            return 0;
        }
        String operationName = seaAccountService.getName(ContextUtil.getAdminLongUserId());
        int result = riskQcChangeResultLogBiz.addLog(qcName, operationName, reason, workOrderId);
        return result;
    }

    public boolean checkStatus(long workOrderId){
        List<Long> orderIds = filterInfo(List.of(workOrderId));
        return CollectionUtils.isNotEmpty(orderIds) && orderIds.contains(workOrderId);
    }

    public boolean judge(RiskQcResultVo riskQcResultVo, int disposeAction) {

        if (disposeAction == 2 || disposeAction == 3) {
            return true;
        }

        List<QcRecordingProblemsVo> qcRecordingProblemsVos = riskQcResultVo.getQcRecordingProblemsVos();
        if (CollectionUtils.isNotEmpty(qcRecordingProblemsVos)) {
            for (QcRecordingProblemsVo vo : qcRecordingProblemsVos) {
                if (StringUtils.isNotEmpty(vo.getProblemIds()) || StringUtils.isNotEmpty(vo.getComment())) {
                    return true;
                }
            }
        }

        if (Objects.isNull(riskQcResultVo)) {
            return false;
        }

        if (StringUtils.isNotEmpty(riskQcResultVo.getPreInfoRemark()) ||
                StringUtils.isNotEmpty(riskQcResultVo.getLowIncomeRemark()) ||
                StringUtils.isNotEmpty(riskQcResultVo.getAddCreditRemark()) ||
                StringUtils.isNotEmpty(riskQcResultVo.getImgWordRemark())) {
            return true;
        }

        Set<Boolean> set = Sets.newHashSet();

        List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos = RiskQcResultVo.getList(riskQcResultVo.getMaterialQcResultOption());
        for (RiskMaterialQcStandardVo riskMaterialQcStandardVo : riskMaterialQcStandardVos) {
            List<RiskQcStandardVo> riskQcStandardVoList = riskMaterialQcStandardVo.getRiskQcStandardVo();
            for (RiskQcStandardVo riskQcStandardVo : riskQcStandardVoList) {
                List<RiskQcStandardDetailVo> riskQcStandardDetailVoList = riskQcStandardVo.getRiskQcStandardSecondVos();
                Set<Boolean> isChecks = riskQcStandardDetailVoList.stream().map(RiskQcStandardDetailVo::isCheck).collect(Collectors.toSet());
                set.addAll(isChecks);
            }
        }

        return set.size() > 1 || set.stream().findFirst().orElse(true);
    }

    //质检列表页中判断那些可以重新质检
    protected List<Long> filterInfo(List<Long> workOrderIds){
        List<Long> orderIds = Lists.newArrayList();
        Response<List<WorkOrderVO>> workOrderVOResponse = cfQcWorkOrderClient.queryQcByIds(workOrderIds);
        log.info("filterInfo cfQcWorkOrderClient workOrderVOResponse:{}", JSON.toJSONString(workOrderVOResponse));
        if (workOrderVOResponse == null || workOrderVOResponse.notOk() || CollectionUtils.isEmpty(workOrderVOResponse.getData())){
            return orderIds;
        }
        List<WorkOrderVO> workOrderVOS = workOrderVOResponse.getData().
                stream().filter(t -> t.getHandleResult() == HandleResultEnum.done.getType() &&
                !APPEAL_QC_WORK_ORDER_LIST.contains(t.getOrderType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderVOS)){
            return orderIds;
        }
        workOrderIds = workOrderVOS.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        Map<Long, WorkOrderExt> againQcStatusMap = Maps.newHashMap();
        Response<List<WorkOrderExt>> againQcStatus =
        cfWorkOrderClient.listExtInfos(workOrderIds, OrderExtName.againQcType.getName());
        log.info("filterInfo cfWorkOrderClient againQcStatus:{}", JSON.toJSONString(againQcStatus));
        if (againQcStatus != null && againQcStatus.ok() && CollectionUtils.isNotEmpty(againQcStatus.getData())){
            againQcStatusMap = againQcStatus.getData().stream()
                    .collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
        }
        for (WorkOrderVO workOrderVO : workOrderVOS){
            if (!checkTime(workOrderVO.getUpdateTime())){
                continue;
            }
            WorkOrderExt workOrderExt = againQcStatusMap.get(workOrderVO.getWorkOrderId());
            if (workOrderExt == null){
                orderIds.add(workOrderVO.getWorkOrderId());
            }else if (workOrderExt.getExtValue() != null && Integer.parseInt(workOrderExt.getExtValue()) == 0){
                orderIds.add(workOrderVO.getWorkOrderId());
            }
        }
        return orderIds;
    }

    public boolean checkTime(Date time){
        if (Objects.isNull(time)){
            return false;
        }
        Date date = DateUtil.addDay(time,1);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 19);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        date = calendar.getTime();
        return date.compareTo(DateUtil.nowDate()) > 0;
    }

    //我的列表页判断是否是重新质检工单
    protected List<Long> getQcAgainIds(List<Long> orderIds){
        List<Long> workOrderIds = Lists.newArrayList();
        Response<List<WorkOrderExt>> againQcStatus =
                cfWorkOrderClient.listExtInfos(orderIds, OrderExtName.againQcType.getName());
        log.info("filterInfo cfWorkOrderClient againQcStatus:{}", JSON.toJSONString(againQcStatus));
        if (againQcStatus != null && againQcStatus.ok() && CollectionUtils.isNotEmpty(againQcStatus.getData())){
            workOrderIds = againQcStatus.getData().stream().filter(t -> Integer.parseInt(t.getExtValue()) == 1)
                    .map(WorkOrderExt::getWorkOrderId).collect(Collectors.toList());
        }
        return workOrderIds;
    }

}
