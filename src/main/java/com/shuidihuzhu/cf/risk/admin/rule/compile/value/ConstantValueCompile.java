package com.shuidihuzhu.cf.risk.admin.rule.compile.value;

import com.shuidihuzhu.cf.risk.admin.rule.enums.ValueType;
import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/18 22:35
 */
@Component("constantValueCompile")
@Slf4j
public class ConstantValueCompile extends AbstractCompileChain<CriterionData> {

    @Override
    public String compileValue(CriterionData criterionData) {
        if (criterionData.getValueType() == ValueType.CONSTANT) {
            return criterionData.getContent();
        } else if(valueCompile != null) {
            return valueCompile.compileValue(criterionData);
        }
        return null;
    }

    @Resource(name = "variableValueCompile")
    @Override
    public void setValueCompile(AbstractCompileChain valueCompile) {
        this.valueCompile = valueCompile;
    }
}
