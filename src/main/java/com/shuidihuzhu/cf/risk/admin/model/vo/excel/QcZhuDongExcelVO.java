package com.shuidihuzhu.cf.risk.admin.model.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class QcZhuDongExcelVO extends BaseRowModel {
//    @ExcelProperty(value = "顾问姓名", index = 0)
//    private String bdName;
    @ExcelProperty(value = "工单id", index = 0)
    private long workOrderId;

    @ExcelProperty(value = "二级工单类型", index = 1)
    private String orderTypeMsg;

    @ExcelProperty(value = "案例ID", index = 2)
    private int caseId;

    @ExcelProperty(value = "质检员姓名", index = 3)
    private String operatorName;

    @ExcelProperty(value = "被质检人姓名", index = 4)
    private String targetName;

    @ExcelProperty(value = "被质检人组织", index = 5)
    private String targetOrgName;

    @ExcelProperty(value = "总通话时长", index = 6)
    private String totalCallTimeMsg;

    @ExcelProperty(value = "被命中的抽检规则", index = 7)
    private String ruleName;

    @ExcelProperty(value = "质检结果", index = 8)
    private String resultMsg;

    @ExcelProperty(value = "所属模块", index = 9)
    private String materialTypeMsg;

    @ExcelProperty(value = "一级问题描述", index = 10)
    private String oneLevelIssue;

    @ExcelProperty(value = "二级问题描述", index = 11)
    private String twoLevelIssue;

    @ExcelProperty(value = "一级属性", index = 12)
    private String oneLevelProperty;

    @ExcelProperty(value = "二级属性", index = 13)
    private String twoLevelProperty;

    @ExcelProperty(value = "录音备注", index = 14)
    private String callRemark;

    @ExcelProperty(value = "其他备注", index = 15)
    private String otherRemark;

    @ExcelProperty(value = "修改意见", index = 16)
    private String reviseOpinion;

    @ExcelProperty(value = "质检工单处理完成时间", index = 17)
    private Date handleTime;

    @ExcelProperty(value = "正确的驳回项", index = 18)
    private String correctRefuse;

    @ExcelProperty(value = "是否包含通话记录", index = 19)
    private String callStatus;

    @ExcelProperty(value = "材审工单处理结果", index = 20)
    private String handleResult;

    @ExcelProperty(value = "被质检人审核结果", index = 21)
    private String qcResult;



}
