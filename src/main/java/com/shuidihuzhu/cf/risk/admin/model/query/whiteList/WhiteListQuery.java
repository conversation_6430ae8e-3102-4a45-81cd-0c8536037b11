package com.shuidihuzhu.cf.risk.admin.model.query.whiteList;

import com.shuidihuzhu.cf.risk.admin.model.query.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WhiteListQuery extends PageQuery {

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("手机号")
    private String phoneNumber;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("状态")
    private Boolean status;

    private Date expireTime;
}
