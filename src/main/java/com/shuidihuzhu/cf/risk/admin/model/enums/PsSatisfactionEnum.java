package com.shuidihuzhu.cf.risk.admin.model.enums;

/**
 * @Auther: subing
 * @Date: 2020/2/24
 */
public enum  PsSatisfactionEnum {
    //
    ACCEPT(1, "认可"),
    NO_ACCET(2, "不认可"),
    NO_REPLY(3, "用户未回复"),
    OTHER(4, "其他");

    private int code;
    private String description;

    PsSatisfactionEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (PsSatisfactionEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
