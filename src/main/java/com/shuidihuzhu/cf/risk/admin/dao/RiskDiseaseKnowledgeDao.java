package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseKnowledge;
import com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseKnowledgeDto;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskDiseaseKnowledgeDao {

    int add(RiskDiseaseKnowledge diseaseKnowledge);

    int update(RiskDiseaseKnowledge diseaseKnowledge);

    int updateById(RiskDiseaseKnowledge diseaseKnowledge);

    Long existByNumber(String number);

    DiseaseKnowledgeDto findById(long id);

    DiseaseKnowledgeDto findByNumber(String number);

    int updateDelById(long id);

    List<DiseaseKnowledgeDto> listByParam(@Param("diseaseName") String diseaseName,
                                          @Param("startTime") String startTime,
                                          @Param("endTime") String endTime,
                                          @Param("limit") int limit,
                                          @Param("offset") int offset);

    int countByParam(@Param("diseaseName") String diseaseName,
                                          @Param("startTime") String startTime,
                                          @Param("endTime") String endTime);
}
