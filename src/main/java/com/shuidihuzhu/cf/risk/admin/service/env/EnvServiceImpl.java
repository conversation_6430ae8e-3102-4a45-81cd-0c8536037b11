package com.shuidihuzhu.cf.risk.admin.service.env;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2018-06-15  13:41
 */
@Service
public class EnvServiceImpl implements EnvService, EnvironmentAware {

    private static final Profiles ENV_PRODUCTION = Profiles.of("production");

    @Setter
    @Getter
    private Environment environment;

    @Override
    public boolean isDevelopment() {
        return !isProduction();
    }

    @Override
    public boolean isProduction() {
        return environment.acceptsProfiles(ENV_PRODUCTION);
    }
}
