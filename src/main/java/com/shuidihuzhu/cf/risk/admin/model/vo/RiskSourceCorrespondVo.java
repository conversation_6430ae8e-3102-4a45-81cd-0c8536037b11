package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.RiskSourceCorrespond;
import lombok.Data;

@Data
public class RiskSourceCorrespondVo {
    private long infoFeedBack;
    private String infoFeedBackContent;

    public static RiskSourceCorrespondVo build(RiskSourceCorrespond riskSourceCorrespond) {
        RiskSourceCorrespondVo riskSourceCorrespondVo = new RiskSourceCorrespondVo();
        riskSourceCorrespondVo.setInfoFeedBack(riskSourceCorrespond.getInfoFeedBack());
        riskSourceCorrespondVo.setInfoFeedBackContent(riskSourceCorrespond.getInfoFeedBackContent());
        return riskSourceCorrespondVo;
    }
}
