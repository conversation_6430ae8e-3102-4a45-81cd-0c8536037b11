package com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling;

import lombok.Data;

import java.util.Date;

@Data
public class RiskQualitySpotLevelConf {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 二级工单类型
     */
    private Long scene;

    /**
     * 是否抽检，0 否，1 是
     */
    private Byte isSampling;

    /**
     * 抽检量级，条/天
     */
    private Integer samplingLevel;

    /**
     * 开始生效时间
     */
    private Date parseTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 是否删除0 否，1 是
     */
    private Byte isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}