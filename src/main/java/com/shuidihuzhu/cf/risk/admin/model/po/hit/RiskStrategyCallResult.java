package com.shuidihuzhu.cf.risk.admin.model.po.hit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2021-04-11 17:47
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskStrategyCallResult {
    /**
     * 主键
     */
    private Long id;

    /**
     * 案例id
     */
    private Integer caseId;

    /**
     * 命中策略,RiskStrategyEnum
     */
    private Integer riskStrategy;

    /**
     * 命中二级策略，RiskStrategySecondEnum
     */
    private Integer secondStrategy;

    /**
     * 策略调用结果：RiskStrategyCallResultEnum
     */
    private Integer callResult;

    /**
     * 策略调用时间
     */
    private Date callTime;

    /**
     * 失信人姓名
     */
    private String userName;

    /**
     * 失信人身份证号码
     */
    private String userIdCard;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
