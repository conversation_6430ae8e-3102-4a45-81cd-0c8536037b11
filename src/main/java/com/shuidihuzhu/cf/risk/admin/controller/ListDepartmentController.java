package com.shuidihuzhu.cf.risk.admin.controller;

import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.list.ListDepartmentCaseQuery;
import com.shuidihuzhu.cf.risk.admin.model.query.list.ListDepartmentQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.list.*;
import com.shuidihuzhu.cf.risk.admin.service.list.ListDepartmentCaseService;
import com.shuidihuzhu.cf.risk.admin.service.list.ListDepartmentService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 医院名单
 * <AUTHOR>
 * @date 2020/6/15 20:29
 */
@Validated
@Slf4j
@RestController
@RequestMapping(path = "/api/cf-risk-admin/list/hospital")
public class ListDepartmentController {

    @Resource
    private ListDepartmentService listDepartmentService;
    @Resource
    private ListDepartmentCaseService caseService;

    @ApiOperation(value = "医院科室电话列表-列表")
    @PostMapping(path = "/list")
    public Response<PageResult<ListDepartmentVo>> list(ListDepartmentQuery query) {
        log.info("医院科室电话列表-列表，请求入参：{}", query);
        return NewResponseUtil.makeSuccess(listDepartmentService.queryByPage(query));
    }

    @ApiOperation(value = "医院科室电话列表-详情")
    @PostMapping(path = "/get")
    public Response<ListDepartmentEditVo> get(@ApiParam("id") @NotNull(message = "id不能为空") @Min(value = 1) Long id) {
        log.info("医院科室电话列表-详情，请求入参：{}", id);
        return NewResponseUtil.makeSuccess(listDepartmentService.getDetail(id));
    }

    @ApiOperation(value = "医院科室电话列表-新增")
    @PostMapping(path = "/save")
    public Response<Void> save(@Valid ListDepartmentBase departmentBase) {
        log.info("医院科室电话列表-新增：{}", departmentBase);
        listDepartmentService.save(departmentBase);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "医院科室电话列表-修改")
    @PostMapping(path = "/modify")
    public Response<Void> edit(@Valid ListDepartmentEditVo editVo) {
        log.info("医院科室电话列表-修改，请求入参：{}", editVo);
        listDepartmentService.edit(editVo);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "医院科室电话列表-删除")
    @PostMapping(path = "/del")
    public Response<Void> delete(@ApiParam("id") @NotNull(message = "id不能为空") @Min(value = 1) Long id) {
        log.info("医院科室电话列表-删除，请求入参：{}", id);
        long adminUserId = ContextUtil.getAdminLongUserId();
        listDepartmentService.del(id, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "医院科室电话列表-日志列表")
    @PostMapping(path = "/list/log")
    public Response<List<ListDepartmentLogVo>> listLog(@ApiParam("id") @NotNull(message = "id不能为空") @Min(value = 1) Long id) {
        log.info("医院科室电话列表-日志列表，请求入参：{}", id);
        return NewResponseUtil.makeSuccess(listDepartmentService.queryListDepartmentLog(id));
    }

    @ApiOperation(value = "医院科室电话列表-提交过改号码的案例信息（限制100条）")
    @PostMapping(path = "/list/cases")
    public Response<List<ListDepartmentCaseInfo>> listCaseInfo(@Valid ListDepartmentCaseQuery caseQuery) {
        log.info("医院科室电话列表-提交过改号码的案例信息（限制100条），请求入参：{}", caseQuery);
        return NewResponseUtil.makeSuccess(caseService.queryLandlineNumberCases(caseQuery));
    }

}
