package com.shuidihuzhu.cf.risk.admin.model.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class QcHospitalDeptExcelVO extends BaseRowModel {
//    @ExcelProperty(value = "顾问姓名", index = 0)
//    private String bdName;
    @ExcelProperty(value = "工单id", index = 0)
    private long workOrderId;

    @ExcelProperty(value = "工单类型", index = 1)
    private String orderTypeMsg;

    @ExcelProperty(value = "质检对象", index = 2)
    private String targetType;

    @ExcelProperty(value = "被质检人姓名", index = 3)
    private String targetName;

    @ExcelProperty(value = "被质检人组织", index = 4)
    private String targetOrgName;

    @ExcelProperty(value = "医院ID及名称", index = 5)
    private String deptIdAndTitle;

    @ExcelProperty(value = "工单分配类型", index = 6)
    private String assignType;

    @ExcelProperty(value = "优先级", index = 7)
    private String orderLevel;

    @ExcelProperty(value = "工单处理状态", index = 8)
    private String handleResultMsg;

    @ExcelProperty(value = "一级质检结果", index = 9)
    private String oneLevelResult;

    @ExcelProperty(value = "处理人", index = 10)
    private String operatorName;

    @ExcelProperty(value = "质检工单创建时间", index = 11)
    private Date createTime;
    @ExcelProperty(value = "质检工单领取时间", index = 12)
    private Date handleTime;
    @ExcelProperty(value = "质检工单结束时间", index = 13)
    private Date updateTime;

    @ExcelProperty(value = "一级问题", index = 14)
    private String oneLevelIssue;
    @ExcelProperty(value = "二级问题", index = 15)
    private String twoLevelIssue;

    @ExcelProperty(value = "其他备注", index = 16)
    private String otherRemark;


}
