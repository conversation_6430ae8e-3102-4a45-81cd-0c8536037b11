package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "黑名单数据-编辑黑名单类型")
public class BlacklistDataTypeEditVo {

    @NotNull(message = "黑名单数据id不能为空")
    @Min(value = 1, message = "黑名单数据id不能小于1")
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("修改后的类型ids")
    private List<Long> typeIds;

    @NotBlank(message = "操作原因不能为空")
    @Length(min = 5, max = 200, message = "操作原因长度必须在[5~200]之间")
    @ApiModelProperty("操作原因")
    private String operateReason;

    @ApiModelProperty("修改后的限制动作ids")
    private List<BlacklistTypeActionRefDto> actionList;


}
