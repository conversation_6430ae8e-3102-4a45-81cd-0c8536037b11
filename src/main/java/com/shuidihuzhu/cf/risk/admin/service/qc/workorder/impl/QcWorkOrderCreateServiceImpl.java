package com.shuidihuzhu.cf.risk.admin.service.qc.workorder.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CaseInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.biz.WorkOrderCallRecordingRelBiz;
import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderCallRecordingRelModel;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcVideoVo;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcWorkOrderCreateService;
import com.shuidihuzhu.client.cf.growthtool.client.CfCrmOrgFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolOrgFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmUserOrgInfo;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.PreproseMaterialVoiceRecordDo;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderOrgRel;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 逻辑抽取自 {@link com.shuidihuzhu.cf.risk.admin.service.mq.QcWorkOrderCreateConsumer}
 */
@Slf4j
@Service
public class QcWorkOrderCreateServiceImpl implements QcWorkOrderCreateService {
    private static final String CHANNEL = "BD";

    @Autowired
    private CaseInfoFeignClient caseInfoFeignClient;
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;
    @Autowired
    private CfCrmOrgFeignClient cfCrmOrgFeignClient;
    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Autowired
    private PreposeMaterialClient preposeMaterialClient;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private CfGrowthtoolOrgFeignClient cfGrowthtoolOrgFeignClient;

    @Autowired
    private WorkOrderExtFeignClient workOrderExtFeignClient;

    @Autowired
    private QcAudioAsrService qcAudioAsrService;

    @Autowired
    private WorkOrderCallRecordingRelBiz workOrderCallRecordingRelBiz;

    private static final String MSG_NOT_IN_CONDITION = "不满足工单生成条件，无法生成";
    private static final String MSG_HAS_CREATE = "已经生成过普通质检工单，无法生成";

    private static final String TAG = "质检工单生成";

    /**
     * 抛出异常会重试
     */
    @NotNull
    @Override
    public OperationResult<Void> promoteQcWorkOrderCreate(int caseId, int userId) throws InterruptedException {
        //检查案例是否是线下bd发起
        Response<CfInfoExt> cfInfoExtResponse = caseInfoFeignClient.getCfExt(caseId);
        if (NewResponseUtil.isNotOk(cfInfoExtResponse)) {
            throw new RuntimeException("caseInfoFeignClient.getCfExt(caseId) feign异常 自动重试");
        }

        CfInfoExt cfInfoExt = cfInfoExtResponse.getData();
        if (cfInfoExt == null) {
            log.info("{} 案例信息错误 ext未找到", TAG);
            return result(false).withShowMessage("案例信息错误");
        }
        Preconditions.checkNotNull(cfInfoExt);
        if (!cfInfoExt.getPrimaryChannel().equals(CHANNEL)) {
            log.info("{} extInfo中channel 不等于 BD", TAG);
            return result(false).withShowMessage(MSG_NOT_IN_CONDITION);
        }

        //判断是否有成功上传的语音

        Response<PreposeMaterialModel.MaterialInfoVo> response = clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(caseId);

        Preconditions.checkNotNull(response);
        Preconditions.checkState(response.ok());

        PreposeMaterialModel.MaterialInfoVo materialInfoVo = response.getData();
        if (materialInfoVo == null) {
            log.info("{} 查询待录入信息 未找到", TAG);
            return result(false).withShowMessage(MSG_NOT_IN_CONDITION);
        }
        //判断该代录入id的创建人姓名与本次生成线下质检工单的被质检人姓名是否相同
        if (!materialInfoVo.getVolunteerUniqueCode().equals(cfInfoExt.getVolunteerUniqueCode())) {
            log.info("{} 不符合 判断该代录入id的创建人姓名与本次生成线下质检工单的被质检人姓名是否相同", TAG);
            return result(false).withShowMessage(MSG_NOT_IN_CONDITION);
        }

        String lockName = "qcLock_" + caseId + "_" + QcTypeEnum.BD.getCode() + "_"
                + cfInfoExt.getVolunteerUniqueCode();
        String identifier = "";
        try {
            identifier = cfRiskRedissonHandler.tryLock(lockName, 3 * 1000L, 30 * 1000L);
            log.info("{} 创建获取锁 {}", TAG, identifier);
            Preconditions.checkState(StringUtils.isNotBlank(identifier));
            //检查是否已生成工单
            Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.qc_common.getType());
            Preconditions.checkState(lastWorkOrder.ok());
            if (Objects.nonNull(lastWorkOrder.getData())) {
                log.info("{} 未通过 检查是否已生成工单", TAG);
                return result(false).withShowMessage(MSG_HAS_CREATE);
            }
            //创建质检基本信息
            Response<CrowdfundingVolunteer> cfVolunteerDoByUniqueCode = cfGrowthtoolVolunteerFeignClient.
                    getCfVolunteerDOByUniqueCode(cfInfoExt.getVolunteerUniqueCode());
            RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
            riskQcBaseInfo.setCaseId(caseId);
            riskQcBaseInfo.setQcType(QcTypeEnum.BD.getCode());
            riskQcBaseInfo.setOrderType(WorkOrderType.qc_common.getType());
            riskQcBaseInfo.setQcUniqueCode(cfInfoExt.getVolunteerUniqueCode());
            String qcByName = "";
            if (cfVolunteerDoByUniqueCode.ok() && Objects.nonNull(cfVolunteerDoByUniqueCode.getData())) {
                qcByName = cfVolunteerDoByUniqueCode.getData().getVolunteerName();
            }
            riskQcBaseInfo.setQcByName(qcByName);
            riskQcBaseInfoBiz.addQc(riskQcBaseInfo);


            int orgId = 0;
            String organization = StringUtils.EMPTY;
            BdCrmUserOrgInfo bdCrmUserOrgInfo = getCrmUserOrgInfo(cfInfoExt.getVolunteerUniqueCode());
            if (Objects.nonNull(bdCrmUserOrgInfo)) {
                List<BdCrmUserOrgInfo.CityInfo> cityInfos = bdCrmUserOrgInfo.getBelongCityInfoList();
                if (CollectionUtils.isNotEmpty(cityInfos)) {
                    BdCrmUserOrgInfo.CityInfo cityInfo = cityInfos.get(0);
                    orgId = cityInfo.getOrgId();
                    organization = Joiner.on("-").join(cityInfo.getChainFromRootNames());
                }
            }

            //记录组织结构id
            RiskQcMaterialsInfo riskQcMaterialsInfo = new RiskQcMaterialsInfo();
            riskQcMaterialsInfo.setQcId(riskQcBaseInfo.getId());
            riskQcMaterialsInfo.setMaterialsKey(QcMaterialsKeyEnum.ORG_ID.getKey());
            riskQcMaterialsInfo.setMaterialsValue(Integer.toString(orgId));
            riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfo);


            //执行工单创建逻辑
            QcWorkOrder qcWorkOrder = new QcWorkOrder();
            qcWorkOrder.setCaseId(caseId);
            qcWorkOrder.setQcId(riskQcBaseInfo.getId());
            qcWorkOrder.setOrderType(WorkOrderType.qc_common.getType());
            qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
            qcWorkOrder.setComment("生成普通质检工单");
            qcWorkOrder.setLoginUserId(userId);
            Response<Long> clientQcWorkOrder = cfQcWorkOrderClient.createQcWorkOrder(qcWorkOrder);

            Preconditions.checkNotNull(clientQcWorkOrder);
            Preconditions.checkState(clientQcWorkOrder.ok());
            Long workOrderId = clientQcWorkOrder.getData();
            Preconditions.checkNotNull(workOrderId);
            log.info("{} 工单创建成功 工单ID {}", TAG, workOrderId);

            //调用质检操作记录接口，记录操作记录
            String content = "生成普通质检工单,工单ID【" + workOrderId + "】";
            riskQcLogService.addLog(RiskQcOperationTypeEnum.CREATE_COMMON_WORK_ORDER, workOrderId, content);
            FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = crowdfundingFeignClient.getCaseInfoById(caseId);

            // 添加搜索索引字段聚合表
            RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
            riskQcSearchIndex.setQcId(riskQcBaseInfo.getId());
            riskQcSearchIndex.setCaseId(caseId);
            riskQcSearchIndex.setWorkOrderId(workOrderId);
            riskQcSearchIndex.setQcType(QcTypeEnum.BD.getCode());
            riskQcSearchIndex.setOrganization(organization);
            riskQcSearchIndex.setQcUniqueCode(cfVolunteerDoByUniqueCode.getData().getUniqueCode());
            riskQcSearchIndex.setQcByName(qcByName);
            riskQcSearchIndex.setRegisterMobileEncrypt("");
            if (crowdfundingInfoFeignResponse.ok() && Objects.nonNull(crowdfundingInfoFeignResponse.getData())) {
                riskQcSearchIndex.setUserId(crowdfundingInfoFeignResponse.getData().getUserId());
            }
            riskQcSearchIndex.setMaterialId(materialInfoVo.getId());
            riskQcSearchIndexBiz.addSearchIndex(riskQcSearchIndex, qcWorkOrder.getOrderType());

            this.addOrgId(riskQcBaseInfo.getQcUniqueCode(), workOrderId);

            // 保存工单ext
            RpcResult<PreposeMaterialModel.MaterialInfoVo> rpcResult = preposeMaterialClient.selectMaterialsById(riskQcSearchIndex.getMaterialId());
            log.info("materialInfoVo:{},caseId:{}", JSON.toJSONString(rpcResult), caseId);
            if (rpcResult != null && rpcResult.isSuccess() && rpcResult.getData() != null) {
                ArrayList<WorkOrderExt> extList = Lists.newArrayList();
                PreposeMaterialModel.MaterialInfoVo prepose = rpcResult.getData();
                Integer remoteRaise = prepose.getRemoteRaise();
                if (remoteRaise != null) {
                    extList.add(WorkOrderExt.create(workOrderId, QcConst.OrderExt.remoteRaise, remoteRaise));
                }
                Von.extUpdate().saveByList(workOrderId, extList);
            }

            // 判断代录入过程中是否有有效录音
            Response<List<PreproseMaterialVoiceRecordDo>> mediaListResp = clewPreproseMaterialFeignClient.getVoiceRecordsByPreposeMaterialId(materialInfoVo.getId());
            Preconditions.checkNotNull(mediaListResp);
            Preconditions.checkState(mediaListResp.ok());

            List<PreproseMaterialVoiceRecordDo> mediaListRespData = mediaListResp.getData();
            if (CollectionUtils.isEmpty(mediaListRespData)) {
                log.info("{} 查询录音为空", TAG);
            } else {
                //记录录音材料
                List<RiskQcMaterialsInfo> riskQcMaterialsInfos = mediaListRespData.stream().map(preproseMaterialVoiceRecordDo -> {
                    RiskQcMaterialsInfo materialsInfo = new RiskQcMaterialsInfo();
                    materialsInfo.setQcId(riskQcBaseInfo.getId());
                    materialsInfo.setMaterialsKey(QcMaterialsKeyEnum.RECORDING.getKey());
                    RiskQcVideoVo riskQcVideoVo = new RiskQcVideoVo(preproseMaterialVoiceRecordDo.getStartTime(),
                            preproseMaterialVoiceRecordDo.getEndTime(), preproseMaterialVoiceRecordDo.getContent(),
                            preproseMaterialVoiceRecordDo.getDuration());
                    materialsInfo.setMaterialsValue(JSON.toJSONString(riskQcVideoVo));
                    return materialsInfo;
                }).collect(Collectors.toList());
                riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfos);

                // asr转译
                final Response<Void> asrResponse = qcAudioAsrService.handleBDRecord(workOrderId, true);
                log.info("bd asr 请求结果 {}", asrResponse);
            }

        } catch (Exception e) {
            log.error("QcWorkOrderCreateConsumer.consumeMessage error", e);
            throw e;
        } finally {
            if (StringUtils.isNotEmpty(identifier)) {
                cfRiskRedissonHandler.unLock(lockName, identifier);
            }
        }
        return result(true);
    }


    /**
     * 绑定工单和通话记录关系
     *
     * @param workOrderId
     * @param recordingUniqueId
     * @param userId
     * @return
     */
    @Override
    public Response<Void> bindWorkOrderIdRecordingUniqueId(Long workOrderId, String recordingUniqueId, Long userId) {
        WorkOrderCallRecordingRelModel workOrderCallRecordingRelModel = workOrderCallRecordingRelBiz.selectByRecordingUniqueId(recordingUniqueId);
        //验证recordingUniqueId的唯一性
        if (workOrderCallRecordingRelModel != null) {
            return NewResponseUtil.makeSuccess();
        }
        WorkOrderCallRecordingRelModel relModel = new WorkOrderCallRecordingRelModel();
        relModel.setWorkOrderId(workOrderId);
        relModel.setRecordingUniqueId(recordingUniqueId);
        relModel.setOperationId(userId);
        workOrderCallRecordingRelBiz.insert(relModel);
        return NewResponseUtil.makeSuccess();
    }

    private OperationResult<Void> result(boolean success) {
        return success ? OperationResult.success() : OperationResult.fail();
    }

    private void addOrgId(String qcUniqueCode, long workOrderId) {
        BdCrmUserOrgInfo crmUserOrgInfo = getCrmUserOrgInfo(qcUniqueCode);
        if (Objects.nonNull(crmUserOrgInfo)) {
            Set<Integer> orgIds = Sets.newHashSet();
            List<BdCrmUserOrgInfo.CityInfo> belongCityInfoList = crmUserOrgInfo.getBelongCityInfoList();
            if (CollectionUtils.isNotEmpty(belongCityInfoList)) {
                for (BdCrmUserOrgInfo.CityInfo cityInfo : belongCityInfoList) {
                    List<Integer> chainFromRootIds = cityInfo.getChainFromRootIds();
                    orgIds.addAll(chainFromRootIds);
                }
            }
            if (CollectionUtils.isNotEmpty(orgIds)) {
                var workOrderOrgRels = orgIds.stream().map(orgId -> {
                    WorkOrderOrgRel workOrderOrgRel = new WorkOrderOrgRel();
                    workOrderOrgRel.setWorkOrderId(workOrderId);
                    workOrderOrgRel.setOrgId(orgId);
                    return workOrderOrgRel;
                }).collect(Collectors.toList());
                cfQcWorkOrderClient.addOrgId(workOrderOrgRels);
            }
        }

    }

    private BdCrmVolunteerOrgnizationSimpleModel getUserOrganizationByMis(String mis) {
        Response<List<BdCrmVolunteerOrgnizationSimpleModel>> response = cfGrowthtoolFeginClient.getBdCrmVolunteerOrgnizationSimpleModelByMisList(List.of(mis));
        if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            return response.getData().get(0);
        }
        return null;
    }

    private BdCrmUserOrgInfo getCrmUserOrgInfo(String uniqueCode) {
        Response<BdCrmUserOrgInfo> response = cfGrowthtoolOrgFeignClient.getCrmUserOrgInfo(uniqueCode);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }
}
