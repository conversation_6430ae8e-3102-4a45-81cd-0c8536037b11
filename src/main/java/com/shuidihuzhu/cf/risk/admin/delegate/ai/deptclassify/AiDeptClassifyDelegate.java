package com.shuidihuzhu.cf.risk.admin.delegate.ai.deptclassify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.cf.risk.admin.delegate.ai.AiAgent;
import com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio.AiTextValidForAudioParam;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * https://wdh.feishu.cn/wiki/wikcnVjt6x7gD9dcUWoKHekSpjf
 */
@Service
@Slf4j
public class AiDeptClassifyDelegate {

    @Autowired
    private AiAgent oceanApiClient;

    public static String TAG = "ai-chou-department-normalization";
    public static String USER_ID = "10007";
    public static String TOKEN = "e2f928d7ef14b6a2";

    public Map<String, String> analyse(List<String> deptList){
        log.info("AiDeptClassifyDelegate in deptList {}", deptList);

        OceanApiRequest req = new OceanApiRequest();
        req.setTag(TAG);
        req.setUserId(USER_ID);
        req.setToken(TOKEN);

        AiDeptClassifyParam param = new AiDeptClassifyParam();
        param.setDepartmentList(deptList);
        req.setBody(JSON.toJSONString(param));
        Response<OceanApiResponse> resp = oceanApiClient.agent(req, null);
        log.info("AiDeptClassifyDelegate analyse req {}, resp {}", req, resp);
        if (resp == null || resp.notOk() || resp.getData() == null) {
            log.warn("AiDeptClassifyDelegate ai error {}", resp);
            return null;
        }
        OceanApiResponse data = resp.getData();
        String body = data.getBody();
        final TypeReference<Map<String, String>> typeReference = new TypeReference<>() {
        };
        return JSON.parseObject(body, typeReference);
    }

    public String analyseSingle(String dept){
        final Map<String, String> map = analyse(Lists.newArrayList(dept));
        if (map == null) {
            return null;
        }
        return map.get(dept);
    }

}
