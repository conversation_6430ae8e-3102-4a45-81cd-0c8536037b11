package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.DiscussionCheckRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface DiscussionCheckRecordDao {

    int save(DiscussionCheckRecord record);


    List<DiscussionCheckRecord> findListByDiscussionId(@Param("discussionIds") List<Long> discussionIds,
                                                       @Param("status") int status,
                                                       PageRequest pageRequest);


    DiscussionCheckRecord findLastByDiscussId(@Param("discussionId") long discussionId);


    List<DiscussionCheckRecord> listByDiscussionIdsIn(List<Long> discussionIds);
}
