package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.common.web.enums.MyErrorCode;

/**
 * <AUTHOR>
 * @date 2020/2/6
 */
public enum RiskAdminErrorCode implements MyErrorCode {

    //10100x 评论相关
    DISEASE_TREATMENT_NAME_REPEAT(101001, "治疗方案名称重复~"),
    DISEASE_NOT_EXIST(101002, "对应疾病信息不存在"),
    DISEASE_TYPE_NOT_EXIST(101003, "对应疾病类型不存在"),
    DISEASE_TYPE_CAN_NOT_MATCH(101004, "不可发起类型的疾病，不支持输入治疗方案"),
    DISEASE_TYPE_CAN_MATCH(101005, "可发起类型的疾病，至少填写一个治疗方案"),
    DISEASE_TREATMENT_PROJECT_SIZE_LIMIT(101006, "本期疾病暂时只支持一个治疗方案"),
    DISEASE_CLASS_NAME_REPEAT(101007, "治疗类别名称重复"),
    DISEASE_INFO_OVER_LIMIT(101008, "信息字数超限"),
    DISEASE_TYPE_CAN_NOT_RAISE(101009, "不可发起类型的疾病，至少填写一个治疗方案"),

    //20000x 质检相关
    QC_STANDARD_LABEL(200001,"该一级标准有正在启用的二级标准，不能弃用"),
    QC_STANDARD_DELETE(200002, "该二级标准已经使用过，不允许操作删除"),
    QC_FIRST_STANDARD_REPEAT(200003, "一级标签名称不能重复，请重新输入"),
    QC_SECOND_STANDARD_REPEAT(200004, "二级标签不能重复，请重新输入"),
    QC_WORK_ORDER_STATUS(200005, "当前工单状态不可操作"),
    QC_WORK_ORDER_LIST_NOT_EXIST(200006,"没有查询到工单"),
    QC_HANDEL_WORK_ORDER_ERROR(200007,"保存质检结果失败，请重新质检"),
    QC_ALLOCATION_WORK_ORDER_ERROR(200008, "该工单已被回收，处理失败"),
    QC_STRATEGY_Id_ERROR(200009, "更新策略主键不存在"),
    QC_WORK_RODER_RULE_SIZE_ERROR(200010, "规则配置最多为10条"),
    QC_FIRST_STANDARD_NOT_USE(200011, "一级标准已经弃用"),
    QC_WORK_ORDER_FIGURE_OUT(200012,"工单已经处理完成"),
    QC_AGAIN_WORK_ORDER_ERROR(200013, "重新质检工单不支持回收"),
    QC_GAIN_WORK_ORDER_ERROR(200014,"获取普通质检工单失败"),
    QC_WORK_ORDER_RESULT_NOT_EXIST(200015,"没有查询到工单质检结果"),


    //21000x 白名单相关
    ID_CARD_OR_PHONE_MUST_EXIST(210001,"身份证或者手机号必须填写其中一项"),
    EXPIRE_TIME_IS_ERROR(210002,"更新的有效时间不允许小于当前时间"),
    MOBILE_IS_ERROR(210003,"手机号格式不正确"),
    ID_CARD_IS_ERROR(210004,"身份证格式不正确"),
    ;


    private int code;

    private String msg;

    RiskAdminErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
