package com.shuidihuzhu.cf.risk.admin.biz.impl;


import com.shuidihuzhu.cf.risk.admin.biz.RiskQcChangeResultLogBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcChangeResultLogDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: subing
 * @Date: 2020/10/28
 */
@Service
public class RiskQcChangeResultLogBizImpl implements RiskQcChangeResultLogBiz {

    @Autowired
    private RiskQcChangeResultLogDao riskQcChangeResultLogDao;

    @Override
    public int addLog(String qcName, String operationName, String reason, long workOrderId) {
        if (StringUtils.isBlank(qcName) || StringUtils.isBlank(reason)){
            return 0;
        }
        return riskQcChangeResultLogDao.addLog(qcName, operationName, reason, workOrderId);
    }
}
