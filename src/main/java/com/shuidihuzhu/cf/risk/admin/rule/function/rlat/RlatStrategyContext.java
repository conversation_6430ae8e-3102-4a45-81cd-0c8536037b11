package com.shuidihuzhu.cf.risk.admin.rule.function.rlat;

import com.shuidihuzhu.cf.risk.admin.rule.enums.RlatOp;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/19 12:25
 */
@Validated
@Component
public class RlatStrategyContext {

    @Resource
    private Map<String, RlatCompile> compileMap;

    public String spliceRlat(@NotNull RlatOp rlatOp, String source, String dest){
        return compileMap.get(rlatOp.name()).splice(source, dest);
    }

}
