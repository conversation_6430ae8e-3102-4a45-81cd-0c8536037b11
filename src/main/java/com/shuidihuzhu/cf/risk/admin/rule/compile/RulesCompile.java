package com.shuidihuzhu.cf.risk.admin.rule.compile;

import com.shuidihuzhu.cf.risk.admin.rule.compile.value.AbstractCompileChain;
import com.shuidihuzhu.cf.risk.admin.rule.model.Rule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/18 23:21
 */
@Service
@Slf4j
public class RulesCompile implements ICriterionCompile<List<Rule>>{

    @Resource
    private LogicalCriterionCompile logicalCriterionCompile;
    @Resource
    private AbstractCompileChain abstractCompileChain;

    @Override
    public String compileCriterion(List<Rule> rules) {
        StringBuilder ruleResult = new StringBuilder();
        for (Rule rule : rules) {
            if (rule.getPreconditionGroup() != null) {
                ruleResult.append("if (").append(logicalCriterionCompile.compileCriterion(rule.getPreconditionGroup())).append(") {\n");
            }
                ruleResult.append(      doGeneralSplice(rule)                                                                         );
            if (rule.getPreconditionGroup() != null) {
                ruleResult.append("}"                                                                                            +"\n");
            }
        }
        return ruleResult.toString();
    }

    private String doGeneralSplice(Rule rule){
        return "if (" + logicalCriterionCompile.compileCriterion(rule.getCriterionGroup()) + ") {"+"\n" +
                    abstractCompileChain.compileValue(rule.getThenAction())                       +"\n" +
               "} else {"                                                                         +"\n" +
                    abstractCompileChain.compileValue(rule.getElseAction())                       +"\n" +
               "}"                                                                                +"\n";
    }
}
