package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskPublicSentimentCorrespondBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPublicSentimentInfoBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPublicSentimentCorrespondDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskSourceCorrespond;
import com.shuidihuzhu.cf.risk.admin.model.enums.PublicSentimentInfoSourceEnum;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskSourceCorrespondVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/2/20
 */
@Service
public class RiskPublicSentimentCorrespondBizImpl implements RiskPublicSentimentCorrespondBiz {
    @Autowired
    private RiskPublicSentimentCorrespondDao riskPublicSentimentCorrespondDao;
    @Autowired
    private RiskPublicSentimentInfoBiz riskPublicSentimentInfoBiz;

    @Override
    public List<RiskSourceCorrespondVo> getByInfoSource(int infoSource, boolean isShowOther) {
        if (infoSource < 0) {
            return Lists.newArrayList();
        }
        List<RiskSourceCorrespondVo> riskSourceCorrespondVos = Lists.newArrayList();
        List<Integer> infoSources = Lists.newArrayList();
        infoSources.add(infoSource);
        infoSources.add(PublicSentimentInfoSourceEnum.OTHER.getCode());
        List<RiskSourceCorrespond> riskSourceCorresponds = riskPublicSentimentCorrespondDao.getByInfoSources(infoSources);
        riskSourceCorresponds.forEach(riskSourceCorrespond -> {
            riskSourceCorrespondVos.add(RiskSourceCorrespondVo.build(riskSourceCorrespond));
        });
        return riskSourceCorrespondVos;
    }

    @Override
    public List<RiskSourceCorrespond> getByInfoSources(List<Integer> infoSources) {
        if (CollectionUtils.isEmpty(infoSources)) {
            return Lists.newArrayList();
        }
        return riskPublicSentimentCorrespondDao.getByInfoSources(infoSources);
    }

    @Override
    public RiskSourceCorrespond getByInfoFeedBack(int infoFeedback) {
        if (infoFeedback < 0) {
            return null;
        }
        return riskPublicSentimentCorrespondDao.getByInfoFeedBack(infoFeedback);
    }
}
