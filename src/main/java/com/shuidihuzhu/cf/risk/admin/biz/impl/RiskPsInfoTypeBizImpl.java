package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskPsInfoTypeBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPsInfoTypeDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsInfoType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskPsInfoTypeBizImpl implements RiskPsInfoTypeBiz {
    @Autowired
    private RiskPsInfoTypeDao riskPsInfoTypeDao;

    @Override
    public List<RiskPsInfoType> getAll() {
        List<RiskPsInfoType> allData = riskPsInfoTypeDao.getAll();
        List<RiskPsInfoType> parentList = allData.stream().filter(t -> t.getGrade() == 1).collect(Collectors.toList());
        for (int i = 1; i < 3; i++) {
            compose(i, parentList, allData);
        }
        return parentList;
    }

    @Override
    public String getByPath(String path) {
        if (StringUtils.isBlank(path)){
            return "";
        }
        String[] split = path.split(",");
        List<RiskPsInfoType> types = riskPsInfoTypeDao.findByIdsIn(Arrays.stream(split).map(Long::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(types)){
            return "";
        }
        List<RiskPsInfoType> sortTypes = types.stream().sorted((o1, o2) -> {
            if (o1.getId() > o2.getId()) {
                return 1;
            } else if (o1.getId() < o2.getId()) {
                return -1;
            } else {
                return 0;
            }
        }).collect(Collectors.toList());
        StringBuilder infoTypeStr = new StringBuilder();
        for (RiskPsInfoType sortType : sortTypes) {
            infoTypeStr.append(sortType.getDescription()).append("-");
        }
        return infoTypeStr.toString();
    }




    @Override
    public String getById(long id) {
        if (id <= 0){
            return "";
        }
        return riskPsInfoTypeDao.getById(id);
    }

    private void compose(int grade, List<RiskPsInfoType> parentList, List<RiskPsInfoType> allData) {
        for (RiskPsInfoType t : parentList) {
//            if (t.getGrade() == 1) {
//                String path = t.getId() + ",";
//                riskPsInfoTypeDao.updateById(path, t.getId());
//            } else if (t.getGrade() == 2 && StringUtils.isNotBlank(t.getPath())) {
//                String path = t.getParentId() + "," + t.getId() + ",";
//                riskPsInfoTypeDao.updateById(path, t.getId());
//                t.setPath(path);
//            }
            if (t.getGrade() != grade) {
                if (t.getChildren().size() != 0) {
                    compose(grade, t.getChildren(), allData);
                }
            }
            Iterator<RiskPsInfoType> iterator = allData.iterator();
            while (iterator.hasNext()) {
                RiskPsInfoType next = iterator.next();
                if (next.getParentId() == t.getId()) {
                    if (CollectionUtils.isEmpty(t.getChildren())) {
                        t.setChildren(new ArrayList<>());
                    }
//                    if (next.getGrade() == 2 && StringUtils.isNotBlank(t.getPath())){
//                        String path = next.getParentId() + "," + next.getId() + ",";
//                        riskPsInfoTypeDao.updateById(path, next.getId());
//                        next.setPath(path);
//                    }
//                    if (next.getGrade() == 3){
//                        String path = t.getPath()+ next.getId() + ",";
//                        riskPsInfoTypeDao.updateById(path, next.getId());
//                    }
                    t.getChildren().add(next);
                    iterator.remove();
                }
            }
        }
    }

}
