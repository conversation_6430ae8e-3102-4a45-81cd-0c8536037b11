package com.shuidihuzhu.cf.risk.admin.model.enums.list;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_white_list_log", columnName = "operate_type")})
@Getter
public enum WhiteOperateTypeEnum {

    ADD(1, "新增身份关联异常白名单"),
    UPDATE(2, "编辑身份关联异常白名单"),
    UPDATE_EXPIRE_TIME(3, "编辑身份关联异常白名单有效期"),
    ;

    private byte code;
    private String desc;

    WhiteOperateTypeEnum(int code, String desc) {
        this.code = (byte) code;
        this.desc = desc;
    }

    public static WhiteOperateTypeEnum findOfCode(int code){
        for (WhiteOperateTypeEnum value : values()) {
            if (value.getCode() == code){
                return value;
            }
        }
        return null;
    }
}
