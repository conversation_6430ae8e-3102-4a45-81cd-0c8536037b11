package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;

@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_standard", columnName = "level")},
descName = "description")
public enum RiskQcStandardLevelEnum {
    //
    ONE_LEVEL(1, "1级"),
    TWO_LEVEL(2, "2级"),
    ;

    int code;
    String description;

    RiskQcStandardLevelEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code) {
        for (RiskQcStandardLevelEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return "";
    }
}
