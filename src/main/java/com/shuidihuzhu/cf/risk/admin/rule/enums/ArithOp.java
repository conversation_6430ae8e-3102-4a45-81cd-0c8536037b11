package com.shuidihuzhu.cf.risk.admin.rule.enums;

/**
 * <AUTHOR>
 * @date 2020-02-28
 **/
public enum ArithOp {

    ADD("+", "+"),
    SUBTRACT("-", "-"),
    MULTIPLY("*", "*"),
    DIVIDE("/", "/"),
    ;

    String name;
    String script;

    ArithOp(String name, String script) {
        this.name = name;
        this.script = script;
    }

    public String getName() {
        return name;
    }

    public String getScript() {
        return script;
    }
}
