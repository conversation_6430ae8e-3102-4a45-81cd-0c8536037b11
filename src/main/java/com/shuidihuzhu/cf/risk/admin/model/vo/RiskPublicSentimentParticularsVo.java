package com.shuidihuzhu.cf.risk.admin.model.vo;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Map;

/**
 * 舆情详情页
 *
 * @Auther: subing
 * @Date: 2020/2/19
 */
@Data
public class RiskPublicSentimentParticularsVo {
    private String infoSource;
    private Timestamp publishTime;
    private String nickName;
    private String infoFeedBack;
    private String userType;
    private Map<String, Object> supplementInfo;


    private int disposeStatus;
    private String disposeName;
    private Timestamp disposeTime;
    private int infoClassify;
    private String infoClassifyOther;
    private int caseId;
    private String infoType;
    private String solution;
    private String solutionOther;
    private String department;
    private long workOrderId;
    private long reportId;
    private String schedule;
    private String replySituation;
    private Timestamp replyTime;
    private Object replyContent;
    private String satisfaction;
    private int disposeCount;
    private String images;
    private String otherExt;

    private String title;
    private String url;
    private String videoUrl;
    private String imgUrl;
    private String content;
}
