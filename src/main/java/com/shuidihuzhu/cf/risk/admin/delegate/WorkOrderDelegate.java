package com.shuidihuzhu.cf.risk.admin.delegate;

import com.shuidihuzhu.cf.risk.admin.model.constant.QcOrderConst;
import com.shuidihuzhu.client.cf.workorder.config.WorkOrderHelper;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageFeignClient;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkOrderDelegate {

    @Resource
    private WorkOrderStorageFeignClient workOrderStorageFeignClient;

    @Resource
    private WorkOrderReadFeignClient workOrderReadFeignClient;

    private static final int KEY_RELATE_ORDER = WorkOrderHelper.Storage.QC_ORDER_ID;

    public Response<List<Long>> getAllQcOrderIdListBySourceId(long workOrderId) {
        Response<List<VonStorageVO>> resp = workOrderStorageFeignClient.getListByType(workOrderId, KEY_RELATE_ORDER);
        if (resp == null || resp.notOk() || resp.getData() == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        List<Long> collect = resp.getData().stream().map(v -> Long.valueOf(v.getValue())).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(collect);
    }

    public Response<List<BasicWorkOrder>> getListByOrderIdList(List<Long> relateWorkOrderIdList) {
        return workOrderReadFeignClient.getListByOrderIdList(relateWorkOrderIdList);
    }

    public Response<BasicWorkOrder> getByOrderId(long workOrderId) {
        return workOrderReadFeignClient.getByOrderId(workOrderId);
    }
}
