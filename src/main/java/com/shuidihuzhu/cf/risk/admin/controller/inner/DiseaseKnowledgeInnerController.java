package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseKnowledgeBiz;
import com.shuidihuzhu.cf.risk.client.admin.disease.DiseaseKnowledgeClient;
import com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseCheckDto;
import com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseKnowledgeDto;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@Api("疾病知识接口")
public class DiseaseKnowledgeInnerController implements DiseaseKnowledgeClient {

    @Autowired
    private RiskDiseaseKnowledgeBiz diseaseKnowledgeBiz;


    @Override
    public Response addOrUpdate(DiseaseKnowledgeDto riskDiseaseKnowledge) {
        return NewResponseUtil.makeSuccess(diseaseKnowledgeBiz.addOrUpdate(riskDiseaseKnowledge));
    }

    @Override
    public Response delKnowledgeById(long id) {
        return NewResponseUtil.makeSuccess(diseaseKnowledgeBiz.del(id));
    }

    @Override
    public Response<DiseaseKnowledgeDto> getKnowledgeDetail(long id) {
        return NewResponseUtil.makeSuccess(diseaseKnowledgeBiz.detail(id));
    }

    @Override
    public  Response<Map<Integer, List<DiseaseKnowledgeDto>>> list(String diseaseName, String startTime, String endTime, int limit, int offset) {
        return NewResponseUtil.makeSuccess(diseaseKnowledgeBiz.list(diseaseName, startTime, endTime, limit, offset));
    }

    @Override
    public Response<DiseaseCheckDto> getCheckDetail(String number) {
        return NewResponseUtil.makeSuccess(diseaseKnowledgeBiz.getCheckDetail(number));
    }
}
