package com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio;

import lombok.Data;

/**
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=932950052
 * <AUTHOR>
 * {
 * "label_class_cn": "该样本为有效质检工单",
 * "prob_score": 0.999986529,
 * "trigger_word": null,
 * "extra": {
 * "predictions": "[[0.0000135243909,0.999986529]]",
 * "timeCost": "[22,169]"
 * }
 */
@Data
public class AiTextValidForAudioResp {

    private float preb;

//    @Data
//    static class Extra {
//        private String timeCost;
//    }
}
