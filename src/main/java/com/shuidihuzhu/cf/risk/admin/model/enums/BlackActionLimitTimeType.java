package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/3/15 14:36
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum BlackActionLimitTimeType {
    DAYS_30(1, "30天"),
    DAYS_90(2, "90天"),
    DAYS_180(3, "180天"),
    DAYS_360(4, "360天"),
    FOREVER(5, "永久"),
    ;

    private final int code;
    private final String desc;

    public static BlackActionLimitTimeType fromCode(int code) {
        return idMap.get(code);
    }

    private static final Map<Integer, BlackActionLimitTimeType> idMap = Arrays.stream(values()).collect(Collectors.toMap(BlackActionLimitTimeType::getCode, Function.identity()));

}
