package com.shuidihuzhu.cf.risk.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Auther: subing
 * @Date: 2020/11/16
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum QcAppealProblemStatusEnum {
    PASS(1, "通过"),
    REJECTED(2,"驳回");

    private int code;
    private String desc;

    public static String findOfCode(int code){
        for (QcAppealProblemStatusEnum value : values()) {
            if (value.getCode() == code){
                return value.getDesc();
            }
        }
        return "";
    }
}
