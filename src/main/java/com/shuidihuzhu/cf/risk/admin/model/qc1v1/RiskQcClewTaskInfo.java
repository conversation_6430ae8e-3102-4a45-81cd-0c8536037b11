package com.shuidihuzhu.cf.risk.admin.model.qc1v1;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.risk.admin.model.enums.qc1v1.TaskPhaseEnum;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Auther: subing
 * @Date: 2020/8/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskQcClewTaskInfo {
    private String mobile;
    private NumberMaskVo mobileMask;
    private String newMobile;
    private NumberMaskVo newMobileMask;
    private String assignTime;
    /**
     * 服务阶段
     */
    private String phase;
    /**
     * 微信通过状态
     */
    private Integer weChatPassStatus;
    /**
     * 初审代录入状态
     */
    private Integer preposeMaterialStatus;
    /**
     *服务人姓名
     */
    private String clewTaskName;
    /**
     * 服务内容
     */
    private Integer clewTaskJobContent;

    public static RiskQcClewTaskInfo buildInfo(CfClueInfoModel cfClueInfoModel, MaskUtil maskUtil){
        if (cfClueInfoModel == null){
            return null;
        }
        RiskQcClewTaskInfo riskQcClewTaskInfo = new RiskQcClewTaskInfo();
        riskQcClewTaskInfo.setMobileMask(maskUtil.buildByDecryptPhone(cfClueInfoModel.getClewPhone()));
        riskQcClewTaskInfo.setMobile(null);
        riskQcClewTaskInfo.setNewMobileMask(maskUtil.buildByDecryptPhone(cfClueInfoModel.getClewSecondPhone()));
        riskQcClewTaskInfo.setNewMobile(null);
        riskQcClewTaskInfo.setAssignTime(cfClueInfoModel.getAssignTime());
        riskQcClewTaskInfo.setPhase(TaskPhaseEnum.fromCode(cfClueInfoModel.getServicePhase()) == null ? null : TaskPhaseEnum.fromCode(cfClueInfoModel.getServicePhase()).getDesc());
        riskQcClewTaskInfo.setWeChatPassStatus(cfClueInfoModel.getWechatPass());
        riskQcClewTaskInfo.setPreposeMaterialStatus(cfClueInfoModel.getPreMsgStatus());
        riskQcClewTaskInfo.setClewTaskJobContent(cfClueInfoModel.getWorkContentType());
        return riskQcClewTaskInfo;
    }

     public RiskQcClewTaskInfo buildClewTaskName(String clewTaskName){
        this.clewTaskName = StringUtils.trimToNull(clewTaskName);
        return this;
    }

}
