package com.shuidihuzhu.cf.risk.admin.model.qc.subject;

import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentEditor;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QcHospitalDeptDetailVO {
    @ApiModelProperty("详情")
    private DepartmentChangeDetailModel info;

    @ApiModelProperty("工单信息")
    private BasicWorkOrder orderInfo;
}
