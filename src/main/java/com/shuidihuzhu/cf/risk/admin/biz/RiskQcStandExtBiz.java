package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardExt;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
public interface RiskQcStandExtBiz {
    List<RiskQcStandardExt> getByStandardIds(List<Long> standardIds);

    int add(long qcStandardId, long firstProperty, long secondProperty, int useScene);

    List<RiskQcStandardExt> findByFirstPropertyAndScene(List<Long> firstPropertyIds, int useScene);
}
