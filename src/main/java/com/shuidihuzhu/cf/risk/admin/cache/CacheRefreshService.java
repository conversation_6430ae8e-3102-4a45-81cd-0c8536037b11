package com.shuidihuzhu.cf.risk.admin.cache;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/8/7 15:05
 */
@Slf4j
@Validated
@Service
public class CacheRefreshService<T> {

    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;

    public void subscribeTopic(@NotBlank String topic, @NotNull ISubscribeHandle<T> handler, Class<T> msgClass) {
        redissonHandler.topicSubscibe(topic, handler::onMessage, msgClass);
    }

    public void publishTopic(@NotBlank String topic, T msg) {
        redissonHandler.topicPublish(topic, msg);
    }

}
