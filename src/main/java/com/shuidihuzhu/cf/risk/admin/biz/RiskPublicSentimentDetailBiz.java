package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail;

import java.util.List;

public interface RiskPublicSentimentDetailBiz {
    RiskPublicSentimentDetail add(String riskPublicSentimentDetail, long id);

    int updateByPublicSentimentId(String riskPublicSentimentDetailJson, long id);

    RiskPublicSentimentDetail getByPublicSentimentId(long publicSentimentId);

    List<RiskPublicSentimentDetail> getByPublicSentimentIds(List<Long> publicSentimentIds);


}
