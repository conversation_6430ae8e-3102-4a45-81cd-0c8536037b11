package com.shuidihuzhu.cf.risk.admin.configuration.exceptionhandle;

import com.shuidihuzhu.cf.enhancer.exception.ServiceResponseException;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class ServiceExceptionHandler {

    @ExceptionHandler(ServiceResponseException.class)
    @ResponseBody
    public Response<Void> handleBindException(ServiceResponseException ex) {
        log.info("服务错误 ServiceResponseException", ex);
        Response<Void> response = ex.getResponse();
        if (response == null) {
            log.error("ServiceResponseException response 不能为null", ex);
            return EhResponseUtils.failWithMessage("ServiceResponseException response 不能为null");
        }
        return response;
    }

}