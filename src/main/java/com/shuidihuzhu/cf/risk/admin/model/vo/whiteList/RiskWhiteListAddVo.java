package com.shuidihuzhu.cf.risk.admin.model.vo.whiteList;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.common.web.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("身份关联异常白名单")
public class RiskWhiteListAddVo {

    @ApiModelProperty("序号")
    private long id;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("身份证号")
    private String idCard;
    private NumberMaskVo idCardMask;

    @ApiModelProperty("手机号")
    private String phoneNumber;
    private NumberMaskVo phoneNumberMask;

    @NotNull(message = "添加原因不能为空")
    @Min(value = 1) @Max(value = 256, message = "原因最多为256个字")
    @ApiModelProperty("添加原因")
    private String addReason;

    @NotNull(message = "过期时间")
    @ApiModelProperty("过期时间")
    private String expireTime;


}
