package com.shuidihuzhu.cf.risk.admin.delegate;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanAsynApiResponse;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.risk.admin.model.param.AsrRequestParam;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcAsrResultVO;
import com.shuidihuzhu.common.web.model.Response;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiAsrDelegate {

    @Autowired
    private OceanApiClient oceanApiClient;

    private static final String FIELD_MATERIAL_ID = "materialId";
    private static final String HANDLE_TYPE = "handleType";
    private static final String FIELD_WORK_ORDER_ID = "workOrderId";

    @Getter
    public enum HandleTypeEnum {
        HANDLE_QC_BD(0,"materialId为risk_qc_materials_info表的id", RiskControlWordCategoryDO.RiskWordUseScene.QC_BD),
        HANDLE_MATERIAL_QC(1,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.MATERIAL_QC),
        HANDLE_MATERIAL_ACTIVE_SERVICE_QC(2,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.MATERIAL_ACTIVE_SERVICE_QC),
        HANDLE_INTERNAL_AUDIT_HIGH_RISK_QC(3,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.INTERNAL_AUDIT_HIGH_RISK_QC),
        HANDLE_WX_1V1_QC_RECHECK(4,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.WX_1V1_QC_RECHECK),
        HANDLE_REPORT_RECORD(5,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.REPORT_RECORD),
        HANDLE_MDC_RECORD(6,"materialId为sd_medical_call_record表的id",RiskControlWordCategoryDO.RiskWordUseScene.REPORT_RECORD),
        HANDLE_QI_WORK_RECORD(7,"materialId为cf_clew_qi_work_call_records表的id",RiskControlWordCategoryDO.RiskWordUseScene.WX_1V1_QC_RECHECK),
        ;
        private int code;
        private String desc;
        private RiskControlWordCategoryDO.RiskWordUseScene riskWordUseScene;

        HandleTypeEnum(int code, String desc, RiskControlWordCategoryDO.RiskWordUseScene riskWordUseScene) {
            this.code = code;
            this.desc = desc;
            this.riskWordUseScene = riskWordUseScene;
        }
        public static Map<Integer, HandleTypeEnum> codeMap  = Maps.newHashMap();
        static {
            Arrays.stream(HandleTypeEnum.values()).forEach(item -> codeMap.put(item.getCode(), item));
        }
    }

    public static String TAG = "ai-asr";
    public static String USER_ID = "10007";
    public static String TOKEN = "e4f70a1f8a8e1912";

    public static String TAG_ZIYAN = "ai-asr";
    public static String USER_ID_ZIYAN = "30007";
    public static String TOKEN_ZIYAN = "e19a8aa487facf65";

    public void analyse(long workOrderId, long materialId, String url, HandleTypeEnum handleTypeEnum, boolean useZiyanAsr) {
        log.info("AiAsrDelegate in workOrderId {}, id {}, url {}", workOrderId, materialId, url);
        url = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam(FIELD_MATERIAL_ID, String.valueOf(materialId))
                .queryParam(HANDLE_TYPE, handleTypeEnum.getCode())
                .queryParam(FIELD_WORK_ORDER_ID, String.valueOf(workOrderId))
                .build()
                .toString();

        OceanApiRequest req = new OceanApiRequest();
        req.setTag(TAG);
        req.setUserId(USER_ID);
        req.setToken(TOKEN);

        if(useZiyanAsr && handleTypeEnum.equals(HandleTypeEnum.HANDLE_QC_BD)){
            req.setTag(TAG_ZIYAN);
            req.setUserId(USER_ID_ZIYAN);
            req.setToken(TOKEN_ZIYAN);
        }

        AsrRequestParam asrRequestParam = new AsrRequestParam();
        asrRequestParam.setOptions(new AsrRequestParam.Options());
        asrRequestParam.setPriority(0);
        asrRequestParam.setInput_url(url);
        req.setBody(JSON.toJSONString(asrRequestParam));
        Response<OceanAsynApiResponse> resp = oceanApiClient.agentAsyn(req);
        log.info("AiAsrDelegate analyse req {}, resp {}", req, resp);
    }

    public void analyseUsePriority(long workOrderId, long materialId, String url, HandleTypeEnum handleTypeEnum,Integer priority) {
        log.info("analyseUsePriority in workOrderId {}, id {}, url {}", workOrderId, materialId, url);
        url = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam(FIELD_MATERIAL_ID, String.valueOf(materialId))
                .queryParam(HANDLE_TYPE, handleTypeEnum.getCode())
                .queryParam(FIELD_WORK_ORDER_ID, String.valueOf(workOrderId))
                .build()
                .toString();

        OceanApiRequest req = new OceanApiRequest();
        req.setTag(TAG);
        req.setUserId(USER_ID);
        req.setToken(TOKEN);

        AsrRequestParam asrRequestParam = new AsrRequestParam();
        asrRequestParam.setOptions(new AsrRequestParam.Options());
        asrRequestParam.setPriority(priority);
        asrRequestParam.setInput_url(url);
        req.setBody(JSON.toJSONString(asrRequestParam));
        Response<OceanAsynApiResponse> resp = oceanApiClient.agentAsyn(req);
        log.info("analyseUsePriority analyse req {}, resp {}", req, resp);
    }

    public static void main(String[] args) {
        String url = "http://www.baidu.com?a=2&";
        UriComponents components = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam(FIELD_MATERIAL_ID, "aaa1")
                .build();
        url = components
                .toString();
        System.out.println("url = " + url);
        System.out.println("components.getQueryParams().get(\"asrId\") = " + components.getQueryParams().getFirst(FIELD_MATERIAL_ID));
    }

    public QcAsrResultVO getResultByCallback(OceanApiMQResponse payload) {
        log.info("getResultByCallback payload {}", payload);
        if (payload == null) {
            log.warn("payload null");
            return null;
        }
        int resultCode = payload.getResultCode();
        if (resultCode != 0) {
            log.warn("fail resultCode {}", resultCode);
            return null;
        }
        String body = payload.getBody();
        if (StringUtils.isEmpty(body)) {
            log.warn("fail body {}", body);
            return null;
        }
        QcAsrResultVO r = JSON.parseObject(body, QcAsrResultVO.class);
        if (r == null) {
            return null;
        }
        String inputUrl = Optional.of(r)
                .map(QcAsrResultVO::getInput_url)
                .orElse("");
        if (StringUtils.isEmpty(inputUrl)) {
            log.warn("inputUrl empty");
            return null;
        }
        UriComponents components = UriComponentsBuilder.fromHttpUrl(inputUrl)
                .build();
        String handleType = components
                .getQueryParams()
                .getFirst(HANDLE_TYPE);
        String asrIdStr = components
                .getQueryParams()
                .getFirst(FIELD_MATERIAL_ID);
        String workOrderIdStr = components
                .getQueryParams()
                .getFirst(FIELD_WORK_ORDER_ID);
        assert asrIdStr != null;
        assert workOrderIdStr != null;
        r.setHandleTypeEnum(Optional.ofNullable(HandleTypeEnum.codeMap.get(Integer.valueOf(handleType))).orElse(HandleTypeEnum.HANDLE_QC_BD));
        r.setMaterialId(Long.valueOf(asrIdStr));
        r.setWorkOrderId(Long.valueOf(workOrderIdStr));
        List<QcAsrResultVO.Sentence> sentences = r.getResult().getSentences();
        if (CollectionUtils.isNotEmpty(sentences)) {
            QcAsrResultVO.Result result = new QcAsrResultVO.Result();
            result.setSentences(sentences.stream().sorted(Comparator.comparing(QcAsrResultVO.Sentence::getBegin_time)).collect(Collectors.toList()));
            r.setResult(result);
        }
        return r;
    }
}
