package com.shuidihuzhu.cf.risk.admin.delegate;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanAsynApiResponse;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.risk.admin.model.param.AsrRequestParam;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcAsrResultVO;
import com.shuidihuzhu.common.web.model.Response;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiAsrDelegate {

    @Autowired
    private OceanApiClient oceanApiClient;

    private static final String FIELD_MATERIAL_ID = "materialId";
    private static final String HANDLE_TYPE = "handleType";
    private static final String FIELD_WORK_ORDER_ID = "workOrderId";

    @Getter
    public enum HandleTypeEnum {
        HANDLE_QC_BD(0,"materialId为risk_qc_materials_info表的id", RiskControlWordCategoryDO.RiskWordUseScene.QC_BD),
        HANDLE_MATERIAL_QC(1,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.MATERIAL_QC),
        HANDLE_MATERIAL_ACTIVE_SERVICE_QC(2,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.MATERIAL_ACTIVE_SERVICE_QC),
        HANDLE_INTERNAL_AUDIT_HIGH_RISK_QC(3,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.INTERNAL_AUDIT_HIGH_RISK_QC),
        HANDLE_WX_1V1_QC_RECHECK(4,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.WX_1V1_QC_RECHECK),
        HANDLE_REPORT_RECORD(5,"materialId为work_order_recording表的id", RiskControlWordCategoryDO.RiskWordUseScene.REPORT_RECORD),
        HANDLE_MDC_RECORD(6,"materialId为sd_medical_call_record表的id",RiskControlWordCategoryDO.RiskWordUseScene.REPORT_RECORD),
        HANDLE_QI_WORK_RECORD(7,"materialId为cf_clew_qi_work_call_records表的id",RiskControlWordCategoryDO.RiskWordUseScene.WX_1V1_QC_RECHECK),
        ;
        private int code;
        private String desc;
        private RiskControlWordCategoryDO.RiskWordUseScene riskWordUseScene;

        HandleTypeEnum(int code, String desc, RiskControlWordCategoryDO.RiskWordUseScene riskWordUseScene) {
            this.code = code;
            this.desc = desc;
            this.riskWordUseScene = riskWordUseScene;
        }
        public static Map<Integer, HandleTypeEnum> codeMap  = Maps.newHashMap();
        static {
            Arrays.stream(HandleTypeEnum.values()).forEach(item -> codeMap.put(item.getCode(), item));
        }
    }

    public static String TAG = "ai-asr";
    public static String USER_ID = "10007";
    public static String TOKEN = "e4f70a1f8a8e1912";

    public static String TAG_ZIYAN = "ai-asr";
    public static String USER_ID_ZIYAN = "30007";
    public static String TOKEN_ZIYAN = "e19a8aa487facf65";

    /**
     * 调用AI语音识别服务进行录音分析
     *
     * 该方法是AI语音识别分析的核心入口：
     * 1. 在录音URL中添加业务参数（工单ID、材料ID、处理类型）
     * 2. 构建Ocean API请求对象，设置认证信息
     * 3. 根据业务类型选择不同的ASR服务（普通ASR或紫燕ASR）
     * 4. 发起异步语音识别请求
     *
     * @param workOrderId 工单ID，用于标识业务上下文
     * @param materialId 材料ID，通常是录音记录的数据库主键
     * @param url 带签名的录音文件COS访问URL
     * @param handleTypeEnum 处理类型枚举，决定使用哪种分析策略
     * @param useZiyanAsr 是否使用紫燕ASR服务（仅对QC_BD类型有效）
     */
    public void analyse(long workOrderId, long materialId, String url, HandleTypeEnum handleTypeEnum, boolean useZiyanAsr) {
        log.info("AiAsrDelegate analyse start - workOrderId: {}, materialId: {}, handleType: {}, useZiyanAsr: {}",
                workOrderId, materialId, handleTypeEnum, useZiyanAsr);

        // 在录音URL中添加查询参数，用于回调时识别具体的业务上下文
        url = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam(FIELD_MATERIAL_ID, String.valueOf(materialId))      // 材料ID
                .queryParam(HANDLE_TYPE, handleTypeEnum.getCode())              // 处理类型代码
                .queryParam(FIELD_WORK_ORDER_ID, String.valueOf(workOrderId))   // 工单ID
                .build()
                .toString();

        // 构建Ocean API请求对象，设置默认的认证信息
        OceanApiRequest req = new OceanApiRequest();
        req.setTag(TAG);           // 服务标识
        req.setUserId(USER_ID);    // 用户ID
        req.setToken(TOKEN);       // 认证令牌

        // 特殊处理：对于QC_BD类型且启用紫燕ASR的情况，使用专用的认证信息
        if(useZiyanAsr && handleTypeEnum.equals(HandleTypeEnum.HANDLE_QC_BD)){
            req.setTag(TAG_ZIYAN);
            req.setUserId(USER_ID_ZIYAN);
            req.setToken(TOKEN_ZIYAN);
            log.info("Using ZiYan ASR service for QC_BD type");
        }

        // 构建ASR请求参数
        AsrRequestParam asrRequestParam = new AsrRequestParam();
        asrRequestParam.setOptions(new AsrRequestParam.Options());  // 设置默认选项
        asrRequestParam.setPriority(0);                             // 设置优先级（0为默认优先级）
        asrRequestParam.setInput_url(url);                          // 设置录音文件URL

        // 将请求参数序列化为JSON并设置到请求体中
        req.setBody(JSON.toJSONString(asrRequestParam));

        // 发起异步语音识别请求
        Response<OceanAsynApiResponse> resp = oceanApiClient.agentAsyn(req);
        log.info("AiAsrDelegate analyse completed - req: {}, resp: {}", req, resp);
    }

    public void analyseUsePriority(long workOrderId, long materialId, String url, HandleTypeEnum handleTypeEnum,Integer priority) {
        log.info("analyseUsePriority in workOrderId {}, id {}, url {}", workOrderId, materialId, url);
        url = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam(FIELD_MATERIAL_ID, String.valueOf(materialId))
                .queryParam(HANDLE_TYPE, handleTypeEnum.getCode())
                .queryParam(FIELD_WORK_ORDER_ID, String.valueOf(workOrderId))
                .build()
                .toString();

        OceanApiRequest req = new OceanApiRequest();
        req.setTag(TAG);
        req.setUserId(USER_ID);
        req.setToken(TOKEN);

        AsrRequestParam asrRequestParam = new AsrRequestParam();
        asrRequestParam.setOptions(new AsrRequestParam.Options());
        asrRequestParam.setPriority(priority);
        asrRequestParam.setInput_url(url);
        req.setBody(JSON.toJSONString(asrRequestParam));
        Response<OceanAsynApiResponse> resp = oceanApiClient.agentAsyn(req);
        log.info("analyseUsePriority analyse req {}, resp {}", req, resp);
    }

    public static void main(String[] args) {
        String url = "http://www.baidu.com?a=2&";
        UriComponents components = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam(FIELD_MATERIAL_ID, "aaa1")
                .build();
        url = components
                .toString();
        System.out.println("url = " + url);
        System.out.println("components.getQueryParams().get(\"asrId\") = " + components.getQueryParams().getFirst(FIELD_MATERIAL_ID));
    }

    public QcAsrResultVO getResultByCallback(OceanApiMQResponse payload) {
        log.info("getResultByCallback payload {}", payload);
        if (payload == null) {
            log.warn("payload null");
            return null;
        }
        int resultCode = payload.getResultCode();
        if (resultCode != 0) {
            log.warn("fail resultCode {}", resultCode);
            return null;
        }
        String body = payload.getBody();
        if (StringUtils.isEmpty(body)) {
            log.warn("fail body {}", body);
            return null;
        }
        QcAsrResultVO r = JSON.parseObject(body, QcAsrResultVO.class);
        if (r == null) {
            return null;
        }
        String inputUrl = Optional.of(r)
                .map(QcAsrResultVO::getInput_url)
                .orElse("");
        if (StringUtils.isEmpty(inputUrl)) {
            log.warn("inputUrl empty");
            return null;
        }
        UriComponents components = UriComponentsBuilder.fromHttpUrl(inputUrl)
                .build();
        String handleType = components
                .getQueryParams()
                .getFirst(HANDLE_TYPE);
        String asrIdStr = components
                .getQueryParams()
                .getFirst(FIELD_MATERIAL_ID);
        String workOrderIdStr = components
                .getQueryParams()
                .getFirst(FIELD_WORK_ORDER_ID);
        assert asrIdStr != null;
        assert workOrderIdStr != null;
        r.setHandleTypeEnum(Optional.ofNullable(HandleTypeEnum.codeMap.get(Integer.valueOf(handleType))).orElse(HandleTypeEnum.HANDLE_QC_BD));
        r.setMaterialId(Long.valueOf(asrIdStr));
        r.setWorkOrderId(Long.valueOf(workOrderIdStr));
        List<QcAsrResultVO.Sentence> sentences = r.getResult().getSentences();
        if (CollectionUtils.isNotEmpty(sentences)) {
            QcAsrResultVO.Result result = new QcAsrResultVO.Result();
            result.setSentences(sentences.stream().sorted(Comparator.comparing(QcAsrResultVO.Sentence::getBegin_time)).collect(Collectors.toList()));
            r.setResult(result);
        }
        return r;
    }
}
