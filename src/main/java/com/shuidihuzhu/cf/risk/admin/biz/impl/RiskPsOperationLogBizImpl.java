package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskPsOperationLogBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPsOperationLogDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsOperationLog;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;
import com.shuidihuzhu.pf.common.v2.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class RiskPsOperationLogBizImpl implements RiskPsOperationLogBiz {

    @Autowired
    private RiskPsOperationLogDao riskPsOperationLogDao;
    @Override
    public int add(RiskPsOperationLog operationLog) {
        return riskPsOperationLogDao.save(operationLog);
    }

    @Override
    public PageResponse<RiskPsOperationLog> listByPsIdOfPage(long psId, String pageJson) {
        PageRequest pageRequest = PageUtil.parseJsonString(pageJson);
        List<RiskPsOperationLog> data = riskPsOperationLogDao.listByPsIdOfPage(psId, pageRequest);

        return PageUtil.buildPageResponse(data, pageRequest);
    }
}
