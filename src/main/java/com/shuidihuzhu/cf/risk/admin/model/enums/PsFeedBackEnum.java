package com.shuidihuzhu.cf.risk.admin.model.enums;

public enum PsFeedBackEnum {
    NONE(1, "无需回复"),
    LATER(2,"稍后回复"),
    CONFIRM(3,"方案确定后回复"),
    RESPONSE(4, "已回复，待应答"),
    ACCEPT(5, "已回复，反馈人认可"),
    NO_ACCEPT(6, "已回复，反馈人不认可"),
    ;

    private int code;
    private String description;

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    PsFeedBackEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String findOfCode(int code){
        for (PsFeedBackEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
