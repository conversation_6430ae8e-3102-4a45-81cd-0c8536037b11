package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcCaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcClewTaskInfo;
import com.shuidihuzhu.cf.risk.admin.model.vo.qc1v1.ClewPreposeMaterialVo;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import lombok.*;

/**
 * @Auther: subing
 * @Date: 2020/8/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskQc1v1DetailInfoVo {
    /**
     * 服务信息
     */
    private RiskQcClewTaskInfo riskQcClewTaskInfo;
    /**
     * 案例信息
     */
    private RiskQcCaseInfo riskQcCaseInfo;
    /**
     * 代录入信息
     */
    private ClewPreposeMaterialVo preposeMaterial;
    /**
     * 预审信息
     */
    private CreditInfoVo creditInfo;
    /**
     * 质检工单信息
     */
    private RiskQcWorkOrderVo workOrderVO;
}
