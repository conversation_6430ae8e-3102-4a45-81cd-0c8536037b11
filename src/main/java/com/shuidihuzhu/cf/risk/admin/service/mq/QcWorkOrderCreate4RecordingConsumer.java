package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.model.QcCreateWorkOrderModel;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.recording.WorkOrderRecordingHandler;
import com.shuidihuzhu.cf.risk.admin.service.recording.WorkOrderRecordingHandlerRegister;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTopicCons;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2022-04-08 11:03,
 **/
@Slf4j
@Service
@RocketMQListener(id = QcConst.QC_WORK_ORDER_CREATE_4_RECODING_MSG,
        tags = QcConst.QC_WORK_ORDER_CREATE_4_RECODING_MSG,
        topic = CfRiskMQTopicCons.CF_RISK_TOPIC)
public class QcWorkOrderCreate4RecordingConsumer implements MessageListener<QcCreateWorkOrderModel> {

    @Autowired
    private QcAudioAsrService qcAudioAsrService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<QcCreateWorkOrderModel> mqMessage) {
        QcCreateWorkOrderModel payload = mqMessage.getPayload();
        log.info("QcWorkOrderCreate4RecordingConsumer payload:{}", payload);
        int reconsumeTimes = mqMessage.getReconsumeTimes();
        if (reconsumeTimes > 20) {
            log.error("QcWorkOrderCreateConsumer reconsumeTimes out 重试超过最大次数 {}", payload);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            qcAudioAsrService.handleWorkOrderRecording(payload.getWorkOrderId(), payload.getWorkOrderType());
        } catch (Exception e) {
            log.error("QcWorkOrderCreate4RecordingConsumer consumeMessage error", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }


}
