package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: lixiaoshuang
 * @create: 2020-08-20 20:07
 **/
@Service
@RocketMQListener(id = CfClientMQTagCons.CF_CLUE_FUWU_CREATE_CASE_MSG,
        tags = CfClientMQTagCons.CF_CLUE_FUWU_CREATE_CASE_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class Qc1v1CreateCaseConsumer implements MessageListener<Map<Long, Integer>> {
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Map<Long, Integer>> mqMessage) {
        Map<Long, Integer> payload = mqMessage.getPayload();
        log.info("Qc1v1CreateCaseConsumer payload:{}", JSONObject.toJSONString(payload));
        if (MapUtils.isEmpty(payload)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        for (Map.Entry<Long, Integer> longIntegerEntry : payload.entrySet()) {
            var taskId = longIntegerEntry.getKey();
            var caseId = longIntegerEntry.getValue();
            FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = crowdfundingFeignClient.getCaseInfoById(caseId);
            if (crowdfundingInfoFeignResponse.notOk()) {
                return ConsumeStatus.RECONSUME_LATER;
            }
            var userId = crowdfundingInfoFeignResponse.getData().getUserId();
            riskQcBaseInfoBiz.updateCaseId(taskId, caseId, List.of(WorkOrderType.qc_wx_1v1.getType(), WorkOrderType.qc_wx_1v1_repeat.getType()));
            riskQcSearchIndexBiz.updateCaseIdAndUserId(taskId, QcTypeEnum.WX_1V1.getCode(), caseId, userId);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
