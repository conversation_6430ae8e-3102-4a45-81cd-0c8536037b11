package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.*;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotRuleDto;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotScheduleTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyOperateTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategyLog;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.quality.sampling.QualitySpotStrategyQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotStrategyDetailVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotStrategyLogVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotStrategyVo;
import com.shuidihuzhu.cf.risk.admin.rule.compile.GroovyCompile;
import com.shuidihuzhu.cf.risk.admin.rule.enums.RuleTypeEnum;
import com.shuidihuzhu.cf.risk.admin.rule.model.Rule;
import com.shuidihuzhu.cf.risk.admin.rule.utils.GroovyScriptRunUtil;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.*;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotExecuteModelEnum;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingOperationFeignClient;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.AssignTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/15 16:36
 */
@Validated
@Service
@Slf4j
public class QualitySpotStrategyService {

    private static final String UPDATE_LEVEL_CONF_KEY = "cf_risk_admin_quality_spot_st_scene_";
    private static final long UPDATE_LEVEL_CONF_KEY_LEAVE_TIME = 10 * 1000;

    @Resource
    private QualitySpotStrategyBiz qualitySpotStrategyBiz;
    @Resource
    private GroovyCompile groovyCompile;
    @Resource
    private QualitySpotLevelConfBiz qualitySpotLevelConfBiz;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private SeaAccountService seaAccountService;
    @Resource
    private QualitySpotRuleBiz qualitySpotRuleBiz;
    @Resource
    private RiskQualitySpotStrategyTypeRelBiz qualitySpotStrategyTypeRelBiz;
    @Resource
    private RiskQualitySpotTypeBiz spotTypeBiz;

    public PageResult<RiskQualitySpotStrategyVo> queryStrategyList(QualitySpotStrategyQuery qualitySpotStrategyQuery) {
        Page<RiskQualitySpotStrategy> strategies = qualitySpotStrategyBiz.listByQuery(qualitySpotStrategyQuery);
        fullSceneInfo(strategies.getResult());
        return new PageResult<>(strategies.getResult().stream()
                .map(RiskQualitySpotStrategyVo::new).collect(Collectors.toList()),
                strategies.getPageNum(), strategies.getPageSize(), strategies.getTotal());
    }

    private void fullSceneInfo(List<RiskQualitySpotStrategy> strategies) {
        if (CollectionUtils.isEmpty(strategies)){
            return;
        }
        List<RiskQualitySpotTypeRel>  riskQualitySpotTypeRels = qualitySpotStrategyTypeRelBiz.
                findByStrategyIdList(strategies.stream().map(RiskQualitySpotStrategy::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(riskQualitySpotTypeRels)){
            return;
        }
        Map<Long,  List<RiskQualitySpotTypeRel>> stringListMap = riskQualitySpotTypeRels.parallelStream()
                .collect(Collectors.groupingBy(RiskQualitySpotTypeRel::getStrategyId));
        for (RiskQualitySpotStrategy spotStrategy : strategies) {
            List<RiskQualitySpotTypeRel> riskQualitySpotTypeRels1 = stringListMap.get(spotStrategy.getId());
            if (CollectionUtils.isEmpty(riskQualitySpotTypeRels1)){
                continue;
            }
            spotStrategy.setSceneInfo(riskQualitySpotTypeRels1.get(0).getSceneInfo());
        }

    }

    public void enableStrategy(Long id, long adminUserId) {
        RiskQualitySpotStrategy dbStrategy = qualitySpotStrategyBiz.getModelById(id);
        checkStrategyScopeValid(getSecondScene(dbStrategy.getId()), QualitySpotScheduleTypeEnum.codeString2CodeList(dbStrategy.getStrategyScope()));
        qualitySpotStrategyBiz.updateStrategyStatus(id, QualitySpotStrategyStatusEnum.ENABLE, DateUtil.getStr2LDate("3000-01-01 00:00:00"), adminUserId);
        qualitySpotRuleBiz.openOldRuleByStrategyId(id);
        //保存操作日志
        saveStrategyLog(id, QualitySpotStrategyOperateTypeEnum.ENABLE, adminUserId);
    }

    public void disableStrategy(Long id, long adminUserId) {
        Date tomorrowStartDate = new Date(Timestamp.valueOf(LocalDateTime.now().plusSeconds(1)).getTime());
        qualitySpotStrategyBiz.updateStrategyStatus(id, QualitySpotStrategyStatusEnum.DISABLE, tomorrowStartDate, adminUserId);
        qualitySpotRuleBiz.closeOldRuleByStrategyId(id);
        //保存操作日志
        saveStrategyLog(id, QualitySpotStrategyOperateTypeEnum.DISABLE, adminUserId);
    }

    public void saveStrategy(RiskQualitySpotStrategyDetailVo qualitySpotStrategyVo, long adminUserId) {
        RiskQualitySpotStrategy riskQualitySpotStrategy = new RiskQualitySpotStrategy();
        checkParam(qualitySpotStrategyVo);
        String key = UPDATE_LEVEL_CONF_KEY + qualitySpotStrategyVo.getSecondScene();
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_LEVEL_CONF_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请稍后重试");
            }
            //1.2 保证只有一个启用的策略
            checkStrategyScopeValidSole(qualitySpotStrategyVo);
            //2. 保存
            BeanUtils.copyProperties(qualitySpotStrategyVo, riskQualitySpotStrategy);
            riskQualitySpotStrategy.setStrategyScope(QualitySpotScheduleTypeEnum.codes2JoinString(qualitySpotStrategyVo.getStrategyScope()));
            riskQualitySpotStrategy.setStrategyParseTime(DateUtil.getStr2LDate(qualitySpotStrategyVo.getStrategyParseTime()));
            riskQualitySpotStrategy.setScene(qualitySpotStrategyVo.getSecondScene());
            SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
            riskQualitySpotStrategy.setOperateId(userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId());
            riskQualitySpotStrategy.setOperateName(userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg());
            qualitySpotStrategyBiz.saveStrategy(riskQualitySpotStrategy);
            //3. 保存规则
            saveRule(riskQualitySpotStrategy.getId(), qualitySpotStrategyVo.getRuleDef());
            //保存引用
            saveTypeRel(riskQualitySpotStrategy.getId(), qualitySpotStrategyVo.getFirstScene(), qualitySpotStrategyVo.getSecondScene());
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                redissonHandler.unLock(key, identify);
            }
        }
        //3. 记录日志
        saveStrategyLog(riskQualitySpotStrategy.getId(), QualitySpotStrategyOperateTypeEnum.CREATE, adminUserId);
    }

    private void checkParam(RiskQualitySpotStrategyDetailVo qualitySpotStrategyVo) {
        //1. 参数检查
        Date parseTime = DateUtil.getStr2LDate(qualitySpotStrategyVo.getStrategyParseTime());
        if (parseTime.getTime() < System.currentTimeMillis()) {
            throw new IllegalArgumentException("策略生效时间不允许早于当前时间");
        }
        if (log.isDebugEnabled()) {
            log.debug("策略保存,ruleDef:{}", qualitySpotStrategyVo.getRuleDef());
        }
        //1.1 将definition转为groovy ，有异常提前抛出
        if (log.isDebugEnabled()) {
            String ruleScript = groovyCompile.compileCriterion(qualitySpotStrategyVo.getRuleDef());
            try {
                GroovyScriptRunUtil.compileGroovy(RuleTypeEnum.RULE, Integer.MAX_VALUE, ruleScript);
            } catch (Exception e) {
                log.error("", e);
                throw new IllegalArgumentException("groovy脚本编译失败");
            }
            log.debug("策略保存,ruleScript:{}", ruleScript);
        }
        //验证执行方式
        QualitySpotExecuteModelEnum executeModelEnum = QualitySpotExecuteModelEnum.fromCode(qualitySpotStrategyVo.getExecuteMode());
        if (executeModelEnum == null) {
            throw new IllegalArgumentException("策略执行方式错误");
        }
    }

    private void saveTypeRel(Long id, long firstScene, long secondScene) {
        RiskQualitySpotTypeRel typeRel = qualitySpotStrategyTypeRelBiz.save(id, secondScene, "");
        if (typeRel == null) {
            return;
        }
        qualitySpotStrategyTypeRelBiz.save(id, firstScene, typeRel.getSceneInfo());
    }

    private void updateTypeRel(long strategyId, long firstScene, long secondScene) {
        List<RiskQualitySpotTypeRel> typeRels = qualitySpotStrategyTypeRelBiz.findByStrategyId(strategyId);
        if (CollectionUtils.isEmpty(typeRels)) {
            saveTypeRel(strategyId, firstScene, secondScene);
            return;
        }
        typeRels = typeRels.stream().filter(v -> v.getTypeId() != firstScene && v.getTypeId() != secondScene).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(typeRels)) {
            return;
        }
        //删除旧的引用
        qualitySpotStrategyTypeRelBiz.deleteByStrategyId(strategyId);
        saveTypeRel(strategyId, firstScene, secondScene);
    }

    private void saveRule(Long strategyId, String ruleDef) {
        List<Rule> rules = JSON.parseArray(ruleDef, Rule.class);
        List<RiskQualitySpotRule> ruleEntities = Lists.newArrayListWithCapacity(rules.size());
        for (Rule rule : rules) {
            RiskQualitySpotRule riskQualitySpotRule = new RiskQualitySpotRule();
            riskQualitySpotRule.setStrategyId(strategyId);
            riskQualitySpotRule.setName(rule.getName());
            riskQualitySpotRule.setPriority(rule.getPriority());
            riskQualitySpotRule.setStatus((byte) rule.getStatus());
            riskQualitySpotRule.setRuleScript(groovyCompile.compileCriterion(JSON.toJSONString(List.of(rule))));
            riskQualitySpotRule.setExcuteModeValue(StringUtils.trimToEmpty(String.valueOf(rule.getExecuteModelValue())));
            riskQualitySpotRule.setDataScopeString(rule.getDataScopeString());
            ruleEntities.add(riskQualitySpotRule);
        }
        qualitySpotRuleBiz.saveBatch(ruleEntities);
    }


    private void checkStrategyScopeValid(Long scene, List<Integer> strategyScopes) {
        if (CollectionUtils.isEmpty(strategyScopes)){
            return;
        }
        List<RiskQualitySpotStrategy> existsStrategies = qualitySpotStrategyBiz.listByValidScene(scene);
        Set<Integer> existsScopes = existsStrategies.stream()
                .map(strategy -> QualitySpotScheduleTypeEnum.codeString2CodeList(strategy.getStrategyScope()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        //如果已配置过每天策略，那么不允许配置任务策略
        if (existsScopes.contains(QualitySpotScheduleTypeEnum.EVERY_DAY.getCode())) {
            throw new IllegalArgumentException(QualitySpotScheduleTypeEnum.codes2Desc(existsScopes) + "已经配置");
        }
        //如果需要添加每天策略，则要求不存在已配置的策略
        if (strategyScopes.contains(QualitySpotScheduleTypeEnum.EVERY_DAY.getCode()) && CollectionUtils.isNotEmpty(existsScopes)) {
            throw new IllegalArgumentException(QualitySpotScheduleTypeEnum.codes2Desc(existsScopes) + "已经配置");
        }
        Collection<Integer> repeatScopes = CollectionUtils.subtract(strategyScopes,
                //剩下可用的适用范围
                CollectionUtils.subtract(QualitySpotScheduleTypeEnum.codeEnumMap.keySet(), existsScopes));
        if (repeatScopes.size() > 0) {
            throw new IllegalArgumentException(QualitySpotScheduleTypeEnum.codes2Desc(repeatScopes) + "已经配置");
        }
    }

    private void checkStrategyScopeValidSole(RiskQualitySpotStrategyDetailVo strategyScopes) {
        Long scene = strategyScopes.getSecondScene();
        List<RiskQualitySpotStrategy> existsStrategies = qualitySpotStrategyBiz.listByValidScene(scene);
        Optional<RiskQualitySpotStrategy> riskQualitySpotStrategies = existsStrategies.stream().filter(v -> v.getStatus() == 0).findFirst();
        if (riskQualitySpotStrategies.isPresent()) {
            strategyScopes.setStatus("1");
        } else {
            strategyScopes.setStatus("0");
        }
    }

    private void saveStrategyLog(Long strategyId, QualitySpotStrategyOperateTypeEnum operateTypeEnum, long adminUserId) {
        RiskQualitySpotStrategyLog strategyLog = new RiskQualitySpotStrategyLog();
        strategyLog.setStrategyId(strategyId);
        strategyLog.setOperateType(operateTypeEnum.getCode());
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        strategyLog.setOperateId(userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId());
        strategyLog.setOperateName(userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg());
        qualitySpotStrategyBiz.saveStrategyLog(strategyLog);
    }

    public List<RiskQualitySpotStrategyLogVo> queryStrategyLogList(Long strategyId) {
        List<RiskQualitySpotStrategyLog> strategyLogs = qualitySpotStrategyBiz.listLogByStrategyId(strategyId);
        return strategyLogs.stream().map(RiskQualitySpotStrategyLogVo::new).collect(Collectors.toList());
    }

    public List<QualitySpotJobConfDto> listJobScopeConf(long secondScene) {
        List<RiskQualitySpotLevelConfVo> levelConfVos = qualitySpotLevelConfBiz.listWithAllCurrentValidScene(secondScene);
        return getQualitySpotJobConfDtos(new Date(), levelConfVos);
    }

    /**
     * 获取job执行必要配置数据
     *
     * @return List<QualitySpotJobConfDto>
     */
    public List<QualitySpotJobConfDto> listJobScopeConf() {
        Date now = new Date();
        List<RiskQualitySpotLevelConfVo> levelConfVos = qualitySpotLevelConfBiz.listWithAllCurrentValidScene(now);
        return getQualitySpotJobConfDtos(now, levelConfVos);
    }

    private List<QualitySpotJobConfDto> getQualitySpotJobConfDtos(Date now, List<RiskQualitySpotLevelConfVo> levelConfVos) {
        Map<Long, RiskQualitySpotLevelConfVo> sceneConfMap = levelConfVos.stream()
                .collect(Collectors.toMap(RiskQualitySpotLevelConfVo::getScene, Function.identity()));
        Map<Long, RiskQualitySpotRuleDto> sceneRuleMap = obtainSceneRuleMap(now);
        List<QualitySpotJobConfDto> qualitySpotJobConfDtos = Lists.newArrayList();
        for (Long scene : sceneConfMap.keySet()) {
            //不作抽检情况
            if (!sceneConfMap.get(scene).isSpotCheck()) {
                qualitySpotJobConfDtos.add(new QualitySpotJobConfDto(scene, false));
                continue;
            }
            RiskQualitySpotRuleDto strategyRule = sceneRuleMap.get(scene);
            //如果没有策略，则不作抽检
            if (strategyRule != null) {
                dealSamplingLevel(sceneConfMap.get(scene).getSamplingLevel(), strategyRule);
                qualitySpotJobConfDtos.add(new QualitySpotJobConfDto(strategyRule.getRuleSamplingLevel(), scene,
                        strategyRule.getExecuteMode(), true));
            }
        }
        return qualitySpotJobConfDtos;
    }

    //对量级进行处理
    private void dealSamplingLevel(Integer samplingLevel, RiskQualitySpotRuleDto strategyRule) {
        List<QualitySpotRuleInfo> ruleSamplingLevel = strategyRule.getRuleSamplingLevel();
        for (QualitySpotRuleInfo ruleDateScope : ruleSamplingLevel) {
            //如果是全量则全部返回
            if (ruleDateScope.getRuleSamplingLevel() == 0){
                ruleDateScope.setRuleSamplingLevel(samplingLevel);
                continue;
            }
            if (strategyRule.getExecuteMode() != QualitySpotExecuteModelEnum.PERCENTAGE.getCode()) {
                return;
            }
            //百分比计算
            ruleDateScope.setRuleSamplingLevel((samplingLevel  * ruleDateScope.getRuleSamplingLevel()) / 100);
        }

    }

    private Map<Long, RiskQualitySpotRuleDto> obtainSceneRuleMap(Date now) {
        Map<Long, RiskQualitySpotRuleDto> sceneRuleMap = Maps.newHashMap();
        Map<Long, RiskQualitySpotStrategy> sceneStrategyMap = selectCurrentStrategyForScene(now);
        //todo 目前推测，场景不会很多，所以直接做全量的查询。如果后期量大还得拆成分页
        Set<Long> strategyIds = sceneStrategyMap.values().parallelStream().map(RiskQualitySpotStrategy::getId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(strategyIds)) {
            List<RiskQualitySpotRule> spotRules = qualitySpotRuleBiz.queryEnableRulesByStrategyIds(new ArrayList<>(strategyIds));
            Map<Long, List<RiskQualitySpotRule>> strategyRulesMap = spotRules.parallelStream()
                    .collect(Collectors.groupingBy(RiskQualitySpotRule::getStrategyId));

            for (Map.Entry<Long, RiskQualitySpotStrategy> entry : sceneStrategyMap.entrySet()) {
                RiskQualitySpotStrategy strategy = entry.getValue();
                sceneRuleMap.put(entry.getKey(), new RiskQualitySpotRuleDto(
                        strategyRulesMap.get(strategy.getId()).stream().map(this::buildRuleInfo)
                                .sorted(Comparator.comparing(QualitySpotRuleInfo::getPriority).reversed())
                                .collect(Collectors.toList()),
                        strategy.getExecuteMode()));
            }
        }

        return sceneRuleMap;
    }

    @NotNull
    private QualitySpotRuleInfo buildRuleInfo(RiskQualitySpotRule rule) {
        QualitySpotRuleInfo qualitySpotRuleInfo = new QualitySpotRuleInfo();
        qualitySpotRuleInfo.setRuleId(rule.getId());
        qualitySpotRuleInfo.setRuleName(rule.getName());
        qualitySpotRuleInfo.setRuleSamplingLevel(StringUtils.isBlank(rule.getExcuteModeValue()) ? 0 : Integer.parseInt(rule.getExcuteModeValue()) );
        qualitySpotRuleInfo.setDateScopeList(buildDataScope(rule.getDataScopeList()));
        qualitySpotRuleInfo.setPriority(rule.getPriority());
        return qualitySpotRuleInfo;
    }

    private List<QualitySpotRuleDateScope> buildDataScope(List<Integer> dataScopeList) {
        List<QualitySpotRuleDateScope> qualitySpotDateScopes = Lists.newArrayList();
        dataScopeList.forEach(v -> qualitySpotDateScopes.add(QualitySpotRuleDateScope.buildByDays(v)));
        return qualitySpotDateScopes;
    }



    private Map<Long, RiskQualitySpotStrategy> selectCurrentStrategyForScene(Date now) {
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(now);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        Long id = 0L;
        int limit = 10000;
        int resultSize;
        Map<Long, RiskQualitySpotStrategy> sceneStrategyMap = Maps.newHashMap();
        BinaryOperator<RiskQualitySpotStrategy> selectValidStrategy = (a, b) -> a.getId() < b.getId() ? a : b;
        do {
            List<RiskQualitySpotStrategy> strategies = qualitySpotStrategyBiz.listValidStrategy(now, id, limit);
            Map<Long, RiskQualitySpotStrategy> tempMap = strategies.stream()
                    .filter(strategy -> {
                        if (strategy.getStrategyParseTime().getTime() > now.getTime()) {
                            return false;
                        }
                        //如果还未到策略生效时间，则跳过
                        List<Integer> codes = QualitySpotScheduleTypeEnum.codeString2CodeList(strategy.getStrategyScope());
                        return codes.contains(QualitySpotScheduleTypeEnum.EVERY_DAY.getCode()) || codes.contains(dayOfWeek);
                    })
                    .collect(Collectors.toMap(
                            RiskQualitySpotStrategy::getScene, Function.identity(), selectValidStrategy));
            for (Map.Entry<Long, RiskQualitySpotStrategy> entry : tempMap.entrySet()) {
                Long key = entry.getKey();
                RiskQualitySpotStrategy newSt = entry.getValue();
                sceneStrategyMap.merge(key, newSt, selectValidStrategy);
            }
            resultSize = strategies.size();
            if (resultSize > 0) {
                id = strategies.get(strategies.size() - 1).getId();
            }
        } while (resultSize == limit);

        return sceneStrategyMap;
    }

    /**
     * 调用策略规则处理质检抽检
     *
     * @param ruleId
     * @param qualitySpotDtos
     */
    public List<QualitySpotDto> doQualitySpotStrategy(@NotNull(message = "规则id不能为空") @Min(value = 1, message = "规则id不能小于1") Long ruleId,
                                                      @Valid List<QualitySpotDto> qualitySpotDtos) {
        Map<String, Integer> dayAllotWorkOrderCountMap = Maps.newHashMapWithExpectedSize(256);
        RiskQualitySpotRule rule = qualitySpotRuleBiz.getById(ruleId);


        for (QualitySpotDto qualitySpotDto : qualitySpotDtos) {
            //修改dayCount
            Integer dayAllotWorkOrderCount = dayAllotWorkOrderCountMap.get(qualitySpotDto.getUniqueCode());
            log.info("QualitySpotDto.getUniqueCode:{} dayAllotWorkOrderCount:{}",
                    qualitySpotDto.getUniqueCode(), dayAllotWorkOrderCount);

            if (dayAllotWorkOrderCount == null) {
                dayAllotWorkOrderCount = qualitySpotDto.getDayAllotWorkOrderCount() + 1;
                dayAllotWorkOrderCountMap.put(qualitySpotDto.getUniqueCode(), dayAllotWorkOrderCount);
            }
            qualitySpotDto.setDayAllotWorkOrderCount(dayAllotWorkOrderCount);


            GroovyScriptRunUtil.executeCommon(RuleTypeEnum.RULE, ruleId, rule.getRuleScript(), new Object[]{qualitySpotDto});





            if (qualitySpotDto.isHit()) {
                dayAllotWorkOrderCountMap.put(qualitySpotDto.getUniqueCode(), dayAllotWorkOrderCount + 1);
            }
        }

        return qualitySpotDtos;
    }


    public List<QualitySpotWxDto> doQualitySpotWxStrategy(@NotNull(message = "规则id不能为空") @Min(value = 1, message = "规则id不能小于1") Long ruleId,
                                                          @Valid List<QualitySpotWxDto> qualitySpotWxDtos) {
        Map<String, Integer> dayAllotWorkOrderCountMap = Maps.newHashMapWithExpectedSize(256);
        Map<String, Integer> weekAllotWorkOrderCountMap = new HashMap<>(256);
        RiskQualitySpotRule rule = qualitySpotRuleBiz.getById(ruleId);
        for (QualitySpotWxDto qualitySpotDto : qualitySpotWxDtos) {
            //修改dayCount
            Integer dayAllotWorkOrderCount = dayAllotWorkOrderCountMap.get(qualitySpotDto.getUniqueCode());
            Integer weekAllotWorkOrderCount = weekAllotWorkOrderCountMap.get(qualitySpotDto.getUniqueCode());
            log.info("QualitySpotWxDto.getUniqueCode:{} dayAllotWorkOrderCount:{} weekAllotWorkOrderCount:{}",
                    qualitySpotDto.getUniqueCode(), dayAllotWorkOrderCount, weekAllotWorkOrderCount);
            if (dayAllotWorkOrderCount == null) {
                dayAllotWorkOrderCount = qualitySpotDto.getDayAllotWorkOrderCount() + 1;
                dayAllotWorkOrderCountMap.put(qualitySpotDto.getUniqueCode(), dayAllotWorkOrderCount);
            }
            if (weekAllotWorkOrderCount == null){
                weekAllotWorkOrderCount = qualitySpotDto.getWeekAllotWorkOrderCount() + 1;
                weekAllotWorkOrderCountMap.put(qualitySpotDto.getUniqueCode(), weekAllotWorkOrderCount);
            }
            qualitySpotDto.setDayAllotWorkOrderCount(dayAllotWorkOrderCount);
            qualitySpotDto.setWeekAllotWorkOrderCount(weekAllotWorkOrderCount);
            GroovyScriptRunUtil.executeCommon(RuleTypeEnum.RULE, ruleId, rule.getRuleScript(), new Object[]{qualitySpotDto});
            if (qualitySpotDto.isHit()) {
                dayAllotWorkOrderCountMap.put(qualitySpotDto.getUniqueCode(), dayAllotWorkOrderCount + 1);
                weekAllotWorkOrderCountMap.put(qualitySpotDto.getUniqueCode(), weekAllotWorkOrderCount + 1);
            }
        }
        return qualitySpotWxDtos;
    }

    private long getSecondScene(Long id) {
        List<RiskQualitySpotTypeRel> typeRelList = qualitySpotStrategyTypeRelBiz.findByStrategyId(id);
        if (CollectionUtils.isEmpty(typeRelList)) {
            return 0;
        }
        List<RiskQualitySpotType> riskQualitySpotTypes = spotTypeBiz.findById(typeRelList.stream()
                .map(RiskQualitySpotTypeRel::getTypeId).distinct().collect(Collectors.toList()));
        //目前只支持两层
        for (RiskQualitySpotType type : riskQualitySpotTypes) {
            if (type.getParentId() == 0) {
                continue;
            }
            return type.getId();
        }
        return 0;
    }

    public void updateStrategy(RiskQualitySpotStrategyDetailVo detailVo, long adminUserId) {
        checkParam(detailVo);
        RiskQualitySpotStrategy riskQualitySpotStrategy = qualitySpotStrategyBiz.getModelById(detailVo.getId());
        String key = UPDATE_LEVEL_CONF_KEY + detailVo.getSecondScene();
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_LEVEL_CONF_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请稍后重试");
            }
            //2. 保存
            BeanUtils.copyProperties(detailVo, riskQualitySpotStrategy);
            riskQualitySpotStrategy.setStrategyScope(QualitySpotScheduleTypeEnum.codes2JoinString(detailVo.getStrategyScope()));
            riskQualitySpotStrategy.setStrategyParseTime(DateUtil.getStr2LDate(detailVo.getStrategyParseTime()));
            SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
            riskQualitySpotStrategy.setOperateId(userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId());
            riskQualitySpotStrategy.setOperateName(userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg());
            riskQualitySpotStrategy.setScene(detailVo.getSecondScene());
            qualitySpotStrategyBiz.updateStrategy(riskQualitySpotStrategy);
            //3. 保存规则
          /*  if (!riskQualitySpotStrategy.getRuleDef().equals(detailVo.getRuleDef())) {
                updateRule(riskQualitySpotStrategy, detailVo);
            }*/
            qualitySpotRuleBiz.deleteOldRuleByStrategyId(riskQualitySpotStrategy.getId());
            saveRule(riskQualitySpotStrategy.getId(), detailVo.getRuleDef());
            //更新场景引用
            updateTypeRel(riskQualitySpotStrategy.getId(), detailVo.getFirstScene(), detailVo.getSecondScene());
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                redissonHandler.unLock(key, identify);
            }
        }
        //3. 记录日志
        saveStrategyLog(riskQualitySpotStrategy.getId(), QualitySpotStrategyOperateTypeEnum.UPDATE, adminUserId);

    }

    /**
     *
     * 处理外呼的策略 工单数量需要特殊处理
     * @return
     */
    public List doQualitySpotOutBoundStrategy(Long ruleId, List<QualitySpotOutBoundDto> qualitySpotOutBoundDtoList) {
        Map<String, Integer> dayAllotWorkOrderCountMap = Maps.newHashMapWithExpectedSize(256);
        Map<String, Integer> hourAllotWorkOrderCountMap = Maps.newHashMapWithExpectedSize(256);
        RiskQualitySpotRule rule = qualitySpotRuleBiz.getById(ruleId);
        for (QualitySpotOutBoundDto qualitySpotDto : qualitySpotOutBoundDtoList) {
            //修改dayCount
            Integer currentCount = dayAllotWorkOrderCountMap.get(qualitySpotDto.getUniqueCode());
            Integer currentHourCount = hourAllotWorkOrderCountMap.get(getOutBoundCheckerHourCountKey(qualitySpotDto.getUniqueCode(),
                    qualitySpotDto.getWorkOrderCreateTime()));
            log.info("qualitySpotDto.getUniqueCode:{} dayCount:{} hourCount:{}",
                    qualitySpotDto.getUniqueCode(), currentCount, currentHourCount);
            if (currentCount == null) {
                currentCount = qualitySpotDto.getDayAllotWorkOrderCount() + 1;
                dayAllotWorkOrderCountMap.put(qualitySpotDto.getUniqueCode(), currentCount);
            }
            qualitySpotDto.setDayAllotWorkOrderCount(currentCount);
            if (currentHourCount == null) {
                currentHourCount = qualitySpotDto.getHourAllotWorkOrderCount() + 1;
                hourAllotWorkOrderCountMap.put(getOutBoundCheckerHourCountKey(qualitySpotDto.getUniqueCode(),
                        qualitySpotDto.getWorkOrderCreateTime()), currentHourCount);
            }
            qualitySpotDto.setHourAllotWorkOrderCount(currentHourCount);
            GroovyScriptRunUtil.executeCommon(RuleTypeEnum.RULE, ruleId, rule.getRuleScript(), new Object[]{qualitySpotDto});
            if (qualitySpotDto.isHit()) {
                dayAllotWorkOrderCountMap.put(qualitySpotDto.getUniqueCode(), currentCount + 1);
                hourAllotWorkOrderCountMap.put(getOutBoundCheckerHourCountKey(qualitySpotDto.getUniqueCode(), qualitySpotDto.getWorkOrderCreateTime()),
                        currentHourCount + 1);
            }
        }
        return qualitySpotOutBoundDtoList;
    }

    private String getOutBoundCheckerHourCountKey(String userKey, Date workOrderCreateTime) {
        return DateUtil.getDate2Str("yyyyMMddHH", workOrderCreateTime) + "_" + userKey;
    }

    private void updateRule(RiskQualitySpotStrategy riskQualitySpotStrategy, RiskQualitySpotStrategyDetailVo detailVo) {
        //删除老的rule
        qualitySpotRuleBiz.deleteOldRuleByStrategyId(riskQualitySpotStrategy.getId());
        saveRule(riskQualitySpotStrategy.getId(), detailVo.getRuleDef());
    }

    public QualitySpotMaterialsAuditDto doQualitySpotMaterialStrategy(Long ruleId, QualitySpotMaterialsAuditDto qualitySpotMaterialsAuditDto) {
        RiskQualitySpotRule rule = qualitySpotRuleBiz.getById(ruleId);
        //工单量需要特殊处理
        qualitySpotMaterialsAuditDto.setDayAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getDayAllotWorkOrderCount() + 1);
        qualitySpotMaterialsAuditDto.setHourAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getHourAllotWorkOrderCount() + 1);
        qualitySpotMaterialsAuditDto.setCheckerHourAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getCheckerHourAllotWorkOrderCount() + 1);
        if (log.isDebugEnabled()){
            log.debug("qualitySpotMaterialsAuditDto:{}", JSON.toJSONString(qualitySpotMaterialsAuditDto));
        }
        GroovyScriptRunUtil.executeCommon(RuleTypeEnum.RULE, ruleId, rule.getRuleScript(), new Object[]{qualitySpotMaterialsAuditDto});
        //分命中则需要返回测试
        if (!qualitySpotMaterialsAuditDto.isHit()) {
            qualitySpotMaterialsAuditDto.setDayAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getDayAllotWorkOrderCount() - 1);
            qualitySpotMaterialsAuditDto.setHourAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getHourAllotWorkOrderCount() - 1);
            qualitySpotMaterialsAuditDto.setCheckerHourAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getCheckerHourAllotWorkOrderCount() - 1);
        }
        return qualitySpotMaterialsAuditDto;
    }

    public QualitySpotMaterialsZhuDongDto doQualitySpotMaterialZhuDongStrategy(Long ruleId, QualitySpotMaterialsZhuDongDto qualitySpotMaterialsAuditDto) {
        RiskQualitySpotRule rule = qualitySpotRuleBiz.getById(ruleId);
        //工单量需要特殊处理
        qualitySpotMaterialsAuditDto.setDayAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getDayAllotWorkOrderCount() + 1);
        qualitySpotMaterialsAuditDto.setHourAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getHourAllotWorkOrderCount() + 1);
        qualitySpotMaterialsAuditDto.setCheckerHourAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getCheckerHourAllotWorkOrderCount() + 1);
        if (log.isDebugEnabled()){
            log.debug("qualitySpotMaterialsAuditDto:{}", JSON.toJSONString(qualitySpotMaterialsAuditDto));
        }
        GroovyScriptRunUtil.executeCommon(RuleTypeEnum.RULE, ruleId, rule.getRuleScript(), new Object[]{qualitySpotMaterialsAuditDto});
        //分命中则需要返回测试
        if (!qualitySpotMaterialsAuditDto.isHit()) {
            qualitySpotMaterialsAuditDto.setDayAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getDayAllotWorkOrderCount() - 1);
            qualitySpotMaterialsAuditDto.setHourAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getHourAllotWorkOrderCount() - 1);
            qualitySpotMaterialsAuditDto.setCheckerHourAllotWorkOrderCount(qualitySpotMaterialsAuditDto.getCheckerHourAllotWorkOrderCount() - 1);
        }
        return qualitySpotMaterialsAuditDto;
    }

    public QualitySpotHighRiskDto doQualitySpotHighRiskDtoStrategy(Long ruleId, QualitySpotHighRiskDto qualitySpotHighRiskDto) {
        RiskQualitySpotRule rule = qualitySpotRuleBiz.getById(ruleId);
        //工单量需要特殊处理
        qualitySpotHighRiskDto.setDayAllotWorkOrderCount(qualitySpotHighRiskDto.getDayAllotWorkOrderCount() + 1);
        qualitySpotHighRiskDto.setHourAllotWorkOrderCount(qualitySpotHighRiskDto.getHourAllotWorkOrderCount() + 1);
        if (log.isDebugEnabled()) {
            log.debug("qualitySpotMaterialsAuditDto:{}", JSON.toJSONString(qualitySpotHighRiskDto));
        }
        String ruleCode = qualitySpotHighRiskDto.getRuleCode();
        if (StringUtils.isEmpty(ruleCode)) {
            GroovyScriptRunUtil.executeCommon(RuleTypeEnum.RULE, ruleId, rule.getRuleScript(), new Object[]{qualitySpotHighRiskDto});
        } else {
            Set<Boolean> hits = Sets.newHashSet();
            List<String> list = Splitter.on(",").splitToList(ruleCode);
            for (String l : list) {
                qualitySpotHighRiskDto.setRuleCode(l);
                GroovyScriptRunUtil.executeCommon(RuleTypeEnum.RULE, ruleId, rule.getRuleScript(), new Object[]{qualitySpotHighRiskDto});
                hits.add(qualitySpotHighRiskDto.isHit());
            }
            qualitySpotHighRiskDto.setHit(hits.size() > 1 || hits.stream().findFirst().get());
        }

        //分命中则需要返回测试
        if (!qualitySpotHighRiskDto.isHit()) {
            qualitySpotHighRiskDto.setDayAllotWorkOrderCount(qualitySpotHighRiskDto.getDayAllotWorkOrderCount() - 1);
            qualitySpotHighRiskDto.setHourAllotWorkOrderCount(qualitySpotHighRiskDto.getHourAllotWorkOrderCount() - 1);
        }
        return qualitySpotHighRiskDto;
    }
}
