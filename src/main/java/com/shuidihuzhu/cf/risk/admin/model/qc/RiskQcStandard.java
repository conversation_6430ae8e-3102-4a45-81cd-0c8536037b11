package com.shuidihuzhu.cf.risk.admin.model.qc;

import lombok.Data;

@Data
public class RiskQcStandard {
    private long id;
    private String standardName;
    private int standardType;
    private int secondStandardType;
    private long parentId;
    private int level;
    private int sort;
    private int isUse;
    private int secondaryUseStatus;

    public RiskQcStandard(String standardName, int standardType, int secondStandardType, long parentId, int level, int sort, int isUse) {
        this.standardName = standardName;
        this.standardType = standardType;
        this.secondStandardType = secondStandardType;
        this.parentId = parentId;
        this.level = level;
        this.sort = sort;
        this.isUse = isUse;
    }

    public RiskQcStandard() {
    }
}
