package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskPsHandleRecordBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPsOperationLogBiz;
import com.shuidihuzhu.cf.risk.admin.biz.UploadBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPsHandleRecordDao;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPublicSentimentInfoDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsOperationLog;
import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;
import com.shuidihuzhu.pf.common.v2.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class RiskHandleRecordBizImpl implements RiskPsHandleRecordBiz {

    @Autowired
    private RiskPsHandleRecordDao handleRecordDao;
    @Autowired
    private RiskPublicSentimentInfoDao infoDao;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private RiskPsOperationLogBiz operationLogService;
    @Autowired
    private UploadBiz uploadBiz;

    @Override
    public Response add(RiskPsHandleRecord handleRecord,long adminUserId) {
        if (handleRecord == null || handleRecord.getId() < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR, null);
        }
        RiskPublicSentimentInfo info = infoDao.getInfoById(handleRecord.getPsId());
        if (info == null){
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(),"没有相关舆情", null);
        }
        String name = "";
        if (adminUserId > 0){
            name = seaAccountService.getOrganization(adminUserId) + seaAccountService.getName(adminUserId);
        }
        handleRecord.setOperator(name);
        handleRecord.setImages(StringUtils.trimToEmpty(handleRecord.getImages()));
        handleRecord.setReplyContent(StringUtils.trimToEmpty(handleRecord.getReplyContent()));
        handleRecord.setInfoClassifyOther(StringUtils.trimToEmpty(handleRecord.getInfoClassifyOther()));
        handleRecord.setDepartment(StringUtils.trimToEmpty(handleRecord.getDepartment()));
        handleRecord.setSatisfactionExt(StringUtils.trimToEmpty(handleRecord.getSatisfactionExt()));
        handleRecord.setOtherExt(StringUtils.trimToEmpty(handleRecord.getOtherExt()));
        handleRecord.setSolution(StringUtils.trimToEmpty(handleRecord.getSolution()));
        handleRecord.setSolutionOther(StringUtils.trimToEmpty(handleRecord.getSolutionOther()));
        handleRecordDao.save(handleRecord);
        info.setInfoClassify(handleRecord.getInfoClassify());
        info.setInfoClassifyOther(handleRecord.getInfoClassifyOther());
        info.setCaseId(handleRecord.getCaseId());
        info.setPublicSentimentInfoType(handleRecord.getPublicSentimentInfoType());
        info.setSolution(handleRecord.getSolution());
        info.setSolutionOther(handleRecord.getSolutionOther());
        info.setLastOperator(name);
        info.setStatus(handleRecord.getStatus() == 0 ? 2 : 1);
        infoDao.updateHandleById(info);
        operationLogService.add(new RiskPsOperationLog(handleRecord.getPsId(), name, "处理舆情"));
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public RiskPsHandleRecord getLastByPsId(long psId) {
        if (psId < 0){
            return null;
        }
        return handleRecordDao.getLastByPsId(psId);
    }

    @Override
    public List<RiskPsHandleRecord> listByPsId(long psId) {
        if (psId < 0){
            return null;
        }
        return handleRecordDao.listByPsId(psId);
    }

    @Override
    public PageResponse<RiskPsHandleRecord> listByPsIdOfPage(long psId, String pageJson) {
        PageRequest pageRequest = PageUtil.parseJsonString(pageJson);
        List<RiskPsHandleRecord> data = handleRecordDao.listByPsIdOfPage(psId, pageRequest);
        if (CollectionUtils.isNotEmpty(data)) {
            for (RiskPsHandleRecord record : data) {
                record.setImages(uploadBiz.getTemporalUrlByUrl(record.getImages()));
            }
        }
        return PageUtil.buildPageResponse(data, pageRequest);
    }

    @Override
    public RiskPsHandleRecord getNoPushRecord(long psId) {
        RiskPsHandleRecord record = handleRecordDao.getByPsIdAndStatus(psId);
        if (Objects.nonNull(record)) {
            record.setImages(uploadBiz.getTemporalUrlByUrl(record.getImages()));
        }
        return record;
    }

    @Override
    public int getRecordCountByPsId(long psId) {
        return handleRecordDao.countRecordSumByPsId(psId);
    }
}
