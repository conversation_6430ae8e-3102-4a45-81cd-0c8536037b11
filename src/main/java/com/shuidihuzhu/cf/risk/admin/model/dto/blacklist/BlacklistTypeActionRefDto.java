package com.shuidihuzhu.cf.risk.admin.model.dto.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.enums.BlackActionLimitTimeType;
import lombok.Data;

/**
 * @Author: wangpeng
 * @Date: 2022/3/15 16:25
 * @Description:
 */
@Data
public class BlacklistTypeActionRefDto {
    private String name;
    private int limitTimeType = BlackActionLimitTimeType.FOREVER.getCode();
    private long limitTime;
    private long actionId;
}
