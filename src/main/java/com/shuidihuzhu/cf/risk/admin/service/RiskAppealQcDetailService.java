package com.shuidihuzhu.cf.risk.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcAppealResultBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQcAppealWorkOrderRelBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.enums.*;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcAppealWorkOrderRel;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealProblemModel;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealResultModel;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotResultIssueInnerService;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QcHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Auther: subing
 * @Date: 2020/11/16
 */
@Service
@Slf4j
@RefreshScope
public class RiskAppealQcDetailService {
    @Value("${qc.pass.style:''}")
    private String passStyle;
    @Value("${qc.reject.style:''}")
    private String rejectStyle;
    @Value("${qc.appeal.result.common.property:合规问题}")
    private String commonResultProperty;
    @Value("${qc.appeal.result.serious.property:合规问题}")
    private String seriousResultProperty;

    @Autowired
    private RiskQcAppealResultBiz riskQcAppealResultBiz;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;

    @Autowired
    private RiskQcLogService riskQcLogService;

    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;

    @Autowired
    private RiskQcResultBiz riskQcResultBiz;

    @Autowired
    private SeaAccountService seaAccountService;

    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;

    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Autowired
    private QualitySpotResultIssueInnerService qualitySpotResultIssueInnerService;

    private static final  List<Integer> NEED_ISSUE_APPEAL_RESULT_ORDER_TYPE =
            ImmutableList.of(WorkOrderType.qc_second_complaint.getType(), WorkOrderType.qc_serious_complaint.getType());

    @Autowired
    private RiskQcAppealWorkOrderRelBiz riskQcAppealWorkOrderRelBiz;


    public RiskQcAppealResultVo getResult(long workOrderId, boolean showFirstRecord) {
        workOrderId = showFirstRecord ? getFirstAppealWorkOrderId(workOrderId) : workOrderId;
        RiskQcAppealResultModel riskQcAppealResultModel = riskQcAppealResultBiz.getByWorkOrderId(workOrderId);
        RiskQcAppealResultVo riskQcAppealResultVo = RiskQcAppealResultVo.buildVo(riskQcAppealResultModel);
        if (showFirstRecord && Objects.nonNull(riskQcAppealResultVo)) {
            Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
            if (workOrderVOResponse != null && workOrderVOResponse.ok() && workOrderVOResponse.getData() != null) {
                WorkOrderVO workOrderVO = workOrderVOResponse.getData();
                riskQcAppealResultVo.setSubmitTime(workOrderVO.getCreateTime());
                riskQcAppealResultVo.setHandleTime(workOrderVO.getHandleTime());
                riskQcAppealResultVo.setOperationName(seaAccountService.getName(workOrderVO.getOperatorId()));
            }
        }
        return riskQcAppealResultVo;
    }

    private long getFirstAppealWorkOrderId(long workOrderId) {
        long qcId = riskQcDetailService.getQcId(workOrderId);
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos =
                riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.ORIGINAL_WORK_ORDER.getKey());
        if (CollectionUtils.isNotEmpty(riskQcMaterialsInfos)) {
            RiskQcMaterialsInfo riskQcMaterialsInfo =
                    Optional.ofNullable(riskQcMaterialsInfos.get(0)).orElseGet(RiskQcMaterialsInfo::new);
            String value = riskQcMaterialsInfo.getMaterialsValue();
            return StringUtils.isBlank(value) ? 0 : Long.parseLong(value);
        }
        return 0;
    }

    public int addInfo(long workOrderId, int appealResult, String appealInfo,
                       int disposeAction, long caseId, int orderType, RiskQcAppealInfoModel riskQcAppealInfoModel) {
        return (disposeAction == RiskQcDisponseActionEnum.LATER_DISPOSE.getCode()) ?
                this.handleAppeal((int) caseId, workOrderId, HandleResultEnum.later_doing, "稍后处理", orderType)
                : save(appealResult, appealInfo, workOrderId, caseId, orderType, riskQcAppealInfoModel);
    }

    public int handleAppeal(int caseId, long workOrderId, HandleResultEnum handleResultEnum,
                            String orderExtName, int orderTpe) {
        Response response =
                cfQcWorkOrderClient.handleQc(buildParam(caseId, workOrderId, handleResultEnum, orderExtName, orderTpe));
        if (response != null && response.ok()) {
            return 1;
        }
        AtomicInteger result = new AtomicInteger();
        if (response == null || ErrorCode.SUCCESS.getCode() != response.getCode()) {
            try {
                RetryerBuilder.<Boolean>newBuilder()
                        .withWaitStrategy(WaitStrategies.fixedWait(2, TimeUnit.SECONDS))
                        .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                        .retryIfResult(BooleanUtils::isFalse)
                        .build()
                        .call(() -> {
                            Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
                            if (Objects.nonNull(workOrderVOResponse) && workOrderVOResponse.ok()
                                    && workOrderVOResponse.getData().getHandleResult() == handleResultEnum.getType()) {
                                result.set(1);
                                return true;
                            }
                            return false;
                        });
            } catch (Exception e) {
                log.error("handleQc 重试失败 workOrderId:" + workOrderId, e);
            }
        }
        return result.get();
    }

    public int save(int appealResult, String appealInfo,
                    long workOrderId, long caseId, int orderType, RiskQcAppealInfoModel riskQcAppealInfoModel) {
        int result = handleAppeal((int) caseId, workOrderId, HandleResultEnum.done, "结束处理", orderType);
        if (result > 0) {
            riskQcAppealResultBiz.addInfo(workOrderId, appealInfo, appealResult, riskQcDetailService.getQcId(workOrderId));
            this.addLog(riskQcAppealInfoModel, workOrderId, appealResult);
            riskQcSearchIndexBiz.updateByWorkOrderId(workOrderId, appealResult, "", "", 0);
            //二次申诉工单和严重问题申诉工单 下发质检结果通知
            if (NEED_ISSUE_APPEAL_RESULT_ORDER_TYPE.contains(orderType)) {
                qualitySpotResultIssueInnerService.dealQcAppealResultMQ(workOrderId, caseId, orderType);
            }

        }
        return result;
    }

    private void addLog(RiskQcAppealInfoModel riskQcAppealInfoModel, long workOrderId, int judgeResult) {
        if (riskQcAppealInfoModel == null) {
            return;
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("申诉判定结果：").append(QcAppealResultEnum.findOfCode(judgeResult));
        stringBuffer.append("\n");
        List<RiskQcAppealProblemModel> riskQcAppealProblemModels = riskQcAppealInfoModel.getRiskQcAppealProblemModels();
        riskQcAppealProblemModels.forEach(riskQcAppealProblemModel -> {
            if (riskQcAppealProblemModel.getStatus() == QcAppealProblemStatusEnum.PASS.getCode()) {
                stringBuffer.append(passStyle.replace("#status#", QcAppealProblemStatusEnum.PASS.getDesc()));
            } else {
                stringBuffer.append(rejectStyle.replace("#status#", QcAppealProblemStatusEnum.REJECTED.getDesc()));
            }
            stringBuffer.append(riskQcAppealProblemModel.getProblem());
            stringBuffer.append("\n");
        });
        stringBuffer.append("添加评论:").append("\n");
        stringBuffer.append(StringUtils.trimToEmpty(riskQcAppealInfoModel.getComment()));
        RiskQcAppealWorkOrderRel riskQcAppealWorkOrderRel = riskQcAppealWorkOrderRelBiz.getByAppealWorkOrderId(workOrderId);
        if (riskQcAppealWorkOrderRel != null) {
            riskQcLogService.addLog(RiskQcOperationTypeEnum.APPEAL_WORK_ORDER_RESULT, riskQcAppealWorkOrderRel.getQcWorkOrderId(), stringBuffer.toString());
        }
    }


    private QcHandleOrderParam buildParam(int caseId, long workOrderId, HandleResultEnum handleResultEnum,
                                          String orderExtName, int orderType) {
        QcHandleOrderParam param = new QcHandleOrderParam();
        param.setCaseId(caseId);
        param.setWorkOrderId(workOrderId);
        param.setHandleResult(handleResultEnum.getType());
        param.setOperComment(orderExtName);
        param.setUserId(ContextUtil.getAdminLongUserId());
        param.setOrderType(orderType);
        return param;
    }

    public RiskQcAppealInfoModel getAppealInfo(long workOrderId) {
        long qcId = riskQcDetailService.getQcId(workOrderId);
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos =
                riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.COMPLAINT_MATERIAL.getKey());
        if (CollectionUtils.isNotEmpty(riskQcMaterialsInfos)) {
            RiskQcMaterialsInfo riskQcMaterialsInfo =
                    Optional.ofNullable(riskQcMaterialsInfos.get(0)).orElseGet(RiskQcMaterialsInfo::new);
            CfGwReplaceInputQualityTestFeedbackModel cfGwReplaceInputQualityTestFeedbackModel =
                    JSON.parseObject(riskQcMaterialsInfo.getMaterialsValue(),
                            new TypeReference<CfGwReplaceInputQualityTestFeedbackModel>() {
                            });
            return RiskQcAppealInfoModel.buildModel(cfGwReplaceInputQualityTestFeedbackModel);
        }
        return null;
    }

    public RiskQcCommonResultVo getInfo(long workOrderId, int workOrderType) {
        RiskQcResult riskQcResult = riskQcResultBiz.getByWorkOrderId(workOrderId);
        if (riskQcResult == null) {
            return null;
        }
        RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<RiskQcResultVo>() {
        });
        if (riskQcResultVo == null) {
            return null;
        }
        RiskQcCommonResultVo riskQcCommonResultVo = new RiskQcCommonResultVo();
        List<RiskQcStandardVo> riskQcStandardVos =
                Optional.ofNullable(riskQcResultVo.getQcResultOption()).orElse(Lists.newArrayList());
        List<String> problems = Lists.newArrayList();
        List<String> commonResultPropertyList = Splitter.on(",").splitToList(commonResultProperty);
        List<String> seriousResultPropertyList = Splitter.on(",").splitToList(seriousResultProperty);
        riskQcStandardVos.forEach(riskQcStandardVo -> {
            List<RiskQcStandardDetailVo> riskQcStandardDetailVos = riskQcStandardVo.getRiskQcStandardSecondVos();
            riskQcStandardDetailVos.forEach(riskQcStandardDetailVo -> {
                String property = Optional.ofNullable(riskQcStandardDetailVo.getProperty()).orElse("");
                List<String> propertyList = Splitter.on("-").splitToList(property);
                if (CollectionUtils.isNotEmpty(propertyList) && workOrderType != WorkOrderType.qc_serious_complaint.getType()
                        && commonResultPropertyList.contains(propertyList.get(0))) {
                    String comment = riskQcStandardDetailVo.getStandardName() + "(" + riskQcStandardDetailVo.getProperty() + ")";
                    problems.add(comment);
                }
                else if (CollectionUtils.isNotEmpty(propertyList) && workOrderType == WorkOrderType.qc_serious_complaint.getType()
                        && seriousResultPropertyList.contains(propertyList.get(0))) {
                    String comment = riskQcStandardDetailVo.getStandardName() + "(" + riskQcStandardDetailVo.getProperty() + ")";
                    problems.add(comment);
                }
            });
        });
        riskQcCommonResultVo.setProblem(problems);
        riskQcCommonResultVo.setQcResult(riskQcResult.getFirstQcResultId());
        riskQcCommonResultVo.setVoiceRemark(riskQcResultVo.getVoiceRemark());
        riskQcCommonResultVo.setUserWriteRemark(riskQcResultVo.getUserWriteRemark());
        return riskQcCommonResultVo;
    }
}
