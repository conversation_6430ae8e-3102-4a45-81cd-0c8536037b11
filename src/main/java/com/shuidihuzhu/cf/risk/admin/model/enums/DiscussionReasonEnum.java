package com.shuidihuzhu.cf.risk.admin.model.enums;

/**
 * 评议原因枚举
 *
 * 用于其他患者治疗、用于患者营养费、用户患者生病期间陪护费、用于丧葬费、用于偿还医疗欠费、用于抚养老人/小孩儿、用于安装假肢、其他款项用途、国外就医、其他
 */
public enum DiscussionReasonEnum {

    ZHILIAO(1, "用于其他患者治疗"),
    YINGYANG(2, "用于患者营养费"),
    PEIHU(3, "用户患者生病期间陪护费"),
    SANGZANG(4, "用于丧葬费"),
    QIANFEI(5, "用于偿还医疗欠费"),
    FUYANG(6,"用于抚养老人，小孩儿"),
    JIAZHI(7,"用于安装假肢"),
    OTHER_FUND(8, "其他款项用途"),
    GUOWAIJIUYI(9,"国外就医"),
    OTHER(10, "其他"),
    ;

    int code;
    String description;

    DiscussionReasonEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (DiscussionReasonEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
