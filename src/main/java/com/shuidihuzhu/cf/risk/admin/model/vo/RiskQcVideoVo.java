package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/6/19
 */
@Data
public class RiskQcVideoVo {
    private long id;
    private String creatTime;
    private String endTime;
    private String videoUrl;
    private int duration;
    private boolean checked;

    @ApiModelProperty("语音转译是否准确 null 未勾选 true 是 false 否")
    private Boolean asrCorrect;

    /**
     * 老数据只存了这个字段
     * 新数据没存这个字段 存储了更全的 {@link RiskQcVideoVo#sentenceInfoList}
     */
    @ApiModelProperty("句子列表")
    @Deprecated
    private List<String> sentences;

    @ApiModelProperty("句子详情列表")
    private List<AsrSentenceVO> sentenceInfoList;

    @ApiModelProperty("语音转译结果 {0: 无, 1: 有效录音, 2: 疑似无效录音}")
    private int audioAsrStatus;

    @ApiModelProperty("标红词检查结果")
    private List<CfBaseInfoRiskHitVO.ColourTag> hitWordColourTags;

    public RiskQcVideoVo(String creatTime, String endTime, String videoUrl, int duration) {
        this.creatTime = creatTime;
        this.endTime = endTime;
        this.videoUrl = videoUrl;
        this.duration = duration;
    }

    public RiskQcVideoVo() {
    }

    /**
     * 兼容部分老数据
     * 后面可删
     */
    public static List<String> getSentencesByAdapt(RiskQcVideoVo v){
        List<String> sentences = v.getSentences();
        if (CollectionUtils.isNotEmpty(sentences)) {
            return sentences;
        }
        List<AsrSentenceVO> sentenceInfoList = v.getSentenceInfoList();
        if (CollectionUtils.isEmpty(sentenceInfoList)) {
            return Lists.newArrayList();
        }
        return sentenceInfoList.stream().map(AsrSentenceVO::getText).collect(Collectors.toList());
    }
}
