package com.shuidihuzhu.cf.risk.admin.dao.list;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartment;
import com.shuidihuzhu.cf.risk.admin.model.query.list.ListDepartmentQuery;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskListDepartmentDao {
    int insertSelective(RiskListDepartment record);

    int updateById(RiskListDepartment record);

    int delById(Long departmentId);

    RiskListDepartment selectByPrimaryKey(Long id);

    RiskListDepartment getByUniqueTel(@Param("areaCode") String areaCode, @Param("landline") String landline, @Param("extension") String extension);

    List<RiskListDepartment> listByAreaCodeLandline(@Param("areaCode") String areaCode, @Param("landline") String landline);

    List<RiskListDepartment> listByOptional(ListDepartmentQuery query);
}
