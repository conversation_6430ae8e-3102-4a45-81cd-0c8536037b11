package com.shuidihuzhu.cf.risk.admin.model.qc;

import lombok.Data;

import java.util.Date;

@Data
public class RiskQcLog {


    private long operationId;
    private String operationName;
    private String operationLog;
    private int operationType;
    private long workOrderId;
    private Date createTime;


    public RiskQcLog() {
    }


    public RiskQcLog(long operationId, String operationName, String operationLog, int operationType, long workOrderId) {
        this.operationId = operationId;
        this.operationName = operationName;
        this.operationLog = operationLog;
        this.operationType = operationType;
        this.workOrderId = workOrderId;
    }
}
