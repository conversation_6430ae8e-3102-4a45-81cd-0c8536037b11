package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class RiskPsHandleRecord implements PageHasId {
    private long id;
    private long psId; //舆情id
    private int caseId; //案例id
    private int infoClassify; //信息分类
    private String infoClassifyOther; //信息分类其他
    private String publicSentimentInfoType; //舆情信息类型
    private String solution; //解决方案
    private String solutionOther;//其他解决方案
    private String department; //解决部门
    private int replyType; //回复类型
    private Timestamp replyTime; //回复时间
    private String replyContent; //回复内容
    private String images; //图片
    private int satisfaction; //满意程度
    private String satisfactionExt; //满意程度其他补充
    private String otherExt; //其他补充说明
    private String operator; //操作人
    private int status;//记录状态 0提交，1保存的舆情处理情况
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;
}
