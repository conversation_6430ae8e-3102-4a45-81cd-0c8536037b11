package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcBaseInfoDao {

    int insertOne(RiskQcBaseInfo riskQcBaseInfo);

    RiskQcBaseInfo getByCaseIdAndOrderTypeAndQcUniqueCode(@Param("caseId") long caseId,
                                                          @Param("orderType") int orderType,
                                                          @Param("qcUniqueCode") String qcUniqueCode);

    RiskQcBaseInfo getByTaskIdAndOrderTypeAndQcUniqueCode(@Param("taskId") long taskId,
                                                          @Param("orderType") int orderType,
                                                          @Param("qcUniqueCode") String qcUniqueCode);

    RiskQcBaseInfo getByCaseIdAndOrderType(@Param("caseId") long caseId,
                                           @Param("orderType") int orderType);

    RiskQcBaseInfo getByTaskIdAndOrderType(@Param("taskId") long taskId,
                                           @Param("orderType") int orderType);

    RiskQcBaseInfo getById(@Param("id") long id);


    List<RiskQcBaseInfo> getByIds(@Param("ids") List<Long> ids);

    int updateCaseId(@Param("taskId") long taskId, @Param("caseId") long caseId, @Param("orderTypes") List<Integer> orderTypes);
}
