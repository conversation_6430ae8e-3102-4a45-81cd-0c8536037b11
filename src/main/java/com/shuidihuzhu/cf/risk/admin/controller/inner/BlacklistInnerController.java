package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistData;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistDataAddVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistDataLogVo;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListDataService;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListTypeService;
import com.shuidihuzhu.cf.risk.client.admin.blacklist.BlacklistClient;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlackListAddParam;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistDataDto;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.UpdateBatchDto;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.VerifyAutoAddBlacklistVo;
import com.shuidihuzhu.cf.risk.model.risk.RiskBlacklistDataTypeRefVo;
import com.shuidihuzhu.common.util.BeanUtils;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/7/21 19:37
 */
@RestController
@Slf4j
public class BlacklistInnerController implements BlacklistClient {

    @Resource
    private BlackListDataService blackListDataService;

    @Resource
    private BlackListTypeService blackListTypeService;

    @Override
    public Response<Void> add(BlackListAddParam blackListAddParam) {
        log.debug("添加黑名单 {}", blackListAddParam);
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            blackListDataService.add(blackListAddParam, adminUserId);
        } catch (IllegalArgumentException e) {
            return NewResponseUtil.makeFail(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<List<BlacklistDataDto>> queryNotBoundUid(Long previousId, Integer limit) {
        log.info("请求根据mobile未查询到uid入参：previousId:{}, limit:{}", previousId, limit);
        return NewResponseUtil.makeSuccess(blackListDataService.queryNotBoundUid(previousId, limit));
    }

    @Override
    public Response<Void> updateBindUidByDataId(List<UpdateBatchDto> updateBatchDtos) {
        log.info("更新根据mobile未查询到uid入参：{}", updateBatchDtos);
        blackListDataService.updateBindUidByDataId(updateBatchDtos);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<List<BlacklistDataDto>> queryNotBoundMobile(Long previousId, Integer limit) {
        log.info("请求根据uid未查询到mobile入参：previousId:{}, limit:{}", previousId, limit);
        return NewResponseUtil.makeSuccess(blackListDataService.queryNotBoundMobile(previousId, limit));
    }

    @Override
    public Response<Void> updateBindMobileByDataId(List<UpdateBatchDto> updateBatchDtos) {
        log.info("更新根据uid未查询到mobile入参：{}", updateBatchDtos);
        blackListDataService.updateBindMobileByDataId(updateBatchDtos);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> verifyAutoAddBlacklist(VerifyAutoAddBlacklistVo verifyAutoAddBlacklistVo) {
        log.info("异常证实自动添加黑名单param:{}", verifyAutoAddBlacklistVo);
        blackListDataService.saveAutoAddVerify(verifyAutoAddBlacklistVo);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<List<BlacklistDataDto>> listByOperateNameAndTime(String operateName, long beginTime, long endTime) {
        return NewResponseUtil.makeSuccess(blackListDataService.listByOperateNameAndTime(operateName, beginTime, endTime));
    }

    @Override
    public Response<Integer> countByOperateNameAndTime(String operateName, long beginTime, long endTime) {
        return NewResponseUtil.makeSuccess(blackListDataService.countByOperateNameAndTime(operateName, beginTime, endTime));
    }

    @Override
    public Response<List<RiskBlacklistDataTypeRefVo>> listByDataIds(List<Long> dataIds) {
        return NewResponseUtil.makeSuccess(blackListDataService.listByDataIds(dataIds));
    }

    @Override
    public Response<String> getAddLogByDataId(Long dataId) {
        List<BlacklistDataLogVo> blacklistDataLogVos = blackListDataService.queryDatLogByDataId(dataId);
        String s = CollectionUtils.isEmpty(blacklistDataLogVos) ? "" : blacklistDataLogVos.get(blacklistDataLogVos.size() - 1).getOperateReason();
        return NewResponseUtil.makeSuccess(s);
    }

    @Override
    public Response<BlacklistDataDto> queryByBlacklistId(Long id) {
        RiskBlacklistData data = blackListDataService.selectByPrimaryKey(id);
        if (Objects.isNull(data)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        BlacklistDataDto dto = new BlacklistDataDto();
        BeanUtils.copyProperties(data, dto);
        return NewResponseUtil.makeSuccess(dto);
    }
}
