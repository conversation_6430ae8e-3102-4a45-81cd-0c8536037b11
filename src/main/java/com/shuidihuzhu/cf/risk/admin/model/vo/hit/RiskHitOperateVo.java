package com.shuidihuzhu.cf.risk.admin.model.vo.hit;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleActionEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitOperate;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/21 15:39
 */
@Data
@NoArgsConstructor
public class RiskHitOperateVo {

    public RiskHitOperateVo(RiskStrategyHitOperate hitOperate) {
        this.id = hitOperate.getId();
        this.handleResult = hitOperate.getResult();
        this.handleActions = RiskHandleActionEnum.codeStr2Codes(hitOperate.getAction());
        this.remark = hitOperate.getRemark();
        this.handleName = hitOperate.getOperateName();
        this.handleTime = DateUtil.getDate2LStr(hitOperate.getUpdateTime());
        this.activeStop= hitOperate.getActiveStop();
        this.radio = hitOperate.getRadio();
        this.otherReason = hitOperate.getOtherReason();
        this.repeatCaseId = hitOperate.getRepeatCaseId();
    }

    @Min(value = 1)
    @NotNull
    @ApiModelProperty("风险命中记录id")
    private Long id;
    @Min(value = 1)
    @NotNull
    @ApiModelProperty("caseId")
    private Integer caseId;
    @Min(value = 1)
    @NotNull
    @ApiModelProperty("命中时机")
    private Integer hitPhaseCode;
    @NotNull
    @Min(value = 0)
    @ApiModelProperty("核实结果")
    private Byte handleResult;
    @Size(min = 1)
    @NotNull
    @ApiModelProperty("处理动作")
    private List<Integer> handleActions;
    @Length(min = 5, max = 200)
    @NotBlank
    @ApiModelProperty("跟进备注")
    private String remark;
    @ApiModelProperty("处理人")
    private String handleName;
    @ApiModelProperty("处理时间")
    private String handleTime;

    @ApiModelProperty("按钮id 可以是-1")
    private String radio;
    @ApiModelProperty("重复案例id")
    private Integer repeatCaseId;
    @ApiModelProperty("主动停止筹款原因")
    private String activeStop;
    @ApiModelProperty("其他原因")
    private String otherReason;
    @ApiModelProperty("黑名单类型Id")
    private Long typeId;
    @ApiModelProperty("限制动作集合")
    private List<BlacklistTypeActionRefDto> actionList;

}
