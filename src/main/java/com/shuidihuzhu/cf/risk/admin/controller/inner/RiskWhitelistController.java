package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.biz.whiteList.RiskWhiteListBiz;
import com.shuidihuzhu.cf.risk.admin.service.hit.RiskHitService;
import com.shuidihuzhu.cf.risk.client.admin.whitelist.RiskWhitelistClient;
import com.shuidihuzhu.cf.risk.model.admin.whitelist.SpotDetectionDto;
import com.shuidihuzhu.cf.risk.model.admin.whitelist.WhitelistIdentifyCheckDto;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.whitelist.RiskWhiteListTypeEnum;
import com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto;
import com.shuidihuzhu.cf.risk.model.risk.whiteList.SpotDetectionCheckDto;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/7/21 19:37
 */
@RestController
@Slf4j
public class RiskWhitelistController implements RiskWhitelistClient {

    @Resource
    private RiskWhiteListBiz whiteListBiz;
    @Resource
    private RiskHitService riskHitService;

    @Override
    public Response<WhitelistIdentifyCheckDto> checkWhitelistData(WhitelistIdentifyCheckDto checkDto) {
        log.info("查询白名单标识，checkDto:{}", checkDto);
        RiskWhiteListDto idCardWhitelist = whiteListBiz.getByCipherIdCardAndType(checkDto.getIdCard(),
                RiskWhiteListTypeEnum.EXCEPTION_IDENTITY_RELEVANCE.getCode());
        if (idCardWhitelist != null) {
            checkDto.setHitIdCard(true);
        }
        RiskWhiteListDto idCardMobile = whiteListBiz.getByCipherMobileAndType(checkDto.getMobile(),
                RiskWhiteListTypeEnum.EXCEPTION_IDENTITY_RELEVANCE.getCode());
        if (idCardMobile != null) {
            checkDto.setHitMobile(true);
        }
        log.info("查询白名单标识，resp:{}", checkDto);
        return NewResponseUtil.makeSuccess(checkDto);
    }

    @Override
    public Response<SpotDetectionCheckDto> checkSpotDetection(SpotDetectionDto spotDetectionDto) {
        log.info("检测是否已经命中过，spotDetectionDto:{}", spotDetectionDto);
        SpotDetectionCheckDto spotDetectionCheckDto = riskHitService.spotDetection(
                RiskStrategyEnum.fromCode(spotDetectionDto.getRiskStrategyEnum()),
                RiskStrategySecondEnum.fromCode(spotDetectionDto.getStrategySecondEnum()), spotDetectionDto.isNeedEncrypt(),
                spotDetectionDto.getCaseId(), spotDetectionDto.getCallPhase(), spotDetectionDto.getHitVerifyDtos());
        log.info("检测是否已经命中过，resp:{}", spotDetectionCheckDto);
        return NewResponseUtil.makeSuccess(spotDetectionCheckDto);
    }
}
