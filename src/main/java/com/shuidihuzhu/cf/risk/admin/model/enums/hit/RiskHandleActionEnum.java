package com.shuidihuzhu.cf.risk.admin.model.enums.hit;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 风控策略枚举
 * <AUTHOR>
 * @date 2020/8/20 17:32
 */
@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_strategy_hit_record", columnName = "action"),
        @DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_strategy_hit_operate", columnName = "action")})
@AllArgsConstructor
@Getter
public enum RiskHandleActionEnum {

    DEFAULT(0, "初始状态，未处理过"),
    KEEP_FOLLOW(1, "持续跟进"),
    OPERATION_UNLOCK(2, "操作解锁"),
    STOP_RAISE(3, "停止筹款"),
    MARK_REPORT(4, "标记举报"),
    LIMIT_RESOURCE(5, "限制推广资源"),
    LIMIT_CROWDFUNDING(6, "限制发起"),
    OTHER(7, "其他"),
    ;

    public static List<Integer> codeStr2Codes(String codeStr){
        if (StringUtils.isBlank(codeStr)) {
            return Collections.emptyList();
        }
        List<Integer> codes = Lists.newArrayListWithCapacity(enumMap.size());
        for (String code : Splitter.on(",").split(codeStr)) {
            codes.add(enumMap.get(Integer.valueOf(code)).getCode());
        }
        return codes;
    }

    public static List<String> codes2Names(String codes){
        if (StringUtils.isBlank(codes)) {
            return Collections.emptyList();
        }
        List<String> names = Lists.newArrayListWithCapacity(enumMap.size());
        for (String code : Splitter.on(",").split(codes)) {
            names.add(enumMap.get(Integer.valueOf(code)).getDesc());
        }
        return names;
    }

    public static Map<Integer, String> usableKeyVal(){
        return Arrays.stream(values()).filter(riskHandleActionEnum -> riskHandleActionEnum.getCode()>0)
                .collect(Collectors.toMap(RiskHandleActionEnum::getCode, RiskHandleActionEnum::getDesc));
    }

    public static boolean isInHand(List<Integer> codes){
        return CollectionUtils.intersection(codes, List.of(KEEP_FOLLOW.code)).size() > 0;
    }

    private int code;
    private String desc;
    private static final Map<Integer, RiskHandleActionEnum> enumMap = Arrays.stream(values())
            .collect(Collectors.toMap(RiskHandleActionEnum::getCode, Function.identity()));

}
