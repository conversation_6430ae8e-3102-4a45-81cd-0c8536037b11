package com.shuidihuzhu.cf.risk.admin.util;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 掩码工具类
 * @Author: panghair<PERSON>
 * @Date: 2022/7/5 7:36 下午
 */
public class MaskUtil {

    public static String maskStrAfterFour(String str) {
        if (StringUtils.isEmpty(str) || str.length() < 4) {
            return "";
        }

        return str.substring(0, str.length() - 4) + "****";
    }

}
