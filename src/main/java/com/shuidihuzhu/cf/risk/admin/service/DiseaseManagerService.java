package com.shuidihuzhu.cf.risk.admin.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.util.ExcelFileUtil;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseOperationRecord;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseDataBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseOperationRecordBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseTreatmentProjectBiz;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseRaiseTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseTreamentChoiceTypeEnum;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;
import com.shuidihuzhu.pf.common.v2.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Service
@Slf4j
public class DiseaseManagerService {


    @Autowired
    private RiskDiseaseDataBiz riskDiseaseDataBiz;
    @Autowired
    private RiskDiseaseTreatmentProjectBiz treatmentProjectBiz;
    @Autowired
    private RiskDiseaseOperationRecordBiz diseaseOperationRecordService;
    @Autowired
    private SeaAccountService seaAccountService;
    @Value("${disease.treatmentProjectName:一般治疗,保守治疗,靶向药治疗,化疗,放疗,骨髓移植,手术,康复,脑起搏器,心脏起搏器,换瓣膜,支架,搭桥,拆钢板,其他}")
    private String treatmentNameListString;
    @Autowired
    private ExcelFileUtil excelFileUtil;

    /**
     *
     * @param diseaseId 疾病序号
     * @return 删除结果
     */
    public Response<Boolean> delete(long diseaseId, String deleteReason) {
        int result = riskDiseaseDataBiz.delete(diseaseId);
        if (result < 1) {
            return NewResponseUtil.makeSuccess(false);
        }
        treatmentProjectBiz.deleteByDiseaseId(diseaseId);
        //保存操作记录
        diseaseOperationRecordService.
                save(RiskDiseaseOperationRecord.buildDelete(Math.toIntExact(ContextUtil.getAdminLongUserId()),
                        seaAccountService.getName(ContextUtil.getAdminLongUserId()), diseaseId, deleteReason));
        return NewResponseUtil.makeSuccess(true);
    }


    public boolean checkDiseaseClassName(RiskDiseaseInfoVO riskDiseaseInfoVO) {
        if (riskDiseaseInfoVO == null) {
            return false;
        }
        RiskDiseaseData riskDiseaseData = riskDiseaseDataBiz
                .getByClassName(StringUtils.trimToEmpty(riskDiseaseInfoVO.getDiseaseClassName()));
        if (riskDiseaseData == null) {
            return false;
        }
        if (riskDiseaseInfoVO.getId() == 0) {
            return true;
        }
        return riskDiseaseData.getId() != riskDiseaseInfoVO.getId();
    }

    /**
     * 保存疾病信息
     * @param riskDiseaseInfoVO 疾病库基本信息
     * @param treatmentProjects 疾病治疗方案
     * @param adminUserId       后台操作人员
     * @return 是否保存成功
     */
    public boolean create(RiskDiseaseInfoVO riskDiseaseInfoVO,
                          List<RiskDiseaseTreatmentProject> treatmentProjects,
                          int adminUserId,
                          String param) {

        RiskDiseaseData riskDiseaseData = RiskDiseaseData.build(riskDiseaseInfoVO);
        if (treatmentProjects.size() > 1) {
            riskDiseaseData.setChoiceType(DiseaseTreamentChoiceTypeEnum.SINGLE_CHOICE.getCode());
        }
        int result = riskDiseaseDataBiz.save(riskDiseaseData);
        if (result < 1) {
            return false;
        }
        treatmentProjects.forEach( v -> v.setDiseaseId(riskDiseaseData.getId()));
        treatmentProjectBiz.saveList(treatmentProjects);
        //保存操作记录
        diseaseOperationRecordService.
                save(RiskDiseaseOperationRecord.buildSave(adminUserId,
                        seaAccountService.getName(ContextUtil.getAdminLongUserId()), riskDiseaseData.getId(), "", param));
        return true;
    }

    /**
     *
     * @param riskDiseaseInfoVO     基本信息
     * @param treatmentProjects     疾病治疗方案信息
     * @param adminUserId           后来操作人员
     * @return  更新是否成功
     */
    public boolean edit(RiskDiseaseInfoVO riskDiseaseInfoVO,
                        List<RiskDiseaseTreatmentProject> treatmentProjects,
                        int adminUserId,
                        String param) {
        //更新数据
        RiskDiseaseData riskDiseaseData = RiskDiseaseData.build(riskDiseaseInfoVO);
        RiskDiseaseData riskDiseaseDataBefore = riskDiseaseDataBiz.getById(riskDiseaseInfoVO.getId());
        if (riskDiseaseDataBefore == null) {
            return false;
        }
        treatmentProjects = updateTreatmentProject(riskDiseaseData.getId(), treatmentProjects);
        if (treatmentProjects.size() >1 &&
                riskDiseaseDataBefore.getChoiceType() == DiseaseTreamentChoiceTypeEnum.DEFAULT.getCode()) {
            riskDiseaseData.setChoiceType(DiseaseTreamentChoiceTypeEnum.SINGLE_CHOICE.getCode());
        } else {
            riskDiseaseData.setChoiceType(riskDiseaseDataBefore.getChoiceType());
        }
        int result = riskDiseaseDataBiz.update(riskDiseaseData);
        log.info("update riskDiseaseData result:{}", result);
        //查找上一次操作记录
        RiskDiseaseOperationRecord operateBefore = diseaseOperationRecordService.getLastOneByDiseaseId(riskDiseaseData.getId());
        //保存操作记录
        diseaseOperationRecordService.
                save(RiskDiseaseOperationRecord.buildUpdate(adminUserId, seaAccountService.getName(ContextUtil.getAdminLongUserId()),
                        riskDiseaseData.getId(), operateBefore == null ? "" : operateBefore.getOperateAfter(), param));
        return true;
    }

    private List<RiskDiseaseTreatmentProject> updateTreatmentProject(long diseaseId, List<RiskDiseaseTreatmentProject> treatmentProjects) {
        //治疗方案写入
        treatmentProjects.forEach(v -> v.setDiseaseId(diseaseId));
        //查询案例现有的治疗方案
        List<RiskDiseaseTreatmentProject> existTreatmentProjectList =  treatmentProjectBiz.findByDiseaseId(diseaseId);
        if (CollectionUtils.isEmpty(existTreatmentProjectList)){
            log.info("no existTreatmentProjectList");
            treatmentProjectBiz.saveList(treatmentProjects);
            return Lists.newArrayList(existTreatmentProjectList);
        }
        //保存存在的list
        List<RiskDiseaseTreatmentProject>  resultList= Lists.newArrayListWithCapacity(treatmentProjects.size());

        Set<Long> existTreatmentProjectIdList =
                existTreatmentProjectList.stream().map(RiskDiseaseTreatmentProject::getId).collect(Collectors.toSet());
        //判断是否有新增的治疗方案
        List<RiskDiseaseTreatmentProject> newTreatmentProjectList =
                treatmentProjects.stream().filter(v -> v.getId() == 0)
                        .collect(Collectors.toList());
        treatmentProjectBiz.saveList(newTreatmentProjectList);
        resultList.addAll(newTreatmentProjectList);

        //删除不包含的治疗方案
        List<Long> nowTreatmentProjectIds = treatmentProjects.stream().map(RiskDiseaseTreatmentProject::getId).collect(Collectors.toList());
        List<Long> needDeleteIdList = existTreatmentProjectIdList.stream().filter(v -> !nowTreatmentProjectIds.contains(v))
                .collect(Collectors.toList());
        treatmentProjectBiz.deleteByIdList(needDeleteIdList);

        //更新治疗方案
        List<RiskDiseaseTreatmentProject> updateList = treatmentProjects.stream()
                .filter(v -> existTreatmentProjectIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateList)) {
            return resultList;
        }
        treatmentProjectBiz.updateList(updateList);
        resultList.addAll(updateList);
        return resultList;
    }

    public Response<RiskDiseaseInfoProjectNameVO> getProjectNameList() {
        RiskDiseaseInfoProjectNameVO projectNameVO = new RiskDiseaseInfoProjectNameVO();
        projectNameVO.setTreatmentNameList(Splitter.on(",").splitToList(treatmentNameListString));
        return NewResponseUtil.makeSuccess(projectNameVO);
    }

    /**
     * 获取疾病详情
     * @param diseaseId
     * @return
     */
    public Response<RiskDiseaseInfoVO> getDetail(long diseaseId) {
        RiskDiseaseData riskDiseaseData = riskDiseaseDataBiz.getById(diseaseId);
        if (riskDiseaseData == null) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_NOT_EXIST);
        }
        RiskDiseaseInfoVO riskDiseaseInfoVO = RiskDiseaseInfoVO.build(riskDiseaseData);
        //查询治疗方案
        List<RiskDiseaseTreatmentProject> treatmentProjects = treatmentProjectBiz.findByDiseaseId(diseaseId);
        List<RiskDiseaseTreatmentProjectVO> treatmentProjectVOS = Lists.newArrayList();
        for (RiskDiseaseTreatmentProject treatmentProject: treatmentProjects) {
            RiskDiseaseTreatmentProjectVO projectVO = RiskDiseaseTreatmentProjectVO.build(treatmentProject);
            projectVO.buildRaiseType(riskDiseaseData.getRaiseType(), treatmentProject.getRaiseType());
            treatmentProjectVOS.add(projectVO);
        }
        riskDiseaseInfoVO.setTreatMethodList(JSONObject.toJSONString(treatmentProjectVOS));
        return NewResponseUtil.makeSuccess(riskDiseaseInfoVO);
    }

    public Response<List<RiskRaiseTypeVO>> getRaiseType() {
        List<RiskRaiseTypeVO> riskRaiseTypeVOS = Lists.newArrayList();
        for (DiseaseRaiseTypeEnum value : DiseaseRaiseTypeEnum.values()) {
            if (value == DiseaseRaiseTypeEnum.DEFAULT) {
                continue;
            }
            RiskRaiseTypeVO riskRaiseTypeVO = new RiskRaiseTypeVO();
            riskRaiseTypeVO.setRaiseTypeName(value.getDesc());
            riskRaiseTypeVO.setRaiseTypeId(value.getCode());
            riskRaiseTypeVOS.add(riskRaiseTypeVO);
        }
        return NewResponseUtil.makeSuccess(riskRaiseTypeVOS);
    }

    public Response<List<RiskDiseaseTreatmentProjectVO>> listTreatment(long diseaseId) {
        List<RiskDiseaseTreatmentProjectVO> treatmentProjectVOS = Lists.newArrayList();
        //查询疾病
        RiskDiseaseData diseaseData = riskDiseaseDataBiz.getById(diseaseId);
        int deseaseRaiseType = diseaseData == null ? 0 : diseaseData.getRaiseType();
        //查询治疗方案
        List<RiskDiseaseTreatmentProject> treatmentProjects = treatmentProjectBiz.findByDiseaseId(diseaseId);
        for (RiskDiseaseTreatmentProject treatmentProject: treatmentProjects) {
            RiskDiseaseTreatmentProjectVO treatmentProjectVO = RiskDiseaseTreatmentProjectVO.build(treatmentProject);
            treatmentProjectVO.buildRaiseType(deseaseRaiseType, treatmentProject.getRaiseType());
            treatmentProjectVOS.add(treatmentProjectVO);
        }
        return NewResponseUtil.makeSuccess(treatmentProjectVOS);
    }

    public Response<List<RiskDiseaseOperationVo>> getOperationRecordList(long diseaseId) {
        //查询操作记录
        List<RiskDiseaseOperationRecord> operationRecords = diseaseOperationRecordService.findListByDiseaseId(diseaseId);
        //创建操作记录
        List<RiskDiseaseOperationVo> data = Lists.newArrayList();
        for (RiskDiseaseOperationRecord operationRecord : operationRecords){
            operationRecord.setOperatorName(getNameWithOrg(operationRecord));
            RiskDiseaseOperationVo operationVo = RiskDiseaseOperationVo.build(operationRecord);
            data.add(operationVo);
        }
        return NewResponseUtil.makeSuccess(data);
    }

    private String getNameWithOrg(RiskDiseaseOperationRecord operationRecord) {
        final String operatorName = operationRecord.getOperatorName();
        final int userId = operationRecord.getUserId();
        if (userId <= 0) {
            return operatorName;
        }
        final String org = seaAccountService.getOrganization(userId);
        if (StringUtils.isEmpty(org)) {
            return operatorName;
        }
        return org + "-" + operatorName;
    }

    public Response getDiseaseList(String diseaseClassName,
                                   String medicalName,
                                   String normalName,
                                   int raiseType,
                                   PageRequest pageRequest) {
        List<RiskDiseaseData>  diseaseDataList =
                riskDiseaseDataBiz.findList(diseaseClassName, medicalName, normalName, raiseType, pageRequest);
        log.info("diseaseDataList get List size :{}", diseaseDataList.size());
        List<RiskDiseaseInfoVO> data = Lists.newArrayList();
        for (RiskDiseaseData diseaseData : diseaseDataList){
            RiskDiseaseInfoVO infoVO = RiskDiseaseInfoVO.build(diseaseData);
            data.add(infoVO);
        }
        return NewResponseUtil.makeSuccess(PageUtil.buildPageResponse
                (data, pageRequest));
    }

    public Response getDiseaseListV2(String diseaseClassName,
                                   String treatMethod,
                                   int isDelete,
                                   String startCreateTime,
                                   String endCreateTime,
                                   int raiseType,
                                   int current,
                                   int pageSize) {
        List<Long> diseaseIds = Lists.newArrayList();
        if(StringUtils.isNotEmpty(treatMethod)){
            List<RiskDiseaseTreatmentProject> treatMethodList = treatmentProjectBiz.findByProjectName(treatMethod);
            if(CollectionUtils.isEmpty(treatMethodList)){
                return createResult(current, pageSize, Lists.newArrayList(), Lists.newArrayList());
            }
            diseaseIds = treatMethodList.stream().map(RiskDiseaseTreatmentProject::getDiseaseId).distinct().collect(Collectors.toList());
        }
        List<RiskDiseaseData>  totalDiseaseDataList =riskDiseaseDataBiz.findListV2(diseaseClassName, isDelete, startCreateTime,
                endCreateTime, raiseType, diseaseIds, current, pageSize);
        PageHelper.startPage(current, pageSize);
        List<RiskDiseaseData>  diseaseDataList =riskDiseaseDataBiz.findListV2(diseaseClassName, isDelete, startCreateTime,
                endCreateTime, raiseType, diseaseIds, current, pageSize);
        log.info("diseaseDataList get List size :{}", diseaseDataList.size());
        List<RiskDiseaseInfoVO> data = Lists.newArrayList();
        for (RiskDiseaseData diseaseData : diseaseDataList){
            RiskDiseaseInfoVO infoVO = RiskDiseaseInfoVO.build(diseaseData);
            data.add(infoVO);
        }
        return createResult(current, pageSize, totalDiseaseDataList, data);
    }

    @NotNull
    private Response<Map<String, Object>> createResult(int current, int pageSize, List<RiskDiseaseData> totalDiseaseDataList, List<RiskDiseaseInfoVO> data) {
        Map<String, Object> pageMap = Maps.newHashMap();
        pageMap.put("current", current);
        pageMap.put("pageSize", pageSize);
        pageMap.put("total", totalDiseaseDataList.size());

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("pagination", pageMap);
        resultMap.put("data", data);
        return NewResponseUtil.makeSuccess(resultMap);
    }

    public void saveExcel(InputStream ins, String originFileName, String sheetName) {
        if (StringUtils.isBlank(sheetName)){
            return;
        }
        Response<List<Sheet>> sheet = excelFileUtil.checkAndGetSheet(ins, originFileName,
                Splitter.on(",").splitToList(sheetName));
        if (sheet.notOk()) {
            log.info("response:{}", sheet.getCode());
            return;
        }
        List<Sheet> sheets = sheet.getData();
        if (CollectionUtils.isEmpty(sheets)) {
            return;
        }
        saveSheetList(sheets);
    }

    private void saveSheetList(List<Sheet> sheets) {
        List<RiskDiseaseTreatmentProject> treatmentProjects = Lists.newLinkedList();
        for (Sheet sheet : sheets) {
            if (sheet == null) {
                log.warn("sheet is null");
                continue;
            }
            //保存是否只保存治疗方案的标识
            boolean saveTreatment = false;
            for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row xssRow = sheet.getRow(rowNum);
                if (excelFileUtil.getCellValue(xssRow.getCell(0)).equals("疾病类别名称")) {
                    //根据第二列判断是否需要生成疾病
                    saveTreatment = excelFileUtil.getCellValue(xssRow.getCell(1)).equals("方案名称");
                    continue;
                }
                String diseaseClassName = excelFileUtil.getCellValue(xssRow.getCell(0));
                //判断老数据是否存在
                RiskDiseaseData riskDiseaseData = riskDiseaseDataBiz.getByClassName(diseaseClassName);
                if (saveTreatment) {
                    if (riskDiseaseData == null) {
                        log.error("diseaseClassName not exist:{}", diseaseClassName);
                        continue;
                    }
                    String projectName= excelFileUtil.getCellValue(xssRow.getCell(1));
                    if (StringUtils.isBlank(projectName)){
                        continue;
                    }
                    //删除之前的治疗方案
                    treatmentProjectBiz.deleteByDiseaseId(riskDiseaseData.getId());
                    //只保存治疗方案
                    treatmentProjects.add(buildTreatment(riskDiseaseData.getId(),
                            excelFileUtil.getCellValue(xssRow.getCell(1)),
                            excelFileUtil.getCellValue(xssRow.getCell(2)),
                            getIntAmount(excelFileUtil.getCellValue(xssRow.getCell(3))),
                            getIntAmount(excelFileUtil.getCellValue(xssRow.getCell(4)))));
                    continue;
                }
                buildDiseaseData(xssRow, riskDiseaseData);
            }
        }
        treatmentProjectBiz.saveList(treatmentProjects);
    }

    private void buildDiseaseData(Row xssRow, RiskDiseaseData riskDiseaseData) {
        if (riskDiseaseData != null) {
            //先删除老的数据
            //int  result = riskDiseaseDataBiz.delete(riskDiseaseData.getId());
            log.debug("delete riskDiseaseData id:{}", riskDiseaseData.getId());
            //疾病库1.3 只新增
            return;
        }
        riskDiseaseData =
                RiskDiseaseData.build(excelFileUtil.getCellValue(xssRow.getCell(0)),
                        excelFileUtil.getCellValue(xssRow.getCell(1)),
                        excelFileUtil.getCellValue(xssRow.getCell(3)),
                        excelFileUtil.getCellValue(xssRow.getCell(2)),
                        excelFileUtil.getCellValue(xssRow.getCell(4)).equals("可发") ? 1 : 2);
        if (StringUtils.isBlank(riskDiseaseData.getDiseaseClassName())){
            return;
        }
        riskDiseaseDataBiz.save(riskDiseaseData);
    }

    private RiskDiseaseTreatmentProject buildTreatment(long disesaeId,
                                                       String projectName,
                                                       String projectMergeRule,
                                                       int minTreatmentFee,
                                                       int maxTreatmentFee) {
        RiskDiseaseTreatmentProject treatmentProject = new RiskDiseaseTreatmentProject();
        treatmentProject.setDiseaseId(disesaeId);
        treatmentProject.setProjectName(projectName);
        treatmentProject.setProjectMergeRule(projectMergeRule);
        treatmentProject.setMinTreatmentFee(minTreatmentFee);
        treatmentProject.setMaxTreatmentFee(maxTreatmentFee);
        log.debug("riskDiseaseData:{}", JSON.toJSONString(treatmentProject));
        return treatmentProject;
    }

    private int  getIntAmount(String amountString){
        if (StringUtils.isBlank(amountString)) {
            return 0;
        }
        return (int)(Double.parseDouble(amountString)*100);
    }

    private List<RiskDiseaseData> filterIsDelete(String isDelete, List<RiskDiseaseData> riskDiseaseDataList) {
        if(StringUtils.isNotEmpty(isDelete)){
            return riskDiseaseDataList.stream().filter(v -> v.getIsDelete() == Integer.parseInt(isDelete)).collect(Collectors.toList());
        }
        return riskDiseaseDataList;
    }
    public static void main(String[] args) {
        System.out.println("尿毒症，肾功能衰竭/慢性肾脏病/慢性肾脏疾病/慢性肾衰/慢性肾功能不全/慢性肾功能衰竭+尿毒症期/CKD4/CKD5/肾衰竭/衰竭期/血液透析/急性加重/Ⅳ期/5期//5D期/Ⅴ期/CKD-5期/失代偿期/CKD5HD期，终末期肾病，肾病终末期，肾终末期疾病".length());
    }
}
