package com.shuidihuzhu.cf.risk.admin.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/6/19
 */
public class ListUtil {
    public static <T> List<T> getModelListFromResponse(Response<List<String>> feignResponse, Class<T> modelClazz) {
        if (feignResponse.ok() && CollectionUtils.isNotEmpty(feignResponse.getData())) {
            List<T> retList = Lists.newArrayList();
            for (String string : feignResponse.getData()) {
                retList.add(JSON.parseObject(string, modelClazz));
            }
            return retList;
        }
        return Lists.newArrayList();
    }

    public static <T> T getModelFromResponse(Response<String> feignResponse, Class<T> modelClazz){
        if(feignResponse.ok() && StringUtils.isNotBlank(feignResponse.getData())){
            return JSON.parseObject(feignResponse.getData(),modelClazz);
        }
        return null;
    }

    public static <K, V extends Comparable<? super V>> Map<K, V> sortByValue(Map<K, V> map) {
        Map<K, V> result = new LinkedHashMap<>();
        map.entrySet().stream()
                .sorted(Map.Entry.<K, V>comparingByValue()
                        .reversed()).forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
        return result;
    }
}
