package com.shuidihuzhu.cf.risk.admin.dao.list;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartmentCase;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskListDepartmentCaseDao {

    int insertSelective(RiskListDepartmentCase record);

    RiskListDepartmentCase selectByPrimaryKey(Long id);

    RiskListDepartmentCase getByUniqueTelAndCaseId(@Param("areaCode") String areaCode, @Param("landline") String landline, @Param("extension") String extension, @Param("caseId") Integer caseId);

    List<RiskListDepartmentCase> listByUniqueTelLimit100(@Param("areaCode") String areaCode, @Param("landline") String landline, @Param("extension") String extension);
}