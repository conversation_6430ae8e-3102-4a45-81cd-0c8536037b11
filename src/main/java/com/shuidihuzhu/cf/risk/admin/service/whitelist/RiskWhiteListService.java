package com.shuidihuzhu.cf.risk.admin.service.whitelist;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.risk.admin.biz.whiteList.RiskWhiteListBiz;
import com.shuidihuzhu.cf.risk.admin.biz.whiteList.RiskWhiteListLogBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.dto.whitelist.RiskWhiteListLog;
import com.shuidihuzhu.cf.risk.admin.model.enums.list.WhiteOperateTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.list.WhitePermissionsEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotScenePermissionsEnum;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.whiteList.WhiteListQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.whiteList.RiskWhiteListAddVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.whiteList.RiskWhiteListLogVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.whiteList.RiskWhiteListVo;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.Consts;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/7
 */
@Service
@Slf4j
public class RiskWhiteListService {

    @Autowired
    private RiskWhiteListBiz riskWhiteListBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private RiskWhiteListLogBiz riskWhiteListLogBiz;

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Autowired
    private MaskUtil maskUtil;


    public Response<Void> add(RiskWhiteListAddVo riskWhiteListAddVo, long adminUserId) {
        Response response = checkVailid(riskWhiteListAddVo);
        if (response.notOk()){
            return response;
        }
        RiskWhiteListDto dto = buildDto(riskWhiteListAddVo, adminUserId);
        int result = riskWhiteListBiz.save(dto);
        if (result > 0) {
            riskWhiteListLogBiz.saveLog(buildRiskWhiteListLog(WhiteOperateTypeEnum.ADD, adminUserId, dto));
        }
        log.info("save fail:{}" , JSON.toJSONString(riskWhiteListAddVo));
        return NewResponseUtil.makeSuccess(null);
    }

    private RiskWhiteListLog buildRiskWhiteListLog(WhiteOperateTypeEnum typeEnum,
                                                   long adminUserId,
                                                   RiskWhiteListDto dto) {
        return buildRiskWhiteListLog(typeEnum, adminUserId, dto, "");
    }


    private RiskWhiteListLog buildRiskWhiteListLog(WhiteOperateTypeEnum typeEnum,
                                                   long adminUserId,
                                                   RiskWhiteListDto dto,
                                                   String otherInfo) {
        RiskWhiteListLog riskWhiteListLog = new RiskWhiteListLog();
        riskWhiteListLog.setOperateType(typeEnum.getCode());
        riskWhiteListLog.setOperateId(adminUserId);
        riskWhiteListLog.setOperateName(dto.getOperator());
        riskWhiteListLog.setWhiteListId(dto.getId());
        riskWhiteListLog.setOtherInfo(otherInfo);
        return riskWhiteListLog;
    }

    private RiskWhiteListDto buildDto(RiskWhiteListAddVo riskWhiteListAddVo, long adminUserId) {
        RiskWhiteListDto dto = new RiskWhiteListDto();
        dto.setAddReason(riskWhiteListAddVo.getAddReason());
        dto.setPhoneNumber(StringUtils.isBlank(riskWhiteListAddVo.getPhoneNumber()) ? "" : shuidiCipher.encrypt(riskWhiteListAddVo.getPhoneNumber()));
        dto.setIdCard(StringUtils.isBlank(riskWhiteListAddVo.getIdCard()) ?
                "" : shuidiCipher.encrypt(StringUtils.upperCase(riskWhiteListAddVo.getIdCard())));
        dto.setName(StringUtils.trimToEmpty(riskWhiteListAddVo.getName()));
        dto.setExpireTime(DateUtil.getDateFromLongString(riskWhiteListAddVo.getExpireTime()));
        dto.setOperator(getOrgWithName(adminUserId));
        dto.setId(riskWhiteListAddVo.getId());
        return dto;
    }

    public  Response<Void> update(RiskWhiteListAddVo riskWhiteListAddVo, long adminUserId) {
        Response response = checkVailid(riskWhiteListAddVo);
        if (response.notOk()){
            return response;
        }
        RiskWhiteListDto dto = buildDto(riskWhiteListAddVo, adminUserId);
        int result = riskWhiteListBiz.update(dto);
        if (result > 0) {
            riskWhiteListLogBiz.saveLog(buildRiskWhiteListLog(WhiteOperateTypeEnum.UPDATE, adminUserId, dto));
        }
        log.info("update fail:{}" , JSON.toJSONString(riskWhiteListAddVo));
        return NewResponseUtil.makeSuccess(null);
    }

    private Response checkVailid(RiskWhiteListAddVo riskWhiteListAddVo) {
        if (StringUtils.isNotBlank(riskWhiteListAddVo.getIdCard())) {
            if (!Consts.pIdCard.matcher(riskWhiteListAddVo.getIdCard()).matches()){
                return NewResponseUtil.makeError(RiskAdminErrorCode.MOBILE_IS_ERROR);
            }
        }
        if (StringUtils.isNotBlank(riskWhiteListAddVo.getPhoneNumber())) {
            if (!Consts.pMobile.matcher(riskWhiteListAddVo.getPhoneNumber()).matches()){
                return NewResponseUtil.makeError(RiskAdminErrorCode.ID_CARD_IS_ERROR);
            }
        }
        return NewResponseUtil.makeSuccess(null);
    }

    public Response<Void> updateExpireTime(Date expireTime, long id, long adminUserId) {
        RiskWhiteListDto dto = riskWhiteListBiz.getById(id);
        if (dto == null){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int result =  riskWhiteListBiz.updateExpireTime(expireTime, id, getOrgWithName(adminUserId));
        if (result > 0) {
            riskWhiteListLogBiz.saveLog(
                    buildRiskWhiteListLog(WhiteOperateTypeEnum.UPDATE_EXPIRE_TIME, Math.toIntExact(adminUserId), id,
                    ":\n" + RiskWhiteListVo.formatExpireTime(dto.getExpireTime())
                            +  "修改为" + DateUtil.formatDateTime(expireTime), adminUserId));
        }
        log.info("updateExpireTime fail id:{}" , id);
        return NewResponseUtil.makeSuccess(null);
    }

    private RiskWhiteListLog buildRiskWhiteListLog(WhiteOperateTypeEnum typeEnum, int adminUserId, long listId, String otherInfo, long userId) {
        RiskWhiteListLog riskWhiteListLog = new RiskWhiteListLog();
        riskWhiteListLog.setOperateType(typeEnum.getCode());
        riskWhiteListLog.setOperateId(adminUserId);
        riskWhiteListLog.setOperateName(getOrgWithName(userId));
        riskWhiteListLog.setWhiteListId(listId);
        riskWhiteListLog.setOtherInfo(otherInfo);
        return riskWhiteListLog;
    }

    private String getOrgWithName(long adminUserId){
        SeaAccountService.AdminUserNameWithOrg adminUserNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        return adminUserNameWithOrg == null ? "" : adminUserNameWithOrg.getUserNameWithOrg();
    }

    public Response<RiskWhiteListVo>  get(long id) {
        RiskWhiteListDto riskWhiteListDto = riskWhiteListBiz.getById(id);
        dealDecryptParam(riskWhiteListDto);
        return NewResponseUtil.makeSuccess(buildVo(riskWhiteListDto));
    }

    private RiskWhiteListVo buildVo(RiskWhiteListDto riskWhiteListDto) {
        if (riskWhiteListDto == null) {
            return null;
        }
        RiskWhiteListVo riskWhiteListVo = new RiskWhiteListVo();
        riskWhiteListVo.setId(riskWhiteListDto.getId());
        riskWhiteListVo.setName(riskWhiteListDto.getName());
        riskWhiteListVo.setExpireTime(riskWhiteListDto.getExpireTime().after(DateUtil.getDateFromLongString("2300-01-01 00:00:00")) ?
                "永久" : DateUtil.formatDateTime(riskWhiteListDto.getExpireTime()));
        riskWhiteListVo.setPhoneNumber(riskWhiteListDto.getPhoneNumber());
        riskWhiteListVo.setIdCard(riskWhiteListDto.getIdCard());
        riskWhiteListVo.setAddReason(riskWhiteListDto.getAddReason());
        return riskWhiteListVo;
    }

    public Response<List<RiskWhiteListLogVo>> getLog(long whiteListId) {
        List<RiskWhiteListLog> riskWhiteListLogs =  riskWhiteListLogBiz.findById(whiteListId);
        return NewResponseUtil.makeSuccess( riskWhiteListLogs.stream().map(RiskWhiteListLogVo::buildVo)
                .sorted(Comparator.comparing(RiskWhiteListLogVo::getOperateTime)).collect(Collectors.toList()));
    }

    public Response<PageResult<RiskWhiteListVo>> getList(WhiteListQuery whiteListQuery) {
        dealParam(whiteListQuery);
        Page<RiskWhiteListDto> riskWhiteListDtoPage = riskWhiteListBiz.getList(whiteListQuery);
        List<RiskWhiteListDto> riskWhiteListDtoList = riskWhiteListDtoPage.getResult();
        riskWhiteListDtoList.forEach(this::dealDecryptParam);
        List<RiskWhiteListVo> result = Lists.newArrayList();
        for(RiskWhiteListDto dto : riskWhiteListDtoList){
            result.add(RiskWhiteListVo.buildVo(dto, maskUtil));
        }
        return  NewResponseUtil.makeSuccess(new PageResult<>(result.stream().sorted(Comparator.comparing(RiskWhiteListVo::getId)
                .reversed())
                .collect(Collectors.toList()),
                riskWhiteListDtoPage.getPageNum(), riskWhiteListDtoPage.getPageSize(), riskWhiteListDtoPage.getTotal()));
    }

    private void dealParam(WhiteListQuery whiteListQuery) {
        if (StringUtils.isNotBlank(whiteListQuery.getIdCard())){
            //处理大小写  统一大写处理
            whiteListQuery.setIdCard(StringUtils.upperCase(whiteListQuery.getIdCard()));
            whiteListQuery.setIdCard(shuidiCipher.encrypt(whiteListQuery.getIdCard()));
        }
        if (StringUtils.isNotBlank(whiteListQuery.getPhoneNumber())){
            whiteListQuery.setPhoneNumber(shuidiCipher.encrypt(whiteListQuery.getPhoneNumber()));
        }
    }

    private void dealDecryptParam(RiskWhiteListDto riskWhiteListDto) {
        if (StringUtils.isNotBlank(riskWhiteListDto.getIdCard())){
            riskWhiteListDto.setIdCard(shuidiCipher.decrypt(riskWhiteListDto.getIdCard()));
        }
        if (StringUtils.isNotBlank(riskWhiteListDto.getPhoneNumber())){
            riskWhiteListDto.setPhoneNumber(shuidiCipher.decrypt(riskWhiteListDto.getPhoneNumber()));
        }
    }

    public List<Long> getWhiteType(long adminUserId, String authSaasAppCode) {
        PermissionParam permissionParam = PermissionParam.builder()
                .userId(adminUserId)
                .appCode(authSaasAppCode)
                .permissions(QualitySpotScenePermissionsEnum.getPermissions())
                .build();
        Set<String> permissions = permissionFeignClient.validUserPermissions(permissionParam).getData();
        log.info("userClassify permissions={}",permissions);
        if (CollectionUtils.isEmpty(permissions)){
            return Lists.newArrayList();
        }
        //过滤存在的权限
        return permissions.stream().map(WhitePermissionsEnum::getFromPermission).map(WhitePermissionsEnum::getType).
                collect(Collectors.toList());
    }
}
