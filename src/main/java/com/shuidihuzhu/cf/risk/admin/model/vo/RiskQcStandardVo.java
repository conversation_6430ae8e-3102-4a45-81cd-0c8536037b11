package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
@Data
public class RiskQcStandardVo {
    private String standardName;
    private int sort;
    private long id;
    private int standardType;
    private int secondStandardType;
    private List<RiskQcStandardDetailVo> riskQcStandardSecondVos = Lists.newArrayList();


    public RiskQcStandardVo(String standardName,long id, List<RiskQcStandardDetailVo> riskQcStandardSecondVos, int sort,
                            int standardType, int secondStandardType) {
        this.standardName = standardName;
        this.riskQcStandardSecondVos = riskQcStandardSecondVos;
        this.sort = sort;
        this.id = id;
        this.secondStandardType = secondStandardType;
        this.standardType = standardType;
    }

    public RiskQcStandardVo() {
    }
}
