package com.shuidihuzhu.cf.risk.admin.model.query;

import com.github.pagehelper.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date
 */
@ApiModel(description = "分页通用实体")
@Data
public class PageResult<T> {

	@ApiModelProperty("记录总数")
	private long count = 0;
	@ApiModelProperty("每页记录数")
	private int pageSize = 10;
	@ApiModelProperty("当前页码")
	private int pageNo = 1;

	@ApiModelProperty("结果揭记录列表")
	private List<T> list;

	public PageResult(List<T> list, int pageNo, int pageSize, long count) {
		this.list = list;
		this.pageNo = pageNo;
		this.pageSize = pageSize;
		this.count = count;
	}

	public PageResult(Page<?> page, List<T> list) {
		this.list = list;
		this.pageNo = page.getPageNum();
		this.pageSize = page.getPageSize();
		this.count = page.getTotal();
	}

	public PageResult(Page<T> page) {
		this.list = page.getResult();
		this.pageNo = page.getPageNum();
		this.pageSize = page.getPageSize();
		this.count = page.getTotal();
	}
}
