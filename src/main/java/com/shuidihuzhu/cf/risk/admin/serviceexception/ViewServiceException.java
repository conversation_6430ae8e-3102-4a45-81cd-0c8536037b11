package com.shuidihuzhu.cf.risk.admin.serviceexception;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * 会返回给前端的服务异常
 */
public class ViewServiceException extends ServiceException{

    @ApiModelProperty("返回给前端视图的错误信息")
    @Setter
    @Getter
    private String viewMsg;

    public ViewServiceException(String message) {
        super(message);
        viewMsg = message;
    }
}
