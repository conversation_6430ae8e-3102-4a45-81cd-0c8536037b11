package com.shuidihuzhu.cf.risk.admin.controller.qc;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.model.param.DeptUpdateParam;
import com.shuidihuzhu.cf.risk.admin.model.qc.subject.QcHospitalDeptDetailVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcFirstStandardVo;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcHospitalDeptService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/cf-risk-admin/hospital-dept")
public class QcHospitalDeptController {

    @Autowired
    private QcHospitalDeptService qcHospitalDeptService;

    @RequiresPermission("qc-dept:query")
    @PostMapping(path = "detail")
    public Response<QcHospitalDeptDetailVO> getInfo(@RequestParam long workOrderId){
        return qcHospitalDeptService.getDetail(workOrderId);
    }

    @RequiresPermission("qc-dept:update")
    @PostMapping(path = "update-building-info")
    @ApiOperation("修改楼宇信息")
    public Response<Boolean> updateBuildingInfo(@RequestParam long workOrderId,
                                                @RequestParam String buildingName,
                                                @RequestParam long userId) {
        return qcHospitalDeptService.updateBuildingInfo(workOrderId, buildingName, userId);
    }

    @RequiresPermission("qc-dept:update")
    @PostMapping(path = "update-department-info")
    @ApiOperation("修改科室信息")
    public Response<Boolean> updateDepartmentInfo(@RequestBody DeptUpdateParam param) {
        final long adminLongUserId = ContextUtil.getAdminLongUserId();
        param.setOperatorId(adminLongUserId);
        return qcHospitalDeptService.updateDepartmentInfo(param);
    }

}
