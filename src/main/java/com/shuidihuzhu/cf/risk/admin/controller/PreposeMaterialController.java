package com.shuidihuzhu.cf.risk.admin.controller;

import com.google.common.collect.ImmutableMap;
import com.shuidihuzhu.cf.client.feign.CfPropertyInsuranceFeignClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.risk.admin.biz.AdminRiskOperatingRecordBiz;
import com.shuidihuzhu.cf.risk.admin.model.AdminRiskOperatingRecord;
import com.shuidihuzhu.cf.risk.admin.model.enums.OperatingRecordBizTypeEnum;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-05-14
 **/
@Slf4j
@RestController
@RequestMapping(value = "/api/cf-risk-admin/prepose-material", method = {RequestMethod.POST}, produces = "application/json;charset=UTF-8")
public class PreposeMaterialController {

    @Autowired
    private CfPropertyInsuranceFeignClient insuranceFeignClient;
    @Autowired
    private AdminRiskOperatingRecordBiz adminRiskOperatingRecordBiz;

    @ApiOperation("增信填写极值判断")
    @RequestMapping(path = "/get-risk-level")
    public Response getRiskLevel(int infoId, long workOrderId) {
        FeignResponse<CfPropertyInsuranceVO> insuranceResponse = insuranceFeignClient.selectInsurance(infoId);
        if (insuranceResponse.notOk() || insuranceResponse.getData() == null) {
            log.info("getByCaseIdByCache rpc insuranceResponse caseId:{}, response:{}", infoId, insuranceResponse);
            return null;
        }
        CfPropertyInsuranceVO insuranceVO = insuranceResponse.getData();
        Integer homeDebt = insuranceVO.getHomeDebt();
        String homeDebtAmount = insuranceVO.getHomeDebtAmount();
        long adminUserId = ContextUtil.getAdminLongUserId();
        AdminRiskOperatingRecord adminRiskOperatingRecord = new AdminRiskOperatingRecord();
        adminRiskOperatingRecord.setAdminUserId(Math.toIntExact(adminUserId));
        adminRiskOperatingRecord.setInfoId(infoId);
        adminRiskOperatingRecord.setWorkOrderId(workOrderId);
        adminRiskOperatingRecord.setBizType(OperatingRecordBizTypeEnum.TYPE_1.getCode());
        int risk = 0;
        String riskStr = "";
        if(homeDebt != null && homeDebt == 1 && StringUtils.isNotBlank(homeDebtAmount) && Integer.parseInt(homeDebtAmount) >= 9999000) {
            adminRiskOperatingRecord.setRisk(1);
            adminRiskOperatingRecord.setRiskLevel("" + PreposeMaterialModel.MaterialRiskLabel.HOME_DEBT_OVERTOP.getCode());
            risk = 1;
            riskStr = "请核实“家庭欠款”是否填写有误；";
        } else {
            adminRiskOperatingRecord.setRisk(0);
            adminRiskOperatingRecord.setRiskLevel("");
        }
        this.adminRiskOperatingRecordBiz.insert(adminRiskOperatingRecord);
        return NewResponseUtil.makeSuccess(ImmutableMap.of("risk", risk, "riskStr", riskStr));
    }
}
