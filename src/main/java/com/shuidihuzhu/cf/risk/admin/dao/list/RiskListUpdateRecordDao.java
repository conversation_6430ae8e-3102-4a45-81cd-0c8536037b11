package com.shuidihuzhu.cf.risk.admin.dao.list;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListUpdateRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskListUpdateRecordDao {
    int insertSelective(RiskListUpdateRecord record);

    RiskListUpdateRecord selectByPrimaryKey(Long id);
}