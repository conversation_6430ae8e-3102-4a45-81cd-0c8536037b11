package com.shuidihuzhu.cf.risk.admin.model.enums.qc1v1;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.Getter;

import java.util.Objects;

//微信1v1服务阶段
@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_search_index", columnName = "service_stage")}
)
public enum TaskPhaseEnum {
    NEW(0,"新增"),
    WECHATNOTPASS(1,"微信未通过"),
    WECHATPASS(2,"微信通过"),
    FIRSTAPRROVE(3,"初审未通过"),
    FUNDRAISING(4,"0<已筹<2000"),
    COMPLETE(5,"已筹>=2000"),
    FUWUCLOSE(6,"服务任务结束"),
    NOTFOLLOW(7,"不再跟进"),
    WAITFUNDRAISE(8,"已筹=0"),
    ;



    public static TaskPhaseEnum fromCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (TaskPhaseEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

    @Getter
    private int code;
    @Getter
    private String desc;

    TaskPhaseEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
