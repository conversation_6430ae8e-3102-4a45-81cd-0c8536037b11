package com.shuidihuzhu.cf.risk.admin.dao.hit;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitOperate;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskStrategyHitOperateDao {
    int insertSelective(RiskStrategyHitOperate record);

    RiskStrategyHitOperate selectByPrimaryKey(Long id);

    List<RiskStrategyHitOperate> listByRecordId(Long recordId);

}