package com.shuidihuzhu.cf.risk.admin.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseKnowledgeBiz;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseKnowledge;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DiseaseKnowledgeListener extends AnalysisEventListener<RiskDiseaseKnowledge> {

    private RiskDiseaseKnowledgeBiz diseaseKnowledgeBiz;

    private List<RiskDiseaseKnowledge> diseaseKnowledgeList = new ArrayList<>();

    public DiseaseKnowledgeListener(RiskDiseaseKnowledgeBiz diseaseKnowledgeBiz) {
        this.diseaseKnowledgeBiz = diseaseKnowledgeBiz;
    }
    @Override
    public void invoke(RiskDiseaseKnowledge diseaseKnowledge, AnalysisContext analysisContext) {
        diseaseKnowledgeList.add(diseaseKnowledge);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        diseaseKnowledgeBiz.handleList(diseaseKnowledgeList);
    }
}
