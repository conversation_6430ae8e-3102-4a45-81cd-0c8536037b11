package com.shuidihuzhu.cf.risk.admin.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: wanghui
 * @create: 2022/4/8 下午10:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class QcCreateWorkOrderModel {
    private int workOrderType;
    private Long workOrderId;
    private Long sourceWorkOrderId;
}
