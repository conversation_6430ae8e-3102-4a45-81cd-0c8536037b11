package com.shuidihuzhu.cf.risk.admin.service.hit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.adminpure.feign.AdminCfInfoFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enhancer.subject.alarm.AlarmHelper;
import com.shuidihuzhu.cf.enums.BooleanEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.configuration.EsConfig;
import com.shuidihuzhu.cf.risk.admin.dao.hit.*;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleActionEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyCallResult;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitLog;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitOperate;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.RiskRecordRecordQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.RiskHitOperateVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.StrategyHitContentVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.StrategyHitLogVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.StrategyHitRecordVo;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.mq.BlacklistHitConsumer;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTopicCons;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyHitDto;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyRoleEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.IdentifySpotInitiatorHitEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistLiftingDto;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.cf.risk.model.risk.whiteList.SpotDetectionCheckDto;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.esdk.EsClient;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/21 16:56
 */
@Validated
@Slf4j
@Service
public class RiskHitService {

    private static final String RISK_STRATEGY_HIT_RECORD_ES_INDEX = "shuidi_cf_risk_risk_strategy_hit_record_index_alias";

    private static final String UPDATE_LEVEL_CONF_KEY = "cf_risk_admin_risk_hit_handle_";
    private static final String UPDATE_HANDLE_LEVEL_CONF_KEY = "cf_risk_admin_risk_hit_handle_update_";
    private static final long UPDATE_LEVEL_CONF_KEY_LEAVE_TIME = 10 * 1000;
    private static final long LEAVE_WAIT_MILLISECOND = 1500;
    private static final int MAX_RESULT_UPDATE_COUNT = 2;

    @Resource
    private RiskStrategyHitRecordDao strategyHitRecordDao;
    @Resource
    private RiskStrategyHitLogDao strategyHitLogDao;
    @Resource
    private RiskStrategyHitOperateDao hitOperateDao;
    @Resource
    private SeaAccountService seaAccountService;
    @Resource
    private Producer producer;
    @Resource(name = "es-es-cf-risk-admin")
    private EsClient esClient;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private ShuidiCipher shuidiCipher;

    @Autowired
    private RiskStrategyCallResultDao riskStrategyCallResultDao;

    @Autowired
    private CfChannelFeignClient channelFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Resource
    private CfRiskRepeatPayOrderCaseRecordDao cfRiskRepeatPayOrderCaseRecordDao;

    @Resource
    private AdminCfInfoFeignClient adminCfInfoFeignClient;

    public String assembleHitInfo(List<BlacklistVerifyDto> verifyDtos, boolean needDecrypt){
        StringBuilder sb = new StringBuilder();

        Map<Integer, Map<Integer, BlacklistVerifyDto>> roleTypeMap = verifyDtos.stream()
                .sorted((o1, o2) -> o1.getVerifyRole() == o2.getVerifyRole()
                        ? o1.getVerifyType()-o2.getVerifyType()
                        : o1.getVerifyRole()-o2.getVerifyRole())
                .collect(Collectors.groupingBy(BlacklistVerifyDto::getVerifyRole, LinkedHashMap::new,
                        Collectors.toMap(BlacklistVerifyDto::getVerifyType, Function.identity(),
                                (b1, b2) -> b2, LinkedHashMap::new)));

        for (Map.Entry<Integer, Map<Integer, BlacklistVerifyDto>> roleTypeEntry : roleTypeMap.entrySet()) {
            for (Map.Entry<Integer, BlacklistVerifyDto> typeEntry : roleTypeEntry.getValue().entrySet()) {
                if (roleTypeEntry.getKey() == BlacklistVerifyRoleEnum.PATIENT.getCode() &&
                        (typeEntry.getKey() == BlacklistVerifyTypeEnum.ID_CARD.getCode() ||
                                typeEntry.getKey() == BlacklistVerifyTypeEnum.BORN_CARD.getCode())) {
                    //补一条证件类型
                    sb.append("患者证件类型：")
                            .append(typeEntry.getKey() == BlacklistVerifyTypeEnum.ID_CARD.getCode() ? "身份证" : "出生证")
                            .append("\n");
                }
                BlacklistVerifyRoleEnum roleEnum = BlacklistVerifyRoleEnum.fromCode(roleTypeEntry.getKey());
                if (roleEnum == BlacklistVerifyRoleEnum.DEFAULT) {
                    sb.append(typeEntry.getValue().getVerifyData())
                            .append("\n");
                } else if(roleEnum == BlacklistVerifyRoleEnum.CASE_RAISE_CHANNEL) {
                    String verifyData = typeEntry.getValue().getVerifyData();
                    String channel = getChannelInfo(verifyData);
                    sb.append("引导用户发起渠道：")
                            .append(channel)
                            .append("\n");
                } else {
                    sb.append(roleEnum.getDesc())
                            .append(BlacklistVerifyTypeEnum.fromCode(typeEntry.getKey()).getShowName())
                            .append("：")
                            .append(BlacklistHitConsumer.decideEncrypt(typeEntry.getValue(), needDecrypt))
                            .append("\n");
                }
            }
        }

        return sb.toString();
    }

    private String getChannelInfo(String caseIdStr) {
        String defaultMsg = "其他";
        if (StringUtils.isBlank(caseIdStr)) {
            return defaultMsg;
        }
        int caseId = Integer.parseInt(caseIdStr);
        if (caseId <= 0) {
            return defaultMsg;
        }
        FeignResponse<CrowdfundingInfo> caseInfoResp = crowdfundingFeignClient.getCaseInfoById(caseId);
        if (caseInfoResp == null || caseInfoResp.notOk()) {
            return defaultMsg;
        }
        CrowdfundingInfo caseInfo = caseInfoResp.getData();
        ChannelRefineDTO refineDTO = new ChannelRefineDTO();
        refineDTO.setInfoId((long) caseInfo.getId());
        refineDTO.setChannel(caseInfo.getChannel());
        refineDTO.setUserId(caseInfo.getUserId());

        Response<List<CfUserInvitedLaunchCaseRecordModel>> caseRecordResult =
                channelFeignClient.getCfUserInvitedLaunchCaseRecordByInfoIds(Lists.newArrayList(refineDTO));
        if (NewResponseUtil.isNotOk(caseRecordResult)) {
            return defaultMsg;
        }
        List<CfUserInvitedLaunchCaseRecordModel> data = caseRecordResult.getData();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(data)) {
            return defaultMsg;
        }
        CfUserInvitedLaunchCaseRecordModel cfUserInvitedLaunchCaseRecordModel = data.get(org.apache.commons.collections.CollectionUtils.size(data) - 1);
        String serviceUserInfo = cfUserInvitedLaunchCaseRecordModel.getServiceUserInfo(shuidiCipher);
        if (StringUtils.isBlank(serviceUserInfo)) {
            return defaultMsg;
        }
        return serviceUserInfo;
    }

    public StrategyHitContentVo queryRiskHitRecord(RiskRecordRecordQuery recordQuery) throws Exception {
        log.info("queryRiskHitRecord query {}", recordQuery);
        List<Long> ids = Lists.newArrayListWithCapacity(recordQuery.getPageSize());
        long count = fillIdsAndReturnCountByEs(recordQuery, ids);
        if (CollectionUtils.isEmpty(ids)) {
            log.info("queryRiskHitRecord empty ids ");
            return new StrategyHitContentVo(new PageResult<>(Collections.emptyList(), recordQuery.getPageNo(), recordQuery.getPageSize(), count),
                    countByStatus(RiskHandleStatusEnum.PENDING), countByStatus(RiskHandleStatusEnum.PROCESSING));
        }
        log.info("queryRiskHitRecord ids {}", ids);
        List<StrategyHitRecordVo> collect = strategyHitRecordDao.listByIds(ids).stream().map(rr -> {
            List<BlacklistVerifyDto> verifyDtos = JSON.parseArray(rr.getHitInfo(), BlacklistVerifyDto.class);
            String hitInfo = rr.getHitPhase() == BlacklistCallPhaseEnum.REPEAT_PAY_ORDER.getCode() ? verifyDtos.get(0).getBlacknessReason() :
                    assembleHitInfo(verifyDtos, true) +
                            (rr.getRiskType() > 0 ?
                                    "风险类型：" + IdentifySpotInitiatorHitEnum.fromCode(rr.getRiskType()).getDesc()
                                    : "所属黑名单类型：" + BlacklistHitConsumer.assembleHitBlacklistType(verifyDtos));
            return new StrategyHitRecordVo(rr, verifyDtos, hitInfo);
        })
                .collect(Collectors.toList());
        return new StrategyHitContentVo(new PageResult<>(collect, recordQuery.getPageNo(), recordQuery.getPageSize(), count),
                countByStatus(RiskHandleStatusEnum.PENDING), countByStatus(RiskHandleStatusEnum.PROCESSING));
    }

    private long countByStatus(RiskHandleStatusEnum statusEnum) throws Exception {
        SearchSourceBuilder searchBuilder = SearchSourceBuilder.searchSource();
        searchBuilder.size(0);
        searchBuilder.fetchSource(false);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("status", statusEnum.getCode()));
        searchBuilder.query(boolQueryBuilder);
        SearchResponse search = esClient.search(EsConfig.CLUSTER_NAME, new SearchRequest(new String[]{RISK_STRATEGY_HIT_RECORD_ES_INDEX}, searchBuilder));
        return search.getHits().getTotalHits().value;
    }

    private long fillIdsAndReturnCountByEs(RiskRecordRecordQuery recordQuery, List<Long> ids) throws Exception {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (recordQuery.getHitPhase() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("hit_phase", recordQuery.getHitPhase()));
        }
        if (recordQuery.getRiskStrategy() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("risk_strategy", recordQuery.getRiskStrategy()));
        }
        if (recordQuery.getSecondStrategy() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("second_strategy", recordQuery.getSecondStrategy()));
        }
        if (recordQuery.getStatus() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("status", recordQuery.getStatus()));
        }
        if (recordQuery.getResult() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("result", recordQuery.getResult()));
        }
        if (recordQuery.getCaseId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("case_id", recordQuery.getCaseId()));
        }
        if (StringUtils.isNotBlank(recordQuery.getOperateName())) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("operate_name", recordQuery.getOperateName()));
        }
        if (StringUtils.isNotBlank(recordQuery.getHitStartTime()) || StringUtils.isNotBlank(recordQuery.getHitEndTime())) {
            RangeQueryBuilder createTime = QueryBuilders.rangeQuery("create_time").format("yyyy-MM-dd HH:mm:ss").timeZone("+08:00");
            if (StringUtils.isNotBlank(recordQuery.getHitStartTime())) {
                createTime.gte(recordQuery.getHitStartTime());
            }
            if (StringUtils.isNotBlank(recordQuery.getHitEndTime())) {
                createTime.lte(recordQuery.getHitEndTime());
            }
            boolQueryBuilder.filter(createTime);
        }
        if (StringUtils.isNotBlank(recordQuery.getHandleStartTime()) || StringUtils.isNotBlank(recordQuery.getHandlerEndTime())) {
            RangeQueryBuilder createTime = QueryBuilders.rangeQuery("update_time").format("yyyy-MM-dd HH:mm:ss").timeZone("+08:00");
            if (StringUtils.isNotBlank(recordQuery.getHandleStartTime())) {
                createTime.gte(recordQuery.getHandleStartTime());
            }
            if (StringUtils.isNotBlank(recordQuery.getHandlerEndTime())) {
                createTime.lte(recordQuery.getHandlerEndTime());
            }
            boolQueryBuilder.filter(createTime);
        }
        if (recordQuery.getBlacklistTypeId() != null) {
            boolQueryBuilder.filter(
                    QueryBuilders.nestedQuery("hit_info",
                            QueryBuilders.boolQuery().filter(
                                    QueryBuilders.termQuery("hit_info" + ".typeIds", recordQuery.getBlacklistTypeId())),
                            ScoreMode.None)
            );
        }
        if (recordQuery.getLimitAction() != null) {
            boolQueryBuilder.filter(
                    QueryBuilders.nestedQuery("hit_info",
                            QueryBuilders.boolQuery().filter(
                                    QueryBuilders.termQuery("hit_info" + ".limitActionIds", recordQuery.getLimitAction())),
                            ScoreMode.None)
            );
        }
        if (recordQuery.getUid() != null) {
            boolQueryBuilder.filter(
                    QueryBuilders.nestedQuery("hit_info",
                            QueryBuilders.boolQuery()
                                    .filter(QueryBuilders.termQuery("hit_info" + ".verifyType", BlacklistVerifyTypeEnum.USER_ID.getCode()))
                                    .filter(QueryBuilders.termQuery("hit_info" + ".verifyData", recordQuery.getUid())),
                            ScoreMode.None)
            );
        }
        if (StringUtils.isNotBlank(recordQuery.getMobile())) {
            boolQueryBuilder.filter(
                    QueryBuilders.nestedQuery("hit_info",
                            QueryBuilders.boolQuery()
                                    .filter(QueryBuilders.termQuery("hit_info" + ".verifyType", BlacklistVerifyTypeEnum.MOBILE.getCode()))
                                    .filter(QueryBuilders.termQuery("hit_info" + ".verifyData", oldShuidiCipher.aesEncrypt(recordQuery.getMobile()))),
                            ScoreMode.None)
            );
        }
        if (StringUtils.isNotBlank(recordQuery.getIdCard())) {
            boolQueryBuilder.filter(
                    QueryBuilders.nestedQuery("hit_info",
                            QueryBuilders.boolQuery()
                                    .filter(QueryBuilders.termQuery("hit_info" + ".verifyType", BlacklistVerifyTypeEnum.ID_CARD.getCode()))
                                    .filter(QueryBuilders.termQuery("hit_info" + ".verifyData", oldShuidiCipher.aesEncrypt(recordQuery.getIdCard()))),
                            ScoreMode.None)
            );
        }
        if (StringUtils.isNotBlank(recordQuery.getBornCard())) {
            boolQueryBuilder.filter(
                    QueryBuilders.nestedQuery("hit_info",
                            QueryBuilders.boolQuery()
                                    .filter(QueryBuilders.termQuery("hit_info" + ".verifyType", BlacklistVerifyTypeEnum.BORN_CARD.getCode()))
                                    .filter(QueryBuilders.termQuery("hit_info" + ".verifyData", oldShuidiCipher.aesEncrypt(recordQuery.getBornCard()))),
                            ScoreMode.None)
            );
        }

        SearchSourceBuilder searchBuilder = SearchSourceBuilder.searchSource();
        searchBuilder.from((recordQuery.getPageNo()-1) * recordQuery.getPageSize());
        searchBuilder.size(recordQuery.getPageSize());
        searchBuilder.sort("id", SortOrder.DESC);
        searchBuilder.fetchSource(new String[]{"id"}, null);
        searchBuilder.query(boolQueryBuilder);
        SearchResponse search = esClient.search(EsConfig.CLUSTER_NAME, new SearchRequest(new String[]{RISK_STRATEGY_HIT_RECORD_ES_INDEX}, searchBuilder));
        log.info("风控命中记录ES query:{} response:{}", searchBuilder, search);
        for (SearchHit hit : search.getHits().getHits()) {
            ids.add(Long.valueOf(hit.getId()));
        }

        return search.getHits().getTotalHits().value;
    }

    public void uniteSaveHitRecord(RiskStrategyHitRecord hitRecord){
        String key = "risk_hit_unite_" + hitRecord.getRiskStrategy() + "_" + hitRecord.getSecondStrategy() +
                "_" + hitRecord.getCaseId() + "_" + hitRecord.getHitPhase();
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, LEAVE_WAIT_MILLISECOND, UPDATE_LEVEL_CONF_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                log.warn("保存命中记录获取锁失败");
                throw new IllegalStateException();
            }
            if (hadHit(RiskStrategyEnum.fromCode(hitRecord.getRiskStrategy()),
                    RiskStrategySecondEnum.fromCode(hitRecord.getSecondStrategy()), false, hitRecord.getCaseId(),
                    hitRecord.getHitPhase(), JSON.parseArray(hitRecord.getHitInfo(), BlacklistVerifyDto.class))) {
                log.info("风险命中记录已存在，跳过保存操作，caseId:{}, callPhase:{}, oneStrategey:{}, twoStrategy:{}",
                        hitRecord.getCaseId(), hitRecord.getHitPhase(), hitRecord.getRiskStrategy(), hitRecord.getSecondStrategy());
                return;
            }
            saveHitRecord(hitRecord);
            repeatOrderSendMsg(hitRecord);
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
    }

    private void repeatOrderSendMsg(RiskStrategyHitRecord hitRecord) {
        if (RiskStrategyEnum.REPEAT_PAY_ORDER_RULE.getCode() != hitRecord.getRiskStrategy()) {
            return;
        }
        FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = crowdfundingFeignClient.getCaseInfoById(hitRecord.getCaseId());
        CrowdfundingInfo crowdfundingInfo = Optional.ofNullable(crowdfundingInfoFeignResponse)
                .map(FeignResponse::getData)
                .orElse(null);
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }
        List<BlacklistVerifyDto> blacklistVerifyDtoList = JSONObject.parseArray(hitRecord.getHitInfo(), BlacklistVerifyDto.class);
        String hitInfo = CollectionUtils.isNotEmpty(blacklistVerifyDtoList) ? blacklistVerifyDtoList.get(0).getBlacknessReason() : "";
        ChannelRefineDTO refineDTO = new ChannelRefineDTO();
        refineDTO.setInfoId((long) crowdfundingInfo.getId());
        refineDTO.setChannel(crowdfundingInfo.getChannel());
        refineDTO.setUserId(crowdfundingInfo.getUserId());
        Response<String> response = channelFeignClient.getChannelByInfoIdWithUserIdAndOldChannel(refineDTO);
        String channel = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .map(ChannelRefine.ChannelRefineResuleEnum::parse)
                .map(ChannelRefine.ChannelRefineResuleEnum::getChannelDesc)
                .orElse("");
        OperationResult<String> currentLifeCircle = adminCfInfoFeignClient.getCurrentLifeCircle(crowdfundingInfo.getId());
        JSONObject jsonObject = Optional.ofNullable(currentLifeCircle)
                .map(OperationResult::getData)
                .map(JSONObject::parseObject)
                .orElse(null);

        StringBuilder feiShuStb = new StringBuilder();
        feiShuStb.append("命中的策略名称：【重复捐单】\n");
        feiShuStb.append("触发时机：重复捐单\n");
        feiShuStb.append("触发时间：");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        feiShuStb.append(format.format(new Date()));
        feiShuStb.append("\n");
        feiShuStb.append("案例id：");
        feiShuStb.append(hitRecord.getCaseId());
        feiShuStb.append("\n");
        feiShuStb.append("案例当前所处环节：");
        if (Objects.nonNull(jsonObject)) {
            feiShuStb.append(jsonObject.get("name"));
            feiShuStb.append(jsonObject.get("status"));
        }
        feiShuStb.append("\n");
        feiShuStb.append("引导用户发起渠道：");
        feiShuStb.append(channel);
        feiShuStb.append("\n");
        feiShuStb.append(hitInfo);
        String msg = feiShuStb.toString();
        AlarmHelper.facade().sentText("9a95348c-e521-4888-9fd7-c50b66138a03", msg, "", null);

    }

    /**
     * 判断同一案例，同一一二级策略，在同一触发时机下是否重复记录命中信息
     * @param riskStrategyEnum 一级策略
     * @param strategySecondEnum 二级策略
     * @param hitVerifyDtos 待记录命中信息
     * @param callPhase 调用时机
     * @param caseId caseId
     * @return boolean
     */
    public boolean hadHit(RiskStrategyEnum riskStrategyEnum, RiskStrategySecondEnum strategySecondEnum, boolean needEncrypt,
                          Integer caseId, Integer callPhase, List<BlacklistVerifyDto> hitVerifyDtos){
        List<RiskStrategyHitRecord> hitRecords = strategyHitRecordDao.listByCaseIdPhase(caseId, callPhase);

        return doHitCheck(riskStrategyEnum, strategySecondEnum, needEncrypt, hitRecords, hitVerifyDtos, false);
    }

    /**
     * 判断同一案例，同一一二级策略，在同一触发时机下是否已经解锁过
     * @param riskStrategyEnum 一级策略
     * @param strategySecondEnum 二级策略
     * @param hitVerifyDtos 待记录命中信息
     * @param callPhase 调用时机
     * @param caseId caseId
     * @return boolean
     */
    public SpotDetectionCheckDto spotDetection(RiskStrategyEnum riskStrategyEnum, RiskStrategySecondEnum strategySecondEnum, boolean needEncrypt,
                                               Integer caseId, Integer callPhase, List<BlacklistVerifyDto> hitVerifyDtos){
        List<RiskStrategyHitRecord> hitRecords = strategyHitRecordDao.listByCaseIdPhase(caseId, callPhase);
        return new SpotDetectionCheckDto(doHitCheck(riskStrategyEnum, strategySecondEnum, needEncrypt, hitRecords, hitVerifyDtos, false),
                doHitCheck(riskStrategyEnum, strategySecondEnum, needEncrypt, hitRecords, hitVerifyDtos, true));
    }

    private boolean doHitCheck(RiskStrategyEnum riskStrategyEnum, RiskStrategySecondEnum strategySecondEnum, boolean needEncrypt,
                               List<RiskStrategyHitRecord> hitRecords, List<BlacklistVerifyDto> hitVerifyDtos, boolean needLifting){
        Set<String> waitVerifySet = hitVerifyDtos.stream()
                .filter(BlacklistVerifyDto::isHit)
                .map(blacklistVerifyDto -> riskStrategyEnum.getCode() + "_" + strategySecondEnum.getCode() +
                        "_" + blacklistVerifyDto.getVerifyRole() +
                        "_" + blacklistVerifyDto.getVerifyType() +
                        "_" + (needEncrypt && BlacklistVerifyTypeEnum.needEncrypt(blacklistVerifyDto.getVerifyType())
                        ? oldShuidiCipher.aesEncrypt(blacklistVerifyDto.getVerifyData())
                        : blacklistVerifyDto.getVerifyData()))
                .collect(Collectors.toSet());
        Set<String> dbHitSet = hitRecords.stream()
                .filter(riskStrategyHitRecord -> !needLifting || riskStrategyHitRecord.getLifting() == BooleanEnum.TRUE_FLAG.getValue())
                .flatMap(hitRecord -> JSON.parseArray(hitRecord.getHitInfo(), BlacklistVerifyDto.class).stream()
                        .filter(BlacklistVerifyDto::isHit)
                        .map(blacklistVerifyDto -> hitRecord.getRiskStrategy() + "_" + hitRecord.getSecondStrategy() +
                                "_" + blacklistVerifyDto.getVerifyRole() +
                                "_" + blacklistVerifyDto.getVerifyType() +
                                "_" + blacklistVerifyDto.getVerifyData()))
                .collect(Collectors.toSet());

        boolean hadHit = CollectionUtils.subtract(waitVerifySet, dbHitSet).size() == 0;

        if (log.isDebugEnabled()) {
            log.debug("风控命中时，保存信息验证，waitVerifySet:{}, dbHitSet:{}, hadHit:{}", waitVerifySet, dbHitSet, hadHit);
        }

        return hadHit;
    }

    /**
     * 保存命中记录
     * @param strategyHitRecord
     * @return
     */
    public int saveHitRecord(RiskStrategyHitRecord strategyHitRecord) {
        strategyHitRecordDao.insertSelective(strategyHitRecord);

        //保存日志
        saveLog(strategyHitRecord.getId(), "被风控策略命中", 0L, "系统生成");

        return 1;
    }

    public List<StrategyHitLogVo> queryRecordLogs(Long hitRecordId){
        return strategyHitLogDao.listByRecordId(hitRecordId).stream().map(StrategyHitLogVo::new).collect(Collectors.toList());
    }

    public List<Long> handleHitInfo(RiskHitOperateVo riskHitOperateVo, long adminUserId){
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        long operateId = userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId();
        String operateName = userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg();

        String key = UPDATE_LEVEL_CONF_KEY + riskHitOperateVo.getId();
        String identify = null;
        List<RiskStrategyHitRecord> hitRecords = Collections.emptyList();
        try {
            identify = redissonHandler.tryLock(key, UPDATE_LEVEL_CONF_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请稍后重试");
            }
            hitRecords = checkAndGetHandleAllow(riskHitOperateVo);
            String action = Joiner.on(",").join(riskHitOperateVo.getHandleActions());
            boolean unlock = riskHitOperateVo.getHandleActions().contains(RiskHandleActionEnum.OPERATION_UNLOCK.getCode());
            strategyHitRecordDao.updateHandleInfo(riskHitOperateVo.getId(),
                    RiskHandleActionEnum.isInHand(riskHitOperateVo.getHandleActions())
                            ? RiskHandleStatusEnum.PROCESSING.getCode()
                            : RiskHandleStatusEnum.COMPLETE.getCode(),
                    riskHitOperateVo.getHandleResult(),
                    action,
                    (byte)(unlock ? 1 : 0),
                    operateId, operateName, false);

            hitRecords = strategyHitRecordDao.listByCaseIdPhase(
                    riskHitOperateVo.getCaseId(), riskHitOperateVo.getHitPhaseCode());
            saveFollowUpRecord(riskHitOperateVo, action, operateId, operateName);
            saveLog(riskHitOperateVo.getId(), "提交处理结果", operateId, operateName);

            //通知业务方，风控已解锁
            noticeLifting2Biz(riskHitOperateVo, hitRecords, unlock);
            // 重复捐单策略将用户加入黑名单
            addBlackList(riskHitOperateVo);
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }

        return hitRecords.stream().filter(hitRecord->!Objects.equals(hitRecord.getId(), riskHitOperateVo.getId()) &&
                hitRecord.getLifting() == BooleanEnum.DEFAULT.getValue() && (hitRecord.getStatus() == RiskHandleStatusEnum.PENDING.getCode() ||
                hitRecord.getStatus() == RiskHandleStatusEnum.PROCESSING.getCode()))
                .map(RiskStrategyHitRecord::getId).collect(Collectors.toList());
    }

    public void addBlackList(RiskHitOperateVo riskHitOperateVo) {
        if (BlacklistCallPhaseEnum.REPEAT_PAY_ORDER.getCode() != riskHitOperateVo.getHitPhaseCode()) {
            return;
        }
        boolean addBlack = riskHitOperateVo.getHandleActions().contains(RiskHandleActionEnum.LIMIT_RESOURCE.getCode()) || riskHitOperateVo.getHandleActions().contains(RiskHandleActionEnum.LIMIT_CROWDFUNDING.getCode());
        if (!addBlack) {
            return;
        }

        producer.send(Message.of(CfRiskMQTopicCons.CF_RISK_TOPIC, CfRiskMQTagCons.AUTO_ADD_BLACKLIST, UUID.randomUUID().toString(), riskHitOperateVo));
    }

    public List<Long> updateHandleHitInfo(RiskHitOperateVo riskHitOperateVo, long adminUserId){
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        String key = UPDATE_HANDLE_LEVEL_CONF_KEY + riskHitOperateVo.getId();
        String identify = null;
        List<RiskStrategyHitRecord> hitRecords = Collections.emptyList();
        try {
            identify = redissonHandler.tryLock(key, UPDATE_LEVEL_CONF_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请稍后重试");
            }
            hitRecords = checkAndGetUpdateAllow(riskHitOperateVo);
            String action = Joiner.on(",").join(riskHitOperateVo.getHandleActions());
            boolean unlock = riskHitOperateVo.getHandleActions().contains(RiskHandleActionEnum.OPERATION_UNLOCK.getCode());
            strategyHitRecordDao.updateHandleInfo(riskHitOperateVo.getId(),
                    RiskHandleActionEnum.isInHand(riskHitOperateVo.getHandleActions())
                            ? RiskHandleStatusEnum.PROCESSING.getCode()
                            : RiskHandleStatusEnum.COMPLETE.getCode(),
                    riskHitOperateVo.getHandleResult(),
                    action,
                    (byte)(unlock ? 1 : 0),
                    (long) userNameWithOrg.getAdminUserId(), userNameWithOrg.getUserNameWithOrg(), true);
            hitRecords = strategyHitRecordDao.listByCaseIdPhase(
                    riskHitOperateVo.getCaseId(), riskHitOperateVo.getHitPhaseCode());
            saveFollowUpRecord(riskHitOperateVo, action, (long) userNameWithOrg.getAdminUserId(), userNameWithOrg.getUserNameWithOrg());
            saveLog(riskHitOperateVo.getId(), "修改处理结果", (long) userNameWithOrg.getAdminUserId(), userNameWithOrg.getUserNameWithOrg());

            //通知业务方，风控已解锁
            noticeLifting2Biz(riskHitOperateVo, hitRecords, unlock);
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }

        return hitRecords.stream().filter(hitRecord->!Objects.equals(hitRecord.getId(), riskHitOperateVo.getId()) &&
                hitRecord.getLifting() == BooleanEnum.DEFAULT.getValue() && (hitRecord.getStatus() == RiskHandleStatusEnum.PENDING.getCode() ||
                hitRecord.getStatus() == RiskHandleStatusEnum.PROCESSING.getCode()))
                .map(RiskStrategyHitRecord::getId).collect(Collectors.toList());
    }

    private BlacklistLiftingDto assembleLiftingDto(RiskStrategyHitRecord hitRecord){
        List<BlacklistVerifyDto> verifyDtos = JSON.parseArray(hitRecord.getHitInfo(), BlacklistVerifyDto.class);

        String payeeIdCard = verifyDtos.stream()
                .filter(blacklistVerifyDto ->
                        Objects.equals(blacklistVerifyDto.getVerifyRole(), BlacklistVerifyRoleEnum.PAYEE.getCode()) &&
                                Objects.equals(blacklistVerifyDto.getVerifyType(), BlacklistVerifyTypeEnum.ID_CARD.getCode()))
                .findAny()
                .map(blacklistVerifyDto -> shuidiCipher.decrypt(blacklistVerifyDto.getVerifyData()))
                .orElse(null);
        String payeeMobile = verifyDtos.stream()
                .filter(blacklistVerifyDto ->
                        Objects.equals(blacklistVerifyDto.getVerifyRole(), BlacklistVerifyRoleEnum.PAYEE.getCode()) &&
                                Objects.equals(blacklistVerifyDto.getVerifyType(), BlacklistVerifyTypeEnum.MOBILE.getCode()))
                .findAny()
                .map(blacklistVerifyDto -> shuidiCipher.decrypt(blacklistVerifyDto.getVerifyData()))
                .orElse(null);

        return new BlacklistLiftingDto(hitRecord.getCaseId(), hitRecord.getHitPhase(), payeeIdCard, payeeMobile);
    }

    private List<RiskStrategyHitRecord> checkAndGetHandleAllow(RiskHitOperateVo riskHitOperateVo){
        List<RiskStrategyHitRecord> riskStrategyHitRecords = strategyHitRecordDao.listByCaseIdPhase(
                riskHitOperateVo.getCaseId(), riskHitOperateVo.getHitPhaseCode());
        if (riskStrategyHitRecords.stream().anyMatch(hitRecord->
                Objects.equals(hitRecord.getId(), riskHitOperateVo.getId()) &&
                        hitRecord.getStatus() == RiskHandleStatusEnum.COMPLETE.getCode())) {
            throw new IllegalArgumentException("风险命中记录已处理，请刷新后查看");
        }

        return riskStrategyHitRecords;
    }

    private List<RiskStrategyHitRecord> checkAndGetUpdateAllow(RiskHitOperateVo riskHitOperateVo){
        List<RiskStrategyHitRecord> riskStrategyHitRecords = strategyHitRecordDao.listByCaseIdPhase(
                riskHitOperateVo.getCaseId(), riskHitOperateVo.getHitPhaseCode());
        RiskStrategyHitRecord currStrategyHitRecord = riskStrategyHitRecords.stream()
                .filter(hitRecord -> Objects.equals(hitRecord.getId(), riskHitOperateVo.getId()))
                .collect(Collectors.toList()).get(0);
        if (currStrategyHitRecord.getStatus() == RiskHandleStatusEnum.COMPLETE.getCode() &&
                currStrategyHitRecord.getUpdateCount() < MAX_RESULT_UPDATE_COUNT) {
            return riskStrategyHitRecords;
        }

        throw new IllegalArgumentException("不满足处理结果修改条件");
    }

    /**
     * 通知业务方，风控已解锁
     * @param riskHitOperateVo
     * @param hitRecords
     * @param unlock
     */
    private void noticeLifting2Biz(RiskHitOperateVo riskHitOperateVo, List<RiskStrategyHitRecord> hitRecords, boolean unlock){
        if (unlock && hitRecords.stream().filter(hitRecord->!Objects.equals(hitRecord.getId(), riskHitOperateVo.getId()))
                .noneMatch(hitRecord -> hitRecord.getLifting() == BooleanEnum.DEFAULT.getValue())) {
            BlacklistLiftingDto payload = assembleLiftingDto(hitRecords.get(0));
            log.info("风控已被解锁，通知业务方继续流转, payload:{}", payload);
            producer.send(Message.of(CfRiskMQTopicCons.CF_RISK_TOPIC, CfRiskMQTagCons.BLACKLIST_LIFTING,
                    UUID.randomUUID().toString(), payload));
        }
    }

    public List<RiskHitOperateVo> queryOperateHistory(Long hitRecordId){
        return hitOperateDao.listByRecordId(hitRecordId).stream().map(RiskHitOperateVo::new).collect(Collectors.toList());
    }

    public RiskStrategyHitRecord queryOneByCaseStrategy(@NotNull Integer caseId, @NotNull List<Integer> hitPhases) {
        return strategyHitRecordDao.oneByCaseStrategy(caseId, hitPhases);
    }

    public List<RiskStrategyHitRecord> listByCaseIdPhase(@NotNull Integer caseId, @NotNull Integer hitPhase) {
        return strategyHitRecordDao.listByCaseIdPhase(caseId, hitPhase);
    }

    private void saveFollowUpRecord(RiskHitOperateVo riskHitOperateVo, String action, Long adminUserId, String adminUserNameWithOrg){
        RiskStrategyHitOperate strategyHitOperate = new RiskStrategyHitOperate();
        strategyHitOperate.setHitRecordId(riskHitOperateVo.getId());
        strategyHitOperate.setResult(riskHitOperateVo.getHandleResult());
        strategyHitOperate.setAction(action);
        strategyHitOperate.setOperateId(adminUserId);
        strategyHitOperate.setOperateName(adminUserNameWithOrg);
        strategyHitOperate.setRemark(riskHitOperateVo.getRemark());
        strategyHitOperate.setRadio(riskHitOperateVo.getRadio());
        strategyHitOperate.setRepeatCaseId(riskHitOperateVo.getRepeatCaseId());
        strategyHitOperate.setActiveStop(riskHitOperateVo.getActiveStop());
        strategyHitOperate.setOtherReason(riskHitOperateVo.getOtherReason());
        hitOperateDao.insertSelective(strategyHitOperate);
    }

    public void saveLog(Long dataId, String modifyContent, Long operateId, String operateName) {
        RiskStrategyHitLog dataLog = new RiskStrategyHitLog();
        dataLog.setOperateId(operateId);
        dataLog.setOperateName(operateName);
        dataLog.setHitRecordId(dataId);
        dataLog.setOperateContent(modifyContent);
        strategyHitLogDao.insertSelective(dataLog);
    }

    public int saveCallResult(RiskStrategyCallResult riskStrategyCallResult){
        return riskStrategyCallResultDao.insertCallResult(riskStrategyCallResult);
    }

    public void insertRepeatPayOrder(StrategyHitDto strategyHitDto) {
        cfRiskRepeatPayOrderCaseRecordDao.insertRepeatPayOrder(strategyHitDto);
    }

    public List<StrategyHitDto> listRepeatPayOrderCaseByCaseId(int caseId) {
        return cfRiskRepeatPayOrderCaseRecordDao.listByCaseId(caseId);
    }
}
