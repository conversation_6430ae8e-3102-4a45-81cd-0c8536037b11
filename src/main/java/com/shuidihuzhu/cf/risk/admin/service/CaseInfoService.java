package com.shuidihuzhu.cf.risk.admin.service;

import com.shuidihuzhu.cf.client.subject.caseend.CaseEndFeignClient;
import com.shuidihuzhu.cf.domain.caseinfo.CaseEndRecordDO;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFundStateFeignClient;
import com.shuidihuzhu.cf.finance.model.financestate.FinanceState;
import com.shuidihuzhu.cf.risk.admin.model.vo.PsCaseInfoVO;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPublicSentimentInfoBiz;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Service
public class CaseInfoService {
    @Autowired
    private CfFinanceFundStateFeignClient financeFundStateFeignClient;
    @Autowired
    private RiskPublicSentimentInfoBiz riskPublicSentimentInfoBiz;
    @Autowired
    private CaseEndFeignClient caseEndClient;

    public PsCaseInfoVO getCaseInfoVO(int isNewPage, int caseId) {
        if (caseId < 1) {
            return null;
        }
        Response<CaseEndRecordDO> recordDOResponse = caseEndClient.getLastRecordByCaseId(caseId);
        if (recordDOResponse.notOk() || recordDOResponse.getData() == null){
            log.info("recordDOResponse is not ok :{}", recordDOResponse);
        }
        com.shuidihuzhu.cf.finance.client.response.FeignResponse<FinanceState> financeStateFeignResponse =
                financeFundStateFeignClient.financeState(caseId);
        if (financeStateFeignResponse.notOk() || financeStateFeignResponse.getData() == null) {
            log.info("financeStateFeignResponse is not ok :{}", financeStateFeignResponse);
            return null;
        }
        FinanceState financeState = financeStateFeignResponse.getData();
        PsCaseInfoVO psCaseInfoVO = new PsCaseInfoVO();
        psCaseInfoVO.setAmount(new BigDecimal(financeState.getDonationAmountInFen()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).doubleValue());
        if (recordDOResponse.getData() != null) {
            psCaseInfoVO.setStatus(CfFinishStatus.getByValue(recordDOResponse.getData().getFinishStatus()).getValue());
        }
        psCaseInfoVO.setCaseId(caseId);
        psCaseInfoVO.setFundStatusStr(financeState.getSubStatus().getDesc());
        int psCount = riskPublicSentimentInfoBiz.countPsByCaseId(caseId);
        if (psCount == 0) {
            psCaseInfoVO.setRelevantNum(psCount);
        } else {
            if (isNewPage == 1) {
                psCaseInfoVO.setRelevantNum(psCount);
            } else {
                psCaseInfoVO.setRelevantNum(psCount - 1);
            }
        }
        return psCaseInfoVO;
    }

}
