package com.shuidihuzhu.cf.risk.admin.configuration;


import com.shuidihuzhu.cf.client.alarm.center.report.ResponseCodeReportServiceImpl;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ResponseCodeReportConfiguration {

    @Bean
    public ResponseCodeReportServiceImpl responseCodeReportService(Analytics analytics) {
        ResponseCodeReportServiceImpl reportService = new ResponseCodeReportServiceImpl();
        reportService.setAnalytics(analytics);
        return reportService;
    }
}
