package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQualitySpotStrategyTypeRelBiz;
import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/12
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQualitySpotStrategyTypeRelDao {

    int save(RiskQualitySpotTypeRel typeRel);


    List<RiskQualitySpotTypeRel> findByStrategyId(@Param("strategyId") long strategyId);


    List<RiskQualitySpotTypeRel> findByTypeId(@Param("typeId") long typeId);

    int deleteByStrategyId(@Param("strategyId") long strategyId);


    List<RiskQualitySpotTypeRel> findByTypeIdList(@Param("typeIds") List<Long> typeIds);


    List<RiskQualitySpotTypeRel> findByStrategyIdList(@Param("strategyIdList") List<Long> strategyIdList);
}
