package com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 质检抽检周期类型枚举
 * <AUTHOR>
 * @date 2020/6/15 21:11
 */
@Getter
public enum QualitySpotScenePermissionsEnum {

    /**
     * 抽检周期配置
     * 注意type 需与 risk_quality_spot_type.id一致
     */
    OFFLINE_BD(1L, "线下顾问", "qualitySpotRule:offlineBd"),
    WX_1V1(2L, "微信1V1", "qualitySpotRule:wx"),
    OFFLINE_BD_QS_WORK_ORDER(3L, "普通质检工单", "qualitySpotRule:offlineBd"),
    WX_1V1_QS_WORK_ORDER(4L, "质检工单", "qualitySpotRule:wx"),
    OUT_BOUND(5L, "外呼工单", "qualitySpotRule:outbound"),
    OUT_BOUND_QS_WORK_ORDER(6L, "外呼质检工单", "qualitySpotRule:outbound"),
    MATERIALS_AUDIT(7L, "材审工单", "qualitySpotRule:materialsAudit"),
    MATERIALS_AUDIT_QC(8L, "材审质检工单", "qualitySpotRule:materialsAudit"),
//    RiskQcStandardUseSceneEnum.HOSPITAL_DEPT(11, "医院科室质检"),
    ZHU_DONG_QC(14, "主动服务质检工单", "qualitySpotRule:zhuDong"),
    INTERNAL_AUDIT(15, "内审", "qualitySpotRule:internalAudit"),
    HIGH_RISK_QUALITY_INSPECTION(16, "高风险质检工单", "qualitySpotRule:internalAudit"),
    ;

    private static Map<Long, QualitySpotScenePermissionsEnum> map = Maps.newHashMap();
    private static Map<String, QualitySpotScenePermissionsEnum> pmap = Maps.newHashMap();

    static {
        for (QualitySpotScenePermissionsEnum w : QualitySpotScenePermissionsEnum.values()){
            map.put(w.getType(), w);
            pmap.put(w.getPermission(),w);
        }
    }

    public static QualitySpotScenePermissionsEnum getFromType(long type) {
        return map.get(type);
    }

    public static QualitySpotScenePermissionsEnum getFromPermission(String permission) {
        return pmap.get(permission);
    }

    public static List<String> getPermissions(){
        List<String> list = Lists.newArrayList();
        for (QualitySpotScenePermissionsEnum w : QualitySpotScenePermissionsEnum.values()){
            list.add(w.getPermission());
        }
        return list;
    }

    /**
     * 场景的id
     */
    private long type;
    private String msg;
    private String permission;

    private QualitySpotScenePermissionsEnum(long type, String msg, String permission) {
        this.type = type;
        this.msg = msg;
        this.permission = permission;
    }

    public long getType() {
        return this.type;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getPermission() {
        return this.permission;
    }


}
