package com.shuidihuzhu.cf.risk.admin.model.enums.list;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * 质检抽检周期类型枚举
 * <AUTHOR>
 * @date 2020/6/15 21:11
 */
@Getter
public enum WhitePermissionsEnum {

    ID_CARD_APPROVE(1L, "身份认证白名单", "white-list:idCardApprove"),
    SENSITIVE_FILTER(2L, "禁止词过滤白名单", "white-list:sensitiveFilter"),
    EXCEPTION_IDENTITY(3L, "异常身份关联白名单", "white-list:ExceptionIdentity"),
    ;

    private static Map<Long, WhitePermissionsEnum> map = Maps.newHashMap();
    private static Map<String, WhitePermissionsEnum> pmap = Maps.newHashMap();

    static {
        for (WhitePermissionsEnum w : WhitePermissionsEnum.values()){
            map.put(w.getType(), w);
            pmap.put(w.getPermission(),w);
        }
    }

    public static WhitePermissionsEnum getFromType(long type) {
        return map.get(type);
    }

    public static WhitePermissionsEnum getFromPermission(String permission) {
        return pmap.get(permission);
    }

    public static List<String> getPermissions(){
        List<String> list = Lists.newArrayList();
        for (WhitePermissionsEnum w : WhitePermissionsEnum.values()){
            list.add(w.getPermission());
        }
        return list;
    }

    /**
     * 场景的id
     */
    private long type;
    private String msg;
    private String permission;

    private WhitePermissionsEnum(long type, String msg, String permission) {
        this.type = type;
        this.msg = msg;
        this.permission = permission;
    }

    public long getType() {
        return this.type;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getPermission() {
        return this.permission;
    }


}
