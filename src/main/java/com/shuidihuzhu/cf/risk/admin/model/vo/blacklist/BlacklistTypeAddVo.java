package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeActionRef;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/15 20:56
 */
@Data
@ApiModel(description = "黑名单类型新增")
public class BlacklistTypeAddVo {

    @ApiModelProperty("层级值类型, 1 选中，2 新增")
    @NotNull(message = "层级值类型不能为空")
    @Size(min = 3, max = 3, message = "层级值类型至少3个")
    private List<Integer> levelType;

    @ApiModelProperty("层级选中值,新增项补0")
    @NotNull(message = "层级选中值不能为空")
    @Size(min = 3, max = 3, message = "层级值类型至少3个")
    private List<Long> levelIds;

    @ApiModelProperty("层级输入值,选择的使用现有名称")
    @NotNull(message = "层级输入值不能为空")
    @Size(min = 3, max = 3, message = "层级输入值至少3个")
    private List<String> levelNames;

    @NotNull(message = "限制动作不能为空")
    @Size(min = 1, message = "限制动作至少选择一项")
    @ApiModelProperty("限制动作")
    private List<RiskBlacklistTypeActionRef> typeActions;

    @AllArgsConstructor
    @Getter
    public enum LevelOperateType {
        SELECT(1, "选择已有的"),
        ADD(2, "新增分类"),
        ;

        public static boolean legalCodes(List<Integer> code) {
            List<Integer> enumList = Arrays.stream(values()).map(LevelOperateType::getCode).collect(Collectors.toList());
            return CollectionUtils.containsAll(enumList, code);
        }

        private int code;
        private String desc;
    }

    public static void main(String[] args) {
        System.out.println(CollectionUtils.containsAll(List.of(12,2,2,2,2,3,3,3), List.of(12,3,2)));
    }

}
