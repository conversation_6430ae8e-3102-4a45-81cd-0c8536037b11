package com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/8/23
 */
@Getter
public enum QualitySpotWorkOrderRangeLimitEnum {
    ONLY(1, "只领取符合规则的工单"),
    PRIORITY(2, "按照配置分单"),
    ;

    public static QualitySpotWorkOrderRangeLimitEnum fromCode(int code){
        for (QualitySpotWorkOrderRangeLimitEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }

        return null;
    }

    private int code;
    private String desc;

    QualitySpotWorkOrderRangeLimitEnum(int code, String desc) {
        this.code = (byte)code;
        this.desc = desc;
    }
}
