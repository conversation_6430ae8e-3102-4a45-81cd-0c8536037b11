package com.shuidihuzhu.cf.risk.admin.controller.inner.qc;

import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcOrderHospitalDeptCreateService;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcOrderZhuDongCreateService;
import com.shuidihuzhu.cf.risk.client.risk.QcOrderInnerFeignClient;
import com.shuidihuzhu.cf.risk.model.admin.qc.QcZhuDongCreateParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

@RestController
@Slf4j
public class QcOrderInnerController implements QcOrderInnerFeignClient {

    @Resource
    private QcOrderHospitalDeptCreateService qcOrderHospitalDeptCreateService;

    @Resource
    private QcOrderZhuDongCreateService qcOrderZhuDongCreateService;

    @Resource
    private QcAudioAsrService qcAudioAsrService;

    @ApiOperation("创建科室质检工单")
    @Override
    public Response<Void> createHospitalDeptOrder(int deptId) {
        return qcOrderHospitalDeptCreateService.create(deptId);
    }

    @Override
    public Response<Void> createZhuDongQcOrder(QcZhuDongCreateParam qcZhuDongCreateParam) {
        return qcOrderZhuDongCreateService.create(qcZhuDongCreateParam);
    }

    @Override
    public Response<Void> onOrderMustAssign(long workOrderId) {
        // TODO 上线后一天延迟 抽检成功不再触发asr
        if (System.currentTimeMillis() > 1661844491616L) {
            return NewResponseUtil.makeSuccess();
        }
        return qcAudioAsrService.handleBDRecord(workOrderId, true);
    }

}
