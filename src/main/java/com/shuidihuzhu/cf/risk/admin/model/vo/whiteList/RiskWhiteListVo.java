package com.shuidihuzhu.cf.risk.admin.model.vo.whiteList;

import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.risk.admin.util.DataFormatUtil;
import com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto;
import com.shuidihuzhu.common.web.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("身份关联异常白名单展示Vo")
public class RiskWhiteListVo extends RiskWhiteListAddVo {

    @ApiModelProperty("最新操作人")
    private String operator;

    @ApiModelProperty("是否有效")
    private boolean status;

    @ApiModelProperty("最新操作时间")
    private String lastOperateTime;

    public static RiskWhiteListVo buildVo(RiskWhiteListDto riskWhiteListDto, MaskUtil maskUtil){
        RiskWhiteListVo riskWhiteListVo = new  RiskWhiteListVo();
        riskWhiteListVo.setId(riskWhiteListDto.getId());
        riskWhiteListVo.setAddReason(riskWhiteListDto.getAddReason());
        riskWhiteListVo.setIdCardMask(maskUtil.buildByDecryptStrAndType(riskWhiteListDto.getIdCard(), DesensitizeEnum.IDCARD));
        riskWhiteListVo.setIdCard(null);
        riskWhiteListVo.setPhoneNumberMask(maskUtil.buildByDecryptPhone(riskWhiteListDto.getPhoneNumber()));
        riskWhiteListVo.setPhoneNumber(null);
        riskWhiteListVo.setName(riskWhiteListDto.getName());
        riskWhiteListVo.setExpireTime(formatExpireTime(riskWhiteListDto.getExpireTime()));
        riskWhiteListVo.setOperator(riskWhiteListDto.getOperator());
        riskWhiteListVo.setStatus(riskWhiteListDto.getExpireTime().after(DateUtil.getCurrentTimestamp()));
        riskWhiteListVo.setLastOperateTime(DateUtil.formatDateTime(riskWhiteListDto.getLastOperateTime()));
        return riskWhiteListVo;
    }

    public static String formatExpireTime(Date expireTime) {
        if (expireTime == null){
            return  "";
        }
        return  expireTime.after(DateUtil.getDateFromLongString("2300-01-01 00:00:00")) ?
                "永久" : DateUtil.formatDateTime(expireTime);
    }

}
