package com.shuidihuzhu.cf.risk.admin.model.enums;

public enum PsSolutionEnum {

    NONE(1, "无需处理"),
    HUIFU(2,"直接回复/告知"),
    KEFU(3,"联系客服"),
    JUBAO(4,"标记举报"),
    FANKUI(5,"反馈至相应部门处理"),
    CHEGAOHAN(6,"撤稿函"),
    CHANGGUIHUASHU(7, "常规舆情话术回复"),
    TESHUHUASHU(8,"特殊舆情报备/话术回复"),
    GUANZHU(9,"持续关注舆情影响"),
    TIEBA(10, "贴吧删帖"),
    OTHER(11, "其他"),

    ;

    private int code;
    private String description;

    PsSolutionEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (PsSolutionEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
