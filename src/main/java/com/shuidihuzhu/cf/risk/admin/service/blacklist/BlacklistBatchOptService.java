package com.shuidihuzhu.cf.risk.admin.service.blacklist;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.*;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.*;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 黑名单批量操作service
 * <AUTHOR>
 * @date 2020/7/23 20:05
 */
@Slf4j
@Service
public class BlacklistBatchOptService {

    public static final int FILE_MAX_SIZE = 10000;
    private static final int BATCH_SIZE = 100;
    private static final long BATCH_IMPORT_BLACKLIST_DATA_LEAVE_TIME = 2 * 60 * 1000;
    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\d{1,19}");
    private static final Pattern MOBILE_PATTERN = Pattern.compile("\\d{1,11}");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("\\d{1,17}[xX\\d]?");
    private static final Pattern BORN_CARD_PATTERN = Pattern.compile("[0-9a-zA-Z]{1,18}");

    @Resource
    private RiskBlacklistTypeActionRefDao typeActionRefDao;
    @Resource
    private RiskBlacklistTypeDao riskBlacklistTypeDao;
    @Resource
    private RiskBlacklistDataDao riskBlacklistDataDao;
    @Resource
    private RiskBlacklistDataActionRefDao dataActionRefDao;
    @Resource
    private RiskBlacklistDataTypeRefDao dataTypeRefDao;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Resource
    private SeaAccountService accountService;
    @Resource
    private RiskBlacklistDataLogDao dataLogDao;

    /**
     * 导出导入黑名单excel模板
     */
    public ByteArrayOutputStream exportImportTemplate(){
        ByteArrayOutputStream byteArrayOs = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOs, ImportExcelEntity.class).sheet("黑名单数据")
                .doWrite(List.of());
        return byteArrayOs;
    }

    /**
     * 导入黑名单数据
     */
    public void importBlacklist(InputStream is, long adminUserId) {
        ExcelReader excelReader = null;
        String key = BlackListDataService.UPDATE_BLACKLIST_DATA_ADD_KEY;
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, BATCH_IMPORT_BLACKLIST_DATA_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请稍后重试");
            }

            InfoExtractListener infoExtractListener = new InfoExtractListener();
            excelReader = EasyExcel.read(is, ImportExcelEntity.class, infoExtractListener).build();
            excelReader.read(EasyExcel.readSheet(0).build());
            List<ImportExcelEntity> sheetBodies = infoExtractListener.getSheetBodies();
            if (CollectionUtils.isEmpty(sheetBodies)) {
                return;
            }
            if (sheetBodies.size() > FILE_MAX_SIZE) {
                throw new IllegalArgumentException("导入数据不要超过1万条");
            }

            saveData(dataCheck(sheetBodies), sheetBodies, adminUserId);
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (excelReader != null) {
                excelReader.finish();
            }
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
    }

    private void saveData(Pair<Map<String, Long>, Map<String, List<Long>>> pair, List<ImportExcelEntity> sheetBodies, long adminUserId){
        SeaAccountService.AdminUserNameWithOrg currAdminUserNameWithOrg = accountService.getCurrAdminUserNameWithOrg(adminUserId);
        Map<String, Long> typeNameIdMap = pair.getKey();
        Map<String, List<Long>> typeNameActionsMap = pair.getValue();
        Lists.partition(sheetBodies, BATCH_SIZE).forEach(entityPart -> {
            try {
                //保存数据
                List<BlacklistDataExt> waitSaveList = entityPart.parallelStream().map(bl -> {
                    BlacklistDataExt blacklistData = new BlacklistDataExt();
                    blacklistData.setRowNum(bl.getRowNum());
                    blacklistData.setUserId(bl.getUserId());
                    blacklistData.setEncryptIdCard(bl.idCard);
                    blacklistData.setEncryptBornCard(bl.bornCard);
                    blacklistData.setEncryptMobile(bl.mobile);
                    blacklistData.setUserName(bl.getUserName());
                    blacklistData.setUserIdBind(bl.getBindUserId());
                    blacklistData.setEncryptMobileBind(bl.getBindMobile());
                    blacklistData.setOperateReason(bl.getOperateReason());
                    blacklistData.setOperateId((long) currAdminUserNameWithOrg.getAdminUserId());
                    blacklistData.setOperateName(currAdminUserNameWithOrg.getUserNameWithOrg());
                    return blacklistData;
                }).collect(Collectors.toList());
                riskBlacklistDataDao.saveBatch(waitSaveList);

                Map<Integer, Long> rowNumDataIdMap = waitSaveList.parallelStream()
                        .collect(Collectors.toMap(BlacklistDataExt::getRowNum, BlacklistDataExt::getId));
                List<RiskBlacklistDataActionRef> dataActionRefs = new Vector<>();
                List<RiskBlacklistDataTypeRef> dataTypeRefs = new Vector<>();
                List<RiskBlacklistDataLog> logs = new Vector<>();
                entityPart.parallelStream().forEach(entity -> {
                    Long dataId = rowNumDataIdMap.get(entity.getRowNum());
                    Set<String> classifyTypes = Sets.newHashSet(Splitter.on(",").split(entity.getClassifyType()));
                    dataActionRefs.addAll(classifyTypes.stream()
                            .flatMap(typeName->typeNameActionsMap.get(typeName).stream())
                            .collect(Collectors.toSet()).stream()
                            .map(actionId->{
                                RiskBlacklistDataActionRef dataActionRef = new RiskBlacklistDataActionRef();
                                dataActionRef.setDataId(dataId);
                                dataActionRef.setActionId(actionId);
                                return dataActionRef;
                            }).collect(Collectors.toList())
                    );
                    dataTypeRefs.addAll(classifyTypes.stream()
                            .map(typeName->{
                                Long typeId = typeNameIdMap.get(typeName);
                                RiskBlacklistDataTypeRef typeRef = new RiskBlacklistDataTypeRef();
                                typeRef.setDataId(dataId);
                                typeRef.setTypeId(typeId);
                                typeRef.setTypeName(typeName);
                                typeRef.setActionIds(Joiner.on(",").join(typeNameActionsMap.get(typeName)));
                                return typeRef;
                            }).collect(Collectors.toList())
                    );
                    RiskBlacklistDataLog dataLog = new RiskBlacklistDataLog();
                    dataLog.setOperateId((long) currAdminUserNameWithOrg.getAdminUserId());
                    dataLog.setOperateName(currAdminUserNameWithOrg.getUserNameWithOrg());
                    dataLog.setDataId(dataId);
                    dataLog.setModifyContent("excel导入黑名单");
                    dataLog.setOperateReason(entity.getOperateReason());
                    logs.add(dataLog);
                });
                //保存数据-限制动作
                dataActionRefDao.saveBatch(dataActionRefs);
                //保存数据-所属类型
                dataTypeRefDao.saveBatch(dataTypeRefs);
                //保存日志
                dataLogDao.insertBatch(logs);

                MaliMQComponent.builder()
                        .setTags(CfRiskMQTagCons.BLACK_LIST_UPDATE)
                        .setPayload("{}")
                        .send();

            } catch (Exception e) {
                String errorInfo = JSON.toJSONString(entityPart);
                log.error("写入数据库失败，数据：{}", errorInfo);
                throw new RuntimeException("写入数据库失败，数据:"+ errorInfo);
            }
        });
    }

    private Pair<Map<String, Long>, Map<String, List<Long>>> dataCheck(List<ImportExcelEntity> sheetBodies){
        //基础验证
        baseCheck(sheetBodies);
        //判断重复数据
        Pair<Boolean, Map<String, Set<Object>>> repeatInfo = transformDataAndCheckRepeatAndExists(sheetBodies);
        if (repeatInfo != null) {
            if (repeatInfo.getLeft()) {
                throw new IllegalArgumentException("excel中发现重复数据--"+repeatInfo.getValue());
            } else {
                throw new IllegalArgumentException("发现数据已经存在系统中--"+repeatInfo.getValue());
            }
        }
        //验证黑名单类型是否存在
        Set<String> excelClassify = sheetBodies.parallelStream()
                .flatMap(entity-> Lists.newArrayList(Splitter.on(",").split(entity.getClassifyType())).stream())
                .collect(Collectors.toSet());
        List<RiskBlacklistType> types = riskBlacklistTypeDao.listByEnabledTypeNames(excelClassify);
        Map<String, Long> typeNameIdMap = types.stream().collect(Collectors.toMap(RiskBlacklistType::getTypeName, RiskBlacklistType::getId));
        Map<Long, String> idTypeNameMap = types.stream().collect(Collectors.toMap(RiskBlacklistType::getId, RiskBlacklistType::getTypeName));
        Collection<String> typeNameSub = CollectionUtils.subtract(excelClassify, typeNameIdMap.keySet());
        if (CollectionUtils.isNotEmpty(typeNameSub)) {
            throw new IllegalArgumentException("黑名单类型不存在:"+JSON.toJSONString(typeNameSub));
        }
        //查找类型配置的限制动作
        List<RiskBlacklistTypeActionRef> typeActionRefs = typeActionRefDao.listByTypeIds(typeNameIdMap.values());
        Map<String, List<Long>> typeActionsMap = Maps.newHashMap();
        typeActionRefs.stream().collect(Collectors.collectingAndThen(Collectors.groupingBy(RiskBlacklistTypeActionRef::getTypeId), longListMap -> {
            longListMap.keySet().forEach(typeId -> typeActionsMap.put(idTypeNameMap.get(typeId), longListMap.get(typeId).stream()
                    .map(RiskBlacklistTypeActionRef::getActionId).collect(Collectors.toList())));
            return null;
        }));

        return Pair.of(typeNameIdMap, typeActionsMap);
    }

    private void baseCheck(List<ImportExcelEntity> sheetBodies){
        //基础必填项验证验证
        Predicate<ImportExcelEntity> noValidData =
                entity->StringUtils.isAllBlank(entity.getBornCard(), entity.getMobile(), entity.getIdCard(), entity.getUserIdStr()) ||
                        StringUtils.isBlank(entity.getClassifyType()) || StringUtils.isBlank(entity.getOperateReason());
        if (sheetBodies.parallelStream().anyMatch(noValidData)) {
            throw new IllegalArgumentException("必填数据校验不通过:\n" +
                    "1.添加原因/黑名单类型为必填字段；\n" +
                    "2.用户uid/用户手机号/用户身份证号/用户出生证号至少一个有值；\n" +
                    "3.注意excel不要存在空行(删除时选中行，右键'删除')");
        }
        //验证数据类型
        Map<String, List<String>> dataLengthExp = Maps.newConcurrentMap();
        sheetBodies.parallelStream().forEach(entity -> {
            Optional.ofNullable(entity.getUserIdStr()).filter(s -> StringUtils.isNotBlank(s) && !NUMBER_PATTERN.matcher(s).matches())
                    .ifPresent(s -> dataLengthExp.computeIfAbsent("用户id，只允许数字，限制最长19位", key->Lists.newArrayList()).add(s));
            Optional.ofNullable(entity.getMobile()).filter(s -> StringUtils.isNotBlank(s) && !MOBILE_PATTERN.matcher(s).matches())
                    .ifPresent(s -> dataLengthExp.computeIfAbsent("用户手机号，只允许数字，限制最长11位", key->Lists.newArrayList()).add(s));
            Optional.ofNullable(entity.getIdCard()).filter(s -> StringUtils.isNotBlank(s) && !ID_CARD_PATTERN.matcher(s).matches())
                    .ifPresent(s -> dataLengthExp.computeIfAbsent("用户身份证号，只允许数字，限制最长18位", key->Lists.newArrayList()).add(s));
            Optional.ofNullable(entity.getBornCard()).filter(s -> StringUtils.isNotBlank(s) && !BORN_CARD_PATTERN.matcher(s).matches())
                    .ifPresent(s -> dataLengthExp.computeIfAbsent("用户出生证号，只允许输入数字与英文，限制最长18位", key->Lists.newArrayList()).add(s));
            Optional.ofNullable(entity.getOperateReason()).filter(s -> s.length() < 5 || s.length() > 200)
                    .ifPresent(s -> dataLengthExp.computeIfAbsent("添加原因长度限制在5~200位", key->Lists.newArrayList()).add(s));
        });
        if (MapUtils.isNotEmpty(dataLengthExp)) {
            throw new IllegalArgumentException(dataLengthExp.entrySet().stream()
                    .map(entry->entry.getKey()+":"+entry.getValue()).collect(Collectors.joining("\n")));
        }
    }

    private Pair<Boolean, Map<String, Set<Object>>> transformDataAndCheckRepeatAndExists(List<ImportExcelEntity> sheetBodies){
        //验证excel中数据是否有重复
        Map<String, Set<Object>> errorMap = Maps.newHashMap();
        List<Long> userIdList = new Vector<>();
        List<String> idCardList = new Vector<>();
        List<String> bornCardList = new Vector<>();
        List<String> mobileList = new Vector<>();
        //优化数据，同时做了转换为密文的操作
        sheetBodies.parallelStream().forEach(entity -> {
            if (StringUtils.isNotBlank(entity.getUserIdStr())) {
                entity.setUserId(Long.valueOf(entity.getUserIdStr()));
            }
            if (entity.getUserId() != null && entity.getUserId() > 0) {
                userIdList.add(entity.getUserId());
            } else {
                entity.setUserId(0L);
            }
            if (StringUtils.isNotBlank(entity.getIdCard())) {
                entity.setIdCard(oldShuidiCipher.aesEncrypt(entity.getIdCard()));
                idCardList.add(entity.getIdCard());
            } else {
                entity.setIdCard("");
            }
            if (StringUtils.isNotBlank(entity.getBornCard())) {
                entity.setBornCard(oldShuidiCipher.aesEncrypt(entity.getBornCard()));
                bornCardList.add(entity.getBornCard());
            } else {
                entity.setBornCard("");
            }
            if (StringUtils.isNotBlank(entity.getMobile())) {
                entity.setMobile(oldShuidiCipher.aesEncrypt(entity.getMobile()));
                mobileList.add(entity.getMobile());
            } else {
                entity.setMobile("");
            }
            Optional.ofNullable(entity.getUserName()).ifPresentOrElse(s -> {}, () -> entity.setUserName(""));
        });
        Optional.of(CollectionUtils.subtract(userIdList, Sets.newHashSet(userIdList))).filter(subtract->subtract.size()>0)
                .ifPresent(subtract -> errorMap.put("用户uid", Sets.newHashSet(subtract)));
        Optional.of(CollectionUtils.subtract(idCardList, Sets.newHashSet(idCardList))).filter(subtract->subtract.size()>0)
                .ifPresent(subtract -> errorMap.put("用户身份证号", decryptCipher(subtract)));
        Optional.of(CollectionUtils.subtract(bornCardList, Sets.newHashSet(bornCardList))).filter(subtract->subtract.size()>0)
                .ifPresent(subtract -> errorMap.put("用户出生证号", decryptCipher(subtract)));
        Optional.of(CollectionUtils.subtract(mobileList, Sets.newHashSet(mobileList))).filter(subtract->subtract.size()>0)
                .ifPresent(subtract -> errorMap.put("用户手机号", decryptCipher(subtract)));
        if (!errorMap.isEmpty()) {
            return Pair.of(true, errorMap);
        }

        //验证DB中是否已经存在
        checkRepeatFromDb(userIdList, idCardList, bornCardList, mobileList, errorMap);
        if (!errorMap.isEmpty()) {
            return Pair.of(false, errorMap);
        }

        //填充绑定值
        Map<Long, String> userIdMobileMap = Collections.emptyMap();
        if (!userIdList.isEmpty()) {
            userIdMobileMap = userInfoDelegateService.getUserInfoByUserIdBatch(userIdList).parallelStream()
                    .collect(Collectors.toMap(UserInfoModel::getUserId, UserInfoModel::getCryptoMobile));
        }
        Map<Long, String> finalUserIdMobileMap = userIdMobileMap;
        sheetBodies.parallelStream().forEach(entity->{
            if (entity.getUserId() > 0) {
                Optional.ofNullable(finalUserIdMobileMap.get(entity.getUserId())).filter(StringUtils::isNotBlank)
                        .ifPresent(entity::setBindMobile);
            }
            if (StringUtils.isNotBlank(entity.getMobile())) {
                UserInfoModel userInfoModel = null;
                try {
                    userInfoModel = userInfoDelegateService.getUserInfoByCryptoMobile(entity.getMobile());
                } catch (Exception e) {
                    log.warn("", e);
                }
                Optional.ofNullable(userInfoModel).map(UserInfoModel::getUserId).ifPresent(entity::setBindUserId);
            }
        });
        return null;
    }

    private void checkRepeatFromDb(List<Long> userIdList, List<String> idCardList, List<String> bornCardList,
                                   List<String> mobileList, Map<String, Set<Object>> errorMap){
        int stepSize = 1000;
        List<Long> userIdRepeatList = Lists.newArrayList();
        List<String> idCardRepeatList = Lists.newArrayList();
        List<String> bornCardRepeatList = Lists.newArrayList();
        List<String> mobileRepeatList = Lists.newArrayList();

        //拆分查询，避免查询的值太多
        int max = Math.max(Math.max(userIdList.size(), idCardList.size()), Math.max(bornCardList.size(), mobileList.size()));
        double cycles = Math.ceil(max * 1.0 / stepSize);
        for (int i=0; i < cycles; i++) {
            int startIdx = i * stepSize;
            int endIdx = (i+1) * stepSize;
            List<RiskBlacklistData> repeatList = riskBlacklistDataDao.getByUserIdOrCryptoIdCardOrMobileOrBornCard(
                    subList(userIdList, startIdx, endIdx), subList(mobileList, startIdx, endIdx),
                    subList(idCardList, startIdx, endIdx), subList(bornCardList, startIdx, endIdx));
            repeatList.forEach(entity -> {
                if (entity.getUserId() != null && entity.getUserId() > 0) {
                    userIdRepeatList.add(entity.getUserId());
                }
                if (StringUtils.isNotBlank(entity.getEncryptIdCard())) {
                    idCardRepeatList.add(entity.getEncryptIdCard());
                }
                if (StringUtils.isNotBlank(entity.getEncryptBornCard())) {
                    bornCardRepeatList.add(entity.getEncryptBornCard());
                }
                if (StringUtils.isNotBlank(entity.getEncryptMobile())) {
                    mobileRepeatList.add(entity.getEncryptMobile());
                }
            });
        }

        Optional.of(CollectionUtils.intersection(userIdList, userIdRepeatList)).filter(subtract->subtract.size()>0)
                .ifPresent(subtract -> errorMap.put("用户uid", Sets.newHashSet(subtract)));
        Optional.of(CollectionUtils.intersection(idCardList, idCardRepeatList)).filter(subtract->subtract.size()>0)
                .ifPresent(subtract -> errorMap.put("用户身份证号", decryptCipher(subtract)));
        Optional.of(CollectionUtils.intersection(bornCardList, bornCardRepeatList)).filter(subtract->subtract.size()>0)
                .ifPresent(subtract -> errorMap.put("用户出生证号", decryptCipher(subtract)));
        Optional.of(CollectionUtils.intersection(mobileList, mobileRepeatList)).filter(subtract->subtract.size()>0)
                .ifPresent(subtract -> errorMap.put("用户手机号", decryptCipher(subtract)));
    }

    private <T> List<T> subList(List<T> pendingList, int startIdx, int endIdx){
        return Optional.of(pendingList).filter(list->list.size() > startIdx).map(list->
                list.size() >= endIdx ? pendingList.subList(startIdx, endIdx) : pendingList.subList(startIdx, list.size())
        ).orElse(null);
    }

    private Set<Object> decryptCipher(Collection<String> cipherSet){
        return Sets.newHashSet(cipherSet).stream().map(data->shuidiCipher.decrypt(data)).collect(Collectors.toSet());
    }

    public void repairDataRefAll(){
        int limit = 500,size;
        long previousId = 0;
        List<RiskBlacklistData> tmpDataList;
        do {
            tmpDataList = riskBlacklistDataDao.listByLimit(previousId, limit);
            size = tmpDataList.size();
            //1. 查找所有有效date_type_ref
            List<Long> dataIds = tmpDataList.parallelStream().map(RiskBlacklistData::getId).collect(Collectors.toList());
            List<RiskBlacklistDataTypeRef> dataTypeRefs = dataTypeRefDao.listByDataIds(dataIds);
            //2. 查询类型关联的actionIds
            List<RiskBlacklistTypeActionRef> typeActionRefs = Collections.emptyList();
            if (!dataTypeRefs.isEmpty()) {
                typeActionRefs = typeActionRefDao.listByTypeIds(dataTypeRefs.parallelStream()
                        .map(RiskBlacklistDataTypeRef::getTypeId).collect(Collectors.toSet()));
            }
            Map<Long, List<Long>> typeActionMap = typeActionRefs.parallelStream().collect(
                    Collectors.collectingAndThen(Collectors.groupingBy(RiskBlacklistTypeActionRef::getTypeId), r ->
                            r.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry ->
                                    entry.getValue().stream().map(RiskBlacklistTypeActionRef::getActionId).distinct()
                                            .sorted().collect(Collectors.toList()))
                            )));
            Map<Long, List<Long>> dataActionMap = dataTypeRefs.parallelStream().collect(Collectors.collectingAndThen(
                    Collectors.groupingBy(RiskBlacklistDataTypeRef::getDataId), r ->
                            r.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                                    .flatMap(dataTypeRef -> typeActionMap.get(dataTypeRef.getTypeId()).stream())
                                    .distinct().collect(Collectors.toList())))
            ));
            //3. 更新data_action_ref
            List<RiskBlacklistDataActionRef> dataActionRefs = dataActionRefDao.listByDataIdAndActionIds(dataIds, null);
            Map<Long, List<Long>> dbDataActionMap = dataActionRefs.parallelStream().collect(Collectors.collectingAndThen(
                    Collectors.groupingBy(RiskBlacklistDataActionRef::getDataId), r -> r.entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                                    .map(RiskBlacklistDataActionRef::getActionId).distinct().collect(Collectors.toList())))
            ));
            List<RiskBlacklistDataActionRef> needAddList = dataActionMap.entrySet().parallelStream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> CollectionUtils.subtract(entry.getValue(), Optional.ofNullable(dbDataActionMap.get(entry.getKey())).orElse(Collections.emptyList()))))
                    .entrySet().parallelStream().filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                    .flatMap(entry -> entry.getValue().stream().map(actionId -> {
                        RiskBlacklistDataActionRef dataActionRef = new RiskBlacklistDataActionRef();
                        dataActionRef.setDataId(entry.getKey());
                        dataActionRef.setActionId(actionId);
                        return dataActionRef;
                    })).collect(Collectors.toList());
            Map<Long, Set<Long>> needDelIdActionMap = dataActionMap.entrySet().parallelStream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> Sets.newHashSet(CollectionUtils.subtract(Optional.ofNullable(dbDataActionMap.get(entry.getKey())).orElse(Collections.emptyList()), entry.getValue()))))
                    .entrySet().parallelStream().filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            List<Long> needDelList = dataActionRefs.parallelStream().filter(ref -> needDelIdActionMap.containsKey(ref.getDataId()) &&
                    needDelIdActionMap.get(ref.getDataId()).contains(ref.getActionId()))
                    .map(RiskBlacklistDataActionRef::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needAddList)) {
                dataActionRefDao.saveBatch(needAddList);
            }
            if (CollectionUtils.isNotEmpty(needDelList)) {
                dataActionRefDao.deleteByIds(needDelList);
            }
            //4. 更新data_type_ref
            dataTypeRefs.parallelStream().forEach(dataTypeRef -> {
                String actionIds = typeActionMap.get(dataTypeRef.getTypeId()).stream().map(String::valueOf).collect(Collectors.joining(","));
                if (!Objects.equals(dataTypeRef.getActionIds(), actionIds)) {
                    dataTypeRefDao.updateActionIdsByIds(List.of(dataTypeRef.getId()), actionIds);
                }});
        } while (size >= limit && (previousId = tmpDataList.get(size-1).getId()) > 0);
    }

    private static class InfoExtractListener extends AnalysisEventListener<ImportExcelEntity> {
        @Getter
        private final List<ImportExcelEntity> sheetBodies = new Vector<>();

        @Override
        public void invoke(ImportExcelEntity data, AnalysisContext context) {
            data.setRowNum(context.readRowHolder().getRowIndex());
            sheetBodies.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {}
    }

    @Data
    public static class ImportExcelEntity {
        @ExcelProperty("用户uid")
        private String userIdStr;
        @ExcelProperty("用户手机号")
        private String mobile;
        @ExcelProperty("用户身份证号")
        private String idCard;
        @ExcelProperty("用户出生证号")
        private String bornCard;
        @ExcelProperty("用户姓名")
        private String userName;
        @ExcelProperty("添加原因")
        private String operateReason;
        @ExcelProperty("黑名单类型")
        private String classifyType;

        /**
         * 其他扩展字段，非excel传入
         */
        @ExcelIgnore
        private Integer rowNum;
        @ExcelIgnore
        private Long userId;
        @ExcelIgnore
        private Long bindUserId = 0L;
        @ExcelIgnore
        private String bindMobile = "";
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    private static final class BlacklistDataExt extends RiskBlacklistData{
        private int rowNum;
    }

}
