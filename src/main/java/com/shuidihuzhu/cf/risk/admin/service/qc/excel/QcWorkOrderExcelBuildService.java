package com.shuidihuzhu.cf.risk.admin.service.qc.excel;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultConfigBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderParam;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResultConfig;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcRecordingProblemsVo;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcDetailService;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.search.param.table.RiskQcSearchIndexTableParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderExtTableParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderTableParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/19  7:43 下午
 */
@Service
@Slf4j
public class QcWorkOrderExcelBuildService {

    @Resource
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Resource
    private CfSearchClient cfSearchClient;
    @Resource
    private RiskQcResultConfigBiz riskQcResultConfigBiz;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Resource
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Resource
    private RiskQcResultBiz riskQcResultBiz;
    @Resource
    private RiskQcDetailService riskQcDetailService;



    /**
     * 获取质检信息
     *
     * @return
     */
    public Map<Long, RiskQcBaseInfo> getQcBaseInfoMap(List<Long> qcIds) {
        List<RiskQcBaseInfo> riskQcBaseInfos = riskQcBaseInfoBiz.getByIds(qcIds);
        return riskQcBaseInfos.stream().collect(Collectors.toMap(RiskQcBaseInfo::getId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 获取质检信息
     *
     * @return
     */
    public Map<Long, RiskQcResultConfig> getRiskQcResultConfigMap() {
        List<RiskQcResultConfig> riskQcResultConfigList = riskQcResultConfigBiz.getAll();
        return riskQcResultConfigList.stream().collect(Collectors.toMap(RiskQcResultConfig::getId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 获取index表信息
     *
     * @return
     */
    public Map<Long, RiskQcSearchIndex> getQcSearchIndexMap(List<Long> workOrderIds) {
        List<RiskQcSearchIndex> riskQcSearchIndexList = riskQcSearchIndexBiz.getByWorkOrderIds(workOrderIds);
        return riskQcSearchIndexList.stream().collect(Collectors.toMap(RiskQcSearchIndex::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 获取质检结果信息
     *
     * @return
     */
    public Map<Long, RiskQcResult> getRiskQcResultMap(List<Long> workOrderIds) {
        List<RiskQcResult> riskQcResult = riskQcResultBiz.findByWorkOrderIds(workOrderIds);
        return riskQcResult.stream().collect(Collectors.toMap(RiskQcResult::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 获取问题录音
     *
     * @param workOrderIds
     * @return
     */
    public Map<Long, List<QcRecordingProblemsVo>> getRecordingProblemsByWorkOrderIds(List<Long> workOrderIds) {
        List<QcRecordingProblemsVo> recordingProblemsVoList = riskQcDetailService.getRecordingProblemsByWorkOrderIds(workOrderIds);
        if (CollectionUtils.isEmpty(recordingProblemsVoList)) {
            return Maps.newHashMap();
        }

        Map<Long, List<QcRecordingProblemsVo>> map = Maps.newHashMap();
        for (QcRecordingProblemsVo qcRecordingProblemsVo : recordingProblemsVoList) {
            long workOrderId = qcRecordingProblemsVo.getWorkOrderId();
            if (CollectionUtils.isEmpty(map.get(workOrderId))) {
                map.put(workOrderId, Lists.newArrayList(qcRecordingProblemsVo));
                continue;
            }
            List<QcRecordingProblemsVo> qcRecordingProblemsVoList = map.get(workOrderId);
            qcRecordingProblemsVoList.add(qcRecordingProblemsVo);
            map.put(workOrderId, qcRecordingProblemsVoList);
        }

        return map;
    }


    /**
     * 通过es查询工单id
     *
     * @param qcWorkOrderParam
     * @return
     */
    public List<Long> getWorkOrderIdByEs(QcWorkOrderParam qcWorkOrderParam) {
        UserInfoModel userInfoModel = null;
        if (StringUtils.isNotBlank(qcWorkOrderParam.getMobile())) {
            userInfoModel = userInfoDelegateService.getUserInfoByMobile(qcWorkOrderParam.getMobile());
        }

        CfWorkOrderV2IndexSearchParam searchParam = buildRiskQcSearchParam(qcWorkOrderParam, userInfoModel);

        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        List<CfWorkOrderModel> cfWorkOrderModels = Optional.ofNullable(searchRpcResult)
                .filter(v -> v.getCode() == ErrorCode.SUCCESS.getCode())
                .map(SearchRpcResult::getData)
                .map(CfWorkOrderIndexSearchResult::getModels)
                .orElse(Lists.newArrayList());

        return cfWorkOrderModels.stream().map(CfWorkOrderModel::getWorkOrderId).collect(Collectors.toList());
    }

    /**
     * 构建质检搜索参数
     */
    public CfWorkOrderV2IndexSearchParam buildRiskQcSearchParam(QcWorkOrderParam qcWorkOrderParam, UserInfoModel userInfoModel) {

        WorkOrderTableParam woTableParam = new WorkOrderTableParam();
        buildWorkOrderTableParam(woTableParam, qcWorkOrderParam);

        RiskQcSearchIndexTableParam riskQcSearchIndexTableParam = new RiskQcSearchIndexTableParam();
        buildRiskQcSearchIndexTableParam(riskQcSearchIndexTableParam, qcWorkOrderParam, userInfoModel);

        List<WorkOrderExtTableParam> workOrderExtTableParamList = Lists.newArrayList();
        buildWorkOrderExtTableParam(workOrderExtTableParamList, qcWorkOrderParam);

        return buildCfWorkOrderV2IndexSearchParam(woTableParam, riskQcSearchIndexTableParam, workOrderExtTableParamList, qcWorkOrderParam);
    }

    /**
     * 构建工单表的相关参数
     *
     * @param workOrderTableParam
     * @param qcWorkOrderParam
     */
    private void buildWorkOrderTableParam(WorkOrderTableParam workOrderTableParam, QcWorkOrderParam qcWorkOrderParam) {
        workOrderTableParam.setOrderTypes(WorkOrderType.QC_WORK_ORDER_LIST);
        if (qcWorkOrderParam.getOrderType() > 0) {
            workOrderTableParam.setOrderTypes(Lists.newArrayList(qcWorkOrderParam.getOrderType()));
        }
        if (qcWorkOrderParam.getHandleStartTime() > 0 && qcWorkOrderParam.getHandleEndTime() > 0) {
            workOrderTableParam.setFinishStartTime(qcWorkOrderParam.getHandleStartTime());
            workOrderTableParam.setFinishEndTime(qcWorkOrderParam.getHandleEndTime());
        }
        if (qcWorkOrderParam.getWorkOrderId() > 0) {
            workOrderTableParam.setIds(Lists.newArrayList(qcWorkOrderParam.getWorkOrderId()));
        }
        if (qcWorkOrderParam.getOperatorId() > 0) {
            workOrderTableParam.setOperatorIds(Lists.newArrayList(qcWorkOrderParam.getOperatorId()));
        }
        if (qcWorkOrderParam.getHandleResult() >= 0) {
            int handleResult = qcWorkOrderParam.getHandleResult();
            List<Integer> searchHandleResultList = handleResult == HandleResultEnum.undoing.getType() ?
                    List.of(HandleResultEnum.undoing.getType(), HandleResultEnum.not_auto_assign.getType()) :
                    List.of(handleResult);
            workOrderTableParam.setHandleResult(searchHandleResultList);
        }
        if (qcWorkOrderParam.getCreateStartTime() > 0 && qcWorkOrderParam.getCreateEndTime() > 0) {
            workOrderTableParam.setCreateStartTime(qcWorkOrderParam.getCreateStartTime());
            workOrderTableParam.setCreateEndTime(qcWorkOrderParam.getCreateEndTime());
        }
    }

    /**
     * 构建质检搜索参数
     *
     * @param riskQcSearchIndexTableParam
     * @param qcWorkOrderParam
     * @param userInfoModel
     */
    private void buildRiskQcSearchIndexTableParam(RiskQcSearchIndexTableParam riskQcSearchIndexTableParam, QcWorkOrderParam qcWorkOrderParam, UserInfoModel userInfoModel) {
        if (StringUtils.isNotBlank(qcWorkOrderParam.getOrganization())) {
            riskQcSearchIndexTableParam.setOrganization(qcWorkOrderParam.getOrganization());
        }
        if (StringUtils.isNotBlank(qcWorkOrderParam.getQcByName())) {
            riskQcSearchIndexTableParam.setQcByName(qcWorkOrderParam.getQcByName());
        }
        if (qcWorkOrderParam.getQcType() > 0) {
            riskQcSearchIndexTableParam.setQcType(qcWorkOrderParam.getQcType());
        }
        if (qcWorkOrderParam.getQcResult() > 0) {
            riskQcSearchIndexTableParam.setQcResult(qcWorkOrderParam.getQcResult());
        }
        if (qcWorkOrderParam.getQcResultSecond() > 0) {
            riskQcSearchIndexTableParam.setQcResultSecond(qcWorkOrderParam.getQcResultSecond());
        }
        if (qcWorkOrderParam.getQuestionType() > 0) {
            riskQcSearchIndexTableParam.setQuestionType(qcWorkOrderParam.getQuestionType());
        }
        if (qcWorkOrderParam.getMaterialId() > 0) {
            riskQcSearchIndexTableParam.setMaterialId(qcWorkOrderParam.getMaterialId());
        }
        if (qcWorkOrderParam.getCaseId() > 0) {
            riskQcSearchIndexTableParam.setCaseId(qcWorkOrderParam.getCaseId());
        }
        if (StringUtils.isNotBlank(qcWorkOrderParam.getRuleName())) {
            riskQcSearchIndexTableParam.setRuleName(qcWorkOrderParam.getRuleName());
        }
        if (Objects.nonNull(userInfoModel)) {
            riskQcSearchIndexTableParam.setUserId(userInfoModel.getUserId());
        }
        if (qcWorkOrderParam.getSecondQuestionType() > 0) {
            riskQcSearchIndexTableParam.setSecondQuestionType(qcWorkOrderParam.getSecondQuestionType());
        }
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.BD.getCode()) {
            if (qcWorkOrderParam.getFirstPropertyId() > 0) {
                riskQcSearchIndexTableParam.setFirstPropertyId(qcWorkOrderParam.getFirstPropertyId());
            }
        }
        // 科室质检工单 前端查询caseId 值为deptId 后续可拆单独字段
        if (qcWorkOrderParam.getOrderType() == WorkOrderType.qc_hospital_dept.getType()) {
            riskQcSearchIndexTableParam.setHospitalDeptId(qcWorkOrderParam.getCaseId());
        }

        //如果是查询wx1v1才拼接的条件
        riskQcSearchIndexTableParam.setServiceStage(-1);
        riskQcSearchIndexTableParam.setJobContent(-1);
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.WX_1V1.getCode()) {
            if (StringUtils.isNotBlank(qcWorkOrderParam.getRegisterMobile())) {
                riskQcSearchIndexTableParam.setRegisterMobile(qcWorkOrderParam.getRegisterMobile());
            }
            riskQcSearchIndexTableParam.setServiceStage(qcWorkOrderParam.getServiceStage());
            if (qcWorkOrderParam.getJobContent() >= 0) {
                riskQcSearchIndexTableParam.setJobContent(qcWorkOrderParam.getJobContent());
            }
        }

        //如果查询外呼才拼接的条件
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.CALL.getCode()) {
            if (qcWorkOrderParam.getFirstLevelLabel() > 0) {
                riskQcSearchIndexTableParam.setFirstLevelLabel(qcWorkOrderParam.getFirstLevelLabel());
            }
            if (qcWorkOrderParam.getTwoLevelLabel() > 0) {
                riskQcSearchIndexTableParam.setTwoLevelLabel(qcWorkOrderParam.getTwoLevelLabel());
            }
            if (qcWorkOrderParam.getCallTaskStatus() > 0) {
                riskQcSearchIndexTableParam.setCallTaskStatus(qcWorkOrderParam.getCallTaskStatus());
            }
            if (StringUtils.isNotBlank(qcWorkOrderParam.getCallCluesChannel())) {
                riskQcSearchIndexTableParam.setCallCluesChannel(qcWorkOrderParam.getCallCluesChannel());
            }
            if (StringUtils.isNotBlank(qcWorkOrderParam.getRegisterMobile())) {
                riskQcSearchIndexTableParam.setRegisterMobile(qcWorkOrderParam.getRegisterMobile());
            }
            if (qcWorkOrderParam.getJobContent() >= 0) {
                riskQcSearchIndexTableParam.setJobContent(qcWorkOrderParam.getJobContent());
            }
        }
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.BD.getCode()) {
            if (qcWorkOrderParam.getFirstPropertyId() > 0) {
                riskQcSearchIndexTableParam.setFirstPropertyId(qcWorkOrderParam.getFirstPropertyId());
            }
        }

        //材审、内审筛选条件
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.MATERIAL.getCode() ||
                qcWorkOrderParam.getQcType() == QcTypeEnum.INTERNAL_AUDIT.getCode()) {
            if (qcWorkOrderParam.getCallStatus() > 0) {
                riskQcSearchIndexTableParam.setCallStatus(qcWorkOrderParam.getCallStatus());
            }
            if (qcWorkOrderParam.getMaterialHandleResult() > 0) {
                riskQcSearchIndexTableParam.setHandleResult(qcWorkOrderParam.getMaterialHandleResult());
            }
        }
    }

    /**
     * 构建工单扩展信息
     *
     * @param workOrderExtTableParamList
     * @param qcWorkOrderParam
     */
    private void buildWorkOrderExtTableParam(List<WorkOrderExtTableParam> workOrderExtTableParamList, QcWorkOrderParam qcWorkOrderParam) {
        WorkOrderExtTableParam workOrderExtTableParam = new WorkOrderExtTableParam();
        if (qcWorkOrderParam.getAssignType() > 0) {
            workOrderExtTableParam.setExtName(OrderExtName.qcAssignType.getName());
            workOrderExtTableParam.setExtValue(Integer.toString(qcWorkOrderParam.getAssignType()));
            workOrderExtTableParamList.add(workOrderExtTableParam);
        }
        WorkOrderExtTableParam tableParam = new WorkOrderExtTableParam();
        if (qcWorkOrderParam.getCallType() > 0) {
            tableParam.setExtName(OrderExtName.qcCallType.getName());
            tableParam.setExtValue(Integer.toString(qcWorkOrderParam.getCallType()));
            workOrderExtTableParamList.add(tableParam);
        }
    }

    /**
     * 构建搜索参数
     *
     * @param woTableParam
     * @param riskQcSearchIndexTableParam
     * @param workOrderExtTableParamList
     * @param qcWorkOrderParam
     * @return
     */
    private CfWorkOrderV2IndexSearchParam buildCfWorkOrderV2IndexSearchParam(WorkOrderTableParam woTableParam
            , RiskQcSearchIndexTableParam riskQcSearchIndexTableParam
            , List<WorkOrderExtTableParam> workOrderExtTableParamList
            , QcWorkOrderParam qcWorkOrderParam) {

        CfWorkOrderV2IndexSearchParam searchParam = new CfWorkOrderV2IndexSearchParam();
        searchParam.setWoTableParam(woTableParam);
        searchParam.setWorkOrderExtTableParamList(workOrderExtTableParamList);
        searchParam.setRiskQcSearchIndexTableParam(riskQcSearchIndexTableParam);
        searchParam.setFrom((qcWorkOrderParam.getPageNum() - 1) * qcWorkOrderParam.getPageSize());
        searchParam.setSize(qcWorkOrderParam.getPageSize());

        return searchParam;
    }
}
