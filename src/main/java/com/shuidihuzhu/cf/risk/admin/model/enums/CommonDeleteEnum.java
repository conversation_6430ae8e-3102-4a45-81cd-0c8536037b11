package com.shuidihuzhu.cf.risk.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用是否删除枚举
 * <AUTHOR>
 * @date 2020/7/16 21:28
 */
@AllArgsConstructor
@Getter
public enum CommonDeleteEnum {

    ENABLE(0, "未删除"),
    DISABLE(1, "已删除"),
    ;

    public static CommonDeleteEnum fromCode(int code){
        for (CommonDeleteEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }

        return null;
    }

    private Byte code;
    private String desc;

    CommonDeleteEnum(int code, String desc) {
        this.code = (byte)code;
        this.desc = desc;
    }

}
