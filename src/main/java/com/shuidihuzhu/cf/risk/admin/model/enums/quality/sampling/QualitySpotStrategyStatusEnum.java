package com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/6/16 10:56
 */
@Getter
public enum QualitySpotStrategyStatusEnum {

    ENABLE(0, "启用"),
    DISABLE(1, "弃用"),
    ;

    public static QualitySpotStrategyStatusEnum fromCode(int code){
        for (QualitySpotStrategyStatusEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }

        return null;
    }

    private Byte code;
    private String desc;

    QualitySpotStrategyStatusEnum(int code, String desc) {
        this.code = (byte)code;
        this.desc = desc;
    }
}
