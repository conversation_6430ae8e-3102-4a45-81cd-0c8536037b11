package com.shuidihuzhu.cf.risk.admin.controller.qc;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.data.platform.client.ExportLargeExcelClient;
import com.shuidihuzhu.cf.data.platform.model.MoreSheetExportParam;
import com.shuidihuzhu.cf.data.platform.model.SmartExportParam;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.param.RiskQcVideoInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcLog;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcCasInfoService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcDetailService;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.util.CosUploadUtil;
import com.shuidihuzhu.cf.risk.admin.util.ExcelFileUtil;
import com.shuidihuzhu.cf.risk.admin.util.SoundUtil;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Auther: subing
 * @Date: 2020/6/16
 */
@RestController
@RequestMapping("/api/cf-risk-admin/qc/detail")
@Slf4j
public class RiskQcDetailController {
    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Autowired
    private RiskQcCasInfoService riskQcCasInfoService;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private SoundUtil soundUtil;
    @Autowired
    private ExcelFileUtil excelFileUtil;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private QcAudioAsrService qcAudioAsrService;
    @Autowired
    private ExportLargeExcelClient exportLargeExcelClient;

    @PostMapping(path = "get-qc-result-config")
    @ApiOperation("质检结果选项")
    public Response<List<RiskQcResultConfigVo>> getQcResultConfig(@RequestParam(defaultValue = "false")boolean isDetail) {
        return NewResponseUtil.makeSuccess(riskQcDetailService.getQcResultConfig(isDetail));
    }

    @PostMapping(path = "add-info")
    @ApiOperation("保存质检结果")
    public Response<Integer> addInfo(@RequestParam(defaultValue = "0", required = false) long firstQcResult,
                                     @RequestParam String problemDescribeJson,
                                     @RequestParam int disposeAction,
                                     @RequestParam(defaultValue = "0", required = false) long secondQcResult,
                                     @RequestParam long workOrderId,
                                     @RequestParam(defaultValue = "0") int caseId,
                                     @RequestParam(defaultValue = "1") int qcType,
                                     @ApiParam("正确的驳回项") @RequestParam(required = false) String correctRefuseIds,
                                     @RequestParam(required = false) Integer scene
    ) {
        RiskQcResultVo riskQcResultVo = null;
        try {
            riskQcResultVo = JSONObject.parseObject(problemDescribeJson, new TypeReference<RiskQcResultVo>() {
            });
        } catch (Exception e) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (qcType == QcTypeEnum.INTERNAL_AUDIT.getCode() && !riskQcDetailService.judge(riskQcResultVo, disposeAction)) {
            return NewResponseUtil.makeFail("未勾选任何问题描述且未填写任何备注信息，不允许提交");
        }

        WorkOrderType workOrderType = null;
        Response<WorkOrderVO> workOrderVoResponse =  cfWorkOrderClient.getWorkOrderById(workOrderId);
        log.info("workOrderVoResponse:{}", workOrderVoResponse);
        if (workOrderVoResponse != null && workOrderVoResponse.ok() && Objects.nonNull(workOrderVoResponse.getData())) {
            WorkOrderVO workOrderVO = workOrderVoResponse.getData();
            if (workOrderVO.getHandleResult() == HandleResultEnum.undoing.getType()) {
                return NewResponseUtil.makeError(RiskAdminErrorCode.QC_ALLOCATION_WORK_ORDER_ERROR);
            }
            long userId = ContextUtil.getAdminLongUserId();
            if (workOrderVO.getOperatorId() != userId){
                return NewResponseUtil.makeError(RiskAdminErrorCode.QC_ALLOCATION_WORK_ORDER_ERROR);
            }
            workOrderType = Arrays.stream(WorkOrderType.values()).filter(a -> a.getType() == workOrderVO.getOrderType()).findFirst().orElse(null);
        }else {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_HANDEL_WORK_ORDER_ERROR);
        }
        int addInfo = riskQcDetailService.addInfo(firstQcResult, problemDescribeJson, secondQcResult, workOrderId,
                disposeAction, caseId, riskQcResultVo, qcType, workOrderType, correctRefuseIds, scene);
        if (addInfo > 0) {
            return NewResponseUtil.makeSuccess(addInfo);
        }
        return NewResponseUtil.makeError(RiskAdminErrorCode.QC_HANDEL_WORK_ORDER_ERROR);
    }

    @PostMapping(path = "get-recording-problems")
    @ApiOperation("获取录音问题信息")
    public Response<List<QcRecordingProblemsVo>> getRecordingProblems(@RequestParam long workOrderId) {
        return riskQcDetailService.getRecordingProblems(workOrderId);
    }

    @PostMapping(path = "judge-rc")
    @ApiOperation("计算复检结果")
    public Response<RiskQcResultVo> judgeRc(@RequestParam String problemDescribeJson,
                                            @RequestParam long workOrderId) {
        //校验参数
        RiskQcResultVo rcVo = null;
        try {
            rcVo = JSONObject.parseObject(problemDescribeJson, new TypeReference<RiskQcResultVo>() {
            });
        } catch (Exception e) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return riskQcDetailService.judgeRc(rcVo, workOrderId);
    }

    @PostMapping(path = "get-qc-result")
    @ApiOperation("返回保存质检结果")
    public Response<RiskQcResultVo> getQcResult(@RequestParam long workOrderId, @RequestParam(defaultValue = "1") int useScene,
                                                @RequestParam(defaultValue = "1") int qcType,
                                                @RequestParam(defaultValue = "0")int materialWorkOrderId) {
        return NewResponseUtil.makeSuccess(riskQcDetailService.getQcResult(workOrderId, useScene, qcType, materialWorkOrderId));
    }

    @PostMapping(path = "get-log")
    @ApiOperation("返回质检记录")
    public Response<List<RiskQcLog>> getLog(@RequestParam long workOrderId) {
        return NewResponseUtil.makeSuccess(riskQcDetailService.getLog(workOrderId));
    }


    @PostMapping(path = "get-initiate-info")
    @ApiOperation("返回发起信息")
    public Response<Map<String, Object>> getInitiateInfo(@RequestParam int caseId, @RequestParam("workOrderId") long workOrderId) {
        return riskQcCasInfoService.getInitiateInfo(caseId,workOrderId);
    }

    @PostMapping(path = "get-video-info")
    @ApiOperation("返回录音信息")
    public Response<List<RiskQcVideoVo>> getVideoInfo(@RequestParam long caseId,
                                                      @RequestParam(defaultValue = "0")long workOrderId) {
        return NewResponseUtil.makeSuccess(riskQcCasInfoService.getVideoInfo(caseId, workOrderId));
    }

    @PostMapping(path = "manual-process-asr")
    @ApiOperation("手动触发asr")
    public Response<Void> manualProcessAsr(@RequestParam long workOrderId) {
        log.info("手动触发asr {}", workOrderId);
        qcAudioAsrService.handleWorkOrderRecording(workOrderId);
        return NewResponseUtil.makeSuccess();
    }

    @RequestMapping(path = "get-video-analyze-result", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation("查看并下载音频详情")
    public Response<Void> getVideoAnalyzeResult(@RequestParam long id, HttpServletRequest request, HttpServletResponse response) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        log.info("id:{};adminUserId:{}", id, adminUserId);
        RiskQcMaterialsInfo riskQcMaterialsInfo = this.riskQcMaterialsInfoBiz.getById(id);
        RiskQcVideoVo riskQcVideoVo = JSONObject.parseObject(riskQcMaterialsInfo.getMaterialsValue(), RiskQcVideoVo.class);
        String url = CosUploadUtil.getCosSignWithUrl(riskQcVideoVo.getVideoUrl());
        Map<String, String> result = this.soundUtil.analyze(url);
        if(MapUtils.isEmpty(result)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        List<String> headList = Lists.newArrayList("音频区间", "数量", "占比");
        List<Object> dataList = new ArrayList<>();
        result.forEach((key, item) -> {
            Map<String, Object> data = Maps.newHashMap();
            List<String> values = Splitter.on(",").splitToList(item);
            data.put("音频区间", key);
            data.put("数量", values.get(0));
            data.put("占比", values.get(1));
            dataList.add(data);
        });
        List<SmartExportParam.Header> headers = headList.stream().map(r -> SmartExportParam.Header.builder().name(r).key(r).build()).collect(Collectors.toList());
        LinkedHashMap<String, SmartExportParam> sheetData = Maps.newLinkedHashMap();
        int turn = 1;
        for (List<Object> item : Lists.partition(dataList, 64500)) {
            sheetData.put("音频详情" + (turn++), SmartExportParam.create(
                    headers,
                    item
            ));
        }

        exportLargeExcelClient.writeExcelSheetSmart(ContextUtil.getAdminLongUserId(), MoreSheetExportParam.create(sheetData, "音频详情"));

        
//        List<String> dataList = Lists.newArrayList();
//        for (Map.Entry<String, String> entry : result.entrySet()) {
//            List<String> values = Splitter.on(",").splitToList(entry.getValue());
//            dataList.add(entry.getKey() + ";" + values.get(0) + ";" + values.get(1));
//        }
//        try (HSSFWorkbook wb = new HSSFWorkbook(); ByteArrayOutputStream os = new ByteArrayOutputStream()) {
//            HSSFCellStyle cellStyle = wb.createCellStyle();
//            cellStyle.setAlignment(HorizontalAlignment.CENTER);
//            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
//            cellStyle.setWrapText(true);
//            this.excelFileUtil.writeHSSFWorkbook(wb, cellStyle, "音频详情", headList, dataList,
//                    val-> Splitter.on(";").splitToList(val));
//            wb.write(os);
//            this.excelFileUtil.downloadFile(os, request, response, "音频详情");
//        } catch (Exception e) {
//            log.error("", e);
//        }
        return NewResponseUtil.makeSuccess(null);
    }

    @Deprecated
    @PostMapping(path = "add-checked-video")
    @ApiOperation("保存勾选的录音")
    public Response<Integer> addCheckedVideo(@RequestParam long workOrderId,
                                             @RequestParam String checkedIds,
                                             @RequestParam boolean againQc){
        int result = riskQcDetailService.addCheckedVideo(workOrderId, checkedIds, againQc);
        return NewResponseUtil.makeSuccess(result);
    }

    @PostMapping(path = "add-checked-video-v2")
    @ApiOperation("保存勾选的录音")
    public Response<Integer> addCheckedVideoV2(@RequestBody RiskQcVideoInfoModel param,
                                             @RequestParam boolean againQc){
        int result = riskQcDetailService.addCheckedVideoV2(param, againQc);
        return NewResponseUtil.makeSuccess(result);
    }


    @PostMapping(path = "get-qc-work-order")
    public Response<RiskQcWorkOrderVo> getQcWorkOrder(@RequestParam long rcWorkOrderId){
        return NewResponseUtil.makeSuccess(riskQcDetailService.getQcWorkOrderVO(rcWorkOrderId));
    }

    @PostMapping(path = "get-video-info-v2")
    @ApiOperation("返回录音信息")
    public Response<List<WorkOrderRecordingModel>> getVideoInfoV2(@RequestParam(defaultValue = "0")long qcWorkOrderId) {
        return NewResponseUtil.makeSuccess(riskQcCasInfoService.getVideoInfoV2(qcWorkOrderId));
    }

}
