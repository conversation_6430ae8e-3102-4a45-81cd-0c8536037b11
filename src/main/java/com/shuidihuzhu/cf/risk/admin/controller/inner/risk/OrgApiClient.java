package com.shuidihuzhu.cf.risk.admin.controller.inner.risk;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    name = "organization-api",
    fallback = OrgApiClient.Fallback.class
)
public interface OrgApiClient {
    @GetMapping("innerapi/organization-api/get/user-match-info")
    UserMatchInfoResponse getUserMatchInfo(@RequestParam("phone") String phone, @RequestParam("name") String name, @RequestParam("sign") String sign);

    @Slf4j
    @Component
    class Fallback implements OrgApiClient {

        @Override
        public UserMatchInfoResponse getUserMatchInfo(String phone, String name, String sign) {
            log.error("getUserMatchInfo error");
            return null;
        }
    }
}
