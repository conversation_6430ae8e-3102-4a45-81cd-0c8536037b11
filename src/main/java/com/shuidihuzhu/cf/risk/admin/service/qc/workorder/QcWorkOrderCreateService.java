package com.shuidihuzhu.cf.risk.admin.service.qc.workorder;

import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface QcWorkOrderCreateService {
    OperationResult<Void> promoteQcWorkOrderCreate(int caseId, int userId) throws Exception;

    Response<Void> bindWorkOrderIdRecordingUniqueId(Long workOrderId, String recordingUniqueId, Long userId);
}
