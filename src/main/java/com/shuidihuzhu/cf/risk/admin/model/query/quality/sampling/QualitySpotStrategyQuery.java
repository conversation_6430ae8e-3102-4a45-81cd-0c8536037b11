package com.shuidihuzhu.cf.risk.admin.model.query.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.model.query.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15 20:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QualitySpotStrategyQuery extends PageQuery {


    @ApiModelProperty("质检对象Id")
    private Long qualitySpotType;

    @ApiModelProperty("场景id")
    private Long scene;

    private List<Long> sceneList;

    @ApiModelProperty("策略名称")
    private String strategyName;

    @ApiModelProperty(value = "状态")
    @Range(min = 0, max = 1, message = "状态值不正确")
    private Byte status;

    @ApiModelProperty("最新操作人")
    private String operateName;

}
