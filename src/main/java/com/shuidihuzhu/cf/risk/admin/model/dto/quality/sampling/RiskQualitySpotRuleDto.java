package com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling;

import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotRuleInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/14 16:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskQualitySpotRuleDto {

    /**
     * 策略规则列表
     */
    private List<QualitySpotRuleInfo> ruleSamplingLevel;

    /**
     * 执行方式
     * @see  com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotExecuteModelEnum
     */
    private int executeMode;


}
