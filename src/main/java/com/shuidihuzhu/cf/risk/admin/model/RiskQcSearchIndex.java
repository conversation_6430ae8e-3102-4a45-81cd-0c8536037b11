package com.shuidihuzhu.cf.risk.admin.model;

import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-15 14:29
 **/
@Data
public class RiskQcSearchIndex {
    /**
     * id
     */
    private long id;

    private long qcId;
    /**
     * 案例id
     */
    private long caseId;
    /**
     * 工单id
     */
    private long workOrderId;
    /**
     * 质检类型
     */
    private int qcType;
    /**
     * 被质疑人组织
     */
    private String organization;
    /**
     * 被质检人标识
     */
    private String qcUniqueCode;
    /**
     * 被质检人姓名
     */
    private String qcByName;
    /**
     * 质检结果
     */
    private long qcResult;

    /**
     * 二级质检结果
     */
    private long qcResultSecond;
    /**
     * 质检问题类型
     */
    private String questionType;
    /**
     * 案例发起人id
     */
    private long userId;
    /**
     * 命中的规则名称
     */
    private String ruleName;

    /**
     * 登记手机号
     */
    private String registerMobileEncrypt;
    /**
     * 服务环节
     */
    private int serviceStage;
    /**
     * 工作内容
     */
    private int jobContent;
    /**
     * 微信1v1任务id
     */
    private long taskId;
    /**
     * 代录入id
     */
    private long materialId;


    //外呼字段

    /**
     * 外呼服务一级标签
     */
    private long firstLevelLabel;
    /**
     * 外呼服务二级标签
     */
    private long twoLevelLabel;
    /**
     * 外呼服务线索渠道
     */
    private String callCluesChannel;
    /**
     * 外呼服务任务状态
     */
    private int callTaskStatus;

    //材审字段

    /**
     * 材审工单通话状态
     * @see com.shuidihuzhu.cf.risk.admin.model.enums.MaterialWorkOrderCallStatusEnum
     */
    private int callStatus;
    /**
     * 材审工单处理状态
     * @see com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum
     */
    private int handleResult;

    private String firstPropertyIds;

    /**
     * 医院科室id
     */
    private long hospitalDeptId;

    /**
     * 被质检工单id
     */
    private long sourceWorkOrderId;

    /**
     * 被质检工单类型
     */
    private int sourceWorkOrderType;

}
