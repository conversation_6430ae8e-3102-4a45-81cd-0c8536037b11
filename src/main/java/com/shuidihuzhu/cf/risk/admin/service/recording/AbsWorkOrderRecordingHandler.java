package com.shuidihuzhu.cf.risk.admin.service.recording;


import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.admin.dao.WorkOrderRecordingMapper;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingDO;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.util.CosUploadUtil;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: wanghui
 * @create: 2022/3/25 下午5:23
 * 工单详情页录音处理逻辑
 */
@Slf4j
public abstract class AbsWorkOrderRecordingHandler implements WorkOrderRecordingHandler {
    @Autowired
    private WorkOrderRecordingMapper workOrderRecordingMapper;
    @Autowired
    private AiAsrDelegate aiAsrDelegate;

    /**
     * 处理工单录音的主入口方法
     *
     * 该方法是工单录音处理的核心流程，负责：
     * 1. 获取工单对应的通话录音记录
     * 2. 将录音记录保存到数据库
     * 3. 启动AI语音识别分析
     *
     * @param workOrderId 工单ID，用于标识需要处理录音的工单
     */
    public void handleWorkOrderRecording(long workOrderId){
        log.info("handleWorkOrderRecording orderType:{} workOrderId:{}", JSON.toJSONString(this.getWorkOrderType()), workOrderId);

        // 步骤1: 根据工单ID获取通话录音文件列表
        // 不同类型的工单会有不同的录音获取策略，由子类实现具体逻辑
        List<WorkOrderRecordingModel.CallRecordModel> callRecordModels = listCallRecordModels(workOrderId);

        // 步骤2: 将获取到的录音记录保存到数据库中
        // 如果数据库中已存在该工单的录音记录，则不会重复插入
        OpResult<List<WorkOrderRecordingDO>> listOpResult = saveCallRecordModels2DB(workOrderId, callRecordModels);

        // 步骤3: 检查保存结果，如果没有有效的录音数据则结束处理
        if (CollectionUtils.isEmpty(listOpResult.getData())) {
            log.info("handleWorkOrderRecording saveCallRecordModels2DB:{}", listOpResult);
            return;
        }

        // 步骤4: 启动AI语音识别分析流程
        // 对保存成功的录音记录进行异步AI分析
        startAsrRecordingAnalyse(workOrderId, listOpResult.getData());
    }

    /**
     * 启动AI语音识别分析流程
     *
     * 该方法负责对录音文件进行AI语音识别分析：
     * 1. 过滤出有效的录音记录（接通状态为200且有录音文件）
     * 2. 为录音URL生成带有长时效的COS签名（32小时）
     * 3. 调用AI语音识别服务进行异步分析
     *
     * @param workOrderId 工单ID
     * @param workOrderRecordings 需要分析的录音记录列表
     */
    private void startAsrRecordingAnalyse(long workOrderId, List<WorkOrderRecordingDO> workOrderRecordings) {
        // 遍历所有录音记录进行AI分析
        for (WorkOrderRecordingDO workOrderRecordingDO : workOrderRecordings) {
            // 数据有效性检查：过滤掉无效的录音记录
            if (workOrderRecordingDO == null
                    || workOrderRecordingDO.getRecordingExt() == null
                    || workOrderRecordingDO.getPhoneStatus() == null
                    // 只处理接通状态为200（成功接通）的录音
                    || workOrderRecordingDO.getPhoneStatus() != 200
                    // 必须有录音文件URL
                    || StringUtils.isBlank(workOrderRecordingDO.getRecordingExt().getVideoUrl())) {
                continue;
            }

            // 为录音URL生成AI分析专用的COS签名（32小时有效期）
            // 比普通签名时间更长，确保AI分析任务有足够时间完成
            String url = CosUploadUtil.getAsrCosSignWithUrl(workOrderRecordingDO.getVideoUrl());

            // 调用AI语音识别服务进行异步分析
            // 参数说明：工单ID、录音记录ID、签名URL、处理类型枚举、是否使用紫燕ASR
            aiAsrDelegate.analyse(workOrderId, workOrderRecordingDO.getId(), url, getAiHandleTypeEnum(), false);
        }
    }

    /**
     * 将通话录音记录保存到数据库
     *
     * 该方法负责将获取到的通话录音记录持久化到数据库中：
     * 1. 参数有效性检查
     * 2. 防重复插入检查（如果数据库中已存在该工单的录音记录则直接返回）
     * 3. 数据转换并批量插入数据库
     *
     * @param workOrderId 工单ID
     * @param callRecordModels 通话录音记录模型列表
     * @return OpResult<List<WorkOrderRecordingDO>> 操作结果，包含保存后的数据库记录
     */
    private OpResult<List<WorkOrderRecordingDO>> saveCallRecordModels2DB(long workOrderId, List<WorkOrderRecordingModel.CallRecordModel> callRecordModels) {
        // 参数有效性检查：录音记录列表不能为空
        if (CollectionUtils.isEmpty(callRecordModels)) {
            log.info("saveCallRecordModels2DB callRecordModels isEmpty");
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        // 防重复插入检查：查询数据库中是否已存在该工单的录音记录
        List<WorkOrderRecordingDO> workOrderRecordingInDB = workOrderRecordingMapper.listByWorkOrderId(workOrderId);
        if (CollectionUtils.isNotEmpty(workOrderRecordingInDB)) {
            // 如果数据库中已有录音记录，直接返回现有数据，避免重复插入
            log.info("saveCallRecordModels2DB workOrderId:{} already exists in DB, skip insert", workOrderId);
            return OpResult.createSucResult(workOrderRecordingInDB);
        }

        // 数据转换：将CallRecordModel转换为WorkOrderRecordingDO数据库实体
        List<WorkOrderRecordingDO> workOrderRecordingDOS = callRecordModels.stream()
                .map(callRecordModel -> new WorkOrderRecordingDO(workOrderId, new WorkOrderRecordingModel(callRecordModel, null, null)))
                .collect(Collectors.toList());

        // 批量插入数据库
        if (CollectionUtils.isNotEmpty(workOrderRecordingDOS)) {
            workOrderRecordingMapper.batchInsert(workOrderRecordingDOS);
            log.info("saveCallRecordModels2DB workOrderId:{} insert {} records", workOrderId, workOrderRecordingDOS.size());
        }

        return OpResult.createSucResult(workOrderRecordingDOS);
    }

    /**
     * 获取工单对应的通话录音记录列表
     *
     * 抽象方法，由具体的工单类型处理器实现。
     * 不同类型的工单有不同的录音获取策略和数据源。
     *
     * @param workOrderId 工单ID
     * @return 通话录音记录模型列表
     */
    public abstract List<WorkOrderRecordingModel.CallRecordModel> listCallRecordModels(long workOrderId) ;

    /**
     * 获取AI处理类型枚举
     *
     * 抽象方法，由具体的工单类型处理器实现。
     * 不同类型的工单需要使用不同的AI分析策略和风控词库场景。
     *
     * @return AI处理类型枚举，决定使用哪种分析策略
     */
    public abstract AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum();

    /**
     * 获取工单类型
     *
     * 抽象方法，由具体的工单类型处理器实现。
     * 用于标识当前处理器负责处理的工单类型。
     *
     * @return 工单类型枚举
     */
    public abstract WorkOrderType getWorkOrderType();

    /**
     * 处理Feign调用响应结果（Response类型）
     *
     * 统一处理Feign远程调用的响应结果：
     * 1. 记录调用日志
     * 2. 检查响应状态
     * 3. 失败时抛出运行时异常
     *
     * @param feignName Feign客户端名称，用于日志记录
     * @param response Feign调用的响应结果
     * @return 验证通过的响应对象
     * @throws RuntimeException 当响应为null或状态不正常时抛出
     */
    public Response handleFeignResponse(String feignName, Response response) {
        log.info("{} result:{}", feignName, response);
        if (response == null || response.notOk()) {
            throw new RuntimeException(feignName + " fail " + response);
        }
        return response;
    }

    /**
     * 处理Feign调用响应结果（FeignResponse类型）
     *
     * 统一处理Feign远程调用的响应结果：
     * 1. 记录调用日志
     * 2. 检查响应状态
     * 3. 失败时抛出运行时异常
     *
     * @param feignName Feign客户端名称，用于日志记录
     * @param response Feign调用的响应结果
     * @return 验证通过的响应对象
     * @throws RuntimeException 当响应为null或状态不正常时抛出
     */
    public FeignResponse handleFeignResponse(String feignName, FeignResponse response) {
        log.info("{} result:{}", feignName, response);
        if (response == null || response.notOk()) {
            throw new RuntimeException(feignName + " fail " + response);
        }
        return response;
    }

}
