package com.shuidihuzhu.cf.risk.admin.service.recording;


import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.admin.dao.WorkOrderRecordingMapper;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingDO;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.util.CosUploadUtil;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: wanghui
 * @create: 2022/3/25 下午5:23
 * 工单详情页录音处理逻辑
 */
@Slf4j
public abstract class AbsWorkOrderRecordingHandler implements WorkOrderRecordingHandler {
    @Autowired
    private WorkOrderRecordingMapper workOrderRecordingMapper;
    @Autowired
    private AiAsrDelegate aiAsrDelegate;

    /**
     * @description: 根据workOrderId 处理工单录音模块数据
     * @param: [workOrderId]
     * @return: java.util.List<com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel>
     */
    public void handleWorkOrderRecording(long workOrderId){
        log.info("handleWorkOrderRecording orderType:{} workOrderId:{}", JSON.toJSONString(this.getWorkOrderType()), workOrderId);
        // 根据工单id获取录音文件
        List<WorkOrderRecordingModel.CallRecordModel> callRecordModels = listCallRecordModels(workOrderId);
        OpResult<List<WorkOrderRecordingDO>> listOpResult = saveCallRecordModels2DB(workOrderId, callRecordModels);
        if (CollectionUtils.isEmpty(listOpResult.getData())) {
            log.info("handleWorkOrderRecording saveCallRecordModels2DB:{}", listOpResult);
            return;
        }
        startAsrRecordingAnalyse(workOrderId, listOpResult.getData());
    }

    // 异步请求ai接口 分析录音
    private void startAsrRecordingAnalyse(long workOrderId, List<WorkOrderRecordingDO> workOrderRecordings) {
        for (WorkOrderRecordingDO workOrderRecordingDO : workOrderRecordings) {
            if (workOrderRecordingDO == null
                    || workOrderRecordingDO.getRecordingExt()==null
                    || workOrderRecordingDO.getPhoneStatus() == null
                    || workOrderRecordingDO.getPhoneStatus() != 200
                    || StringUtils.isBlank(workOrderRecordingDO.getRecordingExt().getVideoUrl())) {
                continue;
            }
            String url = CosUploadUtil.getAsrCosSignWithUrl(workOrderRecordingDO.getVideoUrl());
            aiAsrDelegate.analyse(workOrderId, workOrderRecordingDO.getId(), url, getAiHandleTypeEnum(), false);
        }
    }

    /**
     * 保存录音，且返回表里的数据
     * @param workOrderId
     * @param callRecordModels
     * @return
     */
    private OpResult<List<WorkOrderRecordingDO>> saveCallRecordModels2DB(long workOrderId, List<WorkOrderRecordingModel.CallRecordModel> callRecordModels) {
        if (CollectionUtils.isEmpty(callRecordModels)) {
            log.info("saveCallRecordModels2DB callRecordModels isEmpty");
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        // 检查是否已有数据 有数据不再插入
        List<WorkOrderRecordingDO> workOrderRecordingInDB = workOrderRecordingMapper.listByWorkOrderId(workOrderId);
        if (CollectionUtils.isNotEmpty(workOrderRecordingInDB)) {
            // 只要有值 就不再插入
            return OpResult.createSucResult(workOrderRecordingInDB);
        }
        List<WorkOrderRecordingDO> workOrderRecordingDOS = callRecordModels.stream()
                .map(callRecordModel -> new WorkOrderRecordingDO(workOrderId, new WorkOrderRecordingModel(callRecordModel, null, null))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(workOrderRecordingDOS)) {
            workOrderRecordingMapper.batchInsert(workOrderRecordingDOS);
        }
        return OpResult.createSucResult(workOrderRecordingDOS);
    }

    public abstract List<WorkOrderRecordingModel.CallRecordModel> listCallRecordModels(long workOrderId) ;

    public abstract AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum();

    public abstract WorkOrderType getWorkOrderType();

    public Response handleFeignResponse(String feignName, Response response) {
        log.info("{} result:{}", feignName, response);
        if (response == null || response.notOk()) {
            throw new RuntimeException(feignName + " fail " + response);
        }
        return response;
    }
    public FeignResponse handleFeignResponse(String feignName, FeignResponse response) {
        log.info("{} result:{}", feignName, response);
        if (response == null || response.notOk()) {
            throw new RuntimeException(feignName + " fail " + response);
        }
        return response;
    }

}
