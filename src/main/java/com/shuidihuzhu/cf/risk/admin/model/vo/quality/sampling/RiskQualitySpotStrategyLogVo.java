package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyOperateTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategyLog;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "策略操作日志")
public class RiskQualitySpotStrategyLogVo {

    public RiskQualitySpotStrategyLogVo(RiskQualitySpotStrategyLog riskQualitySpotStrategyLog){
        this.setOperateName(riskQualitySpotStrategyLog.getOperateName());
        this.setOperateType(QualitySpotStrategyOperateTypeEnum.fromCode(riskQualitySpotStrategyLog.getOperateType()).getDesc());
        this.setUpdateTime(DateUtil.getDate2LStr(riskQualitySpotStrategyLog.getUpdateTime()));
    }

    @ApiModelProperty("操作类型")
    private String operateType;

    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("操作时间")
    private String updateTime;

}