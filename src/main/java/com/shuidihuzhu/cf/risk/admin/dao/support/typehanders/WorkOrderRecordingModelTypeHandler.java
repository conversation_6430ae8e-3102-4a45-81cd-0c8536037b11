package com.shuidihuzhu.cf.risk.admin.dao.support.typehanders;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @author: wanghui
 * @create: 2022/3/25 下午4:31
 */
public class WorkOrderRecordingModelTypeHandler extends BaseTypeHandler<WorkOrderRecordingModel> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, WorkOrderRecordingModel parameter, JdbcType jdbcType) throws SQLException {
        if (parameter==null) {
            ps.setString(i, null);
        }else {
            ps.setString(i, JSON.toJSONString(parameter));
        }
    }

    @Override
    public WorkOrderRecordingModel getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String result = rs.getString(columnName);
        if (StringUtils.isBlank(result)) {
            return null;
        }
        return JSON.parseObject(result, WorkOrderRecordingModel.class);
    }

    @Override
    public WorkOrderRecordingModel getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String result = rs.getString(columnIndex);
        if (StringUtils.isBlank(result)) {
            return null;
        }
        return JSON.parseObject(result, WorkOrderRecordingModel.class);    }

    @Override
    public WorkOrderRecordingModel getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String result = cs.getString(columnIndex);
        if (StringUtils.isBlank(result)) {
            return null;
        }
        return JSON.parseObject(result, WorkOrderRecordingModel.class);
    }
}
