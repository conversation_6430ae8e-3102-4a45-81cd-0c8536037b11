package com.shuidihuzhu.cf.risk.admin.model.query.list;

import com.shuidihuzhu.cf.risk.admin.model.query.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/7/28 16:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListDepartmentQuery extends PageQuery {

    @ApiModelProperty("医院所在省份")
    private String province;

    @ApiModelProperty("医院所在城市")
    private String city;

    @ApiModelProperty("医院code码")
    private String hospitalCode;

    @ApiModelProperty("医院名称")
    private String hospitalName;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("科室号码")
    private String landline;

    @ApiModelProperty("科室")
    private String department;

    @ApiModelProperty("名单类型：1 黑名单，2 灰名单，3 白名单")
    private Byte listType;

}
