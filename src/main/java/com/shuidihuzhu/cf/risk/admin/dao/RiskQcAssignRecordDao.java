package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcAssignRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcAssignRecordDao {

    int addList(List<RiskQcAssignRecord> assignRecordList);

    Integer countRangeByCode(@Param("uniqueCode") String uniqueCode,
                             @Param("orderType") int orderType,
                             @Param("startTime") String startTime,
                             @Param("endTime") String endTime);

//    Map<String, Integer> groupCountRangeByCode(@Param("uniqueCodes") List<String> uniqueCodes,
//                                               @Param("orderType") int orderType,
//                                               @Param("startTime") String startTime,
//                                               @Param("endTime") String endTime);
}
