package com.shuidihuzhu.cf.risk.admin.controller;


import com.shuidihuzhu.cf.risk.admin.biz.DiscussionCheckBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(("/api/cf-risk-admin/discussion-check"))
public class DiscussionCheckController {
    @Autowired
    private DiscussionCheckBiz checkService;

    @ApiOperation("评议审核通过")
    @RequestMapping(path = "/pass", method = RequestMethod.POST)
    @RequiresPermission("discussion:pass")
    public Response pass(int caseId, String infoUuid) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        return checkService.pass(Math.toIntExact(adminUserId), caseId, infoUuid);
    }

    @ApiOperation("评议审核拒绝")
    @RequestMapping(path = "/refuse", method = RequestMethod.POST)
    @RequiresPermission("discussion:refuse")
    public Response refuse(int caseId, String infoUuid, String reason) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        return checkService.refuse(Math.toIntExact(adminUserId), caseId, infoUuid, reason);
    }

    @ApiOperation("评议审核拒绝列表")
    @RequestMapping(path = "/refuse-list", method = RequestMethod.POST)
    public Response refuseList(@RequestParam(name = "caseId")int caseId,
                               @RequestParam(name = "pageJson")String pageJson) {
        return NewResponseUtil.makeSuccess(checkService.refuseList(caseId, pageJson));
    }
}
