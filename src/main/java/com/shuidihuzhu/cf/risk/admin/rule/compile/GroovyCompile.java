package com.shuidihuzhu.cf.risk.admin.rule.compile;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.risk.admin.rule.model.Rule;
import lombok.extern.slf4j.Slf4j;
import org.everit.json.schema.Schema;
import org.everit.json.schema.ValidationException;
import org.everit.json.schema.loader.SchemaLoader;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/18 23:21
 */
@Service
@Slf4j
public class GroovyCompile implements ICriterionCompile<String>{

    static {
        try (InputStream inputStream = GroovyCompile.class.getResourceAsStream("/ruleJsonSchema.json")) {
            JSONObject schemaJson = new JSONObject(new JSONTokener(inputStream));
            SCHEMA = SchemaLoader.load(schemaJson);
        } catch (IOException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }

    private static final Schema SCHEMA;

    @Resource
    private RulesCompile rulesCompile;

    @Override
    public String compileCriterion(String rulesJson) {
        //1. 验证json格式是否符合规范要求
        try {
            SCHEMA.validate(new JSONArray(rulesJson));
        } catch (ValidationException validationException) {
            throw new IllegalArgumentException(Joiner.on("\n").join(validationException.getAllMessages()));
        }
        List<Rule> rules = JSON.parseArray(rulesJson, Rule.class);
        //2. 对规则进行排序
        rules.sort(Comparator.comparingInt(Rule::getPriority));
        //3. 挑选启用状态的规则
        List<Rule> validRules = rules.stream().filter(rule -> rule.getStatus() == 0).collect(Collectors.toList());
        //4. 拼接groovy脚本
        // TODO: houys 2020/6/19 4. 可以针对调用的函数做import操作
        return  "class Rule {"                                       +"\n" +
                "    static def check(def data) {"                   +"\n" +
                          rulesCompile.compileCriterion(validRules)        +
                "    }"                                              +"\n" +
                "}";
    }

}
