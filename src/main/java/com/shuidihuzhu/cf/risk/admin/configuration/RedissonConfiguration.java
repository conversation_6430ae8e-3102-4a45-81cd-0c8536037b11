package com.shuidihuzhu.cf.risk.admin.configuration;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.util.redisson.RedissonHandlerWrapper;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by z<PERSON>yo<PERSON> on 2017/12/12.
 */
@Configuration
public class RedissonConfiguration {
//
//    @Bean("cfRiskRedissonHandler")
//    @ConfigurationProperties("redisson-handler.cf-risk")
//    public RedissonHandler cfRiskRedissonHandler(){
//        return new RedissonHandlerWrapper();
//    }

    @Bean("cfRiskRedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-risk-admin.cf-risk-admin")
    public RedissonHandler redissonHandler() {
        return new RedissonHandlerWrapper();
    }

}
