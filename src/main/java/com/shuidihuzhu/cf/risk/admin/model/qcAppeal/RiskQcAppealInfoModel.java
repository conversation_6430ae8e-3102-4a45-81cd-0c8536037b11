package com.shuidihuzhu.cf.risk.admin.model.qcAppeal;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.model.CfGwAppealMaterialModel;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestNoticeModel;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/11/13
 */
@Data
public class RiskQcAppealInfoModel {
    /**
     * 申诉问题
     */
    private List<RiskQcAppealProblemModel> riskQcAppealProblemModels = Lists.newArrayList();
    /**
     * 文字描述
     */
    private String problemDescription;
    /**
     * 图片
     **/
    private List<String> imageUrlList = Lists.newArrayList();
    /**
     * 视频
     **/
    private List<String> videoUrlList = Lists.newArrayList();
    /**
     * 音频
     **/
    private List<String> audioUrlList = List.of();
    /**
     * 评论
     */
    private String comment;
    /**
     * 申诉人
     */
    private String appealName;
    /**
     *  经理审批内容
     */
    private String leaderRemark;

    public static RiskQcAppealInfoModel buildModel(CfGwReplaceInputQualityTestFeedbackModel feedbackModel) {
        if (feedbackModel == null) {
            return null;
        }
        RiskQcAppealInfoModel riskQcAppealResultModel = new RiskQcAppealInfoModel();
        riskQcAppealResultModel.setLeaderRemark(feedbackModel.getLeaderRemark());
        riskQcAppealResultModel.setAppealName(feedbackModel.getName());
        CfGwAppealMaterialModel cfGwAppealMaterialModel = feedbackModel.getCfGwAppealMaterialModel();
        if (cfGwAppealMaterialModel == null) {
            return riskQcAppealResultModel;
        }
        List<CfGwReplaceInputQualityTestNoticeModel.IssueInfo> issueInfos =
                Optional.ofNullable(cfGwAppealMaterialModel.getIssueInfos()).orElse(Lists.newArrayList());
        issueInfos = issueInfos.stream()
                .filter(t -> t.getFeedbackStatus() == CfGwReplaceInputQualityTestNoticeModel.FeedbackStatusEnum.appeal.getStatus())
                .collect(Collectors.toList());
        List<RiskQcAppealProblemModel> riskQcAppealProblemModels = Lists.newArrayList();
        issueInfos.forEach(issueInfo -> {
            RiskQcAppealProblemModel riskQcAppealProblemModel = RiskQcAppealProblemModel.buildInfo(issueInfo);
            if (riskQcAppealProblemModel != null) {
                riskQcAppealProblemModels.add(riskQcAppealProblemModel);
            }
        });
        riskQcAppealResultModel.setRiskQcAppealProblemModels(riskQcAppealProblemModels);
        riskQcAppealResultModel.setProblemDescription(cfGwAppealMaterialModel.getExplainIssue());
        riskQcAppealResultModel.setImageUrlList(cfGwAppealMaterialModel.getImageUrlList());
        riskQcAppealResultModel.setVideoUrlList(cfGwAppealMaterialModel.getVideoUrlList());
        riskQcAppealResultModel.setAudioUrlList(cfGwAppealMaterialModel.getAudioUrlList());
        return riskQcAppealResultModel;
    }
}
