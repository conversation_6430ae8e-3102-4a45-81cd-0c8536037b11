package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.cache.list.CityCodeCache;
import com.shuidihuzhu.cf.risk.client.admin.RiskCityAreaClient;
import com.shuidihuzhu.cf.risk.model.RiskCityAreaDto;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/21 19:37
 */
@RestController
@Slf4j
public class RiskCityAreaInnerController implements RiskCityAreaClient {

    @Resource
    private CityCodeCache cityCodeCache;

    @Override
    public Response<RiskCityAreaDto> getCityArea(String province, String city, int provinceId, int cityId, String customAreaCode) {
        log.info("查询城市区号，provence:{}, city:{}, provenceId:{}, cityId:{}, customAreaCode:{}",
                province, city, provinceId, cityId, customAreaCode);
        RiskCityAreaDto cityCodeDto = cityCodeCache.getCityCodeDto(province, city);
        if (cityCodeDto == null && provinceId > 0 && cityId > 0) {
            log.info("通过省市名称查询失败，再通过id查询一次");
            cityCodeDto = cityCodeCache.getCityCodeDtoById(provinceId, cityId);
        }
        if (cityCodeDto != null) {
            cityCodeDto.setHadCustomAreaCode(cityCodeCache.getCityCodeDtoByAreaCode(customAreaCode) != null);
        }
        return NewResponseUtil.makeSuccess(cityCodeDto);
    }


}
