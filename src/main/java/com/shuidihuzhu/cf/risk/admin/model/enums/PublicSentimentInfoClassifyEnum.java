package com.shuidihuzhu.cf.risk.admin.model.enums;

public enum PublicSentimentInfoClassifyEnum {
    //信息分类
    ANLI(1, "案例相关"),
    PINGTAI(2, "平台相关"),
    OTHER(0,"其他"),
    ;

    int code;
    String description;

    PublicSentimentInfoClassifyEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (PublicSentimentInfoClassifyEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
