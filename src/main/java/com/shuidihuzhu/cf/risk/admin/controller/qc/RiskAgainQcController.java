package com.shuidihuzhu.cf.risk.admin.controller.qc;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcResultVo;
import com.shuidihuzhu.cf.risk.admin.service.RiskAgainQcService;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Auther: subing
 * @Date: 2020/10/28
 */
@RestController
@RequestMapping("/api/cf-risk-admin/qc/again")
@Slf4j
public class RiskAgainQcController {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private RiskAgainQcService riskAgainQcService;

    @PostMapping(path = "add-info")
    @ApiOperation("重新质检保存结果")
    public Response<Integer> addInfo(@RequestParam String problemDescribeJson,
                                     @RequestParam long workOrderId,
                                     @RequestParam(defaultValue = "1", required = false) int disposeAction,
                                     @RequestParam(defaultValue = "1") int qcType,
                                     @RequestParam(defaultValue = "0", required = false) int firstQcResult,
                                     @RequestParam(defaultValue = "0", required = false) int secondQcResult,
                                     @ApiParam("正确的驳回项") @RequestParam(required = false) String correctRefuseIds,
                                     @RequestParam(required = false) Integer scene
    ) {
        RiskQcResultVo riskQcResultVo = null;
        try {
            riskQcResultVo = JSONObject.parseObject(problemDescribeJson, new TypeReference<RiskQcResultVo>() {
            });
        } catch (Exception e) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (qcType == QcTypeEnum.INTERNAL_AUDIT.getCode() && !riskAgainQcService.judge(riskQcResultVo, disposeAction)) {
            return NewResponseUtil.makeFail("未勾选任何问题描述且未填写任何备注信息，不允许提交");
        }

        Response<WorkOrderVO> workOrderVoResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        log.info("workOrderVoResponse:{}", workOrderVoResponse);
        WorkOrderType workOrderType = null;
        if (workOrderVoResponse != null && workOrderVoResponse.ok() && Objects.nonNull(workOrderVoResponse.getData())) {
            WorkOrderVO workOrderVO = workOrderVoResponse.getData();
            if (workOrderVO.getHandleResult() == HandleResultEnum.undoing.getType()) {
                return NewResponseUtil.makeError(RiskAdminErrorCode.QC_ALLOCATION_WORK_ORDER_ERROR);
            }
            if (workOrderVO.getHandleResult() == HandleResultEnum.done.getType()){
                return NewResponseUtil.makeError(RiskAdminErrorCode.QC_WORK_ORDER_FIGURE_OUT);
            }
            long userId = ContextUtil.getAdminLongUserId();
            if (workOrderVO.getOperatorId() != userId) {
                return NewResponseUtil.makeError(RiskAdminErrorCode.QC_ALLOCATION_WORK_ORDER_ERROR);
            }
            workOrderType = Arrays.stream(WorkOrderType.values()).filter(a -> a.getType() == workOrderVO.getOrderType()).findFirst().orElse(null);
        } else {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_HANDEL_WORK_ORDER_ERROR);
        }
        return riskAgainQcService.againQc(problemDescribeJson, workOrderId, firstQcResult, secondQcResult,
                riskQcResultVo, qcType, workOrderType, correctRefuseIds, scene);
    }


    @PostMapping(path = "assign")
    public Response<Integer> againAssign(@RequestParam long workOrderId, @RequestParam String reason, @RequestParam int assignId,
                                @RequestParam String qcName){
        int result = riskAgainQcService.againAssign(workOrderId, reason, assignId, qcName);
        return NewResponseUtil.makeSuccess(result);
    }


    @PostMapping(path = "check-status")
    public Response<Boolean> checkStatus(@RequestParam long workOrderId){
        boolean status = riskAgainQcService.checkStatus(workOrderId);
        return NewResponseUtil.makeSuccess(status);
    }

}
