package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcCheckedVideoInfoBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcCheckedVideoInfoDao;
import com.shuidihuzhu.cf.risk.admin.model.param.RiskQcVideoInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcCheckedVideoInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/11/11
 */
@Service
public class RiskQcCheckedVideoInfoBizImpl implements RiskQcCheckedVideoInfoBiz {
    @Autowired
    private RiskQcCheckedVideoInfoDao riskQcCheckedVideoInfoDao;

    @Override
    public int addInfo(long workOrderId, String checkedId) {
        if (workOrderId <= 0){
            return 0;
        }
        return riskQcCheckedVideoInfoDao.addInfo(workOrderId, checkedId);
    }

    @Override
    public RiskQcCheckedVideoInfo getByWorkOrderId(long workOrderId) {
        if (workOrderId <= 0){
            return null;
        }
        return riskQcCheckedVideoInfoDao.getByWorkOrderId(workOrderId);
    }

    @Override
    public RiskQcVideoInfoModel getInfoByWorkOrderId(long workOrderId) {
        RiskQcCheckedVideoInfo d = getByWorkOrderId(workOrderId);
        if (d == null) {
            return null;
        }
        RiskQcVideoInfoModel v = new RiskQcVideoInfoModel();
        v.setWorkOrderId(d.getWorkOrderId());

        List<Long> checkIds = Arrays.stream(StringUtils.split(StringUtils.trimToEmpty(d.getCheckedId()), ","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        v.setCheckedIds(checkIds);

        RiskQcCheckedVideoInfo.ExtInfo extInfo = JSON.parseObject(d.getExtInfo(), RiskQcCheckedVideoInfo.ExtInfo.class);
        if (extInfo == null) {
            return v;
        }
        v.setAsrCorrectIds(extInfo.getAsrCorrectIds());
        v.setAsrIncorrectIds(extInfo.getAsrIncorrectIds());
        return v;
    }

    @Override
    public int updateByWorkOrderId(String checkId, long workOrderId) {
        if (workOrderId <= 0){
            return 0;
        }
        return riskQcCheckedVideoInfoDao.updateByWorkOrderId(checkId, workOrderId);
    }

    @Override
    public List<RiskQcCheckedVideoInfo> findByWorkOrderId(List<Long> workOrderIdList) {
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return null;
        }
        return riskQcCheckedVideoInfoDao.findByWorkOrderId(workOrderIdList);
    }

    @Override
    public int add(RiskQcCheckedVideoInfo param) {
        return riskQcCheckedVideoInfoDao.add(param);
    }

    @Override
    public int update(RiskQcCheckedVideoInfo param) {
        return riskQcCheckedVideoInfoDao.update(param);
    }
}
