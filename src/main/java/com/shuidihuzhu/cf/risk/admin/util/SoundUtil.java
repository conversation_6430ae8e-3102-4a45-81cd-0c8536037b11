package com.shuidihuzhu.cf.risk.admin.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-09-02
 **/
@Slf4j
@Component
public class SoundUtil {

    public Map<String, String> analyze(String url) {
        OkHttpClient okClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();
        Request request = new Request.Builder().url(url).build();
        Response response = null;
        try {
            response = okClient.newCall(request).execute();
        } catch (Exception e) {
            log.error("", e);
        }
        if(response == null || !response.isSuccessful() || response.body() == null) {
            log.info("获取服务器数据失败");
            return Maps.newLinkedHashMap();
        }
        try (BufferedInputStream buffInputStream = new BufferedInputStream(response.body().byteStream());
             AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(buffInputStream)) {
            // 文件流
            //将AudioInputStream MP3文件 转换为PCM
            AudioFormat audioFormat = audioInputStream.getFormat();
            AudioFormat targetFormat = new AudioFormat(AudioFormat.Encoding.PCM_SIGNED, audioFormat.getSampleRate(), 16,
                    audioFormat.getChannels(), audioFormat.getChannels() * 2, audioFormat.getSampleRate(), false);
            AudioInputStream mp3audioStream = AudioSystem.getAudioInputStream(targetFormat, audioInputStream);
            return analyzeData(targetFormat, mp3audioStream);
        } catch (Exception e) {
            log.error("", e);
            return Maps.newLinkedHashMap();
        }
    }

    private Map<String, String> analyzeData(AudioFormat targetFormat, AudioInputStream mp3audioStream) throws Exception {
        // 指定时间的样本数
        double sec = 1000; // 要表示的时长(s)
        // 读取声音数据
        List<Integer> values = Lists.newArrayList();
        while (true) {
            // 1帧的大小
            int size = targetFormat.getFrameSize();
            byte[] data = new byte[size];
            int readedSize = mp3audioStream.read(data);
            // 读取失败时
            if (readedSize == -1) {
                break;
            }
            // SampleSizeInBits
            switch (targetFormat.getSampleSizeInBits()) {
                case 8:
                    values.add((int) data[0]);
                    break;
                case 16:
                    values.add((int) ByteBuffer.wrap(data).order(ByteOrder.LITTLE_ENDIAN).getShort());
                    break;
                default:
                    break;
            }
        }

        Map<String, String> result = Maps.newLinkedHashMap();
        int space = 1000;// 区间的间隔
        analyzeSpace(values, space, result);
        space = 100;// 区间的间隔
        List<Integer> value1000 = values.stream().filter(value -> value >= -1000 && value < 1000).collect(Collectors.toList());
        analyzeSpace(value1000, space, result);
        space = 10;// 区间的间隔
        List<Integer> value100 = value1000.stream().filter(value -> value >= -100 && value < 100).collect(Collectors.toList());
        analyzeSpace(value100, space, result);
        // 为0的数据
        long count = value100.stream().filter(value -> value == 0).count();
        double percent = MoneyUtil.divide("" + count, "" + values.size(), 4, BigDecimal.ROUND_HALF_UP).
                multiply(BigDecimal.valueOf(100)).doubleValue();
        result.put("0", count + "," + percent + "%");
        return result;
    }

    private void analyzeSpace(List<Integer> values, int space, Map<String, String> result) {
        Map<String, Long> map = values.stream().collect(Collectors.groupingBy((value) -> {
            return "[" + value / space * space + "," + (value / space + 1) * space + ")";// 分组规则
        }, Collectors.counting()));
        map = ListUtil.sortByValue(map);
        map.forEach((k, v) -> {
            double percent = MoneyUtil.divide("" + v, "" + values.size(), 4, BigDecimal.ROUND_HALF_UP).
                    multiply(BigDecimal.valueOf(100)).doubleValue();
            result.put(k, v + "," + percent + "%");
        });
    }

}
