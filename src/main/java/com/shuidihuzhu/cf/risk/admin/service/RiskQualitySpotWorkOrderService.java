package com.shuidihuzhu.cf.risk.admin.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotRuleBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotStrategyBiz;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.cf.risk.admin.model.query.quality.sampling.QualitySpotStrategyQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotRuleAdminDetailVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotWorkOrderDetailVo;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderQualitySpotClient;
import com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig;
import com.shuidihuzhu.client.cf.workorder.model.vo.RiskQualitySpotRuleDetailVo;
import com.shuidihuzhu.client.cf.workorder.model.vo.RiskQualitySpotRuleVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/20
 */
@Service
@Slf4j
public class RiskQualitySpotWorkOrderService {

    @Resource
    private QualitySpotStrategyBiz qualitySpotStrategyBiz;
    @Resource
    private QualitySpotRuleBiz qualitySpotRuleBiz;
    @Resource
    private CfWorkOrderQualitySpotClient cfWorkOrderQualitySpotClient;

    public List<RiskQualitySpotRuleVo> getRuleVoList(long scene) {
        QualitySpotStrategyQuery qualitySpotStrategyQuery = new QualitySpotStrategyQuery();
        qualitySpotStrategyQuery.setPageNo(1);
        qualitySpotStrategyQuery.setPageSize(100);
        qualitySpotStrategyQuery.setScene(scene);
        qualitySpotStrategyQuery.setStatus(QualitySpotStrategyStatusEnum.ENABLE.getCode());
        //获取所有改场景下的工单
        List<RiskQualitySpotStrategy>  spotStrategies  = qualitySpotStrategyBiz.listByQuery(qualitySpotStrategyQuery).getResult();
        if (CollectionUtils.isEmpty(spotStrategies)){
            return Lists.newArrayList();
        }
        //获取策略对应的有效的规则
        List<RiskQualitySpotRule> riskQualitySpotRules = qualitySpotRuleBiz.queryEnableRulesByStrategyIds(spotStrategies.stream()
                .map(RiskQualitySpotStrategy::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(riskQualitySpotRules)){
            return Lists.newArrayList();
        }
        List<RiskQualitySpotRuleVo> spotRuleVoList = Lists.newArrayList();
        riskQualitySpotRules.forEach(v -> {
            RiskQualitySpotRuleVo vo = new RiskQualitySpotRuleVo();
            vo.setRuleId(v.getId());
            vo.setRuleName(v.getName());
            spotRuleVoList.add(vo);
        });
        return spotRuleVoList;
    }

    public Response<RiskQualitySpotWorkOrderDetailVo> getUserRule(long userId, long scene) {
        Response<RiskQualitySpotWorkOrderUserConfig> response = cfWorkOrderQualitySpotClient.getByUserId(userId, scene);
        if (response.notOk()) {
            return NewResponseUtil.makeFail(response.getCode(), response.getMsg(), null);
        }
        RiskQualitySpotWorkOrderDetailVo detailVo = RiskQualitySpotWorkOrderDetailVo.buildVo(response.getData());
        setRuleStatus(detailVo.getRiskQualitySpotRuleDetailVos());
        return NewResponseUtil.makeSuccess(detailVo);
    }

    private void setRuleStatus(List<RiskQualitySpotRuleAdminDetailVo> riskQualitySpotRuleDetailVos) {
        if (CollectionUtils.isEmpty(riskQualitySpotRuleDetailVos)){
            return;
        }
        List<RiskQualitySpotRule> spotRules = qualitySpotRuleBiz.
                findById(riskQualitySpotRuleDetailVos.stream().map(RiskQualitySpotRuleDetailVo::getRuleId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(spotRules)) {
            return;
        }
        Map<Long, RiskQualitySpotRule> spotRuleMap = spotRules.stream().collect(Collectors.toMap(RiskQualitySpotRule::getId, Function.identity()));
        //查询对应的策略
        List<RiskQualitySpotStrategy> spotStrategies = qualitySpotStrategyBiz.listById(spotRules
                .stream().map(RiskQualitySpotRule::getStrategyId).distinct().collect(Collectors.toList()));
        Map<Long, RiskQualitySpotStrategy> spotStrategyMap = spotStrategies.stream().collect(Collectors
                .toMap(RiskQualitySpotStrategy::getId, Function.identity()));
        for (RiskQualitySpotRuleAdminDetailVo detailVo : riskQualitySpotRuleDetailVos) {
            RiskQualitySpotRule spotRule = spotRuleMap.get(detailVo.getRuleId());
            if (spotRule == null){
                continue;
            }
            RiskQualitySpotStrategy spotStrategy = spotStrategyMap.get(spotRule.getStrategyId());
            boolean status =  spotStrategy.getStrategyExpireTime().before(DateUtil.getCurrentTimestamp()) || spotStrategy.getStatus() == 1;
            status = status || spotRule.getStatus() == 1;
            detailVo.setStatus((byte) (status ? 1 : 0));
        }
    }

    public Response<Boolean> saveOrUpdate(RiskQualitySpotWorkOrderDetailVo riskQualitySpotWorkOrderDetailVo) {
        RiskQualitySpotWorkOrderUserConfig userConfig = buildConfig(riskQualitySpotWorkOrderDetailVo);
        Response<RiskQualitySpotWorkOrderUserConfig>  response = cfWorkOrderQualitySpotClient.getByUserId(riskQualitySpotWorkOrderDetailVo.getOperatorId(),
                (long) riskQualitySpotWorkOrderDetailVo.getScene());
        if (response.ok() && response.getData()!=null) {
            userConfig.setId(response.getData().getId());
        }
        if (userConfig.getId() > 0){
            return cfWorkOrderQualitySpotClient.update(userConfig);
        }
        return cfWorkOrderQualitySpotClient.save(userConfig);

    }

    private RiskQualitySpotWorkOrderUserConfig buildConfig(RiskQualitySpotWorkOrderDetailVo detailVo) {
        RiskQualitySpotWorkOrderUserConfig config =  new RiskQualitySpotWorkOrderUserConfig();
        config.setId(detailVo.getId());
        config.setDistributionType(detailVo.getWorkOrderDistributionType());
        config.setRangeLimit(detailVo.getGetWorkOrderRangeLimit());
        config.setScene(detailVo.getScene());
        config.setRuleInfo(JSON.toJSONString(detailVo.getRiskQualitySpotRuleDetailVos()));
        config.setUserId(detailVo.getOperatorId());
        return config;
    }
}
