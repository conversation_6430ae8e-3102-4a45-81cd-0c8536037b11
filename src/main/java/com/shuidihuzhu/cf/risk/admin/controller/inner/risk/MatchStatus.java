
package com.shuidihuzhu.cf.risk.admin.controller.inner.risk;

public enum MatchStatus {
    NO(0),
    YES(1);

    private final Integer value;

    MatchStatus(Integer value) {
        this.value = value;
    }

    public static MatchStatus fromString(String value) {
        Integer valueOf = Integer.valueOf(value);
        return fromInteger(valueOf);
    }

    private static MatchStatus fromInteger(Integer valueOf) {
        MatchStatus[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            MatchStatus matchStatus = var1[var3];
            if (matchStatus.value.equals(valueOf)) {
                return matchStatus;
            }
        }

        return null;
    }

    public Integer getValue() {
        return this.value;
    }
}
