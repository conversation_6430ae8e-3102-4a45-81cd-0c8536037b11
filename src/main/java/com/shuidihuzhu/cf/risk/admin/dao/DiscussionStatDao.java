package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.DiscussionStat;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2020/2/6
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface DiscussionStatDao {

    int save(@Param("discussionId") long discussionId, @Param("caseId") int caseId);

    DiscussionStat findById(long discussionId);
}

