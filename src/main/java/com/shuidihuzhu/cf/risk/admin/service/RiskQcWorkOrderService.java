package com.shuidihuzhu.cf.risk.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultConfigBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcOperationRecordDao;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcSearchIndexDao;
import com.shuidihuzhu.cf.risk.admin.delegate.ClewDelegate;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderParam;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderSecType;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderType;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResultConfig;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcVideoVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcWorkOrderVo;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingChaiFenV2FeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.search.param.table.RiskQcSearchIndexTableParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderExtTableParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderTableParam;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.AssignTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-06-18 15:22
 **/
@Service
@Slf4j
public class RiskQcWorkOrderService {

    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private CfSearchClient cfSearchClient;
    @Autowired
    private RiskQcResultConfigBiz riskQcResultConfigBiz;
    @Autowired
    private SeaAccountService accountService;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private RiskQcResultBiz riskQcResultBiz;
    @Autowired
    private PreposeMaterialClient preposeMaterialClient;
    @Autowired
    private RiskQcSearchIndexDao riskQcSearchIndexDao;
    @Autowired
    private CfWorkOrderStaffClient staffClient;
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private RiskAgainQcService riskAgainQcService;
    @Autowired
    private CrowdfundingChaiFenV2FeignClient crowdfundingChaiFenV2FeignClient;
    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private UserGroupFeignClient userGroupFeignClient;
    @Autowired
    private ClewDelegate clewDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private MaskUtil maskUtil;

    public PageResult<RiskQcWorkOrderVo> myQcWorkOrderList(WorkOrderListParam qcParam) {
        Response<PageResult<WorkOrderVO>> response = cfQcWorkOrderClient.qcOrderlist(qcParam);
        if (Objects.isNull(response) || response.notOk() || Objects.isNull(response.getData())) {
            return new PageResult<>();
        }
        PageResult<WorkOrderVO> pageResult = response.getData();
        List<WorkOrderVO> list = pageResult.getPageList();
        if (CollectionUtils.isEmpty(list)) {
            return new PageResult<>();
        }
        //质检结果配置
        List<RiskQcResultConfig> riskQcResultConfigList = riskQcResultConfigBiz.getAll();
        Map<Long, RiskQcResultConfig> resultConfigMap = riskQcResultConfigList.stream()
                .collect(Collectors.toMap(RiskQcResultConfig::getId, Function.identity(), (k1, k2) -> k2));

        List<Long> workOrderIds = list.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        List<Long> orderIds = riskAgainQcService.getQcAgainIds(workOrderIds);

        // 仅普通质检工单
        Response<List<WorkOrderExt>> seemInvalidOrderList = cfWorkOrderClient.listExtInfos(workOrderIds, QcConst.OrderExt.seenInvalidOrder);
        Map<Long, WorkOrderExt> seemInvalidOrderExtMap = getOrderIdExtMap(seemInvalidOrderList);

        List<RiskQcWorkOrderVo> riskQcWorkOrderVos = this.getRiskQcWorkOrderVos(list, resultConfigMap, null, seemInvalidOrderExtMap, orderIds);

        PageResult<RiskQcWorkOrderVo> result = new PageResult<>();
        result.setHasNext(pageResult.isHasNext());
        result.setPageList(riskQcWorkOrderVos);
        return result;
    }

    private void buildOtherField(WorkOrderVO workOrderVO, RiskQcBaseInfo riskQcBaseInfo, RiskQcWorkOrderVo riskQcWorkOrderVo,
                                 RiskQcSearchIndex riskQcSearchIndex) {
        long qcId = riskQcBaseInfo.getId();
        WorkOrderType workOrderType = WorkOrderType.getFromType(workOrderVO.getOrderType());
        //查询有没有生成过复检工单
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos = riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.HAS_REPEAT.getKey());

        switch (workOrderType) {
            case qc_common:
                //获取录音
                int totalDuration = this.getTotalDuration(qcId);
                riskQcWorkOrderVo.buildCommonQcField(totalDuration);
                if (CollectionUtils.isNotEmpty(riskQcMaterialsInfos)) {
                    riskQcWorkOrderVo.setHasRepeat(true);
                }
                break;
            case qc_wx_1v1:
                //微信1v1服务任务信息
                this.commonWx1v1(workOrderVO, riskQcWorkOrderVo, riskQcSearchIndex);
                if (CollectionUtils.isNotEmpty(riskQcMaterialsInfos)) {
                    riskQcWorkOrderVo.setHasRepeat(true);
                }
                break;
            case qc_call:
                //外呼信息
                CfClewTaskModel cfClewTaskModel = null;
                Response<List<CfClewTaskModel>> listResponse = cfClewtrackTaskFeignClient.getClewTaskModel(List.of(workOrderVO.getTaskId()));
                if (listResponse.ok() && CollectionUtils.isNotEmpty(listResponse.getData())) {
                    cfClewTaskModel = listResponse.getData().get(0);
                }
                riskQcWorkOrderVo.buildQcCallField(riskQcSearchIndex, cfClewTaskModel, workOrderVO.getTaskId(), shuidiCipher, maskUtil);
                break;
            case qc_material_audit:
                long materialWorkOrderId = 0;
                List<RiskQcMaterialsInfo> materials = riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.ORDER_ID.getKey());
                if (CollectionUtils.isNotEmpty(materials)) {
                    var riskQcMaterialsInfo = materials.get(0);
                    materialWorkOrderId = Long.parseLong(riskQcMaterialsInfo.getMaterialsValue());
                }
                riskQcWorkOrderVo.setMaterialWorkOrderType(riskQcSearchIndex.getSourceWorkOrderType());
                riskQcWorkOrderVo.buildMaterialField(riskQcSearchIndex, materialWorkOrderId, workOrderVO.getOperatorId());
                break;
            case qc_zhu_dong:
                long sourceWorkOrderId = riskQcSearchIndex.getSourceWorkOrderId();
                int handleResult = cfWorkOrderClient.getWorkOrderById(sourceWorkOrderId).getData().getHandleResult();
                riskQcWorkOrderVo.setMaterialHandleResult(handleResult);
                riskQcWorkOrderVo.setMaterialWorkOrderType(riskQcSearchIndex.getSourceWorkOrderType());
                riskQcWorkOrderVo.setMaterialWorkOrderId(sourceWorkOrderId);
                riskQcWorkOrderVo.setMaterialOperatorId(Long.parseLong(riskQcBaseInfo.getQcUniqueCode()));
                break;
            case qc_wx_1v1_repeat:
                this.commonWx1v1(workOrderVO, riskQcWorkOrderVo, riskQcSearchIndex);
                break;
            case qc_hospital_dept:
                fillDeptInfo(qcId, workOrderVO, riskQcWorkOrderVo, riskQcSearchIndex);
                break;
            case qc_high_risk_quality_inspection:
                int highRiskHandleResult = cfWorkOrderClient.getWorkOrderById(riskQcSearchIndex.getSourceWorkOrderId()).getData().getHandleResult();
                riskQcWorkOrderVo.setMaterialHandleResult(highRiskHandleResult);
                riskQcWorkOrderVo.setSourceWorkOrderId(riskQcSearchIndex.getSourceWorkOrderId());
                break;
            case qc_common_repeat:
                //获取录音信息
                List<RiskQcMaterialsInfo> qcMaterialsInfos = riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.ORIGINAL_WORK_ORDER.getKey());
                if (CollectionUtils.isNotEmpty(qcMaterialsInfos)) {
                    long workOrderId = Long.parseLong(qcMaterialsInfos.get(0).getMaterialsValue());
                    int duration = this.getTotalDuration(riskQcDetailService.getQcId(workOrderId));
                    riskQcWorkOrderVo.buildCommonQcField(duration);
                }
                break;
            default:
        }
    }

    private void fillDeptInfo(long qcId, WorkOrderVO workOrderVO, RiskQcWorkOrderVo riskQcWorkOrderVo, RiskQcSearchIndex riskQcSearchIndex) {
        RiskQcSearchIndex searchIndex = riskQcSearchIndexBiz.getByWorkOrderId(workOrderVO.getWorkOrderId());
        if (searchIndex == null) {
            return;
        }
        Response<DepartmentChangeDetailModel> resp = clewDelegate.getHospitalDeptInfo((int) searchIndex.getHospitalDeptId());
        if (resp == null || resp.notOk()) {
            return;
        }
        DepartmentChangeDetailModel deptInfo = resp.getData();
        if (deptInfo == null) {
            return;
        }
        String title = deptInfo.getHospitalName() + "-" + deptInfo.getChangedBuildingDetail().getBuildName();
        riskQcWorkOrderVo.setTitle(title);
        riskQcWorkOrderVo.setCaseId(searchIndex.getHospitalDeptId());
    }

    private void commonWx1v1(WorkOrderVO workOrderVO, RiskQcWorkOrderVo riskQcWorkOrderVo, RiskQcSearchIndex riskQcSearchIndex) {
        CfClueInfoModel cfClueInfoModel = null;
        Response<List<CfClueInfoModel>> cfClueInfoResponse = cfClewtrackTaskFeignClient.listCfClueInfo(List.of(workOrderVO.getTaskId()));
        if (cfClueInfoResponse.ok() && CollectionUtils.isNotEmpty(cfClueInfoResponse.getData())) {
            cfClueInfoModel = cfClueInfoResponse.getData().get(0);
        }
        riskQcWorkOrderVo.buildWx1v1Field(riskQcSearchIndex, cfClueInfoModel, workOrderVO.getTaskId(), shuidiCipher, maskUtil);
        //1v1需要单独查询案例信息
        if (riskQcSearchIndex.getCaseId() > 0) {
            FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = crowdfundingFeignClient.getCaseInfoById((int) riskQcSearchIndex.getCaseId());
            if (crowdfundingInfoFeignResponse.ok() && Objects.nonNull(crowdfundingInfoFeignResponse.getData())) {
                riskQcWorkOrderVo.setCaseId(crowdfundingInfoFeignResponse.getData().getId());
                riskQcWorkOrderVo.setInfoUuid(crowdfundingInfoFeignResponse.getData().getInfoId());
                riskQcWorkOrderVo.setTitle(crowdfundingInfoFeignResponse.getData().getTitle());
                riskQcWorkOrderVo.setAmount(crowdfundingInfoFeignResponse.getData().getAmount() / 100);
            }
            CfInfoStat cfInfoStat = null;
            int caseId = (int) riskQcSearchIndex.getCaseId();
            Response<String> response = crowdfundingChaiFenV2FeignClient.mapByIds(Lists.newArrayList(caseId));
            log.info("crowdfundingChaiFenFeignClient response:{}", JSON.toJSONString(response));
            if (response != null && response.ok() && org.apache.commons.lang3.StringUtils.isNotBlank(response.getData())) {
                Map<Integer, CfInfoStat> cfInfoStatMap =
                        JSON.parseObject(response.getData(), new TypeReference<Map<Integer, CfInfoStat>>() {
                        });
                cfInfoStat = cfInfoStatMap.getOrDefault(caseId, null);
            }
            if (Objects.nonNull(cfInfoStat)) {
                riskQcWorkOrderVo.setShareCount(cfInfoStat.getShareCount());
            }
        }
    }

    public int getTotalDuration(long qcId) {
        List<RiskQcMaterialsInfo> materialsInfoList = riskQcMaterialsInfoBiz.getMaterials(qcId,
                QcMaterialsKeyEnum.RECORDING.getKey());
        return materialsInfoList.stream().mapToInt(riskQcMaterialsInfo -> {
            String materialsValue = riskQcMaterialsInfo.getMaterialsValue();
            RiskQcVideoVo riskQcVideoVo = JSONObject.parseObject(materialsValue, RiskQcVideoVo.class);
            return riskQcVideoVo.getDuration();
        }).sum();
    }

    public Map<String, Object> qcWorkOrderList(QcWorkOrderParam qcWorkOrderParam) {
        //如果是手机号搜索 直接验证用户是否存在
        UserInfoModel userInfoModel = null;
        if (StringUtils.isNotBlank(qcWorkOrderParam.getMobile())) {
            String aesEncryptMobile = oldShuidiCipher.aesEncrypt(qcWorkOrderParam.getMobile());
            userInfoModel = userInfoDelegateService.getUserInfoByCryptoMobile(aesEncryptMobile);
            if (Objects.isNull(userInfoModel)) {
                Map<String, Object> pageMap = Maps.newHashMap();
                pageMap.put("pageNum", qcWorkOrderParam.getPageNum());
                pageMap.put("pageSize", qcWorkOrderParam.getPageSize());
                pageMap.put("total", 0);
                Map<String, Object> resultMap = Maps.newHashMap();
                resultMap.put("pagination", pageMap);
                resultMap.put("data", Lists.newArrayList());
                return resultMap;
            }
        }
        //构建搜索参数
        CfWorkOrderV2IndexSearchParam searchParam = buildRiskQcSearchParam(qcWorkOrderParam, userInfoModel);

        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        log.info("cfWorkOrderV2IndexSearch param:{},result:{}", JSON.toJSONString(searchParam), JSON.toJSONString(searchRpcResult.getData()));
        if (Objects.isNull(searchRpcResult) || ErrorCode.SUCCESS.getCode() != searchRpcResult.getCode() || Objects.isNull(searchRpcResult.getData())) {
            return null;
        }
        List<RiskQcWorkOrderVo> riskQcWorkOrderVos = this.buildRiskQcWorkOrderVo(searchRpcResult.getData());
        Map<String, Object> pageMap = Maps.newHashMap();
        pageMap.put("pageNum", qcWorkOrderParam.getPageNum());
        pageMap.put("pageSize", qcWorkOrderParam.getPageSize());
        pageMap.put("total", searchRpcResult.getData().getTotal());

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("pagination", pageMap);
        resultMap.put("data", riskQcWorkOrderVos);

        return resultMap;
    }

    /**
     * 构建质检搜索参数
     */
    public CfWorkOrderV2IndexSearchParam buildRiskQcSearchParam(QcWorkOrderParam qcWorkOrderParam, UserInfoModel userInfoModel) {
        /**
         * 构建工单表的相关参数
         */
        WorkOrderTableParam woTableParam = new WorkOrderTableParam();
        woTableParam.setOrderTypes(WorkOrderType.QC_WORK_ORDER_LIST);
        if (qcWorkOrderParam.getOrderType() > 0) {
            woTableParam.setOrderTypes(List.of(qcWorkOrderParam.getOrderType()));
        }
        if (qcWorkOrderParam.getCreateStartTime() > 0 && qcWorkOrderParam.getCreateEndTime() > 0) {
            woTableParam.setCreateStartTime(qcWorkOrderParam.getCreateStartTime());
            woTableParam.setCreateEndTime(qcWorkOrderParam.getCreateEndTime());
        }
        if (qcWorkOrderParam.getHandleStartTime() > 0 && qcWorkOrderParam.getHandleEndTime() > 0) {
            woTableParam.setFinishStartTime(qcWorkOrderParam.getHandleStartTime());
            woTableParam.setFinishEndTime(qcWorkOrderParam.getHandleEndTime());
        }
        if (qcWorkOrderParam.getWorkOrderId() > 0) {
            woTableParam.setIds(List.of(qcWorkOrderParam.getWorkOrderId()));
        }
        if (qcWorkOrderParam.getOperatorId() > 0) {
            woTableParam.setOperatorIds(List.of(qcWorkOrderParam.getOperatorId()));
        }
        if (qcWorkOrderParam.getHandleResult() >= 0) {
            int handleResult = qcWorkOrderParam.getHandleResult();
            List<Integer> searchHandleResultList = handleResult == HandleResultEnum.undoing.getType() ?
                    List.of(HandleResultEnum.undoing.getType(), HandleResultEnum.not_auto_assign.getType()) :
                    List.of(handleResult);
            woTableParam.setHandleResult(searchHandleResultList);
        }

        /**
         * 构建质检搜索参数
         */
        RiskQcSearchIndexTableParam riskQcSearchIndexTableParam = new RiskQcSearchIndexTableParam();
        if (qcWorkOrderParam.getQcType() > 0) {
            riskQcSearchIndexTableParam.setQcType(qcWorkOrderParam.getQcType());
        }
        if (StringUtils.isNotBlank(qcWorkOrderParam.getOrganization())) {
            riskQcSearchIndexTableParam.setOrganization(qcWorkOrderParam.getOrganization());
        }
        if (StringUtils.isNotBlank(qcWorkOrderParam.getQcByName())) {
            riskQcSearchIndexTableParam.setQcByName(qcWorkOrderParam.getQcByName());
        }
        if (qcWorkOrderParam.getQcResult() > 0) {
            riskQcSearchIndexTableParam.setQcResult(qcWorkOrderParam.getQcResult());
        }
        if (qcWorkOrderParam.getQcResultSecond() > 0) {
            riskQcSearchIndexTableParam.setQcResultSecond(qcWorkOrderParam.getQcResultSecond());
        }
        if (qcWorkOrderParam.getQuestionType() > 0) {
            riskQcSearchIndexTableParam.setQuestionType(qcWorkOrderParam.getQuestionType());
        }
        if (qcWorkOrderParam.getMaterialId() > 0) {
            riskQcSearchIndexTableParam.setMaterialId(qcWorkOrderParam.getMaterialId());
        }
        if (qcWorkOrderParam.getCaseId() > 0) {
            riskQcSearchIndexTableParam.setCaseId(qcWorkOrderParam.getCaseId());
        }
        if (StringUtils.isNotBlank(qcWorkOrderParam.getRuleName())) {
            riskQcSearchIndexTableParam.setRuleName(qcWorkOrderParam.getRuleName());
        }
        if (Objects.nonNull(userInfoModel)) {
            riskQcSearchIndexTableParam.setUserId(userInfoModel.getUserId());
        }
        if (qcWorkOrderParam.getSecondQuestionType() > 0) {
            riskQcSearchIndexTableParam.setSecondQuestionType(qcWorkOrderParam.getSecondQuestionType());
        }
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.BD.getCode()) {
            if (qcWorkOrderParam.getFirstPropertyId() > 0) {
                riskQcSearchIndexTableParam.setFirstPropertyId(qcWorkOrderParam.getFirstPropertyId());
            }
        }
        // 科室质检工单 前端查询caseId 值为deptId 后续可拆单独字段
        if (qcWorkOrderParam.getOrderType() == WorkOrderType.qc_hospital_dept.getType()) {
            riskQcSearchIndexTableParam.setHospitalDeptId(qcWorkOrderParam.getCaseId());
        }

        //如果是查询wx1v1才拼接的条件
        riskQcSearchIndexTableParam.setServiceStage(-1);
        riskQcSearchIndexTableParam.setJobContent(-1);
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.WX_1V1.getCode()) {
            if (StringUtils.isNotBlank(qcWorkOrderParam.getRegisterMobile())) {
                riskQcSearchIndexTableParam.setRegisterMobile(qcWorkOrderParam.getRegisterMobile());
            }
            riskQcSearchIndexTableParam.setServiceStage(qcWorkOrderParam.getServiceStage());
            if (qcWorkOrderParam.getJobContent() >= 0) {
                riskQcSearchIndexTableParam.setJobContent(qcWorkOrderParam.getJobContent());
            }
        }

        //如果查询外呼才拼接的条件
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.CALL.getCode()) {
            if (qcWorkOrderParam.getFirstLevelLabel() > 0) {
                riskQcSearchIndexTableParam.setFirstLevelLabel(qcWorkOrderParam.getFirstLevelLabel());
            }
            if (qcWorkOrderParam.getTwoLevelLabel() > 0) {
                riskQcSearchIndexTableParam.setTwoLevelLabel(qcWorkOrderParam.getTwoLevelLabel());
            }
            if (qcWorkOrderParam.getCallTaskStatus() > 0) {
                riskQcSearchIndexTableParam.setCallTaskStatus(qcWorkOrderParam.getCallTaskStatus());
            }
            if (StringUtils.isNotBlank(qcWorkOrderParam.getCallCluesChannel())) {
                riskQcSearchIndexTableParam.setCallCluesChannel(qcWorkOrderParam.getCallCluesChannel());
            }
            if (StringUtils.isNotBlank(qcWorkOrderParam.getRegisterMobile())) {
                riskQcSearchIndexTableParam.setRegisterMobile(qcWorkOrderParam.getRegisterMobile());
            }
            if (qcWorkOrderParam.getJobContent() >= 0) {
                riskQcSearchIndexTableParam.setJobContent(qcWorkOrderParam.getJobContent());
            }
        }
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.BD.getCode()) {
            if (qcWorkOrderParam.getFirstPropertyId() > 0) {
                riskQcSearchIndexTableParam.setFirstPropertyId(qcWorkOrderParam.getFirstPropertyId());
            }
        }

        //材审筛选条件
        if (qcWorkOrderParam.getQcType() == QcTypeEnum.MATERIAL.getCode() ||
                qcWorkOrderParam.getQcType() == QcTypeEnum.INTERNAL_AUDIT.getCode()) {
            if (qcWorkOrderParam.getCallStatus() > 0) {
                riskQcSearchIndexTableParam.setCallStatus(qcWorkOrderParam.getCallStatus());
            }
            if (qcWorkOrderParam.getMaterialHandleResult() > 0) {
                riskQcSearchIndexTableParam.setHandleResult(qcWorkOrderParam.getMaterialHandleResult());
            }
        }


        /**
         * 构建工单扩展信息
         */
        List<WorkOrderExtTableParam> extList = new ArrayList<>();
        if (qcWorkOrderParam.getSeemInvalidOrder() != null) {
            extList.add(WorkOrderExtTableParam.create(QcConst.OrderExt.seenInvalidOrder, qcWorkOrderParam.getSeemInvalidOrder()));
        }
        if (qcWorkOrderParam.getRemoteRaise() != null) {
            extList.add(WorkOrderExtTableParam.create(QcConst.OrderExt.remoteRaise, qcWorkOrderParam.getRemoteRaise()));
        }
        if (qcWorkOrderParam.getAssignType() > 0) {
            extList.add(WorkOrderExtTableParam.create(OrderExtName.qcAssignType.getName(), Integer.toString(qcWorkOrderParam.getAssignType())));
        }
        WorkOrderExtTableParam tableParam = new WorkOrderExtTableParam();
        if (qcWorkOrderParam.getCallType() > 0) {
            tableParam.setExtName(OrderExtName.qcCallType.getName());
            tableParam.setExtValue(Integer.toString(qcWorkOrderParam.getCallType()));
            extList.add(tableParam);
        }
        List<WorkOrderExtTableParam> extShouldList = new ArrayList<>();

        if (StringUtils.isNotBlank(qcWorkOrderParam.getAsrResult())) {
            extShouldList.add(WorkOrderExtTableParam.create(OrderExtName.asrResult.getName(), qcWorkOrderParam.getAsrResult()));
        }

        // 科室质检字段
        if (StringUtils.isNotBlank(qcWorkOrderParam.getDeptHospitalName())) {
            extShouldList.add(WorkOrderExtTableParam.create(QcConst.OrderExt.deptHospitalName, qcWorkOrderParam.getDeptHospitalName()));
        }
        if (qcWorkOrderParam.getDeptClassifySuccess() != null) {
            extList.add(WorkOrderExtTableParam.create(QcConst.OrderExt.deptClassifySuccess, qcWorkOrderParam.getDeptClassifySuccess()));
        }

        CfWorkOrderV2IndexSearchParam searchParam = new CfWorkOrderV2IndexSearchParam();
        searchParam.setWoTableParam(woTableParam);
        searchParam.setWorkOrderExtTableParamList(extList);
        searchParam.setWorkOrderExtShouldTableParamList(extShouldList);
        searchParam.setRiskQcSearchIndexTableParam(riskQcSearchIndexTableParam);
        searchParam.setFrom((qcWorkOrderParam.getPageNum() - 1) * qcWorkOrderParam.getPageSize());
        searchParam.setSize(qcWorkOrderParam.getPageSize());
        return searchParam;
    }

    private List<RiskQcWorkOrderVo> buildRiskQcWorkOrderVo(CfWorkOrderIndexSearchResult cfWorkOrderIndexSearchResult) {
        //获取工单信息
        List<CfWorkOrderModel> cfWorkOrderModels = cfWorkOrderIndexSearchResult.getModels();
        List<Long> workOrderIds = cfWorkOrderModels.stream().map(CfWorkOrderModel::getWorkOrderId).collect(Collectors.toList());
        Response<List<WorkOrderVO>> listResponse = cfQcWorkOrderClient.queryQcByIds(workOrderIds);
        if (listResponse.notOk() || CollectionUtils.isEmpty(listResponse.getData())) {
            return Collections.emptyList();
        }
        List<WorkOrderVO> workOrderVOS = listResponse.getData();

        //质检结果配置
        Map<Long, RiskQcResultConfig> resultConfigMap = riskQcResultConfigBiz.getAll().stream()
                .collect(Collectors.toMap(RiskQcResultConfig::getId, Function.identity(), (k1, k2) -> k2));

        //获取工单扩展信息
        Response<List<WorkOrderExt>> extInfos = cfWorkOrderClient.listExtInfos(workOrderIds, OrderExtName.qcAssignType.getName());
        Map<Long, WorkOrderExt> workOrderExtMap = getOrderIdExtMap(extInfos);

        // 仅普通质检工单
        Response<List<WorkOrderExt>> seemInvalidOrderList = cfWorkOrderClient.listExtInfos(workOrderIds, QcConst.OrderExt.seenInvalidOrder);
        Map<Long, WorkOrderExt> seemInvalidOrderExtMap = getOrderIdExtMap(seemInvalidOrderList);

        List<Long> orderIds = riskAgainQcService.filterInfo(workOrderIds);
        List<RiskQcWorkOrderVo> qcWorkOrderVos =
                this.getRiskQcWorkOrderVos(workOrderVOS, resultConfigMap, workOrderExtMap, seemInvalidOrderExtMap, orderIds);

        return qcWorkOrderVos.stream().map(v-> {
                        v.setRegisterMobile(null);
                        v.setCreateMobile(null);
                        return v;
        }).sorted(Comparator.comparing(RiskQcWorkOrderVo::getWorkOrderId).reversed()).collect(Collectors.toList());
    }

    @NotNull
    private Map<Long, WorkOrderExt> getOrderIdExtMap(Response<List<WorkOrderExt>> extInfos) {
        Map<Long, WorkOrderExt> workOrderExtMap = Maps.newHashMap();
        if (extInfos.ok() && CollectionUtils.isNotEmpty(extInfos.getData())) {
            workOrderExtMap = extInfos.getData().stream()
                    .collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
        }
        return workOrderExtMap;
    }

    private List<RiskQcWorkOrderVo> getRiskQcWorkOrderVos(List<WorkOrderVO> workOrderVOS,
                                                          Map<Long, RiskQcResultConfig> resultConfigMap,
                                                          Map<Long, WorkOrderExt> workOrderExtMap,
                                                          Map<Long, WorkOrderExt> seemInvalidOrderExtMap,
                                                          List<Long> againQcStatusList) {
        return workOrderVOS.stream().map(workOrderVO -> {
            RiskQcBaseInfo riskQcBaseInfo = riskQcBaseInfoBiz.getById(workOrderVO.getQcId());
            RiskQcSearchIndex riskQcSearchIndex = riskQcSearchIndexBiz.getByWorkOrderId(workOrderVO.getWorkOrderId());

            RiskQcResultConfig riskQcResultConfig = resultConfigMap.get(riskQcSearchIndex.getQcResult());
            String qcResult = riskQcResultConfig == null ? "" : riskQcResultConfig.getQcResult();

            String qcByName = "";
            int qcType = 0;
            String organizationName = "";
            String operatorName = "";
            int assignType = AssignTypeEnum.ASSIGN.getCode();
            String ruleName = "";
            boolean allowAgainQc = false;
            if (Objects.nonNull(riskQcBaseInfo)) {
                qcByName = riskQcBaseInfo.getQcByName();
                qcType = riskQcBaseInfo.getQcType();
            }
            if (Objects.nonNull(riskQcSearchIndex)) {
                organizationName = riskQcSearchIndex.getOrganization() + "-" + qcByName;
                ruleName = riskQcSearchIndex.getRuleName();
            }
            if (workOrderVO.getOperatorId() > 0) {
                operatorName = accountService.getName((int) workOrderVO.getOperatorId());
            }
            if (MapUtils.isNotEmpty(workOrderExtMap)) {
                WorkOrderExt workOrderExt = workOrderExtMap.get(workOrderVO.getWorkOrderId());
                if (Objects.nonNull(workOrderExt)) {
                    assignType = Integer.parseInt(workOrderExt.getExtValue());
                }
            }
            if (CollectionUtils.isNotEmpty(againQcStatusList)) {
                allowAgainQc = againQcStatusList.contains(workOrderVO.getWorkOrderId());
            }
            RiskQcWorkOrderVo riskQcWorkOrderVo = new RiskQcWorkOrderVo().buildCommonField(workOrderVO, qcType, organizationName,
                    operatorName, qcResult, assignType, ruleName, allowAgainQc);
            //构建工单独有的字段
            this.buildOtherField(workOrderVO, riskQcBaseInfo, riskQcWorkOrderVo, riskQcSearchIndex);

            if (MapUtils.isNotEmpty(seemInvalidOrderExtMap)) {
                WorkOrderExt workOrderExt = seemInvalidOrderExtMap.get(workOrderVO.getWorkOrderId());
                if (Objects.nonNull(workOrderExt)) {
                    int seemInvalidAudio = Integer.parseInt(workOrderExt.getExtValue());
                    riskQcWorkOrderVo.setSeemInvalidOrder(seemInvalidAudio);
                }
            }

            riskQcWorkOrderVo.setRegisterMobile(null);
            riskQcWorkOrderVo.setCreateMobile(null);
            return riskQcWorkOrderVo;
        }).collect(Collectors.toList());
    }


    @Async
    public void cleanInfo() {
        long id = 0;
        while (true) {
            List<RiskQcSearchIndex> riskQcSearchIndices = riskQcSearchIndexDao.getAllByPage(id);
            if (CollectionUtils.isEmpty(riskQcSearchIndices)) {
                break;
            }
            for (RiskQcSearchIndex riskQcSearchIndex : riskQcSearchIndices) {
                if (riskQcSearchIndex != null) {
                    long materialId = getId(riskQcSearchIndex);
                    riskQcSearchIndexDao.updateMaterialId(materialId, riskQcSearchIndex.getId());
                }
            }
            id = riskQcSearchIndices.get(riskQcSearchIndices.size() - 1).getId();
        }
    }

    private long getId(RiskQcSearchIndex riskQcSearchIndex) {
        RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> rpcResult = preposeMaterialClient.selectMaterialByCaseId((int) riskQcSearchIndex.getCaseId());
        if (rpcResult != null && rpcResult.isSuccess() && CollectionUtils.isNotEmpty(rpcResult.getData())) {
            List<PreposeMaterialModel.MaterialInfoVo> materialInfoVos = rpcResult.getData();
            PreposeMaterialModel.MaterialInfoVo materialInfoVo = materialInfoVos.stream().sorted(Comparator.comparing(PreposeMaterialModel.MaterialInfoVo::getId).reversed()).collect(Collectors.toList()).get(0);
            return materialInfoVo == null ? 0 : materialInfoVo.getId();
        }
        return 0;
    }

    public List<QcWorkOrderType> userClassify(long userId, Map<Integer, List<Integer>> map) {
        List<QcWorkOrderType> result = Lists.newArrayList();

        for (Integer i : map.keySet()) {
            List<Integer> orderTypes = map.get(i);

            List<QcWorkOrderSecType> qcWorkOrderSecTypes = orderTypes.stream().map(r -> {
                QcWorkOrderSecType secType = this.getCount(userId, r);
                secType.setOrderType(r);
                Response<StaffStatus> response = staffClient.getStaffStatus(r, userId);
                if (response == null || response.getData() == null) {
                    secType.setStaffStatus(StaffStatusEnum.stop.getType());
                } else {
                    StaffStatus ss = response.getData();
                    secType.setStaffStatus(ss.getStaffStatus());
                    secType.setStaffType(ss.getOperType());
                }
                return secType;
            }).collect(Collectors.toList());

            QcWorkOrderType qcWorkOrderType = new QcWorkOrderType();
            qcWorkOrderType.setType(i);
            qcWorkOrderType.setSecTypes(qcWorkOrderSecTypes);
            result.add(qcWorkOrderType);
        }
        return result;
    }


    private QcWorkOrderSecType getCount(long userId, int orderType) {

        QcWorkOrderSecType result = new QcWorkOrderSecType();
        //处理中
        Integer selfDoingCount = cfWorkOrderClient.getUserCount(userId, orderType, HandleResultEnum.doing.getType() + "").getData();
        result.setSelfDoingCount(selfDoingCount == null ? 0 : selfDoingCount);
        //处理完成
        String status = HandleResultEnum.done.getType() + "";

        List<Integer> types = this.getDoneList(orderType);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(types)) {
            status = Joiner.on(",").join(types);
        }

        Integer selfFinishCount = cfWorkOrderClient.getUserCount(userId, orderType, status).getData();
        result.setSelfFinishCount(selfFinishCount == null ? 0 : selfFinishCount);

        //延后处理
        Integer selfYanhouCount = cfWorkOrderClient.getUserCount(userId, orderType, HandleResultEnum.later_doing.getType() + "").getData();
        result.setSelfYanhouCount(selfYanhouCount == null ? 0 : selfYanhouCount);

        //全组未处理
        Integer teamUndoCount = cfQcWorkOrderClient.getAllCount(orderType, HandleResultEnum.undoing.getType()).getData();
        result.setTeamUndoCount(teamUndoCount == null ? 0 : teamUndoCount);

        return result;
    }

    private List<Integer> getDoneList(int orderType) {
        List<Integer> list = Lists.newArrayList();
        WorkOrderType workOrderType = WorkOrderType.getFromType(orderType);
        //防止空指针异常
        if (workOrderType == null) {
            return Lists.newArrayList();
        }
        switch (workOrderType) {
            case qc_common:
            case qc_complaint:
                list.add(HandleResultEnum.done.getType());
            case qc_wx_1v1_repeat:
            case qc_common_repeat:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.manual_lock.getType());
            default:
                break;
        }
        return list;
    }

    public PageResult<RiskQcWorkOrderVo> myQcWorkOrderListBySearch(WorkOrderListParam param, String mobile,
                                                                   String qcByName, int pageNum) {
        //构建搜索参数
        CfWorkOrderV2IndexSearchParam searchParam =
                buildRiskQcSearchParam(QcWorkOrderParam.buildParam(param, pageNum, mobile, qcByName), null);
        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        log.info("cfWorkOrderV2IndexSearch param:{},result:{}", JSON.toJSONString(searchParam), JSON.toJSONString(searchRpcResult.getData()));
        if (Objects.isNull(searchRpcResult) || ErrorCode.SUCCESS.getCode() != searchRpcResult.getCode() || Objects.isNull(searchRpcResult.getData())) {
            return null;
        }
        List<RiskQcWorkOrderVo> riskQcWorkOrderVos = this.buildRiskQcWorkOrderVo(searchRpcResult.getData());
        PageResult<RiskQcWorkOrderVo> result = new PageResult<>();
        boolean hasNextPage;
        long totalPages;
        if ((searchRpcResult.getData().getTotal() % param.getPageSize()) == 0) {
            totalPages = searchRpcResult.getData().getTotal() / param.getPageSize();
        } else {
            totalPages = searchRpcResult.getData().getTotal() / param.getPageSize() + 1;
        }
        hasNextPage = pageNum < totalPages;
        result.setHasNext(hasNextPage);
        result.setPageList(riskQcWorkOrderVos);
        return result;
    }

    /**
     * 重新质检工单不能回收
     *
     * @param workOrderIds
     * @return
     */
    public List<Long> filterInfo(List<Long> workOrderIds) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return workOrderIds;
        }
        Response<List<WorkOrderExt>> response =
                cfWorkOrderClient.listExtInfos(workOrderIds, OrderExtName.againQcType.getName());
        log.info("filterInfo cfWorkOrderClient response:{}", JSON.toJSONString(response));
        if (response == null || response.notOk()) {
            return Lists.newArrayList();
        }
        List<WorkOrderExt> workOrderExts = response.getData();
        if (CollectionUtils.isEmpty(workOrderExts)) {
            return workOrderIds;
        }
        List<Long> againQcWorkOrderIds = workOrderExts.stream().map(WorkOrderExt::getWorkOrderId).collect(Collectors.toList());
        return ListUtils.removeAll(workOrderIds, againQcWorkOrderIds);
    }

    public void createQcWorkOrder(int orderType, List<Long> workOrderIds,int assignId,long adminUserId) {
        Response<List<WorkOrderVO>> response = cfQcWorkOrderClient.queryQcByIds(workOrderIds);
        if (response.notOk() || CollectionUtils.isEmpty(response.getData())) {
            return;
        }
        List<WorkOrderVO> workOrderVOS = response.getData();
        Map<Long, WorkOrderVO> workOrderVOMap = workOrderVOS
                .stream().collect(Collectors.toMap(WorkOrderVO::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
        int size = workOrderIds.size();
        for (Long workOrderId : workOrderIds) {
            WorkOrderVO workOrderVO = workOrderVOMap.get(workOrderId);
            if (Objects.isNull(workOrderVO)) {
                log.info("createQcWorkOrder workOrderId:{},is not exist", workOrderId);
                return;
            }
            if (workOrderVO.getHandleResult() != HandleResultEnum.done.getType()) {
                log.info("createQcWorkOrder workOrderId:{},not done", workOrderId);
                return;
            }
            if (orderType == WorkOrderType.qc_wx_1v1_repeat.getType()
                    && workOrderVO.getOrderType() != WorkOrderType.qc_wx_1v1.getType()) {
                return;
            }
            if (orderType == WorkOrderType.qc_common_repeat.getType()
                    && workOrderVO.getOrderType() != WorkOrderType.qc_common.getType()) {
                return;
            }
            //创建工单
            this.doCreate(orderType, workOrderVO, size, assignId,adminUserId);
        }
    }

    private void doCreate(int orderType, WorkOrderVO workOrderVO, int size, int assignId,long adminUserId) {

        //创建质检基本信息
        String operatorName = seaAccountService.getName(workOrderVO.getOperatorId());
        RiskQcBaseInfo riskQcBaseInfo = this.getRiskQcBaseInfo(workOrderVO, orderType, operatorName);
        riskQcBaseInfoBiz.addQc(riskQcBaseInfo);

        //记录材料
        long beforeWorkOrderId = workOrderVO.getWorkOrderId();
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos = List.of(
                new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.ORIGINAL_WORK_ORDER.getKey(),
                        Long.toString(beforeWorkOrderId)),
                new RiskQcMaterialsInfo(workOrderVO.getQcId(), QcMaterialsKeyEnum.HAS_REPEAT.getKey(),
                        Long.toString(beforeWorkOrderId)));
        riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfos);

        int caseId = this.getCaseId(workOrderVO);
        String comment = this.getComment(orderType);

        //执行工单创建逻辑
        QcWorkOrder qcWorkOrder = new QcWorkOrder();
        qcWorkOrder.setCaseId(caseId);
        qcWorkOrder.setQcId(riskQcBaseInfo.getId());
        qcWorkOrder.setOrderType(orderType);
        qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
        qcWorkOrder.setComment(comment);
        qcWorkOrder.setAssignStatus(AssignTypeEnum.MUST_ASSIGN.getCode());
        qcWorkOrder.setLoginUserId(adminUserId);
        if (assignId > 0) {
            qcWorkOrder.setOperatorId(assignId);
            qcWorkOrder.setHandleResult(HandleResultEnum.doing.getType());
        }
        Response<Long> clientQcWorkOrder = cfQcWorkOrderClient.createQcWorkOrder(qcWorkOrder);

        if (clientQcWorkOrder.ok() && Objects.nonNull(clientQcWorkOrder.getData())) {
            log.info("createQcWorkOrder id:{}", clientQcWorkOrder.getData());
            //调用质检操作记录接口，记录操作记录
            this.addRecord(orderType, size, beforeWorkOrderId, clientQcWorkOrder.getData());

            RiskQcSearchIndex beforeRiskQcSearchIndex = riskQcSearchIndexBiz.getByWorkOrderId(beforeWorkOrderId);

            // 添加搜索索引字段聚合表
            RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
            riskQcSearchIndex.setWorkOrderId(clientQcWorkOrder.getData());
            if (Objects.nonNull(beforeRiskQcSearchIndex)) {
                riskQcSearchIndex.setQcType(beforeRiskQcSearchIndex.getQcType());
                riskQcSearchIndex.setCaseId(beforeRiskQcSearchIndex.getCaseId());
                riskQcSearchIndex.setUserId(beforeRiskQcSearchIndex.getUserId());
                riskQcSearchIndex.setRegisterMobileEncrypt(beforeRiskQcSearchIndex.getRegisterMobileEncrypt());
                riskQcSearchIndex.setServiceStage(beforeRiskQcSearchIndex.getServiceStage());
                riskQcSearchIndex.setJobContent(beforeRiskQcSearchIndex.getJobContent());
                riskQcSearchIndex.setTaskId(beforeRiskQcSearchIndex.getTaskId());
                riskQcSearchIndex.setMaterialId(beforeRiskQcSearchIndex.getMaterialId());
            }
            String organization = "";
            Response<String> authRpcResponse = userGroupFeignClient.getGroupNameByUserId(workOrderVO.getOperatorId());
            if (authRpcResponse.ok()) {
                organization = authRpcResponse.getData();
            }
            riskQcSearchIndex.setOrganization(organization);
            riskQcSearchIndex.setQcUniqueCode(Long.toString(workOrderVO.getOperatorId()));
            riskQcSearchIndex.setQcByName(operatorName);
            riskQcSearchIndexBiz.addSearchIndex(riskQcSearchIndex, qcWorkOrder.getOrderType());
        }
    }

    private void addRecord(int orderType, int size, long beforeWorkOrderId, long workOrderId) {
        String str = "";
        if (orderType == WorkOrderType.qc_wx_1v1_repeat.getType()) {
            str = size == 1 ? "生成微信1v1复检工单" : "批量生成微信1v1复检工单";
        }
        if (orderType == WorkOrderType.qc_common_repeat.getType()) {
            str = size == 1 ? "生成线下顾问复检工单" : "批量生成线下顾问复检工单";
        }

        String content = str + ",工单ID【" + workOrderId + "】";
        riskQcLogService.addLog(RiskQcOperationTypeEnum.CREATE_COMMON_WORK_ORDER, beforeWorkOrderId, content);
    }

    private String getComment(int orderType) {
        if (orderType == WorkOrderType.qc_wx_1v1_repeat.getType()) {
            return "生成微信1v1复检工单";
        }
        if (orderType == WorkOrderType.qc_common_repeat.getType()) {
            return "生成线下顾问复检工单";
        }
        return "";
    }

    private int getCaseId(WorkOrderVO workOrderVO) {
        if (workOrderVO.getOrderType() == WorkOrderType.qc_wx_1v1.getType()) {
            return (int) workOrderVO.getTaskId();
        }
        if (workOrderVO.getOrderType() == WorkOrderType.qc_common.getType()) {
            return workOrderVO.getCaseId();
        }
        return 0;
    }

    public RiskQcBaseInfo getRiskQcBaseInfo(WorkOrderVO workOrderVO, int orderType, String operatorName) {
        RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
        var qcId = riskQcDetailService.getQcId(workOrderVO.getWorkOrderId());
        RiskQcBaseInfo beforeRiskQcBaseInfo = riskQcBaseInfoBiz.getById(qcId);
        if (Objects.nonNull(beforeRiskQcBaseInfo)) {
            riskQcBaseInfo.setCaseId(beforeRiskQcBaseInfo.getCaseId());
            riskQcBaseInfo.setQcType(beforeRiskQcBaseInfo.getQcType());
            riskQcBaseInfo.setOrderType(orderType);
            riskQcBaseInfo.setTaskId(beforeRiskQcBaseInfo.getTaskId());
            riskQcBaseInfo.setQcUniqueCode(Long.toString(workOrderVO.getOperatorId()));
            riskQcBaseInfo.setQcByName(operatorName);
        }

        return riskQcBaseInfo;
    }
}
