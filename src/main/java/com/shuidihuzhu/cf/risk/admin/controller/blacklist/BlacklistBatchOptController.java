package com.shuidihuzhu.cf.risk.admin.controller.blacklist;


import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlacklistBatchOptService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Objects;

/**
 * Created by sven on 18/9/30.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(path = "/api/cf-risk/blacklist/batch/opt")
public class BlacklistBatchOptController {

    @Resource
    private BlacklistBatchOptService batchOptService;

    @RequiresPermission("black-list:list")
    @ApiOperation(value = "导出-导入黑名单excel模板")
    @GetMapping(value = "/export/template-import")
    public ResponseEntity<byte[]> exportTemplate() {
        try (ByteArrayOutputStream os = batchOptService.exportImportTemplate()) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDisposition(ContentDisposition.builder("attachment")
                    .filename("黑名单数据导入模板.xlsx", Charset.defaultCharset()).build());
            return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.CREATED);
        } catch (IOException e) {
            log.error("", e);
            throw new RuntimeException("系统错误");
        }
    }

    @RequiresPermission("black-list:list")
    @ApiOperation(value = "黑名单数据导入")
    @PostMapping(value = "/import")
    public Response<Void> uploadSingle(@ApiParam(value = "上传文件的表单name", required = true) @NotNull(message = "上传的文件为空") MultipartFile file) {
        StopWatch sw = new StopWatch();
        sw.start();
        String originalFilename = file.getOriginalFilename();
        log.info("文件名称：{}", originalFilename);
        long adminUserId = ContextUtil.getAdminLongUserId();
        try(InputStream is = file.getInputStream()) {
            batchOptService.importBlacklist(is, adminUserId);
        } catch (IllegalArgumentException e) {
            return NewResponseUtil.makeFail(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("黑名单导入失败", e);
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        sw.stop();
        log.info("黑名单数据导入耗时：{} ms", sw.getLastTaskTimeMillis());
        return NewResponseUtil.makeSuccess(null);
    }

    @PostMapping(path = "/repair/black-ref")
    public Response<?> dataList(String uuid) {
        if (!Objects.equals(uuid, "50eb2fd1-7830-49ad-8c74-a2e547d962b7")) {
            log.info("black ref info repair skip");
            return null;
        }
        batchOptService.repairDataRefAll();
        return NewResponseUtil.makeSuccess(null);
    }

}
