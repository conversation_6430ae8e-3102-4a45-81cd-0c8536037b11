package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseKnowledge;
import com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseCheckDto;
import com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseKnowledgeDto;
import org.apache.commons.lang3.tuple.MutablePair;

import java.util.List;
import java.util.Map;

public interface RiskDiseaseKnowledgeBiz {

    void handleList(List<RiskDiseaseKnowledge> diseaseKnowledgeList);

    long addOrUpdate(DiseaseKnowledgeDto diseaseKnowledgeDto);

    DiseaseKnowledgeDto detail(long id);

    Map<Integer, List<DiseaseKnowledgeDto>> list(String diseaseName, String startTime,
                                                 String endTime, int limit, int offset);

    int del(long id);

    DiseaseCheckDto getCheckDetail(String number);

    List<String> findDiseaseNormList(String diseaseNorm);

}
