package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;

@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_standard", columnName = "is_use")}
,descName = "description")
public enum RiskQcStandardUseEnum {
    //
    IS_USE(1, "启用"),
    IS_NOT_USE(0, "弃用"),
    ;

    int code;
    String description;

    RiskQcStandardUseEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code) {
        for (RiskQcStandardUseEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return "";
    }
}
