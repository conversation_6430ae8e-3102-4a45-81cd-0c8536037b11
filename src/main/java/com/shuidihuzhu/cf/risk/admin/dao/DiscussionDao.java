package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.Discussion;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface DiscussionDao {

    int save(Discussion discussion);

    List<Discussion> findByCaseId(int caseId);


    List<Discussion> listByCaseId(@Param("caseId") int caseId, PageRequest pageRequest);

    int updateStatus(@Param("newStatus") int newStatus,
                     @Param("oldStatus") int oldStatus,
                     @Param("id") long id);

    int updateCheckStatus(@Param("newStatus") int newStatus,
                          @Param("oldStatus") int oldStatus,
                          @Param("discussionId") long discussionId);

    Discussion findById(long id);

    int updateDelById(long id);

    int updateCloseTime(@Param("id") long id, @Param("closeTime") Timestamp closeTime);


    List<Discussion> findByIds(List<Long> ids);

    int countByCaseId(int caseId);

    Discussion getInfos();
}
