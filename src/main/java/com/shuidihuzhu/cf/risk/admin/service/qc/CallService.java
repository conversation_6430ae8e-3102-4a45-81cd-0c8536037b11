package com.shuidihuzhu.cf.risk.admin.service.qc;

import com.shuidihuzhu.cf.risk.admin.model.enums.MaterialWorkOrderCallStatusEnum;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CallService {

    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    public int getCallStatus(long workOrderId) {
        int callStatus = MaterialWorkOrderCallStatusEnum.NOT_CALL.getCode();
        Response<List<WorkOrderExt>> response = cfQcWorkOrderClient.getWorkOrderExtIgnoreIsDelete(workOrderId, OrderExtName.callUnicode.getName());
        if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            List<WorkOrderExt> workOrderExts = response.getData();
            List<String> uniqueList = workOrderExts.stream().map(WorkOrderExt::getExtValue).collect(Collectors.toList());
            Response<List<ClewCallRecordModel>> callRecordsByUniqueIds = cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(uniqueList);
            if (callRecordsByUniqueIds.ok() && CollectionUtils.isNotEmpty(callRecordsByUniqueIds.getData())) {
                List<ClewCallRecordModel> clewCallRecordModels = callRecordsByUniqueIds.getData();
                if (clewCallRecordModels.stream().anyMatch(clewCallRecordModel -> clewCallRecordModel.getSipCause() == 200)) {
                    callStatus = MaterialWorkOrderCallStatusEnum.EFFECTIVE_CALL.getCode();
                } else {
                    callStatus = MaterialWorkOrderCallStatusEnum.INVALID_CALL.getCode();
                }
            }
        }
        return callStatus;
    }
}
