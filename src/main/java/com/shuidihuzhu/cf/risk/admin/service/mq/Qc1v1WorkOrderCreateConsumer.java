package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotRuleBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotStrategyBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualityWx1V1Service;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoStatModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderOrgRel;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-08-10 16:20
 **/
@Service
@RocketMQListener(id = RiskMQTagCons.QC_1v1_WORK_ORDER_CREATE,
        tags = RiskMQTagCons.QC_1v1_WORK_ORDER_CREATE,
        topic = MQTopicCons.CF)
@Slf4j
public class Qc1v1WorkOrderCreateConsumer implements MessageListener<CfClueInfoStatModel> {

    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private QualityWx1V1Service qualityWx1V1Service;
    @Autowired
    private QualitySpotStrategyBiz qualitySpotStrategyBiz;
    @Autowired
    private QualitySpotRuleBiz qualitySpotRuleBiz;
    @Autowired
    private UserGroupFeignClient userGroupFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfClueInfoStatModel> mqMessage) {
        CfClueInfoStatModel cfClueInfoStatModel = mqMessage.getPayload();
        CfClewTaskDO cfClewTaskDO = cfClueInfoStatModel.getCfClewTaskDO();
        log.info("Qc1v1WorkOrderCreateConsumer cfClewTaskDO:{}", JSONObject.toJSONString(cfClewTaskDO));

        String lockName = "qcLock_" + QcTypeEnum.WX_1V1.getCode() + "_" + cfClewTaskDO.getId() + "_"
                + cfClueInfoStatModel.getCfClewTaskDO().getUserId();

        String identifier = "";
        try {
            identifier = cfRiskRedissonHandler.tryLock(lockName, 3 * 1000L, 30 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                return ConsumeStatus.RECONSUME_LATER;
            }
            //检查是否已生成工单
            Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(cfClewTaskDO.getId().intValue(),
                    WorkOrderType.qc_wx_1v1.getType());
            if (lastWorkOrder.ok() && Objects.nonNull(lastWorkOrder.getData())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //创建质检基本信息
            RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
            riskQcBaseInfo.setTaskId(cfClewTaskDO.getId());
            riskQcBaseInfo.setQcType(QcTypeEnum.WX_1V1.getCode());
            riskQcBaseInfo.setOrderType(WorkOrderType.qc_wx_1v1.getType());
            riskQcBaseInfo.setQcUniqueCode(cfClewTaskDO.getUserId());
            riskQcBaseInfo.setQcByName(cfClewTaskDO.getUserName());
            riskQcBaseInfoBiz.addQc(riskQcBaseInfo);

            //记录组织结构id
            long userId = this.getUserIdByMis(cfClewTaskDO.getUserId());
            long orgId = this.getOrgIdByUserId(userId, cfClewTaskDO.getUserId());

            RiskQcMaterialsInfo riskQcMaterialsInfo = new RiskQcMaterialsInfo();
            riskQcMaterialsInfo.setQcId(riskQcBaseInfo.getId());
            riskQcMaterialsInfo.setMaterialsKey(QcMaterialsKeyEnum.ORG_ID.getKey());
            riskQcMaterialsInfo.setMaterialsValue(Long.toString(orgId));
            riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfo);


            //执行工单创建逻辑
            QcWorkOrder qcWorkOrder = new QcWorkOrder();
            qcWorkOrder.setCaseId(cfClewTaskDO.getId().intValue());
            qcWorkOrder.setQcId(riskQcBaseInfo.getId());
            qcWorkOrder.setOrderType(WorkOrderType.qc_wx_1v1.getType());
            qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
            qcWorkOrder.setComment("生成微信1v1质检工单");
            Response<Long> clientQcWorkOrder = cfQcWorkOrderClient.createQcWorkOrder(qcWorkOrder);

            if (clientQcWorkOrder.ok() && Objects.nonNull(clientQcWorkOrder.getData())) {
                log.info("Qc1v1WorkOrderCreate id:{}", clientQcWorkOrder.getData());
                //调用质检操作记录接口，记录操作记录
                String content = "生成微信1v1质检工单,工单ID【" + clientQcWorkOrder.getData() + "】";
                riskQcLogService.addLog(RiskQcOperationTypeEnum.CREATE_COMMON_WORK_ORDER, clientQcWorkOrder.getData(), content);
                Response<List<CfClueInfoModel>> listResponse = cfClewtrackTaskFeignClient.listCfClueInfo(List.of(cfClewTaskDO.getId()));
                String organization = this.getOrganization(userId, cfClewTaskDO.getUserId());
                // 添加搜索索引字段聚合表
                RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
                riskQcSearchIndex.setTaskId(cfClewTaskDO.getId());
                riskQcSearchIndex.setQcId(riskQcBaseInfo.getId());
                riskQcSearchIndex.setWorkOrderId(clientQcWorkOrder.getData());
                riskQcSearchIndex.setQcType(QcTypeEnum.WX_1V1.getCode());
                riskQcSearchIndex.setOrganization(organization);
                riskQcSearchIndex.setQcUniqueCode(cfClewTaskDO.getUserId());
                riskQcSearchIndex.setQcByName(cfClewTaskDO.getUserName());
                riskQcSearchIndex.setRegisterMobileEncrypt("");
                if (listResponse.ok() && CollectionUtils.isNotEmpty(listResponse.getData())) {
                    var cfClueInfoModel = listResponse.getData().get(0);
                    String registerMobile = cfClueInfoModel.getClewPhone();
                    if(StringUtils.isBlank(registerMobile)){
                        log.info("Qc1v1WorkOrderCreateConsumer registerMobile is null,listResponse:{},cfClueInfoModel:{}",
                                listResponse, cfClueInfoModel);
                        return ConsumeStatus.CONSUME_SUCCESS;
                    }
                    String registerMobilEncrypt = Optional.ofNullable(oldShuidiCipher.aesEncrypt(cfClueInfoModel.getClewPhone())).orElse(StringUtils.EMPTY);
                    riskQcSearchIndex.setRegisterMobileEncrypt(registerMobilEncrypt);
                    riskQcSearchIndex.setServiceStage(cfClueInfoModel.getServicePhase() == null ? 0 : cfClueInfoModel.getServicePhase());
                }
                riskQcSearchIndex.setJobContent(cfClewTaskDO.getWorkContentType() == null ? 0 : cfClewTaskDO.getWorkContentType());
                riskQcSearchIndexBiz.addSearchIndex(riskQcSearchIndex, qcWorkOrder.getOrderType());

                this.addOrgId(cfClewTaskDO.getUserId(),userId,clientQcWorkOrder.getData());

                List<RiskQualitySpotStrategy> riskQualitySpotStrategies = qualitySpotStrategyBiz.listByValidScene(QualitySpotSceneEnum.OFFLINE_Wx_1V1_WORK_ORDER.getCode());
                if (CollectionUtils.isNotEmpty(riskQualitySpotStrategies)) {
                    List<RiskQualitySpotRule> riskQualitySpotRules = qualitySpotRuleBiz.findByStrategyId(riskQualitySpotStrategies.get(0).getId());
                    riskQualitySpotRules = riskQualitySpotRules.stream().filter(v -> StringUtils.equals(v.getDataScopeString(), "0")).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(riskQualitySpotRules)) {
                        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(clientQcWorkOrder.getData());
                        if (response.notOk()) {
                            return ConsumeStatus.RECONSUME_LATER;
                        }
                        WorkOrderVO workOrderVO = response.getData();
                        qualityWx1V1Service.spotMaterial(workOrderVO);
                    }
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }

        } catch (Exception e) {
            log.error("Qc1v1WorkOrderCreateConsumer.consumeMessage error", e);
        } finally {
            if (StringUtils.isNotEmpty(identifier)) {
                cfRiskRedissonHandler.unLock(lockName, identifier);
            }
        }
        return ConsumeStatus.RECONSUME_LATER;
    }

    private void addOrgId(String mis, long userId, long workOrderId) {
        List<AuthGroupDto> adminOrganizations = null;
        if (mis.startsWith("SD")){
            Response<List<AuthGroupDto>> superiorOrgByUserIdWithDb = userGroupFeignClient.getUserOrgs(userId);
            if (superiorOrgByUserIdWithDb.ok()){
                adminOrganizations = superiorOrgByUserIdWithDb.getData();
            }
        }else {
            Response<List<AuthGroupDto>> superiorOrgByUserIdWithDb = userGroupFeignClient.getUserOrgs(userId);
            if (superiorOrgByUserIdWithDb.ok()){
                adminOrganizations = superiorOrgByUserIdWithDb.getData();
            }
        }
        if (CollectionUtils.isNotEmpty(adminOrganizations)){
            Set<Long> orgIds = Sets.newHashSet();
            for (AuthGroupDto adminOrganization : adminOrganizations) {
                orgIds.add(adminOrganization.getGroupBizId());
            }
            var workOrderOrgRels = orgIds.stream().map(orgId -> {
                WorkOrderOrgRel workOrderOrgRel = new WorkOrderOrgRel();
                workOrderOrgRel.setWorkOrderId(workOrderId);
                workOrderOrgRel.setOrgId(Math.toIntExact(orgId));
                return workOrderOrgRel;
            }).collect(Collectors.toList());
            cfQcWorkOrderClient.addOrgId(workOrderOrgRels);
        }
    }

    private String getOrganization(long userId, String mis) {
        //外包
        if (mis.startsWith("SD")) {
            Response<String> authRpcResponse = userGroupFeignClient.getGroupNameByUserId(userId);
            if (authRpcResponse.ok()) {
                return authRpcResponse.getData();
            }
            return "";
        }
        //内部
        Response<String> authRpcResponse = userGroupFeignClient.getGroupNameByUserId(userId);
        if (authRpcResponse.ok()) {
            return authRpcResponse.getData();
        }
        return "";
    }

    private long getOrgIdByUserId(long userId, String mis) {
        //外包
        if (mis.startsWith("SD")) {
            Response<AuthGroupDto> userOrgInfo = userGroupFeignClient.selectByUserId(userId);
            if (userOrgInfo.ok()) {
                return userOrgInfo.getData() == null ? 0 : userOrgInfo.getData().getGroupBizId();
            }
            return 0;
        }
        //内部
        Response<AuthGroupDto> userOrgInfo = userGroupFeignClient.selectByUserId(userId);
        if (userOrgInfo.ok()) {
            return userOrgInfo.getData() == null ? 0 : userOrgInfo.getData().getGroupBizId();
        }
        return 0;
    }


    private long getUserIdByMis(String mis) {
        //外包
        if (mis.startsWith("SD")) {
            Response<AuthUserDto> userAccountByMis = userFeignClient.getByLoginName(mis);
            if (userAccountByMis.ok()) {
                return userAccountByMis.getData() == null ? 0 : userAccountByMis.getData().getUserId();
            }
            return 0;
        }
        //内部
        Response<AuthUserDto> userAccountByMis = userFeignClient.getByLoginName(mis);
        if (userAccountByMis.ok()) {
            return userAccountByMis.getData() == null ? 0 : userAccountByMis.getData().getUserId();
        }
        return 0;
    }
}
