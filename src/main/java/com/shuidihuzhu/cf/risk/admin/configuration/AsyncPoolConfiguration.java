package com.shuidihuzhu.cf.risk.admin.configuration;

import brave.Tracing;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.*;

import static com.shuidihuzhu.cf.risk.admin.constant.AsyncPoolConstants.HANDLE_EXCEL_POOL;
import static com.shuidihuzhu.cf.risk.admin.constant.AsyncPoolConstants.HANDLE_WORK_ORDER_RECORDING_ASR;


/**
 * <AUTHOR>
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncPoolConfiguration {

    @Autowired
    private Tracing tracing;

    private Executor createExecutor(String nameFormat) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(nameFormat + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(2, 4, 10,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), threadFactory, new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.error("The {} is rejectedExecution", nameFormat);
                super.rejectedExecution(r, e);
            }
        });
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

    @Bean(HANDLE_EXCEL_POOL)
    public Executor handleExcelPool() {
        return createExecutor(HANDLE_EXCEL_POOL);
    }

    @Bean(HANDLE_WORK_ORDER_RECORDING_ASR)
    public Executor handleWorkOrderRecordingAsr() {
        return createExecutor(HANDLE_WORK_ORDER_RECORDING_ASR);
    }

}
