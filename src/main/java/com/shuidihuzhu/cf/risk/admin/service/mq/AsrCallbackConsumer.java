package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.alps.feign.config.OceanApiMqConfig;
import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcAsrResultVO;
import com.shuidihuzhu.cf.risk.admin.service.mdc.MdcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.recording.WorkOrderRecordingExtHandler;
import com.shuidihuzhu.cf.risk.admin.service.recording.WorkOrderRecordingHandlerRegister;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfAsrResultModel;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@RocketMQListener(id = "ai-asr_10007", tags = "ai-asr_10007", topic = OceanApiMqConfig.TOPIC)
@Slf4j
public class AsrCallbackConsumer extends MaliBaseMQConsumer<OceanApiMQResponse> implements MessageListener<OceanApiMQResponse> {

    @Autowired
    private QcAudioAsrService qcAudioAsrService;

    @Autowired
    private AiAsrDelegate aiAsrDelegate;

    @Autowired
    private WorkOrderRecordingHandlerRegister workOrderRecordingHandlerRegister;
    @Autowired
    private MdcAudioAsrService mdcAudioAsrService;

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    private final static Integer TEXT_LENGTH = 60000;

    @Override
    protected boolean handle(ConsumerMessage<OceanApiMQResponse> consumerMessage) {
        OceanApiMQResponse payload = consumerMessage.getPayload();
        log.info("AsrCallbackConsumer payload:{}", payload);
        QcAsrResultVO result = aiAsrDelegate.getResultByCallback(payload);
        if (result == null) {
            log.warn("AsrCallbackConsumer result null");
            return true;
        }
        switch (result.getHandleTypeEnum()) {
            case HANDLE_QC_BD:
                qcAudioAsrService.onAudioAsrCallback(result);
                break;
            case HANDLE_REPORT_RECORD:
            case HANDLE_MATERIAL_QC:
            case HANDLE_WX_1V1_QC_RECHECK:
            case HANDLE_MATERIAL_ACTIVE_SERVICE_QC:
            case HANDLE_INTERNAL_AUDIT_HIGH_RISK_QC:
                // 处理其他类型 的asr结果回调
                handleWorkOrderRecordingExt(result);
                break;
            case HANDLE_MDC_RECORD:
                mdcAudioAsrService.onAudioAsrCallback(result);
                break;
            case HANDLE_QI_WORK_RECORD:
                CfAsrResultModel asrResultModel = new CfAsrResultModel();
                asrResultModel.setId(result.getMaterialId());
                int resultLength = 0;
                QcAsrResultVO.Result sententceResult = result.getResult();
                List<QcAsrResultVO.Sentence> sentences = Lists.newArrayList();
                for (QcAsrResultVO.Sentence sentence : sententceResult.getSentences()) {
                    resultLength += JSON.toJSONString(sentence).getBytes(StandardCharsets.UTF_8).length;
                    // 针对超长的结果进行截断
                    if (resultLength > TEXT_LENGTH) {
                        break;
                    }
                    sentences.add(sentence);
                }
                asrResultModel.setAsrResult(JSONObject.toJSONString(sentences));
                cfClewtrackFeignClient.doHandleAsrResult(asrResultModel);
        }
        return true;
    }

    private void handleWorkOrderRecordingExt(QcAsrResultVO result) {
        WorkOrderRecordingExtHandler workOrderRecordingExtHandler = workOrderRecordingHandlerRegister.getBean(result.getHandleTypeEnum());
        if (workOrderRecordingExtHandler ==null) {
            log.info("WorkOrderCreateConsumer workOrderRecordingExtHandler getBean is null handleTypeEnum :{}", result.getHandleTypeEnum());
            return;
        }
        workOrderRecordingExtHandler.handleWorkOrderRecordingExt(result.getWorkOrderId(), result.getMaterialId(), result);
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
