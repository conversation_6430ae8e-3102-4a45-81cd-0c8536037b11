package com.shuidihuzhu.cf.risk.admin.rule.compile.action;

import com.shuidihuzhu.cf.risk.admin.rule.compile.value.AbstractCompileChain;
import com.shuidihuzhu.cf.risk.admin.rule.enums.ActionType;
import com.shuidihuzhu.cf.risk.admin.rule.enums.ValueType;
import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionAction;
import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/18 22:35
 */
@Component("returnValActionCompile")
@Slf4j
public class ReturnValActionCompile extends AbstractCompileChain<CriterionAction> {

    @Override
    public String compileValue(CriterionAction action) {
        if (action.getActionType() == ActionType.RETURN) {
            // TODO: houys 2020/6/19 未处理返回类型代码拼接
            return "";
        } else if(valueCompile != null) {
            return valueCompile.compileValue(new CriterionData(ValueType.CALL_METHOD, action.getCallMethod()));
        }
        return null;
    }

    @Resource(name = "callMethodValueCompile")
    @Override
    public void setValueCompile(AbstractCompileChain valueCompile) {
        this.valueCompile = valueCompile;
    }
}
