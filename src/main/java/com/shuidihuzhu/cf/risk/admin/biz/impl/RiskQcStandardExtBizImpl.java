package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandExtBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcStandardExtDao;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardExt;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
@Service
public class RiskQcStandardExtBizImpl implements RiskQcStandExtBiz {
    @Autowired
    private RiskQcStandardExtDao riskQcStandExtDao;

    @Override
    public List<RiskQcStandardExt> getByStandardIds(List<Long> standardIds) {
        if (CollectionUtils.isEmpty(standardIds)) {
            return Lists.newArrayList();
        }
        return riskQcStandExtDao.getByStandardIds(standardIds);
    }

    @Override
    public int add(long qcStandardId, long firstProperty, long secondProperty, int useScene) {
        if (qcStandardId < 0) {
            return 0;
        }
        return riskQcStandExtDao.add(qcStandardId, firstProperty, secondProperty, useScene);
    }

    @Override
    public List<RiskQcStandardExt> findByFirstPropertyAndScene(List<Long> firstPropertyIds, int useScene) {
        if (CollectionUtils.isEmpty(firstPropertyIds)) {
            return Lists.newArrayList();
        }
        return riskQcStandExtDao.findByFirstPropertyAndScene(firstPropertyIds, useScene);
    }
}
