package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingOrderFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.risk.admin.biz.DiscussionCheckBiz;
import com.shuidihuzhu.cf.risk.admin.dao.DiscussionCheckRecordDao;
import com.shuidihuzhu.cf.risk.admin.dao.DiscussionDao;
import com.shuidihuzhu.cf.risk.admin.dao.DiscussionRecordDao;
import com.shuidihuzhu.cf.risk.admin.delegate.ShortUrlDelegate;
import com.shuidihuzhu.cf.risk.admin.model.*;
import com.shuidihuzhu.cf.client.subject.caseend.CaseEndClient;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFundStateFeignClient;
import com.shuidihuzhu.cf.finance.enums.CfDiscussionEnum;
import com.shuidihuzhu.cf.risk.admin.dao.DiscussionStatDao;
import com.shuidihuzhu.cf.risk.admin.service.EventCenterService;
import com.shuidihuzhu.cf.risk.admin.service.MsgClientV2Service;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.client.constant.MQTagCons;
import com.shuidihuzhu.client.model.event.DiscussionEndEvent;
import com.shuidihuzhu.common.util.JsonUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;
import com.shuidihuzhu.pf.common.v2.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DiscussionCheckBizImpl implements DiscussionCheckBiz {

    @Autowired
    private DiscussionCheckRecordDao checkRecordDao;
    @Autowired
    private DiscussionDao discussionDao;
    @Autowired
    private DiscussionStatDao statDao;
    @Autowired
    private CaseEndClient caseEndClient;
    @Autowired
    private EventCenterService eventCenterService;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private CfFinanceFundStateFeignClient cfFinanceFundStateFeignClient;
    @Autowired
    private CrowdfundingOrderFeignClient cfOrderClient;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Autowired
    private DiscussionStatDao discussionStatDao;
    @Autowired
    private DiscussionRecordDao discussionRecordDao;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;
    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private CfFirstApproveClient cfFirstApproveClient;
    @Resource
    private ShortUrlDelegate shortUrlDelegate;
    @Override
    public Response pass(int userId, int caseId, String infoUuid) {

        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isEmpty(discussions)) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), ErrorCode.SYSTEM_PARAM_ERROR.getMsg(), null);
        }
        Discussion discussion = discussions.get(0);
        if (discussion.getCheckStatus() != 1 || discussion.getStatus() != 1){
            if (discussion.getCheckStatus() == 0){
                return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "评议信息未填写 无法审核", null);
            }
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "评议已关闭 无法审核", null);
        }
        com.shuidihuzhu.cf.finance.client.response.FeignResponse cfFundStateFeignResponse = null;
        try {
            log.info("检查案例资金状态 caseId:{}", caseId);
            cfFundStateFeignResponse = cfFinanceFundStateFeignClient.checkDiscussionFinanceState(caseId, CfDiscussionEnum.DiscussionType.AUDIT_APPLY);
            log.info("检查案例资金状态 responseCode:{}, caseId:{}",
                    cfFundStateFeignResponse == null ? -1 : cfFundStateFeignResponse.getCode(), caseId);
            if (cfFundStateFeignResponse != null && cfFundStateFeignResponse.notOk()){
                return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), cfFundStateFeignResponse.getMsg(), null);
            }
        } catch (Exception e) {
            log.error("", e);
        }

        try {
            //停止筹款
            log.info("评议审核通过 停止筹款 caseId:{} user:Id:{}", caseId, userId);
            Response<Object> response = caseEndClient.stopCase(caseId, CfFinishStatus.FINISH_BY_SHUIDI, userId, "评议审核通过,停止筹款");
            log.info("评议审核通过 停止筹款 responseCode:{} caseId:{} user:Id:{}", response == null ? -1 : response.getCode(), caseId, userId);
            if (response != null && response.notOk()){
                return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "评议审核通过,停止筹款失败", null);
            }
        } catch (Exception e) {
            log.error("评议审核通过 停止筹款 失败");
            log.error("", e);
        }

        String name = seaAccountService.getName(userId);
        String organization = seaAccountService.getOrganization(userId);
        DiscussionCheckRecord record = new DiscussionCheckRecord(discussion.getId(), 2, "", organization + name);
        record.buildInfo(StringUtils.trimToEmpty(discussion.getTitle()),
                StringUtils.trimToEmpty(discussion.getDescription()),
                StringUtils.trimToEmpty(discussion.getImages()));
        checkRecordDao.save(record);
        //评议统计
        statDao.save(discussion.getId(), caseId);
        //修改评议关闭时间
        int result = discussionDao.updateCloseTime(discussion.getId(), new Timestamp(System.currentTimeMillis() + (discussion.getCloseHour() * 1000 * 60 * 60)));
        //修改评议审核状态
        result += discussionDao.updateCheckStatus(2, 1, discussion.getId());
        if (result < 2) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "修改评议审核状态失败", null);
        }
        try {
            //消息发送
            if (caseId == 4374464 || caseId == 4394786) {
                //特殊案例不发筹款人消息
            } else {
                eventCenterService.eventSend(userId, UserOperationTypeEnum.DISCUSSION_CHECK_PASS.getCode(), caseId, infoUuid);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        //获取患者姓名
        String patientRealName = "";
        RpcResult<CfFirsApproveMaterial> firsApproveMaterialRpcResult = cfFirstApproveClient.selectFirstApproveByCaseId(caseId);
        CfFirsApproveMaterial cfFirsApproveMaterial = firsApproveMaterialRpcResult.getData();
        if(cfFirsApproveMaterial != null) {
            patientRealName = cfFirsApproveMaterial.getPatientRealName();
        }
        //获取捐款人信息
        List<Long> userIds = getCfOrderUserIds(caseId);
        List<UserInfoModel> userInfoModels = userInfoDelegateService.getUserInfoByUserIdBatch(userIds);
        //给捐款人发消息
        FeignResponse<CrowdfundingInfo> crowdfunding = crowdfundingFeignClient.getCaseInfoById(caseId);
        CrowdfundingInfo crowdfundingInfo = crowdfunding.getData();
        String url = crowdfundingInfo.getInfoId();
        for(UserInfoModel userInfoModel : userInfoModels){
            Map<Integer, String> wxParams = Maps.newHashMap();
            wxParams.put(1, url);
            wxParams.put(2, crowdfundingInfo.getTitle());
            wxParams.put(3, patientRealName);
            wxParams.put(5,discussion.getTitle());
            Map<Integer, String> msgParams = Maps.newHashMap();
            msgParams.put(1, url);
            msgParams.put(2, crowdfundingInfo.getTitle());
            msgParams.put(3, patientRealName);
            msgParams.put(4, getTrackUrl(crowdfundingInfo.getInfoId()));
            msgParams.put(5,discussion.getTitle());
            Map<String, Map<Integer, String>> msgMap = Maps.newHashMap();
            msgMap.put(userInfoModel.getCryptoMobile(), msgParams);
            Map<Long, Map<Integer, String>> wxMap = Maps.newHashMap();
            wxMap.put(userInfoModel.getUserId(),wxParams);
            msgClientV2Service.sendWxParamsMsg("PVP0673",wxMap);
            msgClientV2Service.sendSmsParamsMsg("YJE1021", msgMap, true);
        }
        DiscussionEndEvent discussionEndEvent = DiscussionEndEvent.builder()
                .caseId(discussion.getCaseId())
                .discussionId(discussion.getId())
                .build();
        Timestamp time = discussion.getCreateTime();
        log.info("测试发短信 start");
        if(discussion.getCloseHour() >= 24){
            MaliMQComponent.builder()
                    .setTags(MQTagCons.NOT_DISCUSSION_MSG)
                    .addKey(MQTagCons.NOT_DISCUSSION_MSG, System.currentTimeMillis())
                    .setPayload(discussionEndEvent)
                    .setDelayTime(24 * 60 * 60 * 1000L)
                    .send();
            time.setDate(time.getDay()-1);
            time.setHours(14);
            time.setMinutes(0);
            time.setSeconds(0);
            MaliMQComponent.builder()
                    .setTags(MQTagCons.NOT_DISCUSSION_MSG)
                    .addKey(MQTagCons.NOT_DISCUSSION_MSG, System.currentTimeMillis())
                    .setPayload(discussionEndEvent)
                    .setTargetTime(time.getTime())
                    .send();
        }
        if(discussion.getCloseHour() > 3 && discussion.getCloseHour() <24){
            //结束前3h发消息
            time.setHours(time.getHours()-3);
            MaliMQComponent.builder()
                    .setTags(MQTagCons.NOT_DISCUSSION_MSG)
                    .addKey(MQTagCons.NOT_DISCUSSION_MSG, System.currentTimeMillis())
                    .setPayload(discussionEndEvent)
                    .setTargetTime(time.getTime())
                    .send();
        }

        cfRiskRedissonHandler.del(Constant.DISCUSSION_KEY + caseId);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response refuse(int userId, int caseId, String infoUuid, String reason) {
        String name = seaAccountService.getName(userId);
        String organization = seaAccountService.getOrganization(userId);
        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isEmpty(discussions)) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), ErrorCode.SYSTEM_PARAM_ERROR.getMsg(), null);
        }
        Discussion discussion = discussions.get(0);
        if (discussion.getStatus() != 1){
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "评议已关闭 无法驳回", null);
        }
        DiscussionCheckRecord record = new DiscussionCheckRecord(discussion.getId(), 3, reason, organization + name);
        record.buildInfo(StringUtils.trimToEmpty(discussion.getTitle()),
                StringUtils.trimToEmpty(discussion.getDescription()),
                StringUtils.trimToEmpty(discussion.getImages()));
        int saveResult = checkRecordDao.save(record);
        if (saveResult < 1){
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "记录添加失败", null);
        }
        //修改评议审核状态
        int checkStatusResult = discussionDao.updateCheckStatus(3, 1, discussion.getId());
        if (checkStatusResult < 1) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "修改评议审核状态失败", null);
        }
        try {
            if (caseId == 4374464 || caseId == 4394786) {
                //特殊案例不发筹款人消息
            } else {
                //发送消息
                eventCenterService.eventSend(userId, UserOperationTypeEnum.DISCUSSION_CHECK_REFUSE.getCode(), caseId, infoUuid);
            }

        } catch (Exception e) {
            log.error("", e);
        }
        cfRiskRedissonHandler.del(Constant.DISCUSSION_KEY + caseId);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public PageResponse<DiscussionCheckRecord> refuseList(int caseId, String pageJson) {
        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isEmpty(discussions)) {
            return null;
        }
        List<Long> idList = discussions.stream().map(Discussion::getId).collect(Collectors.toList());
        PageRequest pageRequest = PageUtil.parseJsonString(pageJson);
        List<DiscussionCheckRecord> data = checkRecordDao.findListByDiscussionId(idList, 3, pageRequest);
        return PageUtil.buildPageResponse
                (data, pageRequest);
    }


    //获取捐款人id
    private List<Long> getCfOrderUserIds(int caseId) {
        int offset = 0;
        int limit = 1000;
        FeignResponse<List<CrowdfundingOrder>> orderFeignResponse = cfOrderClient.getListByInfoId(caseId,
                offset, limit);
        List<CrowdfundingOrder> crowdFundingOrderList = Lists.newArrayList();
        if (orderFeignResponse.ok()) {
            crowdFundingOrderList = orderFeignResponse.getData();
        }
        Set<Long> userIds = Sets.newHashSet();
        while (!org.apache.commons.collections4.CollectionUtils.isEmpty(crowdFundingOrderList)) {
            List<Long> uids = crowdFundingOrderList.stream()
                    .map(CrowdfundingOrder::getUserId)
                    .collect(Collectors.toList());
            userIds.addAll(uids);

            offset += crowdFundingOrderList.size();
            orderFeignResponse = cfOrderClient.getListByInfoId(caseId, offset, limit);
            if (orderFeignResponse.ok()) {
                crowdFundingOrderList = orderFeignResponse.getData();
            }
        }
        return Lists.newArrayList(userIds);
    }
    private String getTrackUrl(String infoUuid) {
        String url = "https://www.shuidichou.com/cf/discussion/detail/" + infoUuid;
        String shortUrl = shortUrlDelegate.process(url);
        if (StringUtils.isNotEmpty(shortUrl)) {
            return shortUrl;
        }
        return StringUtils.EMPTY;
    }
}
