package com.shuidihuzhu.cf.risk.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckContext;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordCheckFeignV2Client;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandExtBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandardPropertyBiz;
import com.shuidihuzhu.cf.risk.admin.delegate.CreditRiskDelegate;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.*;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardExt;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty;
import com.shuidihuzhu.cf.risk.admin.model.vo.CreditInfoVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQc1v1DetailInfoVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcWorkOrderVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.qc1v1.ClewPreposeMaterialVo;
import com.shuidihuzhu.cf.risk.client.highrisk.HighRiskClient;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDto;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingChaiFenV2FeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueCallRecordSumModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreposeMaterialSaveOrUpdateModel;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.kratos.client.feign.WxChatMessageFeignClient;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageSearchParam;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageSearchResult;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/8/73
 */
@Service
@RefreshScope
@Slf4j
public class Risk1v1QcDetailService {
    @Value("${qc.critical.error:顾客关键错误,业务关键错误}")
    private String criticalError;
    @Value("${qc.noncritical.error:顾客非关键错误,业务非关键错误}")
    private String nonCriticalError;

    private static final String RAISE_COMMENT = "常规表扬二级,常规表扬三级,常规表扬较优";

    private static final int SEARCH_DAY_MAX = 7;
    private static final String VOICE = "voice";
    private static final String VIDEO = "video";

    @Autowired
    private RiskQcStandExtBiz riskQcStandExtBiz;
    @Autowired
    private RiskQcStandardPropertyBiz riskQcStandardPropertyBiz;
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private CrowdfundingChaiFenV2FeignClient crowdfundingChaiFenFeignClient;
    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Autowired
    private RiskQcCasInfoService riskQcCasInfoService;
    @Autowired
    private WxChatMessageFeignClient wxChatMessageFeignClient;
    @Autowired
    private RiskControlWordCheckFeignV2Client riskControlWordCheckFeignV2Client;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private CfWorkOrderClient workOrderClient;
    @Autowired
    private SeaAccountService accountService;
    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Resource
    private HighRiskClient highRiskClient;
    @Autowired
    private MaskUtil maskUtil;

    @Resource
    private CreditRiskDelegate creditRiskDelegate;


    public RiskQcCalculateResult getResult(List<Long> ids, Integer scene) {
        if (CollectionUtils.isEmpty(ids)) {
            return new RiskQcCalculateResult(RiskQcResultEnum.QUALIFIED.getType(), 0, 0, 0);
        }
        List<RiskQcStandardExt> riskQcStandardExts = riskQcStandExtBiz.getByStandardIds(ids);
        List<Long> propertyIds = riskQcStandardExts.stream().map(RiskQcStandardExt::getSecondPropertyId).collect(Collectors.toList());
        List<RiskQcStandardProperty> riskQcStandardProperties = riskQcStandardPropertyBiz.getByIds(propertyIds);
       /* riskQcStandardProperties = riskQcStandardProperties.stream()
                .filter(t -> t.getPropertyType() == QcTypeEnum.WX_1V1.getCode()).collect(Collectors.toList());*/
        Map<Long, RiskQcStandardProperty> riskQcStandardPropertyMap =
                riskQcStandardProperties.stream().collect(Collectors.toMap(RiskQcStandardProperty::getId, Function.identity(), (t1, t2) -> t2));
        int criticalErrorCount = 0;
        int nonCriticalErrorCount = 0;
        int praiseCount =0;
        for (RiskQcStandardExt riskQcStandardExt : riskQcStandardExts) {
            if (scene != null && riskQcStandardExt.getUseScene() != scene){
                continue;
            }
            RiskQcStandardProperty riskQcStandardProperty = riskQcStandardPropertyMap.get(riskQcStandardExt.getSecondPropertyId());
            if (riskQcStandardProperty != null &&
                    Splitter.on(",").splitToList(criticalError).contains(riskQcStandardProperty.getProperty())) {
                criticalErrorCount++;
            } else if (riskQcStandardProperty != null &&
                    Splitter.on(",").splitToList(nonCriticalError).contains(riskQcStandardProperty.getProperty())) {
                nonCriticalErrorCount++;
            }else if(riskQcStandardProperty != null &&
                    Splitter.on(",").splitToList(RAISE_COMMENT).contains(riskQcStandardProperty.getProperty())){
                praiseCount++;
            }
        }
        RiskQcCalculateResult riskQcCalculateResult = new RiskQcCalculateResult();
        riskQcCalculateResult.setCriticalError(criticalErrorCount);
        riskQcCalculateResult.setNonCriticalError(nonCriticalErrorCount);
        riskQcCalculateResult.setPraiseCount(praiseCount);
        riskQcCalculateResult.setQcResult(criticalErrorCount >= 1 || nonCriticalErrorCount >= 3 ? RiskQcResultEnum.NO_QUALIFIED.getType() : RiskQcResultEnum.QUALIFIED.getType());
        log.info("get-calculate-info result {}, resultDetail {}", riskQcCalculateResult.getQcResult(), riskQcCalculateResult);
        return riskQcCalculateResult;
    }

    public RiskQc1v1DetailInfoVo getClewTaskInfo(long clewTaskId, long workOrderId) {
        return buildDetailInfo(clewTaskId, workOrderId);
    }

    public RiskQc1v1DetailInfoVo buildDetailInfo(long clewTaskId, long workOrderId) {
        RiskQcClewTaskInfo riskQcClewTaskInfo = null;
        RiskQcCaseInfo riskQcCaseInfo = null;
        ClewPreposeMaterialVo clewPreposeMaterialVo = null;
        CreditInfoVo creditInfoVo = null;
        RiskQcWorkOrderVo workOrderVo = null;

        Response<List<CfClueInfoModel>> response = cfClewtrackTaskFeignClient.listCfClueInfo(Lists.newArrayList(clewTaskId));
        List<CfClueInfoModel> cfClueInfoModels = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());

        if (CollectionUtils.isNotEmpty(cfClueInfoModels)) {
            CfClueInfoModel cfClueInfoModel = cfClueInfoModels.get(0);

            riskQcClewTaskInfo = RiskQcClewTaskInfo.buildInfo(cfClueInfoModel, maskUtil).buildClewTaskName(seaAccountService.getInfoByMis(cfClueInfoModel.getUserId()));

            if (Objects.nonNull(cfClueInfoModel.getInfoId()) && cfClueInfoModel.getInfoId() > 0L) {
                riskQcCaseInfo = getRiskQcCaseInfo(Math.toIntExact(cfClueInfoModel.getInfoId()), cfClueInfoModel);
                creditInfoVo = riskQcCasInfoService.addCreditInfo(Math.toIntExact(cfClueInfoModel.getInfoId()));
                creditInfoVo.setCaseVersion(riskQcCasInfoService.getVersion((Math.toIntExact(cfClueInfoModel.getInfoId()))));
            }
            if (Objects.nonNull(cfClueInfoModel.getClewId())) {
                clewPreposeMaterialVo = getPreposeMaterialInfo(cfClueInfoModel.getClewId());
                Optional.ofNullable(clewPreposeMaterialVo)
                                .ifPresent(r -> r.setSelfCryptoIdcard(StringUtils.EMPTY));
                replaceCodes(clewPreposeMaterialVo, Math.toIntExact(cfClueInfoModel.getInfoId()));
            }
            if (workOrderId > 0L) {
                final long qcOrderId = riskQcDetailService.getQcOrderIdByRcOrderId(workOrderId);
                Response<WorkOrderVO> getResponse = workOrderClient.getWorkOrderById(qcOrderId);
                WorkOrderVO vo = Optional.ofNullable(getResponse).filter(Response::ok).map(Response::getData).orElse(null);
                if (Objects.nonNull(vo)) {
                    final long operatorId = vo.getOperatorId();
                    workOrderVo = new RiskQcWorkOrderVo();
                    workOrderVo.setWorkOrderId(qcOrderId);
                    workOrderVo.setOperatorName(operatorId > 0L ? accountService.getName(operatorId) : StringUtils.EMPTY);
                    workOrderVo.setCreateTime(vo.getCreateTime());
                    workOrderVo.setFinishTime(vo.getFinishTime());
                }
            }
        }

        return RiskQc1v1DetailInfoVo.builder()
                .creditInfo(creditInfoVo)
                .preposeMaterial(clewPreposeMaterialVo)
                .riskQcClewTaskInfo(riskQcClewTaskInfo)
                .riskQcCaseInfo(riskQcCaseInfo)
                .workOrderVO(workOrderVo)
                .build();
    }


    public RiskQcCaseInfo getRiskQcCaseInfo(int caseId, CfClueInfoModel cfClueInfoModel) {

        FeignResponse<CrowdfundingInfo> infoResponse = crowdfundingFeignClient.getCaseInfoById(caseId);
        CrowdfundingInfo crowdfundingInfo = Optional.ofNullable(infoResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);

        FeignResponse<CfInfoExt> cfInfoExtFeignResponse = crowdfundingFeignClient.getCfInfoExtByCaseId(caseId);
        CfInfoExt cfInfoExt = Optional.ofNullable(cfInfoExtFeignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);

        CfInfoStat cfInfoStat = null;
        Response<String> response = crowdfundingChaiFenFeignClient.mapByIds(Lists.newArrayList(caseId));
        String infoStat = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (StringUtils.isNotBlank(infoStat)) {
            Map<Integer, CfInfoStat> cfInfoStatMap = JSON.parseObject(infoStat, new TypeReference<Map<Integer, CfInfoStat>>() {
            });
            cfInfoStat = cfInfoStatMap.getOrDefault(caseId, null);
        }

        return RiskQcCaseInfo.riskQcCaseInfo(crowdfundingInfo, cfInfoExt, cfInfoStat).setPreMsgStatusDesc(cfClueInfoModel.getPreMsgStatusDesc());
    }

    public ClewPreposeMaterialVo getPreposeMaterialInfo(Long clewId) {
        Response<ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel> response = clewPreproseMaterialFeignClient.getMaterialInfoVoByClewId(clewId);

        return ClewPreposeMaterialVo.buildPreposeVo(Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null), maskUtil);
    }


    public RiskQcTotalCallRecords getCallRecord(long clewTaskId) {
        Response<List<CfClueInfoModel>> cfClueInfoResponse = cfClewtrackTaskFeignClient.listCfClueInfo(Lists.newArrayList(clewTaskId));
        log.info("cfClewtrackTaskFeignClient listCfClueInfo response:{}", JSON.toJSONString(cfClueInfoResponse));
        if (cfClueInfoResponse == null || cfClueInfoResponse.notOk() || CollectionUtils.isEmpty(cfClueInfoResponse.getData())){
            return null;
        }
        CfClueInfoModel cfClueInfoModel = cfClueInfoResponse.getData().get(0);
        String mis = cfClueInfoModel.getUserId();
        String encryptPhone = oldShuidiCipher.aesEncrypt(cfClueInfoModel.getClewPhone());
        String encryptSecondPhone = oldShuidiCipher.aesEncrypt(cfClueInfoModel.getClewSecondPhone());
        Date time = DateUtil.parseDateTime(cfClueInfoModel.getAssignTime());
        return buildRiskQcTotalCallRecords(mis, encryptPhone, encryptSecondPhone, time);
    }

    public RiskQcTotalCallRecords getCallRecordByBesides(long clewTaskId){
        Response<List<CfClewTaskModel>> response = cfClewtrackTaskFeignClient.getClewTaskModel(Lists.newArrayList(clewTaskId));
        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
            return null;
        }
        CfClewTaskModel cfClewTaskModel = response.getData().get(0);
        String encryptPhone =  oldShuidiCipher.aesEncrypt(cfClewTaskModel.getMobile());
        String mis = cfClewTaskModel.getClewUserId();
        Date assignTime =  cfClewTaskModel.getAssignTime();
        return buildRiskQcTotalCallRecords(mis, encryptPhone, null, assignTime);
    }

    public RiskQcTotalCallRecords buildRiskQcTotalCallRecords(String mis, String encryptPhone,
                                                              String encryptSecondPhone, Date time){
        Response<CfClueCallRecordSumModel> response = cfClewtrackTaskFeignClient.getCallRecordSummaryByMisAndPhone(mis, encryptPhone, encryptSecondPhone, time == null ? null : time.getTime());
        log.info("cfClewtrackTaskFeignClient getCallRecordSummaryByMisAndPhone response:{}", JSON.toJSONString(response));
        if (response == null || response.notOk() || response.getData() == null) {
            return null;
        }
        CfClueCallRecordSumModel cfClueCallRecordSumModel = response.getData();
        return RiskQcTotalCallRecords.buildInfo(cfClueCallRecordSumModel).buildRiskQcCallRecords(getCallRecords(mis, encryptPhone, encryptSecondPhone, time));
    }

    private List<RiskQcCallRecords> getCallRecords(String mis, String encryptPhone, String encryptSecondPhone, Date time) {
        Response<List<CfClewCallRecordsDO>> response = cfClewtrackTaskFeignClient.listCallRecordByMisAndPhone(mis, encryptPhone, encryptSecondPhone, time == null ? null : time.getTime());
        log.info("listCallRecordByMisAndPhone response:{}", JSON.toJSONString(response));
        List<RiskQcCallRecords> riskQcCallRecords = Lists.newArrayList();
        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())) {
            return riskQcCallRecords;
        }
        List<CfClewCallRecordsDO> cfClewCallRecordsDOS = response.getData();
        for (CfClewCallRecordsDO cfClewCallRecordsDO : cfClewCallRecordsDOS){
            RiskQcCallRecords records = RiskQcCallRecords.buildRecords(cfClewCallRecordsDO);
            if (records != null) {
                records.setMobile(shuidiCipher.decrypt(records.getMobile()));
                riskQcCallRecords.add(records);
            }
        }
        return riskQcCallRecords.stream().sorted(Comparator.comparing(RiskQcCallRecords::getStartTime).reversed()).collect(Collectors.toList());
    }

    public List<RiskQcChatRecordDetailInfo> getChatSearch(long clewTaskId, int pageNum, int pageSize, String searchTime) {
        Response<List<CfClueInfoModel>> response = cfClewtrackTaskFeignClient.listCfClueInfo(Lists.newArrayList(clewTaskId));
        if (response != null && response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            CfClueInfoModel cfClueInfoModel = response.getData().get(0);
            if (!cfClueInfoModel.isHasQywxRecord()){
                return Lists.newArrayList();
            }
            ChatMessageSearchParam chatMessageSearchParam = new ChatMessageSearchParam();
            chatMessageSearchParam.setCorpId(cfClueInfoModel.getCorpId());
            chatMessageSearchParam.setUserId(cfClueInfoModel.getQyWechatUserId());
            chatMessageSearchParam.setExternalUserId(cfClueInfoModel.getExternalUserid());
            chatMessageSearchParam.setLimit(pageSize);
            chatMessageSearchParam.setSkip((pageNum - 1) * pageSize);
            chatMessageSearchParam.setStartTime(getTime(searchTime).getTime());
            chatMessageSearchParam.setEndTime(DateUtil.addDay(getTime(searchTime), 1).getTime());
            ChatMessageSearchResult chatMessageSearchResult = getChatMessageSearchResult(chatMessageSearchParam);
            List<ChatMessageVo> chatMessageVos = chatMessageSearchResult == null ? null : chatMessageSearchResult.getMessageList();
            if (CollectionUtils.isEmpty(chatMessageVos)) {
                return Lists.newArrayList();
            }
            List<RiskQcChatRecordDetailInfo> riskQcChatRecordDetailInfos = Lists.newArrayList();
            chatMessageVos.forEach(chatMessageVo -> {
                riskQcChatRecordDetailInfos.add(
                        RiskQcChatRecordDetailInfo.buildInfo(chatMessageVo).buildRiskQcChatKeywordsInfo(getKeyWordsInfo(chatMessageVo.getContent())));
            });
            return riskQcChatRecordDetailInfos;
        }
        return Lists.newArrayList();
    }

    public List<RiskQcChatKeywordsInfo> getKeyWordsInfo(String content) {
        if (StringUtils.isBlank(content)) {
            return Lists.newArrayList();
        }
        RiskWordCheckContext ctx = RiskWordCheckContext.builder()
                .content(content)
                .useScenes(Lists.newArrayList(RiskControlWordCategoryDO.RiskWordUseScene.WX_1V1_CHAT_RECORD_QC.getCode()))
                .isCheckAll(true)
                .build();
        RpcResult<RiskWordResult> riskWordResultRpcResult = riskControlWordCheckFeignV2Client.check(ctx);
        log.info("riskControlWordCheckFeignV2Client riskWordResultRpcResult:{}", JSON.toJSONString(riskWordResultRpcResult));
        List<RiskQcChatKeywordsInfo> riskQcChatKeywordsInfos = Lists.newArrayList();
        if (riskWordResultRpcResult != null && riskWordResultRpcResult.isSuccess() && riskWordResultRpcResult.getData() != null) {
            RiskWordResult riskWordResult = riskWordResultRpcResult.getData();
            List<CfBaseInfoRiskHitVO.ColourTag> colourTags = Optional.ofNullable(riskWordResult.getHitWordColourTags()).orElse(Lists.newArrayList());
            colourTags.forEach(colourTag -> {
                riskQcChatKeywordsInfos.add(RiskQcChatKeywordsInfo.builder().color(colourTag.getColourChoice())
                        .colorTag(colourTag.getColourTag()).keyWord(colourTag.getHitWord()).build());
            });
        }
        return riskQcChatKeywordsInfos;
    }

    public List<RiskQcChatTotalCount> getChatRecordCount(long clewTaskId) {
        Response<List<CfClueInfoModel>> response = cfClewtrackTaskFeignClient.listCfClueInfo(Lists.newArrayList(clewTaskId));
        if (response != null && response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            CfClueInfoModel cfClueInfoModel = response.getData().get(0);
            return buildChatCount(cfClueInfoModel);
        }
        return Lists.newArrayList();
    }

    public List<RiskQcChatTotalCount> buildChatCount(CfClueInfoModel cfClueInfoModel) {
        List<RiskQcChatTotalCount> riskQcChatTotalCounts = Lists.newArrayList();
        if (cfClueInfoModel != null && !StringUtils.isBlank(cfClueInfoModel.getAssignTime())) {
            if (!cfClueInfoModel.isHasQywxRecord()){
                return Lists.newArrayList();
            }
            ChatMessageSearchParam chatMessageSearchParam = new ChatMessageSearchParam();
            chatMessageSearchParam.setCorpId(cfClueInfoModel.getCorpId());
            chatMessageSearchParam.setUserId(cfClueInfoModel.getQyWechatUserId());
            chatMessageSearchParam.setExternalUserId(cfClueInfoModel.getExternalUserid());
            chatMessageSearchParam.setLimit(1);
            chatMessageSearchParam.setSkip(0);
            String assignTime = cfClueInfoModel.getAssignTime();
            if (StringUtils.isBlank(assignTime)) {
                return riskQcChatTotalCounts;
            }
            Date assignDate = getTime(assignTime);
            for (int day = 0; day < SEARCH_DAY_MAX; day++) {
                Date startDate = DateUtil.addDay(assignDate, day);
                Date endDate = DateUtil.addDay(assignDate, (day + 1));
                if (startDate.compareTo(DateUtil.nowDate()) > 0) {
                    break;
                }
                chatMessageSearchParam.setStartTime(startDate.getTime());
                chatMessageSearchParam.setEndTime(endDate.getTime());
                ChatMessageSearchResult chatMessageSearchResult = getChatMessageSearchResult(chatMessageSearchParam);
                riskQcChatTotalCounts.add(new RiskQcChatTotalCount(startDate, chatMessageSearchResult == null ? 0 : chatMessageSearchResult.getTotalCount()));
            }
        }
        riskQcChatTotalCounts =
                riskQcChatTotalCounts.stream().filter(t -> t.getRecordCount() > 0).collect(Collectors.toList());
        return riskQcChatTotalCounts;
    }

    public ChatMessageSearchResult getChatMessageSearchResult(ChatMessageSearchParam chatMessageSearchParam) {
        Response<ChatMessageSearchResult> resultResponse = wxChatMessageFeignClient.searchMessageList(chatMessageSearchParam);
        log.info("wxChatMessageFeignClient resultResponse:{}", JSON.toJSONString(resultResponse));
        if (resultResponse != null && resultResponse.ok() && resultResponse.getData() != null) {
            return resultResponse.getData();
        }
        return null;
    }

    private Date getTime(String time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.parseDateTime(time));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    private void replaceCodes(ClewPreposeMaterialVo clewPreposeMaterialVo, int caseId) {
        if (Objects.isNull(clewPreposeMaterialVo)) {
            return;
        }
        clewPreposeMaterialVo.setRiskLabels(creditRiskDelegate.getRiskLabels(caseId));
    }
}
