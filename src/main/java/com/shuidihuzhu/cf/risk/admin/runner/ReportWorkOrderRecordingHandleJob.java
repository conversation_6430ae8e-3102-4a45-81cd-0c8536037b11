package com.shuidihuzhu.cf.risk.admin.runner;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.service.recording.support.WorkOrderRecordingHandlerReport;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderTableParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.enums.ErrorCode;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.elasticjob.annotation.ElasticTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@ElasticTask(jobName = "reportWorkOrderRecordingHandleJob",
        description = "举报工单录音处理job",
        cron = "0 5 0 * * ?", errorEmails = {"<EMAIL>"}, autoTriggerForTest = false)
@Slf4j
@RefreshScope
public class ReportWorkOrderRecordingHandleJob extends AbstractSimpleJobEnhancer {
    @Autowired
    private CfSearchClient cfSearchClient;
    @Autowired
    private WorkOrderRecordingHandlerReport workOrderRecordingHandlerReport;

    @Override
    public void doRealExecute(ShardingContext shardingContext) {
        log.info("ReportWorkOrderRecordingHandleJob start......");
        //查询工单
        List<Integer> handleResults = Lists.newArrayList(HandleResultEnum.noneed_deal.getType(), HandleResultEnum.end_deal.getType(), HandleResultEnum.end_deal_lost.getType());
        CfWorkOrderIndexSearchResult searchResult = queryWorkOrderBySearch(WorkOrderType.REPORT_TYPES, handleResults, DateUtil.addDay(DateUtil.getCurrentDate(), -1), DateUtil.getCurrentDate());
        List<CfWorkOrderModel> workOrderModels = Optional.ofNullable(searchResult).map(CfWorkOrderIndexSearchResult::getModels).orElse(Lists.newArrayList());
        //根据工单id查询电话流水号
        workOrderModels.forEach(item -> workOrderRecordingHandlerReport.handleWorkOrderRecording(item.getWorkOrderId()));
        log.info("ReportWorkOrderRecordingHandleJob end......");
    }


    public CfWorkOrderIndexSearchResult queryWorkOrderBySearch(List<Integer> orderTypes,
                                                               List<Integer> handleResults, Date startHandleTime, Date endHandleTime) {
        /**
         * 构建工单表的相关参数
         */
        WorkOrderTableParam woTableParam = new WorkOrderTableParam();
        woTableParam.setOrderTypes(orderTypes);
        woTableParam.setHandleResult(handleResults);
        woTableParam.setHandleStartTime(startHandleTime.getTime());
        woTableParam.setHandleEndTime(endHandleTime.getTime());
        CfWorkOrderV2IndexSearchParam searchParam = new CfWorkOrderV2IndexSearchParam();
        searchParam.setWoTableParam(woTableParam);

        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        if (Objects.isNull(searchRpcResult) || ErrorCode.SUCCESS.getCode() != searchRpcResult.getCode() || Objects.isNull(searchRpcResult.getData())) {
            return null;
        }
        return searchRpcResult.getData();
    }
}

