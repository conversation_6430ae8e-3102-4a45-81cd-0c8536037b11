package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.*;
import com.shuidihuzhu.cf.risk.admin.controller.PublicSentimentController;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPsProgressDao;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPublicSentimentInfoDao;
import com.shuidihuzhu.cf.risk.admin.model.*;
import com.shuidihuzhu.cf.risk.admin.model.enums.PsProgressActionEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.PublicSentimentInfoSourceEnum;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskPsSimpleInfoVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskPublicSentimentListVo;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPsInfoTypeBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPublicSentimentCorrespondBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPublicSentimentDetailBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskPublicSentimentInfoBiz;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import lombok.extern.slf4j.Slf4j;
import com.shuidihuzhu.common.web.util.ContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.sql.Timestamp;

/**
 * @Auther: subing
 * @Date: 2020/2/20
 */
@Service
@Slf4j
public class RiskPublicSentimentInfoBizImpl implements RiskPublicSentimentInfoBiz {
    @Autowired
    private RiskPublicSentimentInfoDao riskPublicSentimentInfoDao;
    @Autowired
    private RiskPublicSentimentDetailBiz sentimentDetailService;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private RiskPsInfoTypeBiz infoTypeService;
    @Autowired
    private RiskPsProgressDao psProgressDao;
    @Autowired
    private RiskPsInfoTypeBiz riskPsInfoTypeBiz;
    @Autowired
    private RiskPublicSentimentCorrespondBiz riskPublicSentimentCorrespondBiz;
    @Autowired
    private RiskPsProgressBiz riskPsProgressBiz;
    @Autowired
    private RiskPsOperationLogBiz operationLogService;
    @Autowired
    private RiskPsHandleRecordBiz riskPsHandleRecordBiz;

    @Override
    public int add(String riskPublicSentimentInfoJson, String detailInformationJson, long adminUserId) {
        RiskPublicSentimentInfo riskPublicSentimentInfo = buildInfo(riskPublicSentimentInfoJson);
        if (riskPublicSentimentInfo == null) {
            return 0;
        }
        String operator = "";

        if (adminUserId> 0) {
            operator = seaAccountService.getOrganization(adminUserId) + seaAccountService.getName(adminUserId);
        }
        riskPublicSentimentInfo.setLastOperator(operator);
        if (riskPublicSentimentInfo.getInfoClassify() == null) {
            riskPublicSentimentInfo.setInfoClassify(-1);
        }
        int result = riskPublicSentimentInfoDao.add(riskPublicSentimentInfo);
        RiskPublicSentimentDetail detail = sentimentDetailService.add(detailInformationJson, riskPublicSentimentInfo.getId());
        if (result > 0) {
            long psId = riskPublicSentimentInfo.getId();
            RiskPsProgress publish = new RiskPsProgress(psId, operator,
                    PsProgressActionEnum.PUBLISH.getCode(),
                    "",
                    riskPublicSentimentInfo.getPublishTime());
            RiskPsProgress infoEntering = new RiskPsProgress(psId, operator,
                    PsProgressActionEnum.INFO_ENTERING.getCode(),
                    StringUtils.trimToEmpty(detail.getFermentationCondition()),
                    new Timestamp(System.currentTimeMillis()));
            psProgressDao.saveList(Arrays.asList(publish, infoEntering));
            operationLogService.add(new RiskPsOperationLog(psId, operator, "录入舆情信息"));
        }
        return result;
    }

    @Override
    public int updateById(String riskPublicSentimentInfoJson, long id, String detailInformationJson, long adminUserId) {
        RiskPublicSentimentInfo riskPublicSentimentInfo = buildInfo(riskPublicSentimentInfoJson);
        if (riskPublicSentimentInfo == null) {
            return 0;
        }
        riskPublicSentimentInfo.setId(id);
        String operator = "";

        if (adminUserId > 0) {
            operator = seaAccountService.getOrganization(adminUserId) + seaAccountService.getName(adminUserId);
        }
        riskPublicSentimentInfo.setLastOperator(operator);
        int result = riskPublicSentimentInfoDao.updateById(riskPublicSentimentInfo);
        int result1 = sentimentDetailService.updateByPublicSentimentId(detailInformationJson, id);
        operationLogService.add(new RiskPsOperationLog(id, operator, "编辑舆情信息"));
        return (result > 0 && result1 > 0) ? 1 : 0;
    }

    @Override
    public RiskPublicSentimentInfo getInfoById(long id) {
        if (id <= 0) {
            return null;
        }
        return riskPublicSentimentInfoDao.getInfoById(id);
    }

    @Override
    public Map<String, Object> getInfoList(int infoSource, int infoFeedBack, int infoClassify, String startTime, String endTime, String publicSentimentInfoType, int solution, int caseId,
                                                               int pageNo, int pageSize, String lastOperator, String disposeStatus, String infoFeedBackOther) {
        String solutionString = solution < 0 ? null : String.valueOf(solution);
        infoFeedBack = StringUtils.isBlank(infoFeedBackOther) ? infoFeedBack : 0;
        PageHelper.startPage(pageNo, pageSize);
        PageInfo<RiskPublicSentimentInfo> pageResult = new PageInfo<>(riskPublicSentimentInfoDao.getInfoList(infoSource, infoFeedBack, infoClassify, StringUtils.trimToNull(startTime),
                StringUtils.trimToNull(endTime), StringUtils.trimToNull(publicSentimentInfoType), solutionString, caseId, StringUtils.trimToNull(lastOperator), disposeStatus));
        List<RiskPublicSentimentInfo> riskPublicSentimentInfos = pageResult.getList();
        List<Long> ids = riskPublicSentimentInfos.stream().map(RiskPublicSentimentInfo::getId).collect(Collectors.toList());
        List<RiskPublicSentimentDetail> riskPublicSentimentDetails = sentimentDetailService.getByPublicSentimentIds(ids);
        List<Integer> infoSources = riskPublicSentimentInfos.stream().map(RiskPublicSentimentInfo::getInfoSource).collect(Collectors.toList());
        List<RiskSourceCorrespond> riskSourceCorresponds = riskPublicSentimentCorrespondBiz.getByInfoSources(infoSources);

        Map<Long, RiskPublicSentimentDetail> riskPublicSentimentDetailMap =
                riskPublicSentimentDetails.stream().collect(Collectors.toMap(RiskPublicSentimentDetail::getPublicSentimentId, Function.identity(), (old, news) -> news));
        Map<Integer, RiskSourceCorrespond> riskSourceCorrespondMap =
                riskSourceCorresponds.stream().collect(Collectors.toMap(RiskSourceCorrespond::getInfoFeedBack, Function.identity(), (old, news) -> news));
        List<RiskPublicSentimentListVo> riskPublicSentimentListVos = Lists.newArrayList();
        riskPublicSentimentInfos.forEach(riskPublicSentimentInfo -> {
            buildList(riskPublicSentimentDetailMap, riskSourceCorrespondMap, riskPublicSentimentInfo, riskPublicSentimentListVos);
        });
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("pageNo", pageResult.getPageNum());
        resultMap.put("pageSize", pageResult.getPageSize());
        resultMap.put("pages", pageResult.getPages());
        resultMap.put("total", pageResult.getTotal());
        resultMap.put("result", riskPublicSentimentListVos);
        return resultMap;
    }

    private void buildList(Map<Long, RiskPublicSentimentDetail> riskPublicSentimentDetailMap,
                           Map<Integer, RiskSourceCorrespond> riskSourceCorrespondMap,
                           RiskPublicSentimentInfo riskPublicSentimentInfo,
                           List<RiskPublicSentimentListVo> riskPublicSentimentListVos) {
        RiskPublicSentimentDetail riskPublicSentimentDetail =
                riskPublicSentimentDetailMap.get(riskPublicSentimentInfo.getId());
        RiskSourceCorrespond riskSourceCorrespond = riskSourceCorrespondMap.get(riskPublicSentimentInfo.getInfoFeedBack());
        if (riskPublicSentimentDetail != null) {
            Timestamp time;
            RiskPsProgress riskPsProgress = Optional.ofNullable(riskPsProgressBiz.getLastProgress(riskPublicSentimentInfo.getId())).orElse(new RiskPsProgress());
            if (riskPublicSentimentInfo.getStatus() != 0) {
                RiskPsHandleRecord riskPsHandleRecord = Optional.ofNullable(riskPsHandleRecordBiz.getLastByPsId(riskPublicSentimentInfo.getId())).orElse(new RiskPsHandleRecord());
                time = riskPsHandleRecord.getCreateTime();
            } else {
                time = riskPsProgress.getCreateTime();
            }
            String infoFeedBackInfo = riskSourceCorrespond == null ? "" : riskSourceCorrespond.getInfoFeedBackContent();
            riskPublicSentimentListVos.add(RiskPublicSentimentListVo.buildVo(riskPublicSentimentInfo, riskPublicSentimentDetail, infoFeedBackInfo, riskPsProgress.getBizTime(), riskPsProgress.getFermentationCondition(), time, riskPsInfoTypeBiz.getByPath(riskPublicSentimentInfo.getPublicSentimentInfoType())));
        }
    }

    @Override
    public List<RiskPsSimpleInfoVO> getInfoByCaseId(int caseId) {
        List<RiskPublicSentimentInfo> infoList = riskPublicSentimentInfoDao.getInfoByCaseId(caseId);
        if (CollectionUtils.isEmpty(infoList)) {
            return new ArrayList<>();
        }
        List<RiskPsSimpleInfoVO> voList = new ArrayList<>();
        infoList.forEach(t -> {
            RiskPsSimpleInfoVO vo = RiskPsSimpleInfoVO.convertVO(t);
            vo.setPublicSentimentInfoTypeStr(infoTypeService.getByPath(t.getPublicSentimentInfoType()));
            vo.setInfoSourceStr(PublicSentimentInfoSourceEnum.findOfCode(t.getInfoSource()));
            RiskSourceCorrespond infoFeedBack = riskPublicSentimentCorrespondBiz.getByInfoFeedBack(t.getInfoFeedBack());
            vo.setInfoFeedBackStr(infoFeedBack.getInfoFeedBackContent());
            vo.setInfoEnteringTime(t.getCreateTime());
            voList.add(vo);
        });

        return voList;
    }

    @Override
    public int countPsByCaseId(int caseId) {
        return riskPublicSentimentInfoDao.countPsByCaseId(caseId);
    }

    @Override
    public List<String> getByInfoFeedBack() {
        return riskPublicSentimentInfoDao.getByInfoFeedBack();
    }

    public RiskPublicSentimentInfo buildInfo(String riskPublicSentimentInfoJson) {
        RiskPublicSentimentInfo riskPublicSentimentInfo = null;
        try {
            riskPublicSentimentInfo = JSONObject.parseObject(riskPublicSentimentInfoJson, RiskPublicSentimentInfo.class);
        } catch (Exception e) {
            log.error("", e);
        }
        return riskPublicSentimentInfo;
    }
}
