package com.shuidihuzhu.cf.risk.admin.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工单和电话记录关系model
 */
@Data
@AllArgsConstructor
@NoArgsConstructor

public class WorkOrderCallRecordingRelModel {


    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("工单id")
    private Long workOrderId;

    @ApiModelProperty("电话流水号")
    private String recordingUniqueId;

    @ApiModelProperty("操作人id")
    private Long operationId;

    private Date createTime;

    private Date updateTime;


}
