package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotLevelConfDao;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotLevelConfLogDao;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConf;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConfLog;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfUpdateVo;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/6/15 16:36
 */
@Service
@Slf4j
public class QualitySpotLevelConfService {

    @Resource
    private RiskQualitySpotLevelConfLogDao spotLevelConfLogDao;
    @Resource
    private RiskQualitySpotLevelConfDao riskQualitySpotLevelConfDao;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private SeaAccountService seaAccountService;

    private static final String UPDATE_LEVEL_CONF_KEY = "cf_risk_admin_quality_spot_level_";
    private static final long UPDATE_LEVEL_CONF_KEY_LEAVE_TIME = 10 * 1000;

    public void updateLevelConf(RiskQualitySpotLevelConfUpdateVo levelConfUpdateVo, long adminUserId){
        String key = UPDATE_LEVEL_CONF_KEY + levelConfUpdateVo.getSecondScene();
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_LEVEL_CONF_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请稍后重试");
            }
            //1. 查看是否有次日生效的策略
            LocalDateTime tomorrowStartLocalDate = LocalDate.now().minusDays(-1).atStartOfDay();
            Date tomorrowStartDate = Date.from(tomorrowStartLocalDate.toInstant(ZoneOffset.of("+8")));
            String currEndDateStr = DateUtil.getDate2LStr(Date.from(tomorrowStartLocalDate.minusSeconds(1).toInstant(ZoneOffset.of("+8"))));
            List<RiskQualitySpotLevelConf> latestParseSpotLevelConfList = riskQualitySpotLevelConfDao.selectBySceneAndExpireTime(
                    levelConfUpdateVo.getSecondScene(), currEndDateStr);
            log.info("config list by scene of all:{}", latestParseSpotLevelConfList);
            latestParseSpotLevelConfList.sort((o1, o2) -> (int) (o2.getExpireTime().getTime()/1000-o1.getExpireTime().getTime()/1000));
            //1.1 如果最新的记录为null，则插入一条
            RiskQualitySpotLevelConf latestLevelConf = latestParseSpotLevelConfList.get(0);
            if (Objects.equals(latestLevelConf.getSamplingLevel(), levelConfUpdateVo.getSamplingLevel())) {
                throw new IllegalArgumentException("每天抽检量级未发生变化,请刷新页面后重试");
            }
            if (latestParseSpotLevelConfList.size() == 1) {
                //1.1.1 查询新量级配置
                RiskQualitySpotLevelConf spotLevelConf = new RiskQualitySpotLevelConf();
                spotLevelConf.setParseTime(tomorrowStartDate);
                spotLevelConf.setSamplingLevel(levelConfUpdateVo.getSamplingLevel());
                spotLevelConf.setScene(levelConfUpdateVo.getSecondScene());
                riskQualitySpotLevelConfDao.insertSelective(spotLevelConf);
                //1.1.2 更新旧量级配置失效时间
                riskQualitySpotLevelConfDao.updateExpireTimeById(latestLevelConf.getId(), currEndDateStr);
                //1.2 如果存在次日生效的策略，则更新最新的量级
            } else {
                riskQualitySpotLevelConfDao.updateSamplingLevelById(latestLevelConf.getId(), levelConfUpdateVo.getSamplingLevel());
            }
            //2. 记录日志
            RiskQualitySpotLevelConfLog riskQualitySpotLevelConfLog = new RiskQualitySpotLevelConfLog();
            riskQualitySpotLevelConfLog.setScene(latestLevelConf.getScene());
            SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
            riskQualitySpotLevelConfLog.setOperateId(userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId());
            riskQualitySpotLevelConfLog.setOperateName(userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg());
            riskQualitySpotLevelConfLog.setParseTime(tomorrowStartDate);
            riskQualitySpotLevelConfLog.setModifyReason(levelConfUpdateVo.getModifyReason());
            riskQualitySpotLevelConfLog.setModifyContent("\""+latestLevelConf.getSamplingLevel()+"条/天\"修改为\""+
                    levelConfUpdateVo.getSamplingLevel()+"条/天\"");
            spotLevelConfLogDao.insertSelective(riskQualitySpotLevelConfLog);
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                redissonHandler.unLock(key, identify);
            }
        }
    }

}
