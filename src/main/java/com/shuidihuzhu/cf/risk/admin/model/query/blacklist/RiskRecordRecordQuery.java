package com.shuidihuzhu.cf.risk.admin.model.query.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/7/16 11:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(description = "风控命中记录查询")
public class RiskRecordRecordQuery extends PageQuery {

    @ApiModelProperty("命中时机")
    private Integer hitPhase;
    @ApiModelProperty("风控策略名称")
    private Integer riskStrategy;
    @ApiModelProperty("风控二级策略名称")
    private Integer secondStrategy;
    @ApiModelProperty("黑名单类型")
    private Long blacklistTypeId;
    @ApiModelProperty("限制动作类型")
    private Long limitAction;
    @ApiModelProperty("处理状态")
    private Integer status;
    @ApiModelProperty("风险核实结果")
    private Byte result;

    private Integer caseId;

    private String mobile;

    private Long uid;

    private String idCard;

    private String bornCard;

    private String operateName;

    @ApiModelProperty("风控命中开始时间，格式yyyy-MM-dd HH:mm:ss")
    private String hitStartTime;
    private String hitEndTime;

    private String handleStartTime;
    private String handlerEndTime;
}
