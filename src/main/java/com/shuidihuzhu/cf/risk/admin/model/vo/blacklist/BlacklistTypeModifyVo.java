package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/15 20:56
 */
@Data
@ApiModel(description = "黑名单类型修改")
public class BlacklistTypeModifyVo {

    @NotNull(message = "类型id不能为空")
    @Min(value = 1, message = "类型id不能小于1")
    @ApiModelProperty("类型id")
    private Long typeId;

    @NotNull(message = "限制动作不能为空")
    @Size(min = 1, message = "限制动作至少选择一项")
    @ApiModelProperty("限制动作")
    private List<Long> typeActions;

    @ApiModelProperty("修改后的限制动作ids")
    private List<BlacklistTypeActionRefDto> actionList;

}
