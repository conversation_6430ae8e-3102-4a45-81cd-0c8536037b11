package com.shuidihuzhu.cf.risk.admin.feign;

import com.shuidihuzhu.cf.risk.admin.model.disease.DiseaseSyncDTO;
import com.shuidihuzhu.common.web.model.Response;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 */
public interface DiseaseSyncRetrofitClient {

    @POST("/api/cf-risk-admin/disease-sync/accept")
    Call<Response<DiseaseSyncDTO>> accept(@Body DiseaseSyncDTO param);

}
