package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.client.cf.admin.model.AdminMarkReportParam;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class MarkReportVO {
    /**
     * 舆情id
     */
    private long psId;
    /**
     * 案例id
     */
    private int caseId;
    /**
     * 举报内容
     */
    private String content;
    /**
     * 质疑人姓名
     */
    private String reporterName;
    /**
     * 质疑人电话
     */
    private String reporterMobile;
    /**
     * 举报类型 多个举报类型用英文逗号分割
     */
    private String reportTypes;
    /**
     * 是否为用户实名举报
     */
    private boolean realNameReport;
    /**
     * 质疑人身份证号
     */
    private String reporterIdentity;
    /**
     *举报渠道
     */
    private int reportChannel;

    /**
     * 其他举报渠道
     */
    private String reportChannelOther;

    /**
     * 图片
     * @return
     */
    private String imageUrls;

    public  AdminMarkReportParam convertParam(){
        AdminMarkReportParam adminMarkReportParam = new AdminMarkReportParam();
        adminMarkReportParam.setCaseId(caseId);
        adminMarkReportParam.setContent(content);
        adminMarkReportParam.setReporterName(reporterName);
        adminMarkReportParam.setReporterMobile(reporterMobile);
        adminMarkReportParam.setReportTypes(reportTypes);
        adminMarkReportParam.setRealNameReport(realNameReport);
        adminMarkReportParam.setReporterIdentity(reporterIdentity);
        adminMarkReportParam.setReportChannel(reportChannel);
        adminMarkReportParam.setReportChannelOther(StringUtils.trimToEmpty(reportChannelOther));
        adminMarkReportParam.setImageUrls(imageUrls);
        return adminMarkReportParam;
    }
}
