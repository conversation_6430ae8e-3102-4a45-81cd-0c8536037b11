package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcRecordingProblemsVo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/7
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface CfRiskRecordingProblemsRecordDao {

    int insertList(@Param("list") List<QcRecordingProblemsVo> qcRecordingProblemsVos);

    List<QcRecordingProblemsVo> getListByWorkOrderId(@Param("workOrderId") long workOrderId);

    List<QcRecordingProblemsVo> getListByWorkOrderIds(@Param("workOrderIds") List<Long> workOrderIds);
}
