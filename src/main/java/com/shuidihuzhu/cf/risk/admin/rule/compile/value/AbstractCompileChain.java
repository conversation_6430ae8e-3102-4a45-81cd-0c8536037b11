package com.shuidihuzhu.cf.risk.admin.rule.compile.value;

/**
 * <AUTHOR>
 * @date 2020/6/18 22:30
 */
public abstract class AbstractCompileChain<T> {

    protected static final String METHOD_VARIABLE_NAME = "data";

    protected AbstractCompileChain valueCompile;

    public abstract String compileValue(T criterionData);

    protected abstract void setValueCompile(AbstractCompileChain valueCompile);

}
