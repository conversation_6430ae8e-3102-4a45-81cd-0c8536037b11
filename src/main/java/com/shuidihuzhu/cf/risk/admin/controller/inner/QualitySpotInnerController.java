package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotStrategyService;
import com.shuidihuzhu.cf.risk.client.admin.quality.sampling.QualitySpotClient;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotJobConfDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotWorkOrderWaitCheck;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/22 15:07
 */
@RestController
@Slf4j
public class QualitySpotInnerController implements QualitySpotClient {

    @Resource
    private QualitySpotStrategyService qualitySpotStrategyService;

    @Override
    public Response<List<QualitySpotJobConfDto>> listJobScopeConf() {
        List<QualitySpotJobConfDto> qualitySpotJobConfDtos = qualitySpotStrategyService.listJobScopeConf();
        log.info("listJobScopeConf获取抽检配置:{}", qualitySpotJobConfDtos);
        return NewResponseUtil.makeSuccess(qualitySpotJobConfDtos);
    }

    @Override
    public Response<QualitySpotWorkOrderWaitCheck> doQualitySpotStrategy(QualitySpotWorkOrderWaitCheck spotWorkOrderWaitCheck) {
        log.info("Work order call strategy of params:{}", JSON.toJSONString(spotWorkOrderWaitCheck));
        List qualitySpotDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(spotWorkOrderWaitCheck.getQualitySpotDtos())){
            qualitySpotDtos =
                    qualitySpotStrategyService.doQualitySpotStrategy(spotWorkOrderWaitCheck.getRuleId(),
                            spotWorkOrderWaitCheck.getQualitySpotDtos());

        } else if (CollectionUtils.isNotEmpty(spotWorkOrderWaitCheck.getQualitySpotWxDtoList())){
            qualitySpotDtos =  qualitySpotStrategyService.doQualitySpotWxStrategy(spotWorkOrderWaitCheck.getRuleId(),
                    spotWorkOrderWaitCheck.getQualitySpotWxDtoList());

        } else if (CollectionUtils.isNotEmpty(spotWorkOrderWaitCheck.getQualitySpotOutBoundDto())){
            qualitySpotDtos =  qualitySpotStrategyService.doQualitySpotOutBoundStrategy(spotWorkOrderWaitCheck.getRuleId(),
                    spotWorkOrderWaitCheck.getQualitySpotOutBoundDto());
        }
        log.info("doQualitySpotStrategy执行抽检:{}", qualitySpotDtos);
        return NewResponseUtil.makeSuccess(spotWorkOrderWaitCheck);
    }
}
