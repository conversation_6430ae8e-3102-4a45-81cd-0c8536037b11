package com.shuidihuzhu.cf.risk.admin;

import com.shuidihuzhu.eb.grafana.configuration.plugin.groupdatasource.check.EnableGroupDataSourceUseCheck;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeCommonsClient;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeConsulHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeDataSourceHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeDiskSpaceHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeRedissonHealth;
import com.shuidihuzhu.infra.starter.elasticjob.annotation.EnableElasticJob;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {
		"com.shuidihuzhu",
		},
		exclude = {
				DataSourceAutoConfiguration.class,
				ElasticsearchRestClientAutoConfiguration.class
		})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {
		"com.shuidihuzhu.client",
		"com.shuidihuzhu.cf.feign",
		"com.shuidihuzhu.cf.data",
		"com.shuidihuzhu.data",
		"com.shuidihuzhu.auth",
		"com.shuidihuzhu.cf.client.feign",
		"com.shuidihuzhu.cf.client.subject",
		"com.shuidihuzhu.cf.client.ugc",
		"com.shuidihuzhu.cf.client.adminpure",
		"com.shuidihuzhu.cf.finance.client",
		"com.shuidihuzhu.cf.client.material",
		"com.shuidihuzhu.cf.client.ugc",
		"com.shuidihuzhu.cf.risk.client",
		"com.shuidihuzhu.cf.activity.feign",
		"com.shuidihuzhu.kratos.client.feign",
		"com.shuidihuzhu.alps.feign.ocean",
		"com.shuidihuzhu.cf.risk.admin.controller.inner.risk",
		"com.shuidihuzhu.mdc.client",
		"com.shuidihuzhu.cf.risk.admin.controller.disease"
})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableFakeDataSourceHealth
@EnableFakeRedissonHealth
@EnableAsync
@EnableFakeCommonsClient
@EnableFakeConsulHealth
@EnableFakeDiskSpaceHealth
@EnableGroupDataSourceUseCheck
@EnableElasticJob(basePackages = {"com.shuidihuzhu.cf.risk.admin.runner"})
public class Application {
	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
		}
}
