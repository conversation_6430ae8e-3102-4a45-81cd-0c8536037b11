package com.shuidihuzhu.cf.risk.admin.biz.impl.whiteList;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.whiteList.RiskWhiteListLogBiz;
import com.shuidihuzhu.cf.risk.admin.dao.whiteList.RiskWhiteListLogDao;
import com.shuidihuzhu.cf.risk.admin.model.dto.whitelist.RiskWhiteListLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@Service
public class RiskWhiteListLogBizImpl implements RiskWhiteListLogBiz {

    @Autowired
    private RiskWhiteListLogDao riskWhiteListLogDao;

    @Override
    public int saveLog(RiskWhiteListLog buildRiskWhiteListLog) {
        return riskWhiteListLogDao.saveLog(buildRiskWhiteListLog);
    }

    @Override
    public List<RiskWhiteListLog> findById(long whiteListId) {
        if (whiteListId <= 0){
            return Lists.newArrayList();
        }
        return riskWhiteListLogDao.findById(whiteListId);
    }
}
