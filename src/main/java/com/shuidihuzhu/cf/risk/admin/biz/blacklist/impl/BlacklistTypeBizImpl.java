package com.shuidihuzhu.cf.risk.admin.biz.blacklist.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.blacklist.BlacklistTypeBiz;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistTypeActionRefDao;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistTypeDao;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeNameActionDto;
import com.shuidihuzhu.cf.risk.admin.model.enums.BlackActionLimitTimeType;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistType;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeActionRef;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/20 14:33
 */
@Service
@Slf4j
public class BlacklistTypeBizImpl implements BlacklistTypeBiz {

    @Resource
    private RiskBlacklistTypeDao typeDao;
    @Resource
    private RiskBlacklistTypeActionRefDao dataTypeRefDao;

    @Override
    public List<RiskBlacklistTypeActionRef> listTypeActionRefsByTypeIds(Collection<Long> typeIds) {
        return dataTypeRefDao.listByTypeIds(typeIds);
    }

    @Override
    public List<BlacklistTypeNameActionDto> queryTypeActionsByTypeIds(Collection<Long> typeIds) {
        if (CollectionUtils.isEmpty(typeIds)) {
            return Lists.newArrayList();
        }
        List<RiskBlacklistType> types = typeDao.listByIds(typeIds);
        List<RiskBlacklistTypeActionRef> refs = dataTypeRefDao.listByTypeIds(typeIds);
        Map<Long, List<RiskBlacklistTypeActionRef>> typeIdMap = refs.stream()
                .collect(Collectors.groupingBy(RiskBlacklistTypeActionRef::getTypeId));
        return types.stream().map(type -> {
            BlacklistTypeNameActionDto nameActionDto = new BlacklistTypeNameActionDto();
            nameActionDto.setTypeId(type.getId());
            nameActionDto.setTypeName(type.getTypeName());
            List<Long> actions = typeIdMap.get(type.getId()).stream()
                    .map(RiskBlacklistTypeActionRef::getActionId).collect(Collectors.toList());
            nameActionDto.setActions(actions);
            List<BlacklistTypeActionRefDto> actionList = new ArrayList<>();
            for (Long actionId : actions) {
                Integer limitType = typeIdMap.get(type.getId())
                        .stream()
                        .filter(f -> f.getActionId().equals(actionId))
                        .map(RiskBlacklistTypeActionRef::getLimitTimeType)
                        .findFirst()
                        .orElse(BlackActionLimitTimeType.FOREVER.getCode());
                BlacklistTypeActionRefDto dto = new BlacklistTypeActionRefDto();
                dto.setActionId(actionId);
                dto.setLimitTimeType(limitType);
                dto.setName(LimitActionEnum.fromCode(actionId).getName());
                actionList.add(dto);
            }
            nameActionDto.setActionList(actionList);
            return nameActionDto;
        }).collect(Collectors.toList());
    }

}
