package com.shuidihuzhu.cf.risk.admin.runner;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.service.mdc.MdcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.recording.support.WorkOrderRecordingHandlerReport;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderTableParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.enums.ErrorCode;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.elasticjob.annotation.ElasticTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@ElasticTask(jobName = "mdcCallRecordAsrHandleJob",
        description = "举报工单录音处理job",
        cron = "0 5 1 * * ?", errorEmails = {"<EMAIL>"}, autoTriggerForTest = false)
@Slf4j
@RefreshScope
public class MdcCallRecordAsrHandleJob extends AbstractSimpleJobEnhancer {

    @Autowired
    private MdcAudioAsrService mdcAudioAsrService;

    @Override
    public void doRealExecute(ShardingContext shardingContext) {
        log.info("mdcCallRecordAsrHandleJob start......");
        //查询工单
        Date curDate = DateUtil.getCurrentDate();
        Date yestDate = DateUtil.addDay(curDate, -1);
        mdcAudioAsrService.doHandleAsrRecord(yestDate,curDate);
        log.info("mdcCallRecordAsrHandleJob end......");
    }

}

