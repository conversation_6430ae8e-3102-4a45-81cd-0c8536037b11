package com.shuidihuzhu.cf.risk.admin.biz.impl.quality.sampling;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQualitySpotTypeBiz;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotTypeDao;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotScenePermissionsEnum;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/12
 */
@Service
@Slf4j
public class RiskQualitySpotTypeImpl implements RiskQualitySpotTypeBiz {

    @Resource
    private RiskQualitySpotTypeDao riskQualitySpotTypeDao;
    @Autowired
    private PermissionFeignClient permissionFeignClient;


    @Override
    public List<RiskQualitySpotType> findAllParentById(long typeId) {
        List<RiskQualitySpotType> typeList = Lists.newArrayList();
        RiskQualitySpotType riskQualitySpotType = riskQualitySpotTypeDao.getById(typeId);
        if (riskQualitySpotType == null) {
            return null;
        }
        typeList.add(riskQualitySpotType);
        addParentId(typeList, riskQualitySpotType);
        //对list进行反转
        Collections.reverse(typeList);
        return typeList;
    }

    @Override
    public List<RiskQualitySpotType> getListByParentId(long parentId) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (parentId < 0 || adminUserId <=0 ) {
            return Lists.newArrayList();
        }
        List<RiskQualitySpotType> riskQualitySpotTypes = riskQualitySpotTypeDao.getByParentId(parentId);
        return checkPermission(adminUserId, riskQualitySpotTypes);
    }

    private List<RiskQualitySpotType> checkPermission(long adminUserId, List<RiskQualitySpotType> riskQualitySpotTypes) {
        if (CollectionUtils.isEmpty(riskQualitySpotTypes) || adminUserId <= 0){
            return Lists.newArrayList();
        }
        PermissionParam permissionParam = PermissionParam.builder()
                .userId(adminUserId)
                .appCode(AuthSaasContext.getAuthAppCode())
                .permissions(QualitySpotScenePermissionsEnum.getPermissions())
                .build();
        Set<String> permissions = permissionFeignClient.validUserPermissions(permissionParam).getData();

        log.info("userClassify permissions={}",permissions);

        if (CollectionUtils.isEmpty(permissions)){
            return Lists.newArrayList();
        }
        //过滤存在的权限
        return riskQualitySpotTypes.stream().filter(v -> {
            QualitySpotScenePermissionsEnum permissionsEnum = QualitySpotScenePermissionsEnum.getFromType(v.getId());
            return permissionsEnum !=null && permissions.contains(permissionsEnum.getPermission());
        }).collect(Collectors.toList());
    }

    @Override
    public List<RiskQualitySpotType> findById(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return riskQualitySpotTypeDao.findById(ids);
    }

    private void addParentId(List<RiskQualitySpotType> typeList, RiskQualitySpotType riskQualitySpotType) {
        if (riskQualitySpotType == null || riskQualitySpotType.getParentId() <= 0){
            return;
        }
        RiskQualitySpotType parent =  riskQualitySpotTypeDao.getById(riskQualitySpotType.getParentId());
        if (parent == null) {
            return;
        }
        typeList.add(parent);
        addParentId(typeList, parent);
    }

}
