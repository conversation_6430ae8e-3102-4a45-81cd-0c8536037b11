package com.shuidihuzhu.cf.risk.admin.model.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class QcHighRiskExcelVO {

    @ExcelProperty(value = "质检日期", index = 0)
    private String qcDate;

    @ExcelProperty(value = "所在组别", index = 1)
    private String targetOrgName;

    @ExcelProperty(value = "坐席姓名", index = 2)
    private String targetName;

    @ExcelProperty(value = "工单ID", index = 3)
    private long workOrderId;

    @ExcelProperty(value = "案例ID", index = 4)
    private int caseId;

    @ExcelProperty(value = "是否失误", index = 5)
    private String isFault;

    @ExcelProperty(value = "客户电话", index = 6)
    private String customerPhone;

    @ExcelProperty(value = "通话时长", index = 7)
    private String callDuration;

    @ExcelProperty(value = "工单类别", index = 8)
    private String handleResultMsg;

    @ExcelProperty(value = "错误类型", index = 9)
    private String errorType;

    @ExcelProperty(value = "错误模块", index = 10)
    private String errorModule;

    @ExcelProperty(value = "具体错误点", index = 11)
    private String specificError;

    @ExcelProperty(value = "关键属性", index = 12)
    private String determinantAttribute;

    @ExcelProperty(value = "关键错误", index = 13)
    private int criticalErrorCount;

    @ExcelProperty(value = "非关键错误", index = 14)
    private int nonCriticalErrorCount;

    @ExcelProperty(value = "是否合格", index = 15)
    private String isEligibility;

    @ExcelProperty(value = "前置信息备注", index = 16)
    private String preInfoRemark;

    @ExcelProperty(value = "图文信息备注", index = 17)
    private String imgWordRemark;

    @ExcelProperty(value = "增信信息备注", index = 18)
    private String addCreditRemark;

    @ExcelProperty(value = "低保信息备注", index = 19)
    private String lowIncomeRemark;

    @ExcelProperty(value = "其他备注", index = 20)
    private String otherRemark;

    @ExcelProperty(value = "修改建议", index = 21)
    private String reviseOpinion;

    @ExcelProperty(value = "质检员", index = 22)
    private String operatorName;

}
