package com.shuidihuzhu.cf.risk.admin.feign;

import lombok.extern.slf4j.Slf4j;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public class ApiUtils {

    public static <T> Optional<T> execute(Call<T> call) {
        Response<T> response;
        try {
            response = call.execute();
            if (!response.isSuccessful()) {
                log.warn("ApiUtils fail url {} response {}", call.request().url(), response.errorBody().string());
            }
            return Optional.ofNullable(response.body());
        } catch (IOException e) {
            log.warn("ApiUtils fail url {}", call.request().url(), e);
            return Optional.empty();
        }
    }
}
