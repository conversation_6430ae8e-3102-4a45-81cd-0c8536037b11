package com.shuidihuzhu.cf.risk.admin.service.blacklist;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistClassifyDao;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionDto;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.RiskBlacklistClassifyEnhance;
import com.shuidihuzhu.cf.risk.admin.model.enums.CommonStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistClassify;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistClassifyVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:54
 */
@Service
@Slf4j
public class BlackListClassifyService {

    @Resource
    private RiskBlacklistClassifyDao riskBlacklistClassifyDao;
    @Resource
    private BlackListTypeService blackListTypeService;

    public RiskBlacklistClassifyEnhance getClassifyObtain(Long classifyId){
        RiskBlacklistClassify classify = riskBlacklistClassifyDao.selectByPrimaryKey(classifyId);
        if (classify == null) {
            return null;
        }
        List<Long> ids = Lists.newArrayList();
        Splitter.on(",").split(classify.getLevelPath()).forEach(s -> ids.add(Long.valueOf(s)));
        List<RiskBlacklistClassify> parents = riskBlacklistClassifyDao.listByIds(ids);

        RiskBlacklistClassifyEnhance enhance = new RiskBlacklistClassifyEnhance();
        BeanUtils.copyProperties(parents, enhance);
        List<String> namePaths = parents.stream().map(RiskBlacklistClassify::getName).collect(Collectors.toList());
        namePaths.add(classify.getName());
        enhance.setNamePath(namePaths);

        return enhance;
    }

    /**
     *
     * <AUTHOR>
     * @date 2020/7/17 11:49
     * @param levelNames 完整的层级名称
     * @return 叶子节点id
     */
    public long saveBatchClassify(List<String> levelNames) {
        return doSaveBatch(levelNames, null);
    }

    /**
     *
     * <AUTHOR>
     * @date 2020/7/17 11:49
     * @param parentClassify 父级实体
     * @param levelNames 下级名称
     * @return 叶子节点id
     */
    public long saveBatchClassify(RiskBlacklistClassify parentClassify, List<String> levelNames) {
        return doSaveBatch(levelNames, parentClassify);
    }

    private long doSaveBatch(List<String> levelNames, RiskBlacklistClassify parentClassify) {
        int level = 0;
        int parentClassifySize = parentClassify != null ? 1 : 0;
        List<RiskBlacklistClassify> classifies = Lists.newArrayListWithCapacity(levelNames.size() + parentClassifySize);
        if (parentClassify != null) {
            classifies.add(parentClassify);
            level = parentClassify.getLevel() + 1;
        }
        for (String levelName : levelNames) {
            RiskBlacklistClassify classify = new RiskBlacklistClassify();
            classify.setLevel(level++);
            classify.setName(levelName);
            classify.setParentId(0L);
            classify.setLevelPath("");
            classifies.add(classify);
        }
        riskBlacklistClassifyDao.saveBatch(classifies);
        //更新levelPath和parentId
        for (int i = 1; i < classifies.size(); i++) {
            StringBuilder levelPath = new StringBuilder(parentClassify != null ? parentClassify.getLevelPath()+"," : "");
            for (int idx = 0; idx < i; idx++) {
                levelPath.append(classifies.get(idx).getId()).append(",");
            }
            riskBlacklistClassifyDao.updateLevelPathAndParentId(classifies.get(i).getId(),
                    levelPath.substring(0, levelPath.length()-1), classifies.get(i-1).getId());
        }
        return classifies.get(classifies.size()-1).getId();
    }

    /**
     *
     * <AUTHOR>
     * @date 2020/7/17 11:49
     * @param classify 实体
     * @return 叶子节点id
     */
    public long saveClassify(RiskBlacklistClassify classify) {
        riskBlacklistClassifyDao.insertSelective(classify);
        return classify.getId();
    }

    public List<BlacklistClassifyVo> queryChildren(Long parentId) {
        List<RiskBlacklistClassify> classifies = riskBlacklistClassifyDao.listParentIdAndOptional(parentId,
                CommonStatusEnum.ENABLE.getCode(), null);
        if (CollectionUtils.isEmpty(classifies)) {
            return Collections.emptyList();
        }
        Map<Long, BlacklistTypeActionDto> classifyIdActionNameMap = blackListTypeService.getActionNameByClassifyIds(classifies.stream()
                .map(RiskBlacklistClassify::getId)
                .collect(Collectors.toList()));
        return classifies.stream().map(classify -> {
            BlacklistClassifyVo blacklistClassifyVo = new BlacklistClassifyVo();
            blacklistClassifyVo.setStatus(CommonStatusEnum.fromCode(classify.getStatus()).getDesc());
            blacklistClassifyVo.setId(classify.getId());
            blacklistClassifyVo.setName(classify.getName());
            if (MapUtils.isNotEmpty(classifyIdActionNameMap)) {
                BlacklistTypeActionDto blacklistTypeActionDto = classifyIdActionNameMap.get(classify.getId());
                blacklistClassifyVo.setActions(blacklistTypeActionDto.getActions());
                blacklistClassifyVo.setTypeId(blacklistTypeActionDto.getTypeId());
            }
            return blacklistClassifyVo;
        }).collect(Collectors.toList());
    }

    public boolean exists(Long parentId, String levelName){
        List<RiskBlacklistClassify> classifies = riskBlacklistClassifyDao.listParentIdAndOptional(parentId,
                CommonStatusEnum.ENABLE.getCode(), levelName);
        return classifies.stream().anyMatch(classify -> Objects.equals(classify.getName(), levelName));
    }

    public int enableClassify(Long classifyId){
        return riskBlacklistClassifyDao.updateStatusById(classifyId, CommonStatusEnum.ENABLE.getCode());
    }

    public int disableClassify(Long classifyId){
        return riskBlacklistClassifyDao.updateStatusById(classifyId, CommonStatusEnum.DISABLE.getCode());
    }

}
