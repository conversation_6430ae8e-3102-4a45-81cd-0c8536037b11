package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/2/20
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskPublicSentimentDetailDao {
    int add(RiskPublicSentimentDetail riskPublicSentimentDetail);

    int updateByPublicSentimentId(RiskPublicSentimentDetail riskPublicSentimentDetail);

    RiskPublicSentimentDetail getByPublicSentimentId(@Param("publicSentimentId")long publicSentimentId);

    List<RiskPublicSentimentDetail> getByPublicSentimentIds(@Param("publicSentimentIds")List<Long> publicSentimentIds);

}
