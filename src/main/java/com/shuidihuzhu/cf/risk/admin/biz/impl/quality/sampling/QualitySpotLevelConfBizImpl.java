package com.shuidihuzhu.cf.risk.admin.biz.impl.quality.sampling;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotLevelConfBiz;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotLevelConfDao;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotLevelConfLogDao;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotTypeDao;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConf;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConfLog;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfLogVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/17 19:43
 */
@Slf4j
@Service
public class QualitySpotLevelConfBizImpl implements QualitySpotLevelConfBiz {

    @Resource
    private RiskQualitySpotLevelConfDao riskQualitySpotLevelConfDao;
    @Resource
    private RiskQualitySpotLevelConfLogDao riskQualitySpotLevelConfLogDao;
    @Resource
    private RiskQualitySpotTypeDao spotTypeDao;


    @Override
    public RiskQualitySpotLevelConfVo getByLevelId(Long id) {
        RiskQualitySpotLevelConf riskQualitySpotLevelConf = riskQualitySpotLevelConfDao.selectByPrimaryKey(id);
        return riskQualitySpotLevelConf == null ? null : new RiskQualitySpotLevelConfVo(riskQualitySpotLevelConf);
    }

    @Override
    public List<RiskQualitySpotLevelConfVo> listWithAllLatestValidScene(long firstScene,  long secondScene) {
        return doListWithAllValidScene((p1, p2) -> p1.getExpireTime().getTime() > p2.getExpireTime().getTime() ? p1 : p2, new Date(),
                firstScene, secondScene);
    }

    @Override
    public List<RiskQualitySpotLevelConfVo> listWithAllCurrentValidScene(long secondScene) {
        return doListWithAllValidScene((p1, p2) -> p1.getExpireTime().getTime() > p2.getExpireTime().getTime() ? p2 : p1, new Date(), 0, secondScene);
    }

    @Override
    public List<RiskQualitySpotLevelConfVo> listWithAllCurrentValidScene(Date now) {
        return doListWithAllValidScene((p1, p2) -> p1.getExpireTime().getTime() > p2.getExpireTime().getTime() ? p2 : p1, now, 0, 0);
    }


    private List<RiskQualitySpotLevelConfVo> doListWithAllValidScene(BinaryOperator<RiskQualitySpotLevelConf> mergeFunction,
                                                                     Date now, long firstScene, long secondScene){
        List<RiskQualitySpotLevelConf> validSceneAllLevelConfList = listByWhere(now, firstScene, secondScene);
        Map<Long, RiskQualitySpotLevelConf> collectMap = validSceneAllLevelConfList.stream()
                .collect(Collectors.toMap(RiskQualitySpotLevelConf::getScene,
                        Function.identity(), mergeFunction, LinkedHashMap::new));
        List<RiskQualitySpotLevelConfVo> confVos =  collectMap.values().stream().map(RiskQualitySpotLevelConfVo::new).collect(Collectors.toList());
        fullScene(confVos);
        return confVos;
    }

    private void fullScene(List<RiskQualitySpotLevelConfVo> confVos) {
        if (CollectionUtils.isEmpty(confVos)){
            return;
        }
        List<Long> secondSceneList = confVos.stream().map(RiskQualitySpotLevelConfVo::getScene).collect(Collectors.toList());
        List<RiskQualitySpotType> spotTypeList = spotTypeDao.findById(secondSceneList);
        spotTypeList.addAll(spotTypeDao.findById(spotTypeList.stream()
                .map(RiskQualitySpotType::getParentId).collect(Collectors.toList())));
        Map<Long, RiskQualitySpotType> spotTypeMap = spotTypeList.stream().distinct()
                .collect(Collectors.toMap(RiskQualitySpotType::getId, Function.identity()));
        for (RiskQualitySpotLevelConfVo vo : confVos) {
            RiskQualitySpotType spotType = spotTypeMap.get(vo.getScene());
            if (spotType == null) {
                return;
            }
            vo.setSecondScene(spotType.getTypeName());
            vo.setFirstScene(spotTypeMap.get(spotType.getParentId()).getTypeName());
        }
    }

    private List<RiskQualitySpotLevelConf> listByWhere(Date now, long firstScene, long secondScene) {
        if (firstScene == 0 && secondScene ==0) {
            return riskQualitySpotLevelConfDao.listWithAllValid(now);
        }
        if (secondScene > 0) {
           return riskQualitySpotLevelConfDao.listWithAllValidScene(List.of(secondScene), now);
        } else {
            List<RiskQualitySpotType> spotTypeList = spotTypeDao.getByParentId(firstScene);
            if (CollectionUtils.isEmpty(spotTypeList)) {
                return Lists.newArrayList();
            }
            return riskQualitySpotLevelConfDao.listWithAllValidScene(spotTypeList.stream()
                    .map(RiskQualitySpotType::getId).collect(Collectors.toList()), now);
        }
    }

    @Override
    public List<RiskQualitySpotLevelConfLogVo> listByConfId(Integer confId) {
        List<RiskQualitySpotLevelConfLog> levelConfLogs = riskQualitySpotLevelConfLogDao.listByScene(confId);
        return levelConfLogs.stream().map(RiskQualitySpotLevelConfLogVo::new).collect(Collectors.toList());
    }

}
