package com.shuidihuzhu.cf.risk.admin.model.disease;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Data
public class RiskDiseaseTreatmentProject {

    private long id;

    private long diseaseId;

    private String projectName;

    private String projectMergeRule = "";

    private int minTreatmentFee;

    private int maxTreatmentFee;

    private int raiseType;

    /**
     * 自定义治疗方案
     */
    private String customTreatment = "";
}
