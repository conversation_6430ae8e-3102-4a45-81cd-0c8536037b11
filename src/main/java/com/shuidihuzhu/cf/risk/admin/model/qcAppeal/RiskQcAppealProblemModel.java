package com.shuidihuzhu.cf.risk.admin.model.qcAppeal;

import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestNoticeModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


/**
 * @Auther: subing
 * @Date: 2020/11/13
 */
@Data
public class RiskQcAppealProblemModel {
    private int status = -1;
    private String problem;
    private String property;

    public static RiskQcAppealProblemModel buildInfo(CfGwReplaceInputQualityTestNoticeModel.IssueInfo issueInfos){
        if (issueInfos == null){
            return null;
        }
        RiskQcAppealProblemModel riskQcAppealProblemModel = new RiskQcAppealProblemModel();
        riskQcAppealProblemModel.setProblem(issueInfos.getRemark());
        //todo allProperty
        riskQcAppealProblemModel.setProperty(StringUtils.isNotBlank(issueInfos.getAllProperty()) ? issueInfos.getAllProperty() :
                issueInfos.getProperty());
        return riskQcAppealProblemModel;
    }

}
