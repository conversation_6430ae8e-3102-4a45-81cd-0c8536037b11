package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@NoArgsConstructor
@ApiModel(description = "黑名单数据")
public class BlacklistDataVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("用户id")
    private Long userIdAlias;

    @ApiModelProperty("通过用户id查询的用户绑定手机号")
    private String mobileBind;

    @ApiModelProperty("用户身份证号")
    private String idCard;

    @ApiModelProperty("用户手机号")
    private String mobile;

    @ApiModelProperty("用户手机号反查到的用户id")
    private Long userIdBind;

    @ApiModelProperty("用户出生证")
    private String bornCard;

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("黑名单类型")
    private List<String> typeNames;

    @ApiModelProperty("限制动作")
    private String action;

    @ApiModelProperty("最新操作原因")
    private String operateReason;

    @ApiModelProperty(" 操作人")
    private String operateName;

    @ApiModelProperty("最新操作时间")
    private String updateTime;

    @ApiModelProperty("是否删除")
    private Boolean isDelete;

}
