package com.shuidihuzhu.cf.risk.admin.model.vo.list;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/7/28 16:20
 */
@ApiModel(description = "科室电话修改")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListDepartmentEditVo extends ListDepartmentBase{

    @NotNull(message = "id不能为空")
    @Min(value = 1)
    @ApiModelProperty("id")
    private Long id;

}
