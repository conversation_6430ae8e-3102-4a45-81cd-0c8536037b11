package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskOperationRecordDao {

    List<Long> listByCaseIdAndType(@Param("caseId") int caseId, @Param("type") int type);

    List<Long> listByBizIdAndType(@Param("commentIds") List<Long> commentIds, @Param("type") int type);
}
