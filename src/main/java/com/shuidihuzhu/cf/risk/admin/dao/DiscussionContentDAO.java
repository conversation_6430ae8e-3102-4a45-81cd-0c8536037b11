package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.DiscussionContentVO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

@DataSource(RiskAdminDS.CF_RISK_RW)

public interface DiscussionContentDAO {
    int save(DiscussionContentVO discussionContentVO);

    DiscussionContentVO getByDiscussionId(@Param("discussionId") long discussionId,@Param("sourceNum") int sourceNum);
}
