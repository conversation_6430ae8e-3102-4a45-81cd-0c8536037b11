package com.shuidihuzhu.cf.risk.admin.controller.qc;

import com.alibaba.fastjson.TypeReference;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcmaterialService;
import com.shuidihuzhu.cf.risk.admin.serviceexception.ServiceExceptionUtils;
import com.shuidihuzhu.client.cf.workorder.config.WorkOrderHelper;
import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageService;
import com.shuidihuzhu.client.cf.workorder.storage.value.CaiLiaoHandleIdsBO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Set;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
@RestController
@Slf4j
@RequestMapping("/api/cf-risk-admin/qc-material")
public class RiskQcmaterialController {

    @Autowired
    private RiskQcmaterialService riskQcmaterialService;

    @PostMapping(path = "detail")
    public Response<String> getInfo(@RequestParam long workOrderId) {
        return ServiceExceptionUtils.handleResponse(() -> getInfoResponse(workOrderId));
    }

    @NotNull
    private Response<String> getInfoResponse(long workOrderId) {
        String json = riskQcmaterialService.getJsonString(workOrderId);
        return NewResponseUtil.makeSuccess(json);
    }

    @Resource
    private WorkOrderStorageService workOrderStorageService;

    @ApiOperation(value = "查询需质检的模块类型列表")
    @PostMapping(path = "/get-material-needs-qc-types")
    public Response<Set<Integer>> getMaterialRefuseIds(@ApiParam("质检工单id") @RequestParam int workOrderId) {
        Response<VonStorageVO> lastByTypeResp = workOrderStorageService.getLastByType(workOrderId, WorkOrderHelper.Storage.CAI_LIAO_NEED_QC_TYPES);
        if (lastByTypeResp.notOk()) {
            return NewResponseUtil.makeResponse(lastByTypeResp.getCode(), lastByTypeResp.getMsg(), null);
        }
        Set<Integer> needQcTypes = VonStorageVO.getByData(lastByTypeResp.getData(), new TypeReference<Set<Integer>>() {
        }).orElse(null);
        return NewResponseUtil.makeSuccess(needQcTypes);
    }

    @ApiOperation(value = "保存修正后的驳回项")
    @PostMapping(path = "/save-material-correct-refuse-ids")
    public Response<Void> saveMaterialCorrectRefuseIds(@RequestBody CaiLiaoHandleIdsBO param) {
        Response<VonStorageVO> resp = workOrderStorageService.addByTypeOfJson(param.getWorkOrderId(),
                WorkOrderHelper.Storage.CORRECT_CAI_LIAO_HANDLE_IDS, param);
        return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), null);
    }
}
