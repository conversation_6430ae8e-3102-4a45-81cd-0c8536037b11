package com.shuidihuzhu.cf.risk.admin.delegate;



/**
 * <AUTHOR>
 */
public interface ShortUrlDelegate {

    /**
     * 短链服务异常时会返回输入url
     * @param url 需要转换的url
     * @return 转换后的短地址
     */
    String process(String url);

    /**
     * 短链服务异常时会抛出异常
     * @param url 需要转换的url
     * @return 转换后的短地址
     * @throws ShortUrlException
     */
    String processWithException(String url) throws ShortUrlException;

    /**
     * 短链服务异常时会返回null
     * @param url 需要转换的url
     * @return 转换后的短地址
     */
    String processNullable(String url);
}

