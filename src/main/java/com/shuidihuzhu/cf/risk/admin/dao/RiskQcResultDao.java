package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcResultDao {

    List<RiskQcResult> getQcResultByTime(@Param("qcUniqueCode") String qcUniqueCode,
                                         @Param("startTime") String startTime,
                                         @Param("endTime") String endTime);

    int add(@Param("firstQcResultId") long firstQcResultId, @Param("secondQcResultId") long secondQcResultId,
            @Param("problemDescribe") String problemDescribe, @Param("wordOrderId") long wordOrderId,
            @Param("qcUniqueCode") String qcUniqueCode, @Param("resultType") int resultType, @Param("qcId") long qcId);


    RiskQcResult getByWorkOrderId(@Param("wordOrderId") long wordOrderId);

    int updateInfo(@Param("firstQcResultId") long firstQcResultId, @Param("secondQcResultId") long secondQcResultId,
                   @Param("problemDescribe") String problemDescribe, @Param("wordOrderId") long wordOrderId);


    List<RiskQcResult> findByWorkOrderIds(@Param("wordOrderIds") List<Long> wordOrderIds);

}


