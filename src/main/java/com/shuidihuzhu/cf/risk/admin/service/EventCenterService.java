package com.shuidihuzhu.cf.risk.admin.service;

import com.shuidihuzhu.cf.clinet.event.center.constants.MQTagCons;
import com.shuidihuzhu.cf.clinet.event.center.constants.MQTopicCons;
import com.shuidihuzhu.cf.clinet.event.center.model.CfUserEvent;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class EventCenterService {
    @Autowired(required = false)
    private Producer producer;

    public void eventSend(int operatorUserId, int type, int caseId, String infoUuid) {
        log.info("eventMq param caseId:{}, infoUuId:{} operatorUserId:{}, type:{}", caseId, infoUuid, operatorUserId, type);
        try {
            Map<String, String> dataMap = new HashMap<>();
            dataMap.put("infoUuid", infoUuid);
            CfUserEvent cfUserEvent = new CfUserEvent();
            cfUserEvent.setCaseId(caseId);
            cfUserEvent.setUserId(operatorUserId);
            cfUserEvent.setDataMap(dataMap);
            cfUserEvent.setType(type);
            producer.send(new Message<>(MQTopicCons.CF, MQTagCons.USER_EVENT_FOR_EVENT_CENTER,
                    MQTagCons.USER_EVENT_FOR_EVENT_CENTER + "_" + caseId, cfUserEvent));
        }catch (Exception e){
            log.error("", e);
        }
    }
}
