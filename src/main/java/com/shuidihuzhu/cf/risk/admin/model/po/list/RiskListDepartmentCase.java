package com.shuidihuzhu.cf.risk.admin.model.po.list;

import lombok.Data;

import java.util.Date;
@Data
public class RiskListDepartmentCase {
    /**
     * 主键
     */
    private Long id;

    /**
     * 座机区号
     */
    private String areaCode;

    /**
     * 座机号
     */
    private String landline;

    /**
     * 座机分机号
     */
    private String extension;

    /**
     * 案例id
     */
    private Integer caseId;

    /**
     * 案例发起时间
     */
    private Date launchTime;

    /**
     * 医院code码
     */
    private String hospitalCode;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 医院所在省份
     */
    private String province;

    /**
     * 医院所在城市
     */
    private String city;

    /**
     * 科室
     */
    private String departments;

    /**
     * 发起人手机号
     */
    private String mobile;

    /**
     * 发起人姓名
     */
    private String name;

    /**
     * 发起人身份证号
     */
    private String idCard;

    /**
     * 患者证件类型
     */
    private Integer patientIdType;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者证件号码
     */
    private String patientIdNumber;

    /**
     * 收款人手机号
     */
    private String payeePhone;

    /**
     * 收款人姓名
     */
    private String payeeName;

    /**
     * 收款人身份证号
     */
    private String payeeIdCard;

    /**
     * 收款关系类型
     */
    private Byte relationType;

    /**
     * 对公打款开户支行
     */
    private String hospitalBankBranch;

    /**
     * 对公打款银行卡号
     */
    private String hospitalBankCard;

    /**
     * 对公打款账户名称
     */
    private String hospitalAccountName;

    /**
     * 对公打款住院号
     */
    private String hospitalNum;

    /**
     * 对公打款床号
     */
    private String bedNum;

    /**
     * 对公打款科室
     */
    private String payeeDepartment;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}