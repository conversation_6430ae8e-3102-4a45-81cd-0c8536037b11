package com.shuidihuzhu.cf.risk.admin.service.qc.export;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.adminpure.feign.OrderCallFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.call.CfCallOutRecordMsg;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.data.platform.util.ExportUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.biz.*;
import com.shuidihuzhu.cf.risk.admin.dao.CfRiskQcBeQualityInspectedResultRecordDao;
import com.shuidihuzhu.cf.risk.admin.dao.CfRiskQcCorrectRefuseRecordDao;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcOperationRecordDao;
import com.shuidihuzhu.cf.risk.admin.delegate.ClewDelegate;
import com.shuidihuzhu.cf.risk.admin.model.*;
import com.shuidihuzhu.cf.risk.admin.model.enums.MaterialWorkOrderCallStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcSecondStandardTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.mapper.BeanMapperConvertHandler;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderParam;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResultConfig;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcTotalCallRecords;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.shuidihuzhu.cf.risk.admin.model.vo.excel.ExcelExportDataVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.excel.QcHospitalDeptExcelVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.excel.QcZhuDongExcelVO;
import com.shuidihuzhu.cf.risk.admin.service.Risk1v1QcDetailService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcDetailService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcWorkOrderService;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 抽取自 {@link RiskQcWorkOrderService}
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class QcExportServiceImpl implements QcExportService {


    @Resource
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Resource
    private CfSearchClient cfSearchClient;
    @Autowired
    private RiskQcResultConfigBiz riskQcResultConfigBiz;
    @Autowired
    private SeaAccountService accountService;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private RiskQcResultBiz riskQcResultBiz;
    @Resource
    private PreposeMaterialClient preposeMaterialClient;
    @Resource
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Autowired
    private Risk1v1QcDetailService risk1v1QcDetailService;
    @Resource
    private RiskQcOperationRecordDao riskQcOperationRecordDao;
    @Autowired
    private ClewDelegate clewDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private RiskQcWorkOrderService riskQcWorkOrderService;

    @Autowired
    private OrderCallFeignClient orderCallFeignClient;

    @Autowired
    private CfRiskQcCorrectRefuseRecordDao cfRiskQcCorrectRefuseRecordDao;

    @Autowired
    private CfRiskQcBeQualityInspectedResultRecordDao cfRiskQcBeQualityInspectedResultRecordDao;

    @Resource
    private ExportUtil exportUtil;

    @Resource
    private BeanMapperConvertHandler beanMapperConvertHandler;

    @Override
    public void getDetailListV2(QcWorkOrderParam qcWorkOrderParam, long adminUserId) {
        int orderType = qcWorkOrderParam.getOrderType();
        if (orderType == WorkOrderType.qc_hospital_dept.getType()) {
            getDeptExcelData(qcWorkOrderParam, adminUserId);
        } else if (orderType == WorkOrderType.qc_zhu_dong.getType() || orderType == WorkOrderType.qc_material_audit.getType()) {
            getExportData(qcWorkOrderParam, this::getZhuDongOrderExcelVo, QcZhuDongExcelVO.class, adminUserId);
        } else {
            getDetailList(qcWorkOrderParam, adminUserId);
        }
    }


    @Override
    public List<?> getWx1v1ExcelVos(QcWorkOrderParam qcWorkOrderParam,long adminUserId) {
        List<Long> workOrderIds = this.getWorkOrderIdByEs(qcWorkOrderParam);
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return Collections.emptyList();
        }
        Response<List<WorkOrderVO>> response = cfQcWorkOrderClient.queryQcByIds(workOrderIds);
        if (response.notOk() || CollectionUtils.isEmpty(response.getData())) {
            return Collections.emptyList();
        }
        List<WorkOrderVO> workOrderVos = response.getData();
        if (qcWorkOrderParam.getOrderType() == WorkOrderType.qc_wx_1v1.getType()) {
            return this.convertVo(workOrderVos, adminUserId);
        } else if (qcWorkOrderParam.getOrderType() == WorkOrderType.qc_wx_1v1_repeat.getType()) {
            return this.convertRepeatVo(workOrderVos, adminUserId);
        }
        return Collections.emptyList();
    }


    private List<QcWorkOrderWx1v1RepeatExcelVo> convertRepeatVo(List<WorkOrderVO> workOrderVos, long adminUserId) {

        //获取index表
        List<Long> workOrderIds = workOrderVos.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        List<RiskQcSearchIndex> riskQcSearchIndexList = riskQcSearchIndexBiz.getByWorkOrderIds(workOrderIds);
        Map<Long, RiskQcSearchIndex> qcSearchIndexMap = riskQcSearchIndexList.stream()
                .collect(Collectors.toMap(RiskQcSearchIndex::getWorkOrderId, Function.identity(), (k1, k2) -> k2));

        //获取质检结果映射关系
        List<RiskQcResultConfig> riskQcResultConfigList = riskQcResultConfigBiz.getAll();
        Map<Long, RiskQcResultConfig> riskQcResultConfigMap = riskQcResultConfigList.stream()
                .collect(Collectors.toMap(RiskQcResultConfig::getId, Function.identity(), (k1, k2) -> k2));

        List<QcWorkOrderWx1v1RepeatExcelVo> result = Lists.newArrayList();
        for (WorkOrderVO workOrderVo : workOrderVos) {
            QcWorkOrderWx1v1RepeatExcelVo qcWorkOrderWx1v1RepeatExcelVo = new QcWorkOrderWx1v1RepeatExcelVo();
            qcWorkOrderWx1v1RepeatExcelVo.setUpdateTime(workOrderVo.getUpdateTime());
            qcWorkOrderWx1v1RepeatExcelVo.setWorkOrderId(workOrderVo.getWorkOrderId());

            RiskQcSearchIndex riskQcSearchIndex = qcSearchIndexMap.get(workOrderVo.getWorkOrderId());
            if (Objects.nonNull(riskQcSearchIndex)) {
                qcWorkOrderWx1v1RepeatExcelVo.setByName(riskQcSearchIndex.getQcByName());
            }
            RiskQcResultConfig riskQcResultConfig = riskQcResultConfigMap.get(riskQcSearchIndex.getQcResult());
            if (Objects.nonNull(riskQcResultConfig)) {
                qcWorkOrderWx1v1RepeatExcelVo.setWorkOrderResult(riskQcResultConfig.getQcResult());
            }
            // 问题描述、备注
            RiskQcResult riskQcResult = riskQcResultBiz.getByWorkOrderId(workOrderVo.getWorkOrderId());
            if (Objects.nonNull(riskQcResult)) {
                RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<>() {
                });
                if (Objects.nonNull(riskQcResultVo)) {
                    this.setRepeatSecondDesc(qcWorkOrderWx1v1RepeatExcelVo, riskQcResultVo);
                    qcWorkOrderWx1v1RepeatExcelVo.setRepeatOtherNotes(riskQcResultVo.getRemark());
                }
            }
            // 原质检工单问题描述、备注
            long qcId = riskQcDetailService.getQcId(workOrderVo.getWorkOrderId());
            List<RiskQcMaterialsInfo> riskQcMaterialsInfos = riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.ORIGINAL_WORK_ORDER.getKey());
            if (CollectionUtils.isNotEmpty(riskQcMaterialsInfos)) {
                long originWorkOrderId = Long.parseLong(riskQcMaterialsInfos.get(0).getMaterialsValue());
                this.setOriginWorkOrderInfo(qcWorkOrderWx1v1RepeatExcelVo, originWorkOrderId);
            }
            result.add(qcWorkOrderWx1v1RepeatExcelVo);
        }
        RiskQcOperationRecord riskQcOperationRecord = new RiskQcOperationRecord();
        riskQcOperationRecord.setDownloadNumber(result.size());
        riskQcOperationRecord.setUserId(adminUserId);
        riskQcOperationRecord.setUserName(accountService.getName(adminUserId));
        riskQcOperationRecord.setOperation("微信1v1复检数据导出");
        riskQcOperationRecordDao.save(riskQcOperationRecord);
        return result;
    }

    private List<WorkOrderVO> getWorkOrderVOS(String startTime, String endTime, int orderType,long adminUserId) {
        //组装参数
        WorkOrderListParam workOrderListParam = new WorkOrderListParam();
        workOrderListParam.setHandleResult(String.valueOf(HandleResultEnum.done.getType()));
        workOrderListParam.setPageSize(2000);
        workOrderListParam.setOrderType(orderType);
        workOrderListParam.setStartTime(startTime);
        workOrderListParam.setEndTime(endTime);
        workOrderListParam.setUserId(adminUserId);
        //获取工单
        List<WorkOrderVO> workOrderVOS = Lists.newArrayList();
        Response<PageResult<WorkOrderVO>> response = cfQcWorkOrderClient.qcOrderlist(workOrderListParam);
        if (response.ok() && Objects.nonNull(response.getData())) {
            PageResult<WorkOrderVO> pageResult = response.getData();
            workOrderVOS = pageResult.getPageList();
        }
        return workOrderVOS;
    }


    @Override
    public ExcelExportDataVO getList(String startTime, String endTime, int orderType,long adminUserId) {
        ExcelExportDataVO data = new ExcelExportDataVO();

        List<WorkOrderVO> workOrderVOS = this.getWorkOrderVOS(startTime, endTime, orderType,adminUserId);
        if (CollectionUtils.isEmpty(workOrderVOS)) {
            data.setClazz(QcWorkOrderExcelVo.class);
            data.setList(Lists.newArrayList());
            return data;
        }

        if (orderType == WorkOrderType.qc_hospital_dept.getType()) {
            List<QcHospitalDeptExcelVO> list = getDeptOrderExcelVo(workOrderVOS);
            data.setClazz(QcHospitalDeptExcelVO.class);
            data.setList(list);
            return data;
        }

        List<QcWorkOrderExcelVo> list = getQcWorkOrderExcelVo(workOrderVOS);
        data.setClazz(QcWorkOrderExcelVo.class);
        data.setList(list);
        return data;
    }

    private <T> void getExportData(QcWorkOrderParam qcWorkOrderParam,
                                                          IOrderExcelDataLoader<T> excelDataLoader,
                                                          Class<T> itemType,long adminUserId) {
        if (Objects.isNull(qcWorkOrderParam)) {
            return;
        }
        List<Long> workOrderIds = this.getWorkOrderIdByEs(qcWorkOrderParam);
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return;
        }

        List<T> qcWorkOrderExcelVos = Lists.newArrayList();
        List<List<Long>> workOrderIdsGroup = Lists.partition(workOrderIds, 50);
        for (List<Long> subWorkOrderIds : workOrderIdsGroup) {
            Response<List<WorkOrderVO>> listResponse = cfQcWorkOrderClient.queryQcByIds(subWorkOrderIds);
            if (listResponse == null || listResponse.notOk()) {
                return;
            }
            List<WorkOrderVO> workOrderVOS = listResponse.getData();
            if (CollectionUtils.isNotEmpty(workOrderVOS)) {
                qcWorkOrderExcelVos.addAll(excelDataLoader.load(workOrderVOS));
            }
        }

        String fileName = "质检工单导出" + qcWorkOrderParam.getOrderType() + "-" + System.currentTimeMillis();
        exportUtil.export(adminUserId, qcWorkOrderExcelVos, itemType, fileName);
    }

    public interface IOrderExcelDataLoader<T> {

        List<T> load(List<WorkOrderVO> orderList);
    }

    private List<QcZhuDongExcelVO> getZhuDongOrderExcelVo(List<WorkOrderVO> workOrderVOList) {
        List<Long> qcIds = workOrderVOList.stream().map(WorkOrderVO::getQcId).collect(Collectors.toList());
        List<Long> workOrderIds = workOrderVOList.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        List<Long> userIds = workOrderVOList.stream().map(WorkOrderVO::getOperatorId).collect(Collectors.toList());


        //获取质检信息
        Map<Long, RiskQcBaseInfo> qcBaseInfoMap = getQcBaseInfoMap(qcIds);

        //获取录音问题配置
        Map<Long, RiskQcResultConfig> riskQcResultConfigMap = getRiskQcResultConfigMap();

        //获取index表
        Map<Long, RiskQcSearchIndex> qcSearchIndexMap = getQcSearchIndexMap(workOrderIds);

        //获取用户信息
        Map<Long, AuthUserDto> authUserDtoMap = accountService.getAuthUserDtoList(userIds);

        //获取质检结果
        Map<Long, RiskQcResult> riskQcResultMap = getRiskQcResultMap(workOrderIds);

        List<QcZhuDongExcelVO> qcWorkOrderExcelVos = Lists.newArrayList();
        for (WorkOrderVO workOrderVO : workOrderVOList) {
            long workOrderId = workOrderVO.getWorkOrderId();

            QcZhuDongExcelVO v = new QcZhuDongExcelVO();
            v.setWorkOrderId(workOrderId);
            v.setOrderTypeMsg(WorkOrderType.getFromType(workOrderVO.getOrderType()).getMsg());
            v.setCaseId(workOrderVO.getCaseId());

            String name = StringUtils.EMPTY;
            AuthUserDto authUserDto = authUserDtoMap.get(workOrderVO.getOperatorId());
            if (Objects.nonNull(authUserDto) && StringUtils.isNotEmpty(authUserDto.getUserName())) {
                name = authUserDto.getUserName();
            }
            v.setOperatorName(name);

            RiskQcBaseInfo riskQcBaseInfo = qcBaseInfoMap.get(workOrderVO.getQcId());
            if (riskQcBaseInfo == null) {
                continue;
            }
            RiskQcSearchIndex index = qcSearchIndexMap.get(workOrderId);
            if (index == null) {
                continue;
            }

            v.setTargetName(index.getQcByName());
            v.setTargetOrgName(index.getOrganization());

            //导出正确的驳回项
            List<RiskQcCorrectRefuseRecord> riskQcCorrectRefuseRecordList = cfRiskQcCorrectRefuseRecordDao.getCorrectRefuseByWorkOrderId(workOrderId);
            if (CollectionUtils.isEmpty(riskQcCorrectRefuseRecordList)) {
                v.setCorrectRefuse("无驳回项");
            } else {
                List<RiskQcCorrectRefuseRecord> recordList = riskQcCorrectRefuseRecordList.stream().filter(y ->
                        y.getOperationType() == RiskQcOperationTypeEnum.CHANGE_QC_RESULT.getType()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(recordList)) {
                    RiskQcCorrectRefuseRecord riskQcCorrectRefuseRecord = riskQcCorrectRefuseRecordList.get(0);
                    v.setCorrectRefuse(riskQcCorrectRefuseRecord.getCorrectRefuse());
                } else {
                    RiskQcCorrectRefuseRecord riskQcCorrectRefuseRecord = recordList.get(0);
                    v.setCorrectRefuse(riskQcCorrectRefuseRecord.getCorrectRefuse());
                }
            }

            OperationResult<CfCallOutRecordMsg> operationResult = null;
            if (workOrderVO.getOrderType() == WorkOrderType.qc_zhu_dong.getType()) {
                operationResult = orderCallFeignClient.selectZhuDongCallRecords(workOrderId);
            } else if (workOrderVO.getOrderType() == WorkOrderType.qc_material_audit.getType()) {
                operationResult = orderCallFeignClient.selectCallRecords(index.getSourceWorkOrderId());
            }

            if (operationResult != null && operationResult.isSuccess() && operationResult.getData() != null) {
                CfCallOutRecordMsg cfCallOutRecordMsg = operationResult.getData();
                List<CfCallOutRecordMsg.CallOutDetail> callOutDetailList = cfCallOutRecordMsg.getCallOutDetails();
                int status = 1;
                //无通话记录
                if (CollectionUtils.isNotEmpty(callOutDetailList)) {
                    //PhoneStatus() == 200  == 呼通
                    List<CfCallOutRecordMsg.CallOutDetail> callOutDetails = callOutDetailList.stream().filter(y -> y.getPhoneStatus() == 200).collect(Collectors.toList());
                    //有有效通话记录
                    if (CollectionUtils.isNotEmpty(callOutDetails)) {
                        status = 2;
                    } else {
                        //仅包含无效通话记录
                        status = 3;
                    }
                }
                v.setCallStatus(MaterialWorkOrderCallStatusEnum.findDesc(status));
                v.setTotalCallTimeMsg(getFormatDuration(cfCallOutRecordMsg.getTotalDuration()));
            } else {
                v.setCallStatus("读取失败");
                v.setTotalCallTimeMsg("读取失败");
            }

            if (workOrderVO.getOrderType() == WorkOrderType.qc_zhu_dong.getType()) {
                Integer result = cfRiskQcBeQualityInspectedResultRecordDao.getInspectedResult(workOrderId);
                if (Objects.isNull(result)) {
                    v.setQcResult("读取失败");
                } else {
                    String msg = "读取失败";
                    if (result == -1) {
                        msg = "未审核";
                    } else if (result == 1) {
                        msg = "审核驳回";
                    } else if (result == 2) {
                        msg = "审核通过";
                    }
                    v.setQcResult(msg);
                }
            } else if (workOrderVO.getOrderType() == WorkOrderType.qc_material_audit.getType()) {
                v.setHandleResult(HandleResultEnum.getFromType(index.getHandleResult()).getShowMsg());
            }

            v.setRuleName(index.getRuleName());
            v.setHandleTime(workOrderVO.getFinishTime());

            RiskQcResult riskQcResult = riskQcResultMap.get(workOrderId);
            if (Objects.isNull(riskQcResult)) {
                qcWorkOrderExcelVos.add(v);
                continue;
            }

            //质检结果
            long firstQcResultId = riskQcResult.getFirstQcResultId();
            RiskQcResultConfig firstQcResult = riskQcResultConfigMap.get(firstQcResultId);

            if (Objects.nonNull(firstQcResult)) {
                v.setResultMsg(firstQcResult.getQcResult());
            }
            RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<>() {
            });
            if (riskQcResultVo == null) {
                qcWorkOrderExcelVos.add(v);
                continue;
            }
            Map<String, RiskMaterialQcStandardVo> materialQcResultOption = riskQcResultVo.getMaterialQcResultOption();
            if (MapUtils.isEmpty(materialQcResultOption)) {
                qcWorkOrderExcelVos.add(v);
                continue;
            }
            v.setCallRemark(riskQcResultVo.getVoiceRemark());
            v.setOtherRemark(riskQcResultVo.getUserWriteRemark());
            v.setReviseOpinion(riskQcResultVo.getSuggest());
            List<QcZhuDongExcelVO> optionExcelList = Lists.newArrayList();
            materialQcResultOption.values().stream()
                    .filter(b -> b.getRiskQcStandardVo() != null)
                    .flatMap(a -> a.getRiskQcStandardVo().stream())
                    .forEach(oneOption -> {

                        // 二级问题要放大条数 有几个二级 就放大几条
                        List<RiskQcStandardDetailVo> riskQcStandardSecondVos = oneOption.getRiskQcStandardSecondVos();
                        List<RiskQcStandardDetailVo> checkList = riskQcStandardSecondVos.stream()
                                .filter(RiskQcStandardDetailVo::isCheck)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(checkList)) {
                            return;
                        }
                        for (RiskQcStandardDetailVo twoOption : riskQcStandardSecondVos) {
                            if (!twoOption.isCheck()){
                                continue;
                            }
                            v.setOneLevelIssue(oneOption.getStandardName());
                            v.setMaterialTypeMsg(getMaterialTypeMsgBySecondType(oneOption.getSecondStandardType()));
                            QcZhuDongExcelVO newV = new QcZhuDongExcelVO();
                            BeanUtils.copyProperties(v, newV);
                            String standardName = twoOption.getStandardName();
                            newV.setTwoLevelIssue(standardName);
                            List<String> list = Stream.of(twoOption.getProperty().split("-")).collect(Collectors.toList());
                            newV.setOneLevelProperty(list.get(0));
                            if (list.size() > 1){
                                newV.setTwoLevelProperty(list.get(1));
                            }
                            optionExcelList.add(newV);
                        }
                    });
            // 若结果为空 则为合格 没有选中任何二级问题 需导出
            if (CollectionUtils.isEmpty(optionExcelList)) {
                qcWorkOrderExcelVos.add(v);
            } else {
                qcWorkOrderExcelVos.addAll(optionExcelList);
            }
        }
        return qcWorkOrderExcelVos;
    }

    private String getMaterialTypeMsgBySecondType(int secondStandardType) {
        return RiskQcSecondStandardTypeEnum.findOfCode(secondStandardType);
    }

    /**
     * eg: 01:02:12
     */
    private String getFormatDuration(Integer duration) {
        int hour = duration / 3600;
        int minute = (duration % 3600) / 60;
        int sec = duration % 60;
        return String.format("%02d", hour) + ":" +
                String.format("%02d", minute) + ":" +
                String.format("%02d", sec);
    }

    private void getDeptExcelData(QcWorkOrderParam qcWorkOrderParam,long adminUserId) {
        getExportData(qcWorkOrderParam, this::getDeptOrderExcelVo, QcHospitalDeptExcelVO.class,adminUserId);
    }

    private List<QcHospitalDeptExcelVO> getDeptOrderExcelVo(List<WorkOrderVO> workOrderVOS) {
        //获取质检信息
        List<Long> qcIds = workOrderVOS.stream().map(WorkOrderVO::getQcId).collect(Collectors.toList());
        List<RiskQcBaseInfo> riskQcBaseInfos = riskQcBaseInfoBiz.getByIds(qcIds);
        Map<Long, RiskQcBaseInfo> qcBaseInfoMap = riskQcBaseInfos.stream()
                .collect(Collectors.toMap(RiskQcBaseInfo::getId, Function.identity(), (k1, k2) -> k2));

        List<RiskQcResultConfig> riskQcResultConfigList = riskQcResultConfigBiz.getAll();
        Map<Long, RiskQcResultConfig> riskQcResultConfigMap = riskQcResultConfigList.stream()
                .collect(Collectors.toMap(RiskQcResultConfig::getId, Function.identity(), (k1, k2) -> k2));

        //获取index表
        List<Long> workOrderIds = workOrderVOS.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        List<RiskQcSearchIndex> riskQcSearchIndexList = riskQcSearchIndexBiz.getByWorkOrderIds(workOrderIds);
        Map<Long, RiskQcSearchIndex> qcSearchIndexMap = riskQcSearchIndexList.stream()
                .collect(Collectors.toMap(RiskQcSearchIndex::getWorkOrderId, Function.identity(), (k1, k2) -> k2));

        List<QcHospitalDeptExcelVO> qcWorkOrderExcelVos = Lists.newArrayList();
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            QcHospitalDeptExcelVO v = new QcHospitalDeptExcelVO();
            v.setCreateTime(workOrderVO.getCreateTime());
            v.setHandleTime(workOrderVO.getHandleTime());
            v.setUpdateTime(workOrderVO.getUpdateTime());

            long workOrderId = workOrderVO.getWorkOrderId();
            v.setWorkOrderId(workOrderId);
//            v.setOrderTypeMsg(WorkOrderType.getFromType(workOrderVO.getOrderType()).getMsg());
            v.setOrderTypeMsg("科室质检工单");
            v.setTargetType("线下顾问");

            v.setAssignType("必须分配");
            v.setOrderLevel(OrderLevel.getFromType(workOrderVO.getOrderLevel()).getMsg());
            v.setHandleResultMsg(HandleResultEnum.getFromType(workOrderVO.getHandleResult()).getShowMsg());

//            v.setOtherRemark();
            String name = accountService.getName(Math.toIntExact(workOrderVO.getOperatorId()));
            v.setOperatorName(name);

            RiskQcBaseInfo riskQcBaseInfo = qcBaseInfoMap.get(workOrderVO.getQcId());
            if (riskQcBaseInfo == null) {
                continue;
            }

            // 北京-朝阳-张三,北京-顺义-李四
            String qcByName = riskQcBaseInfo.getQcByName();
            // 张三,李四
            String showName = Arrays.stream(StringUtils.split(qcByName, ","))
                    .map(a -> StringUtils.substring(a, StringUtils.lastIndexOf(a, "-") + 1))
                    .collect(Collectors.joining(","));
            v.setTargetName(showName);
            v.setTargetOrgName(qcByName);
            RiskQcSearchIndex index = qcSearchIndexMap.get(workOrderId);
            if (index == null) {
                continue;
            }
            long deptId = index.getHospitalDeptId();
            if (deptId <= 0) {
                continue;
            }
            DepartmentChangeDetailModel deptInfo = clewDelegate.getHospitalDeptInfo((int) deptId).getData();
            String deptName = deptInfo.getHospitalName() + "-" + deptInfo.getChangedBuildingDetail().getBuildName();
            v.setDeptIdAndTitle(deptId + String.valueOf((char) 10) + deptName);

            RiskQcResult riskQcResult = riskQcResultBiz.getByWorkOrderId(workOrderId);
            if (Objects.isNull(riskQcResult)) {
                qcWorkOrderExcelVos.add(v);
                continue;
//            this.addQcResult(riskQcResultConfigMap, workOrderVO, v);
            }

            //质检结果
            long firstQcResultId = riskQcResult.getFirstQcResultId();
            RiskQcResultConfig firstQcResult = riskQcResultConfigMap.get(firstQcResultId);

            if (Objects.nonNull(firstQcResult)) {
                v.setOneLevelResult(firstQcResult.getQcResult());
            }
            RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<>() {
            });
            if (riskQcResultVo == null) {
                qcWorkOrderExcelVos.add(v);
                continue;
            }
            List<RiskQcStandardVo> qcResultOption = riskQcResultVo.getQcResultOption();
            if (CollectionUtils.isEmpty(qcResultOption)) {
                qcWorkOrderExcelVos.add(v);
                continue;
            }
            for (RiskQcStandardVo oneOption : qcResultOption) {
                // 二级问题要放大条数 有几个二级 就放大几条
                for (RiskQcStandardDetailVo twoOption : oneOption.getRiskQcStandardSecondVos()) {
                    v.setOneLevelIssue(oneOption.getStandardName());
                    v.setOtherRemark(riskQcResultVo.getUserWriteRemark());
                    QcHospitalDeptExcelVO newV = new QcHospitalDeptExcelVO();
                    BeanUtils.copyProperties(v, newV);
                    String standardName = twoOption.getStandardName();
                    newV.setTwoLevelIssue(standardName);
                    qcWorkOrderExcelVos.add(newV);
                }
            }
        }
        return qcWorkOrderExcelVos;
    }

    public void getDetailList(QcWorkOrderParam qcWorkOrderParam, long adminUserId) {
        getExportData(qcWorkOrderParam, this::getQcWorkOrderExcelVo, QcWorkOrderExcelVo.class,adminUserId);
    }

    private List<QcWorkOrderExcelVo> getQcWorkOrderExcelVo(List<WorkOrderVO> workOrderVOS) {
        //获取质检信息
        List<Long> qcIds = workOrderVOS.stream().map(WorkOrderVO::getQcId).collect(Collectors.toList());
        List<RiskQcBaseInfo> riskQcBaseInfos = riskQcBaseInfoBiz.getByIds(qcIds);
        Map<Long, RiskQcBaseInfo> qcBaseInfoMap = riskQcBaseInfos.stream()
                .collect(Collectors.toMap(RiskQcBaseInfo::getId, Function.identity(), (k1, k2) -> k2));

        //获取index表
        List<Long> workOrderIds = workOrderVOS.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        List<RiskQcSearchIndex> riskQcSearchIndexList = riskQcSearchIndexBiz.getByWorkOrderIds(workOrderIds);
        Map<Long, RiskQcSearchIndex> qcSearchIndexMap = riskQcSearchIndexList.stream()
                .collect(Collectors.toMap(RiskQcSearchIndex::getWorkOrderId, Function.identity(), (k1, k2) -> k2));

        //获取用户信息
        List<Integer> caseIds = workOrderVOS.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());
        FeignResponse<List<CrowdfundingInfo>> crowdfundingInfoResponse = crowdfundingFeignClient.getCrowdfundingListById(caseIds);
        Map<Long, UserInfoModel> userInfoModelMap = Maps.newHashMap();
        if (crowdfundingInfoResponse.ok() && CollectionUtils.isNotEmpty(crowdfundingInfoResponse.getData())) {
            List<CrowdfundingInfo> crowdfundingInfos = crowdfundingInfoResponse.getData();
            List<Long> userIds = crowdfundingInfos.stream().map(CrowdfundingInfo::getUserId).collect(Collectors.toList());
            List<UserInfoModel> userInfoModels = userInfoDelegateService.getUserInfoByUserIdBatch(userIds);
            userInfoModelMap = userInfoModels.stream()
                    .collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity(), (k1, k2) -> k2));
        }

        List<RiskQcResultConfig> riskQcResultConfigList = riskQcResultConfigBiz.getAll();
        Map<Long, RiskQcResultConfig> riskQcResultConfigMap = riskQcResultConfigList.stream()
                .collect(Collectors.toMap(RiskQcResultConfig::getId, Function.identity(), (k1, k2) -> k2));

        List<QcWorkOrderExcelVo> qcWorkOrderExcelVos = Lists.newArrayList();
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            QcWorkOrderExcelVo qcWorkOrderExcelVo = buildQcWorkOrderExcelVO(qcBaseInfoMap, qcSearchIndexMap, userInfoModelMap, riskQcResultConfigMap, workOrderVO);
            if (qcWorkOrderExcelVo != null) {
                qcWorkOrderExcelVos.add(qcWorkOrderExcelVo);
            }
        }
        return qcWorkOrderExcelVos;
    }

    private QcWorkOrderExcelVo buildQcWorkOrderExcelVO(Map<Long, RiskQcBaseInfo> qcBaseInfoMap, Map<Long, RiskQcSearchIndex> qcSearchIndexMap, Map<Long, UserInfoModel> userInfoModelMap, Map<Long, RiskQcResultConfig> riskQcResultConfigMap, WorkOrderVO workOrderVO) {
        try {
            QcWorkOrderExcelVo qcWorkOrderExcelVo = new QcWorkOrderExcelVo();
            qcWorkOrderExcelVo.setCreateTime(workOrderVO.getCreateTime());
            qcWorkOrderExcelVo.setHandleTime(workOrderVO.getHandleTime());
            qcWorkOrderExcelVo.setUpdateTime(workOrderVO.getUpdateTime());

            RiskQcSearchIndex riskQcSearchIndex = qcSearchIndexMap.get(workOrderVO.getWorkOrderId());
            if (Objects.nonNull(riskQcSearchIndex)) {
                qcWorkOrderExcelVo.setOrganization(riskQcSearchIndex.getOrganization());
                qcWorkOrderExcelVo.setMaterialId(riskQcSearchIndex.getMaterialId());
            }

            String name = accountService.getName(Math.toIntExact(workOrderVO.getOperatorId()));
            qcWorkOrderExcelVo.setOperatorName(name);

            RiskQcBaseInfo riskQcBaseInfo = qcBaseInfoMap.get(workOrderVO.getQcId());
            if (Objects.nonNull(riskQcBaseInfo)) {
                qcWorkOrderExcelVo.setBdName(riskQcBaseInfo.getQcByName());
                qcWorkOrderExcelVo.setCaseId(riskQcBaseInfo.getCaseId());
                qcWorkOrderExcelVo.setPatientName(getPatientName((int) riskQcBaseInfo.getCaseId()));
            }

            UserInfoModel userInfoModel = userInfoModelMap.get(workOrderVO.getCaseUserId());
            if (Objects.nonNull(userInfoModel)) {
                qcWorkOrderExcelVo.setMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
            }
            int totalDuration = riskQcWorkOrderService.getTotalDuration(riskQcBaseInfo.getId());
            qcWorkOrderExcelVo.setTotalDuration(totalDuration);

            this.addQcResult(riskQcResultConfigMap, workOrderVO, qcWorkOrderExcelVo);
            return qcWorkOrderExcelVo;
        } catch (Exception e) {
            log.error("数据导出单条信息报错", e);
        }
        return null;
    }


    private List<QcWorkOrderWx1v1ExcelVo> convertVo(List<WorkOrderVO> workOrderVos, long adminUserId) {

        //获取index表
        List<Long> workOrderIds = workOrderVos.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        List<RiskQcSearchIndex> riskQcSearchIndexList = riskQcSearchIndexBiz.getByWorkOrderIds(workOrderIds);
        Map<Long, RiskQcSearchIndex> qcSearchIndexMap = riskQcSearchIndexList.stream()
                .collect(Collectors.toMap(RiskQcSearchIndex::getWorkOrderId, Function.identity(), (k1, k2) -> k2));

        //获取质检结果映射关系
        List<RiskQcResultConfig> riskQcResultConfigList = riskQcResultConfigBiz.getAll();
        Map<Long, RiskQcResultConfig> riskQcResultConfigMap = riskQcResultConfigList.stream()
                .collect(Collectors.toMap(RiskQcResultConfig::getId, Function.identity(), (k1, k2) -> k2));

        //查询任务信息
        List<Long> taskId = workOrderVos.stream().map(WorkOrderVO::getTaskId).collect(Collectors.toList());
        Response<List<CfClueInfoModel>> listResponse = cfClewtrackTaskFeignClient.listCfClueInfo(taskId);
        Map<Long, CfClueInfoModel> cfClueInfoModelMap = Maps.newHashMap();
        if (listResponse.ok() && CollectionUtils.isNotEmpty(listResponse.getData())) {
            cfClueInfoModelMap = listResponse.getData().stream().collect(Collectors.toMap(CfClueInfoModel::getTaskId,
                    Function.identity(), (k1, k2) -> k2));
        }

        List<QcWorkOrderWx1v1ExcelVo> result = Lists.newArrayList();
        for (WorkOrderVO workOrderVo : workOrderVos) {
            QcWorkOrderWx1v1ExcelVo qcWorkOrderWx1v1ExcelVo = new QcWorkOrderWx1v1ExcelVo();
            qcWorkOrderWx1v1ExcelVo.setUpdateTime(workOrderVo.getUpdateTime());
            qcWorkOrderWx1v1ExcelVo.setWorkOrderId(workOrderVo.getWorkOrderId());
            String operatorName = accountService.getName(workOrderVo.getOperatorId());
            qcWorkOrderWx1v1ExcelVo.setName(operatorName);

            RiskQcSearchIndex riskQcSearchIndex = qcSearchIndexMap.get(workOrderVo.getWorkOrderId());
            if (Objects.nonNull(riskQcSearchIndex)) {
                qcWorkOrderWx1v1ExcelVo.setRegisterMobile(beanMapperConvertHandler.decrypt(riskQcSearchIndex.getRegisterMobileEncrypt()));
                qcWorkOrderWx1v1ExcelVo.setOrganization(riskQcSearchIndex.getOrganization());
                qcWorkOrderWx1v1ExcelVo.setByName(riskQcSearchIndex.getQcByName());
                qcWorkOrderWx1v1ExcelVo.setRuleName(riskQcSearchIndex.getRuleName());
                RiskQcResultConfig riskQcResultConfig = riskQcResultConfigMap.get(riskQcSearchIndex.getQcResult());
                if (Objects.nonNull(riskQcResultConfig)) {
                    qcWorkOrderWx1v1ExcelVo.setWorkOrderResult(riskQcResultConfig.getQcResult());
                }
            }
            //设置新手机号、通话时长
            CfClueInfoModel cfClueInfoModel = cfClueInfoModelMap.get(workOrderVo.getTaskId());
            if (Objects.nonNull(cfClueInfoModel)) {
                qcWorkOrderWx1v1ExcelVo.setNewMobile(cfClueInfoModel.getClewSecondPhone());
                String mis = cfClueInfoModel.getUserId();
                String encryptPhone = oldShuidiCipher.aesEncrypt(cfClueInfoModel.getClewPhone());
                String encryptSecondPhone = oldShuidiCipher.aesEncrypt(cfClueInfoModel.getClewSecondPhone());
                Date time = DateUtil.parseDateTime(cfClueInfoModel.getAssignTime());
                RiskQcTotalCallRecords riskQcTotalCallRecords = risk1v1QcDetailService.buildRiskQcTotalCallRecords(mis, encryptPhone, encryptSecondPhone, time);
                qcWorkOrderWx1v1ExcelVo.setTotalDuration(riskQcTotalCallRecords == null ? 0 : riskQcTotalCallRecords.getTotalDuration());
            }
            // 问题描述、属性
            RiskQcResult riskQcResult = riskQcResultBiz.getByWorkOrderId(workOrderVo.getWorkOrderId());
            if (Objects.nonNull(riskQcResult)) {
                RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<>() {
                });
                if (Objects.nonNull(riskQcResultVo)) {
                    qcWorkOrderWx1v1ExcelVo.setRecordingNotes(riskQcResultVo.getVoiceRemark());
                    qcWorkOrderWx1v1ExcelVo.setOtherNotes(riskQcResultVo.getUserWriteRemark());
                    this.setFirstDesc(qcWorkOrderWx1v1ExcelVo, riskQcResultVo);
                    this.setSecondDesc(qcWorkOrderWx1v1ExcelVo, riskQcResultVo);
                    this.setFirstAttribute(qcWorkOrderWx1v1ExcelVo, riskQcResultVo);
                    this.setSecondAttribute(qcWorkOrderWx1v1ExcelVo, riskQcResultVo);
                }
            }
            result.add(qcWorkOrderWx1v1ExcelVo);
        }
        // add log
        RiskQcOperationRecord riskQcOperationRecord = new RiskQcOperationRecord();
        riskQcOperationRecord.setDownloadNumber(result.size());
        riskQcOperationRecord.setUserId(adminUserId);
        riskQcOperationRecord.setUserName(accountService.getName(adminUserId));
        riskQcOperationRecord.setOperation("微信1v1质检数据导出");
        riskQcOperationRecordDao.save(riskQcOperationRecord);
        return result;
    }


    private void setRepeatSecondDesc(QcWorkOrderWx1v1RepeatExcelVo qcWorkOrderWx1v1RepeatExcelVo, RiskQcResultVo riskQcResultVo) {
        List<RiskQcStandardVo> riskQcStandardVos = riskQcResultVo.getQcResultOption();
        List<String> secondDescSet = riskQcStandardVos.stream().flatMap(riskQcStandardVo ->
                riskQcStandardVo.getRiskQcStandardSecondVos().stream().map(RiskQcStandardDetailVo::getStandardName))
                .collect(Collectors.toList());
        var secondDesc = String.join("\n", secondDescSet);
        qcWorkOrderWx1v1RepeatExcelVo.setSecondDesc(secondDesc);
    }


    private void setFirstAttribute(QcWorkOrderWx1v1ExcelVo qcWorkOrderWx1v1ExcelVo, RiskQcResultVo riskQcResultVo) {
        List<RiskQcStandardVo> riskQcStandardVos = riskQcResultVo.getQcResultOption();
        List<String> firstAttributeSet = riskQcStandardVos
                .stream()
                .flatMap(riskQcStandardVo -> riskQcStandardVo.getRiskQcStandardSecondVos().stream().map(RiskQcStandardDetailVo::getProperty)).map(s -> {
                    List<String> list = Stream.of(s.split("-")).collect(Collectors.toList());
                    return list.get(0);
                })
                .collect(Collectors.toList());
        var firstAttribute = String.join("\n", firstAttributeSet);
        qcWorkOrderWx1v1ExcelVo.setFirstAttribute(firstAttribute);
    }

    private void setSecondAttribute(QcWorkOrderWx1v1ExcelVo qcWorkOrderWx1v1ExcelVo, RiskQcResultVo riskQcResultVo) {
        List<RiskQcStandardVo> riskQcStandardVos = riskQcResultVo.getQcResultOption();
        List<String> firstAttributeSet = riskQcStandardVos
                .stream()
                .flatMap(riskQcStandardVo -> riskQcStandardVo.getRiskQcStandardSecondVos().stream().map(RiskQcStandardDetailVo::getProperty)).map(s -> {
                    List<String> list = Stream.of(s.split("-")).collect(Collectors.toList());
                    return list.get(1);
                })
                .collect(Collectors.toList());
        var firstAttribute = String.join("\n", firstAttributeSet);
        qcWorkOrderWx1v1ExcelVo.setSecondAttribute(firstAttribute);
    }

    private void setSecondDesc(QcWorkOrderWx1v1ExcelVo qcWorkOrderWx1v1ExcelVo, RiskQcResultVo riskQcResultVo) {
        List<RiskQcStandardVo> riskQcStandardVos = riskQcResultVo.getQcResultOption();
        List<String> secondDescSet = riskQcStandardVos.stream().flatMap(riskQcStandardVo ->
                riskQcStandardVo.getRiskQcStandardSecondVos().stream().map(RiskQcStandardDetailVo::getStandardName))
                .collect(Collectors.toList());
        var secondDesc = String.join("\n", secondDescSet);
        qcWorkOrderWx1v1ExcelVo.setSecondDesc(secondDesc);
    }

    private void setFirstDesc(QcWorkOrderWx1v1ExcelVo qcWorkOrderWx1v1ExcelVo, RiskQcResultVo riskQcResultVo) {
        List<RiskQcStandardVo> riskQcStandardVos = riskQcResultVo.getQcResultOption();
        List<String> collect = riskQcStandardVos.stream().map(RiskQcStandardVo::getStandardName).collect(Collectors.toList());
        var firstDesc = String.join("\n", collect);
        qcWorkOrderWx1v1ExcelVo.setFirstDesc(firstDesc);
    }

    private String getPatientName(int caseId) {
        RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> rpcResult = preposeMaterialClient.selectMaterialByCaseId(caseId);
        if (rpcResult == null || rpcResult.isFail() || CollectionUtils.isEmpty(rpcResult.getData())) {
            return "";
        }
        List<PreposeMaterialModel.MaterialInfoVo> infoVoList = rpcResult.getData();
        PreposeMaterialModel.MaterialInfoVo materialInfoVo =
                infoVoList.stream().sorted(Comparator.comparing(PreposeMaterialModel.MaterialInfoVo::getId).reversed()).collect(Collectors.toList()).get(0);
        return materialInfoVo.getPatientName();
    }

    private void addQcResult(Map<Long, RiskQcResultConfig> riskQcResultConfigMap,
                             WorkOrderVO workOrderVO,
                             QcWorkOrderExcelVo qcWorkOrderExcelVo) {
        RiskQcResult riskQcResult = riskQcResultBiz.getByWorkOrderId(workOrderVO.getWorkOrderId());
        if (Objects.nonNull(riskQcResult)) {
            //质检结果
            long firstQcResultId = riskQcResult.getFirstQcResultId();
            long secondQcResultId = riskQcResult.getSecondQcResultId();
            RiskQcResultConfig firstQcResult = riskQcResultConfigMap.get(firstQcResultId);
            RiskQcResultConfig secondQcResult = riskQcResultConfigMap.get(secondQcResultId);

            if (Objects.nonNull(firstQcResult)) {
                qcWorkOrderExcelVo.setFirstQcResult(firstQcResult.getQcResult());
            }
            if (Objects.nonNull(secondQcResult)) {
                qcWorkOrderExcelVo.setSecondQcResult(firstQcResult.getQcResult());
            }
            //录音备注
            RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<RiskQcResultVo>() {
            });
            if (Objects.nonNull(riskQcResultVo)) {
                qcWorkOrderExcelVo.setRecordingNotes(riskQcResultVo.getVoiceRemark());
                qcWorkOrderExcelVo.setOtherNotes(riskQcResultVo.getUserWriteRemark());
            }

            //问题属性
            List<RiskQcStandardVo> qcResultOption = riskQcResultVo.getQcResultOption();
            if (CollectionUtils.isEmpty(qcResultOption)) {
                return;
            }
            for (RiskQcStandardVo riskQcStandardVo : qcResultOption) {
                List<RiskQcStandardDetailVo> riskQcStandardSecondVos = riskQcStandardVo.getRiskQcStandardSecondVos();
                for (RiskQcStandardDetailVo riskQcStandardSecondVo : riskQcStandardSecondVos) {
                    String property = riskQcStandardSecondVo.getProperty();
                    List<String> list = Stream.of(property.split("-")).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(list)) {
                        String firstProperty = list.get(0);
                        switch (firstProperty) {
                            case "高压线":
                                if (Objects.isNull(qcWorkOrderExcelVo.getHighTensionLine())) {
                                    qcWorkOrderExcelVo.setHighTensionLine(riskQcStandardSecondVo.getStandardName());
                                } else {
                                    qcWorkOrderExcelVo.setHighTensionLine(qcWorkOrderExcelVo.getHighTensionLine() + ";" + riskQcStandardSecondVo.getStandardName());
                                }
                                break;
                            case "一类违规":
                                if (Objects.isNull(qcWorkOrderExcelVo.getOneType())) {
                                    qcWorkOrderExcelVo.setOneType(riskQcStandardSecondVo.getStandardName());
                                } else {
                                    qcWorkOrderExcelVo.setOneType(qcWorkOrderExcelVo.getOneType() + ";" + riskQcStandardSecondVo.getStandardName());
                                }
                                break;
                            case "二类违规":
                                if (Objects.isNull(qcWorkOrderExcelVo.getTwoType())) {
                                    qcWorkOrderExcelVo.setTwoType(riskQcStandardSecondVo.getStandardName());

                                } else {
                                    qcWorkOrderExcelVo.setTwoType(qcWorkOrderExcelVo.getTwoType() + ";" + riskQcStandardSecondVo.getStandardName());
                                }
                                break;
                            case "服务问题":
                                if (Objects.isNull(qcWorkOrderExcelVo.getServiceProblems())) {
                                    qcWorkOrderExcelVo.setServiceProblems(riskQcStandardSecondVo.getStandardName());
                                } else {
                                    qcWorkOrderExcelVo.setServiceProblems(qcWorkOrderExcelVo.getServiceProblems() + ";" + riskQcStandardSecondVo.getStandardName());
                                }
                                break;
                            default:
                        }
                    }
                }
            }
        }
    }


    private void setOriginWorkOrderInfo(QcWorkOrderWx1v1RepeatExcelVo qcWorkOrderWx1v1RepeatExcelVo, long originWorkOrderId) {
        // 原工单问题描述、备注
        RiskQcResult riskQcResult = riskQcResultBiz.getByWorkOrderId(originWorkOrderId);
        RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<>() {
        });
        if (Objects.nonNull(riskQcResultVo)) {
            Set<String> secondDescSet = riskQcResultVo.getQcResultOption().stream().flatMap(riskQcStandardVo ->
                    riskQcStandardVo.getRiskQcStandardSecondVos().stream().map(RiskQcStandardDetailVo::getStandardName))
                    .collect(Collectors.toSet());
            var secondDesc = String.join("\n", secondDescSet);
            qcWorkOrderWx1v1RepeatExcelVo.setOriginRecordingNotes(riskQcResultVo.getVoiceRemark());
            qcWorkOrderWx1v1RepeatExcelVo.setOriginOtherNotes(riskQcResultVo.getUserWriteRemark());
            qcWorkOrderWx1v1RepeatExcelVo.setOriginSecondDesc(secondDesc);
        }
    }



    /**
     * 获取质检信息
     *
     * @return
     */
    private Map<Long, RiskQcBaseInfo> getQcBaseInfoMap(List<Long> qcIds) {
        List<RiskQcBaseInfo> riskQcBaseInfos = riskQcBaseInfoBiz.getByIds(qcIds);
        return riskQcBaseInfos.stream().collect(Collectors.toMap(RiskQcBaseInfo::getId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 获取质检信息
     *
     * @return
     */
    private Map<Long, RiskQcResultConfig> getRiskQcResultConfigMap() {
        List<RiskQcResultConfig> riskQcResultConfigList = riskQcResultConfigBiz.getAll();
        return riskQcResultConfigList.stream().collect(Collectors.toMap(RiskQcResultConfig::getId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 获取index表信息
     *
     * @return
     */
    private Map<Long, RiskQcSearchIndex> getQcSearchIndexMap(List<Long> workOrderIds) {
        List<RiskQcSearchIndex> riskQcSearchIndexList = riskQcSearchIndexBiz.getByWorkOrderIds(workOrderIds);
        return riskQcSearchIndexList.stream().collect(Collectors.toMap(RiskQcSearchIndex::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 获取index表信息
     *
     * @return
     */
    private Map<Long, RiskQcResult> getRiskQcResultMap(List<Long> workOrderIds) {
        List<RiskQcResult> riskQcResult = riskQcResultBiz.findByWorkOrderIds(workOrderIds);
        return riskQcResult.stream().collect(Collectors.toMap(RiskQcResult::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
    }


    /**
     * 通过es查询工单id
     *
     * @param qcWorkOrderParam
     * @return
     */
    private List<Long> getWorkOrderIdByEs(QcWorkOrderParam qcWorkOrderParam) {
        UserInfoModel userInfoModel = null;
        if (StringUtils.isNotBlank(qcWorkOrderParam.getMobile())) {
            userInfoModel = userInfoDelegateService.getUserInfoByMobile(qcWorkOrderParam.getMobile());
        }

        CfWorkOrderV2IndexSearchParam searchParam = riskQcWorkOrderService.buildRiskQcSearchParam(qcWorkOrderParam, userInfoModel);

        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        List<CfWorkOrderModel> cfWorkOrderModels = Optional.ofNullable(searchRpcResult)
                .filter(v -> v.getCode() == ErrorCode.SUCCESS.getCode())
                .map(SearchRpcResult::getData)
                .map(CfWorkOrderIndexSearchResult::getModels)
                .orElse(Lists.newArrayList());

        return cfWorkOrderModels.stream().map(CfWorkOrderModel::getWorkOrderId).collect(Collectors.toList());
    }


}
