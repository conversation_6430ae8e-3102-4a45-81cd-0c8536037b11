package com.shuidihuzhu.cf.risk.admin.model.enums;

/**
 * @Auther: subing
 * @Date: 2020/2/23
 */
public enum  PublicSentimentUserTypeEnum {
    //
    PERSON(1, "个人"),
    MEDIA(2, "官方媒体");

    int code;
    String description;

    PublicSentimentUserTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (PublicSentimentUserTypeEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
