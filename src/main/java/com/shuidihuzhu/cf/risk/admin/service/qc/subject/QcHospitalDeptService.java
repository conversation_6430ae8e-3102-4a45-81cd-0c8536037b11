package com.shuidihuzhu.cf.risk.admin.service.qc.subject;

import com.shuidihuzhu.cf.risk.admin.model.param.DeptUpdateParam;
import com.shuidihuzhu.cf.risk.admin.model.qc.subject.QcHospitalDeptDetailVO;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentNameChangeMqModel;
import com.shuidihuzhu.common.web.model.Response;

/**
 * <AUTHOR>
 */
public interface QcHospitalDeptService {

    Response<QcHospitalDeptDetailVO> getDetail(long workOrderId);

    void saveSnapshot(Long workOrderId, DepartmentChangeDetailModel clewInfo);

    Response<Boolean> updateBuildingInfo(long workOrderId, String buildingName, long userId);

    Response<Boolean> updateDepartmentInfo(DeptUpdateParam param);

    boolean getDeptClassifySuccess(DepartmentChangeDetailModel clewInfo);

    void onDeptChange(DepartmentNameChangeMqModel payload);

    void refreshData(int page, int size);
}
