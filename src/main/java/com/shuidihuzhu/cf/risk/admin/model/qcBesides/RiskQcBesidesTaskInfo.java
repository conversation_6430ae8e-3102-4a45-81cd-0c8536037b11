package com.shuidihuzhu.cf.risk.admin.model.qcBesides;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Auther: subing
 * @Date: 2020/9/2
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskQcBesidesTaskInfo {
    /**
     * 手机号
     **/
    private String phone;
    /**
     * 手机号掩码（后台用）
     **/
    private NumberMaskVo phoneMask;
    /**
     * 线索id
     **/
    private String clewId;
    /**
     * 渠道名称
     **/
    private String channelName;

    /**
     * 分配时间
     **/
    private String allotDate;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 人员名称
     */
    private String name;

    /**
     * 工作内容
     */
    private String workContent;
}
