package com.shuidihuzhu.cf.risk.admin.biz.quality.sampling;

import com.github.pagehelper.Page;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategyLog;
import com.shuidihuzhu.cf.risk.admin.model.query.quality.sampling.QualitySpotStrategyQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotStrategyDetailVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotStrategyVo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15 16:33
 */
public interface QualitySpotStrategyBiz {

    Page<RiskQualitySpotStrategy> listByQuery(QualitySpotStrategyQuery strategyQuery);

    RiskQualitySpotStrategyDetailVo getById(Long id);

    RiskQualitySpotStrategy getModelById(Long id);

    int saveStrategy(RiskQualitySpotStrategy riskQualitySpotStrategy);

    RiskQualitySpotLevelConfVo getLatestLevelConf(Long scene);

    RiskQualitySpotLevelConfVo getCurrentLevelConf(Long scene);

    List<RiskQualitySpotStrategy> listByValidScene(Long scene);

    int updateStrategyStatus(Long id, QualitySpotStrategyStatusEnum statusEnum, Date expireTime, long adminUserId);

    List<RiskQualitySpotStrategyLog> listLogByStrategyId(Long strategyId);

    int saveStrategyLog(RiskQualitySpotStrategyLog riskQualitySpotStrategyLog);

    List<RiskQualitySpotStrategy> listValidStrategy(Date currTime, Long startId, Integer limit);

    List<RiskQualitySpotStrategy> listById(List<Long> ids);

    int updateStrategy(RiskQualitySpotStrategy riskQualitySpotStrategy);

}
