package com.shuidihuzhu.cf.risk.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseKnowledgeBiz;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseKnowledge;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.service.DiseaseManagerService;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.constant.DiseaseConstant;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.admin.service.listener.DiseaseKnowledgeListener;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseRaiseTypeEnum;
import com.shuidihuzhu.client.auth.saas.annotation.NoLoginRequired;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import com.shuidihuzhu.pf.common.v2.util.PageUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: niejiangnan
 * @create 2019-11-08 17:12
 **/
@RestController
@Slf4j
@RequestMapping(value = "/api/cf-risk-admin/disease/manager")
@Api("疾病管理")
public class DiseaseManagerController {


    @Autowired
    private DiseaseManagerService diseaseManagerBiz;

    @Autowired
    private RiskDiseaseKnowledgeBiz diseaseKnowledgeBiz;

    @ApiOperation("创建疾病信息")
    @PostMapping(path = "create")
    public Response<Boolean> create(@ApiParam("疾病json字符串") @RequestParam("param") String param) {
        log.info("DiseaseManagerController create param:{}", param);
        Response<RiskDiseaseInfoVO> response = buildCheck(param);
        if (response.notOk()) {
            return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
        }
        RiskDiseaseInfoVO riskDiseaseInfoVO = response.getData();
        //验证治疗方案
        Response<List<RiskDiseaseTreatmentProjectVO>> treatmentResponse = buildCheckTreatment(riskDiseaseInfoVO.getTreatMethodList(),
                riskDiseaseInfoVO.getRaiseType());
        if (treatmentResponse.notOk()) {
            return NewResponseUtil.makeResponse(treatmentResponse.getCode(), treatmentResponse.getMsg(), null);
        }
        return NewResponseUtil.makeSuccess(diseaseManagerBiz.create(riskDiseaseInfoVO, transToBO(treatmentResponse.getData()),
                Math.toIntExact(ContextUtil.getAdminLongUserId()), param));
    }


    @ApiOperation("修改疾病信息")
    @PostMapping(path = "edit")
    public Response<Boolean> edit(@ApiParam("疾病json字符串") @RequestParam("param") String param) {
        log.info("DiseaseManagerController edit param:{}", param);
        Response<RiskDiseaseInfoVO> response = buildCheck(param);
        if (response.notOk() || response.getData() == null) {
            log.info("response:{}", JSON.toJSONString(response));
            return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
        }
        RiskDiseaseInfoVO riskDiseaseInfoVO = response.getData();
        //判断疾病的id
        if (riskDiseaseInfoVO.getId() <= 0) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_NOT_EXIST);
        }
        //验证治疗方案
        Response<List<RiskDiseaseTreatmentProjectVO>> treatmentResponse =
                buildCheckTreatment(riskDiseaseInfoVO.getTreatMethodList(), riskDiseaseInfoVO.getRaiseType());
        if (treatmentResponse.notOk()) {
            return NewResponseUtil.makeResponse(treatmentResponse.getCode(), treatmentResponse.getMsg(), null);
        }
        return NewResponseUtil.makeSuccess(diseaseManagerBiz.edit(riskDiseaseInfoVO, transToBO(treatmentResponse.getData()),
                Math.toIntExact(ContextUtil.getAdminLongUserId()), param));
    }

    private List<RiskDiseaseTreatmentProject> transToBO(List<RiskDiseaseTreatmentProjectVO> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }
        List<RiskDiseaseTreatmentProject> treatmentProjects = Lists.newArrayList();
        for (RiskDiseaseTreatmentProjectVO vo : data) {
            RiskDiseaseTreatmentProject treatmentProject = new RiskDiseaseTreatmentProject();
            treatmentProject.setId(vo.getId());
            treatmentProject.setProjectName(vo.getProjectName());
            treatmentProject.setProjectMergeRule(vo.getProjectMergeRule());
            treatmentProject.setMinTreatmentFee((int)(vo.getMinTreatmentFee() * 100));
            treatmentProject.setMaxTreatmentFee((int)(vo.getMaxTreatmentFee() * 100));
            treatmentProject.setCustomTreatment(StringUtils.trimToEmpty(vo.getCustomTreatment()));
            treatmentProject.setRaiseType(vo.getRaiseType());
            treatmentProjects.add(treatmentProject);
        }
        return treatmentProjects;
    }


    @ApiOperation("删除疾病信息")
    @PostMapping(path = "delete")
    public Response<Boolean> delete(@ApiParam("疾病序号")
                                    @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId,
                                    @RequestParam(value = "deleteReason", defaultValue = "") String deleteReason) {
        log.info("DiseaseManagerController delete  diseaseId:{} deleteReason:{}", diseaseId, deleteReason);
        return diseaseManagerBiz.delete(diseaseId, deleteReason);
    }


    @ApiOperation("疾病详情信息")
    @PostMapping(path = "/disease/detail")
    public Response<RiskDiseaseInfoVO> detail(@ApiParam("疾病序号")
                                              @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId) {
        log.info("DiseaseManagerController delete  diseaseId:{}", diseaseId);
        if (diseaseId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return diseaseManagerBiz.getDetail(diseaseId);
    }



    @ApiOperation("查找列表信息")
    @PostMapping(path = "list")
    public Response<RiskDiseaseInfoVO> list(@ApiParam("疾病类别名称")@RequestParam(value = "diseaseClassName", required = false) String diseaseClassName,
                                            @ApiParam("医学诊断名称")@RequestParam(value = "medicalName", required = false) String medicalName,
                                            @ApiParam("常用名")@RequestParam(value = "normalName", required = false) String normalName,
                                            @ApiParam("疾病类型")@RequestParam(value = "raiseType", defaultValue = "0", required = false) int raiseType,
                                            @RequestParam(defaultValue = "pageJson") String pageJson) {
        log.info("DiseaseManagerController list  diseaseClassName:{} medicalName:{} normalName:{} raiseType:{} pageJson:{}",
                diseaseClassName, medicalName, normalName, raiseType, pageJson);
        //验证type是否存在
        if (DiseaseRaiseTypeEnum.findByCode(raiseType) == null) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_TYPE_NOT_EXIST);
        }
        PageRequest pageRequest = PageUtil.parseJsonString(pageJson);
        return diseaseManagerBiz.getDiseaseList(diseaseClassName, medicalName, normalName,
                raiseType, pageRequest);
    }

    @ApiOperation("查找列表信息")
    @PostMapping(path = "list-v2")
    public Response listV2(@ApiParam("疾病类别名称")@RequestParam(value = "diseaseClassName", required = false) String diseaseClassName,
                         @ApiParam("疾病类型")@RequestParam(value = "raiseType", defaultValue = "0") int raiseType,
                         @ApiParam("治疗方案")@RequestParam(value = "treatMethod", defaultValue = "") String treatMethod,
                         @ApiParam("是否删除")@RequestParam(value = "isDelete", defaultValue = "2") int isDelete,
                         @ApiParam("开始时间")@RequestParam(value = "startCreateTime", defaultValue = "") String startCreateTime,
                         @ApiParam("结束时间")@RequestParam(value = "endCreateTime", defaultValue = "") String endCreateTime,
                         @RequestParam("current") int current, @RequestParam("pageSize") int pageSize) {
        if (current < 0 || pageSize < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        log.info("DiseaseManagerController list  diseaseClassName:{} treatMethod:{} isDelete:{} startCreateTime:{} endCreateTime:{} raiseType:{} current:{} pageSize:{}",
                diseaseClassName, treatMethod, isDelete, startCreateTime, endCreateTime, raiseType, current, pageSize);
        //验证type是否存在
        if (DiseaseRaiseTypeEnum.findByCode(raiseType) == null) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_TYPE_NOT_EXIST);
        }
        return diseaseManagerBiz.getDiseaseListV2(diseaseClassName, treatMethod, isDelete, startCreateTime, endCreateTime,
                raiseType, current, pageSize);
    }

    @ApiOperation("获取疾病类型")
    @PostMapping(path = "/raise-type/get")
    public Response<List<RiskRaiseTypeVO>> getRaiseType() {
        log.info("getRaiseType");
        return diseaseManagerBiz.getRaiseType();
    }


    @ApiOperation("查看操作记录接口")
    @PostMapping(path = "/operation-record/list")
    public Response<List<RiskDiseaseOperationVo>> listOperationRecord(@ApiParam("疾病序号")
                                                                          @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId) {
        log.info("listOperationRecord diseaseId:{}", diseaseId);
        return diseaseManagerBiz.getOperationRecordList(diseaseId);
    }


    @ApiOperation("获取方案名称")
    @PostMapping(path = "/treatment/name-list")
    public Response<RiskDiseaseInfoProjectNameVO> treatmentNameList() {
        log.info("DiseaseManagerController treatmentNameList  ");
        return diseaseManagerBiz.getProjectNameList();
    }


    @ApiOperation("查看治疗方案")
    @PostMapping(path = "/treatment/list")
    public Response<List<RiskDiseaseTreatmentProjectVO>> listTreatment(@ApiParam("疾病序号")
                                                                       @RequestParam(value = "diseaseId", defaultValue = "0") long diseaseId) {
        log.info("listTreatment diseaseId:{}", diseaseId);
        if (diseaseId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return diseaseManagerBiz.listTreatment(diseaseId);
    }


    private Response<List<RiskDiseaseTreatmentProjectVO>> buildCheckTreatment(String treatMethodList, int raiseType) {
        //对于可发起类型的疾病，至少填写一个治疗方案；
        if (DiseaseRaiseTypeEnum.findByCode(raiseType) == DiseaseRaiseTypeEnum.CAN_RAISE
                && StringUtils.isBlank(treatMethodList)) {
            log.info("treatMethodList is empty");
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_TYPE_CAN_MATCH);
        }
        /*
        // 对于不可发起类型的疾病，不支持输入治疗方案及对应花费；
        if (DiseaseRaiseTypeEnum.findByCode(raiseType) == DiseaseRaiseTypeEnum.CAN_NOT_RAISE
                && StringUtils.isNotBlank(treatMethodList)) {
            log.info("treatMethodList is not match raiseType:{}", raiseType);
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_TYPE_CAN_NOT_MATCH);
        }*/
        if (StringUtils.isBlank(treatMethodList)){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<RiskDiseaseTreatmentProjectVO> treatmentProjects =
                JSONObject.parseArray(treatMethodList, RiskDiseaseTreatmentProjectVO.class);
        if (DiseaseRaiseTypeEnum.findByCode(raiseType) == DiseaseRaiseTypeEnum.CAN_NOT_RAISE
                && CollectionUtils.isEmpty(treatmentProjects)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        if (CollectionUtils.isEmpty(treatmentProjects)) {
            log.info("treatMethodList is empty");
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        //同一个疾病，在选择治疗方案时，不允许重复
        if (treatmentProjects.stream().map(RiskDiseaseTreatmentProjectVO::getProjectName).distinct().count()
                < treatmentProjects.size()) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_TREATMENT_NAME_REPEAT);
        }
        // 疾病类型选择”特殊可发起筹款“，治疗方案数量需要≥2个，且可发起的治疗方案数量≥1个，且不可发起的治疗方案数量≥1个
        if(DiseaseRaiseTypeEnum.findByCode(raiseType) == DiseaseRaiseTypeEnum.SPECIAL_RAISE){
            // 可发起
            List<RiskDiseaseTreatmentProjectVO> canRaise = treatmentProjects.stream().filter(v -> v.getRaiseType() == 1).collect(Collectors.toList());
            // 不可发起
            List<RiskDiseaseTreatmentProjectVO> canNotRaise = treatmentProjects.stream().filter(v -> v.getRaiseType() == 0).collect(Collectors.toList());
            if(canRaise.size() == 0){
                return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_TYPE_CAN_MATCH);
            }
            if(canNotRaise.size() == 0){
                return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_TYPE_CAN_NOT_RAISE);
            }
        }
        //验证治疗费用
        for (RiskDiseaseTreatmentProjectVO treatmentProject : treatmentProjects){
            if (StringUtils.isBlank(treatmentProject.getProjectName())
                    || treatmentProject.getMinTreatmentFee() > treatmentProject.getMaxTreatmentFee()
                    || treatmentProject.getMaxTreatmentFee() > DiseaseConstant.TREATMENT_MAX_FEE
                    || treatmentProject.getMinTreatmentFee() < DiseaseConstant.TREATMENT_MIN_FEE
                    || treatmentProject.getCustomTreatment().length() > 100) {
                log.info("treatmentProject:{}", JSONObject.toJSONString(treatmentProject));
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }
            if (treatmentProject.getRaiseType() == DiseaseRaiseTypeEnum.CAN_RAISE.getCode()
                    && (treatmentProject.getMaxTreatmentFee() == 0d || treatmentProject.getMinTreatmentFee() == 0d)){
                log.info("treatmentProject canRaise illegal:{}", JSONObject.toJSONString(treatmentProject));
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }
        }
        return NewResponseUtil.makeSuccess(treatmentProjects);
    }

    private Response<RiskDiseaseInfoVO> buildCheck(String param) {
        RiskDiseaseInfoVO riskDiseaseInfoVO = JSONObject.parseObject(param, RiskDiseaseInfoVO.class);
        log.info("riskDiseaseInfoVO:{}", JSON.toJSONString(riskDiseaseInfoVO));
        if (riskDiseaseInfoVO == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        //check
        //诊断名称 归一规则 用户口语名称选填
        boolean checkParam = StringUtils.isBlank(riskDiseaseInfoVO.getDiseaseClassName())
                || DiseaseRaiseTypeEnum.findByCode(riskDiseaseInfoVO.getRaiseType()) == null;
        if (checkParam)  {
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_NOT_EXIST);
        }
        //check 疾病类别名称是否重复
        if (diseaseManagerBiz.checkDiseaseClassName(riskDiseaseInfoVO)){
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_CLASS_NAME_REPEAT);
        }
        //限制输入文本信息
        if (Optional.ofNullable(riskDiseaseInfoVO.getDiseaseClassName()).orElse("").length() > 100 ||
                Optional.ofNullable(riskDiseaseInfoVO.getMedicalName()).orElse("").length() > 200 ||
                Optional.ofNullable(riskDiseaseInfoVO.getDiseaseMergeRule()).orElse("").length() > 600 ||
                Optional.ofNullable(riskDiseaseInfoVO.getNormalName()).orElse("").length() > 200) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.DISEASE_INFO_OVER_LIMIT);
        }
        //诊断名称 归一规则 用户口语名称选填处理
        riskDiseaseInfoVO.setMedicalName(StringUtils.trimToEmpty(riskDiseaseInfoVO.getMedicalName()));
        riskDiseaseInfoVO.setNormalName(StringUtils.trimToEmpty(riskDiseaseInfoVO.getNormalName()));
        riskDiseaseInfoVO.setDiseaseMergeRule(StringUtils.trimToEmpty(riskDiseaseInfoVO.getDiseaseMergeRule()));
        return NewResponseUtil.makeSuccess(riskDiseaseInfoVO);
    }

    @NoLoginRequired
    @ApiOperation(value = "上传疾病知识excel")
    @PostMapping("/upload-disease-knowledge-list")
    public Response uploadDiseaseKnowledgeList(MultipartFile file){
        try {
            EasyExcel.read(file.getInputStream(), RiskDiseaseKnowledge.class, new DiseaseKnowledgeListener(diseaseKnowledgeBiz)).sheet().doRead();
            return NewResponseUtil.makeSuccess(null);
        } catch (IOException e) {
            log.error("", e);
        }
        return NewResponseUtil.makeFail("upload fail");
    }

    @ApiOperation("查找疾病列表")
    @PostMapping(path = "find-disease-norm-list")
    public Response<List<String>> findDiseaseNormList(@ApiParam("疾病类别名称") @RequestParam(value = "diseaseNorm") String diseaseNorm) {
        List<String> diseaseNormList = diseaseKnowledgeBiz.findDiseaseNormList(diseaseNorm);
        return NewResponseUtil.makeSuccess(diseaseNormList);
    }

}
