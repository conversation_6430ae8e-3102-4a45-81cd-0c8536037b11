package com.shuidihuzhu.cf.risk.admin.biz.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfLogVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15 16:33
 */
public interface QualitySpotLevelConfBiz {

    RiskQualitySpotLevelConfVo getByLevelId(Long id);

    List<RiskQualitySpotLevelConfVo> listWithAllLatestValidScene(long firstScene, long secondScene);

    List<RiskQualitySpotLevelConfVo> listWithAllCurrentValidScene(long secondScene);

    List<RiskQualitySpotLevelConfVo> listWithAllCurrentValidScene(Date now);

    List<RiskQualitySpotLevelConfLogVo> listByConfId(Integer confId);

}
