package com.shuidihuzhu.cf.risk.admin.controller.inner.risk;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.risk.admin.dao.GetInternalStaffAndEmergencyMobileDAO;
import com.shuidihuzhu.cf.risk.admin.model.GetInternalStaffAndEmergencyMobileDO;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.cf.risk.admin.service.hit.RiskHitService;
import com.shuidihuzhu.cf.risk.admin.util.EnvLoadUtil;
import com.shuidihuzhu.cf.risk.admin.util.SDEncryptUtils;
import com.shuidihuzhu.cf.risk.client.risk.RiskRaiserMobileIsInnerStaffFeignClient;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyRoleEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.IdentifySpotInitiatorHitEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.cf.risk.model.risk.RiskRaiserMobileCheckParam;
import com.shuidihuzhu.cf.risk.model.risk.RiskRaiserMobileCheckResult;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
public class RiskRaiserMobileIsInnerStaffFeignController implements RiskRaiserMobileIsInnerStaffFeignClient {

    @Resource
    private GetInternalStaffAndEmergencyMobileDAO getInternalStaffAndEmergencyMobileDAO;

    @Resource
    private WonRecordClient wonRecordClient;

    @Resource
    private OrgApiClient orgApiClient;

    @Resource
    private RiskHitService riskHitService;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private EnvLoadUtil envLoadUtil;

    private static final String EMPOLYEE = "正式员工";

    private Set<String> testWhiteListMobile;

    private String KEY_TEST_WHITELIST_MOBILE = "apollo.cf-risk-admin.raiser-mobile-whitelist";

    @PostConstruct
    public void init(){
        loadWhiteUsers();
        ConfigService.getAppConfig()
                .addChangeListener(v -> loadWhiteUsers(), Sets.newHashSet(KEY_TEST_WHITELIST_MOBILE));

    }

    private void loadWhiteUsers(){
        String property = ConfigService.getAppConfig().getProperty(KEY_TEST_WHITELIST_MOBILE, "");
        testWhiteListMobile = Arrays.stream(StringUtils.split(property, ",")).collect(Collectors.toSet());
    }

    @Override
    public Response<RiskRaiserMobileCheckResult> check(RiskRaiserMobileCheckParam param) {

        if(!envLoadUtil.isOnlineOrCanary()) {
            if (checkInWhiteList(param)) {
                RiskRaiserMobileCheckResult riskRaiserMobileCheckResult = new RiskRaiserMobileCheckResult();
                riskRaiserMobileCheckResult.setHit(false);
                return NewResponseUtil.makeSuccess(riskRaiserMobileCheckResult);
            }
        }
        return checkHit(param);
    }

    private boolean checkInWhiteList(RiskRaiserMobileCheckParam param){
        String mobile = SDEncryptUtils.decrypt(param.getEncryptRaiserMobile());
        return testWhiteListMobile.contains(mobile);
    }

    @Nullable
    private Response<RiskRaiserMobileCheckResult> checkHit(RiskRaiserMobileCheckParam param) {
        log.info("检查内部手机号风险 param {}", param);
        RiskRaiserMobileCheckResult result = new RiskRaiserMobileCheckResult();
        result.setHit(false);
        String encryptRaiserMobile = param.getEncryptRaiserMobile();
        String raiserName = param.getRaiserName();
        String hitType = "";
        boolean shouldCheckName = !StringUtils.equals(param.getChannel(), RiskRaiserMobileCheckParam.Channel.SUBMIT_INITIAL);

        // 正式员工ssc校验
        String mobile = SDEncryptUtils.decrypt(encryptRaiserMobile);
        String hitName = "";
        boolean hit = checkIsInnerUser(mobile, raiserName, shouldCheckName);
        if (hit) {
            hitType = EMPOLYEE;
            result.setHit(true);
            log.info("SSC命中");
        }

        // excel维护数据校验
        GetInternalStaffAndEmergencyMobileDO d = checkIsInnerUserManual(encryptRaiserMobile, raiserName, shouldCheckName);
        if (d != null) {
            hitType = d.getType();
            hitName = d.getSelfName();
            result.setHit(true);
            log.info("excel 员工命中");
        }

        GetInternalStaffAndEmergencyMobileDO emergencyMobileDO = checkIsInnerEmergencyUserManual(encryptRaiserMobile, raiserName, shouldCheckName);
        if (emergencyMobileDO != null) {
            hitType = emergencyMobileDO.getType() + "紧急联系人";
            hitName = emergencyMobileDO.getEmergencyName();
            result.setHit(true);
            log.info("excel 紧急联系人命中");
        }

        // 存储命中记录
        if (result.getHit()) {
            HitRecord hitRecord = HitRecord.builder()
                    .caseId(param.getCaseId())
                    .clewId(param.getClewId())
                    .channel(param.getChannel())
                    .hitType(hitType)
                    .requestParam(param)
                    .build();
            wonRecordClient.create()
                    .buildBasic(param.getClewId(), 109)
                    .buildCaseId(param.getCaseId())
                    .buildData(hitRecord)
                    .save();
        }

        // 存储风控记录
        if (result.getHit() && StringUtils.equals(param.getChannel(), RiskRaiserMobileCheckParam.Channel.SUBMIT_INITIAL)) {

            String info = "发起人手机号系统中对应姓名:" + hitName;

            List<BlacklistVerifyDto> list = Lists.newArrayList();

            BlacklistVerifyDto black0 = new BlacklistVerifyDto(BlacklistVerifyRoleEnum.CASE_RAISE_CHANNEL.getCode(), BlacklistVerifyTypeEnum.MOBILE.getCode(), String.valueOf(param.getCaseId()));
            black0.setHit(true);
            black0.setLimitActionIds(Lists.newArrayList());
            black0.setTypeIds(Lists.newArrayList());
            list.add(black0);

            BlacklistVerifyDto black1 = new BlacklistVerifyDto(BlacklistVerifyRoleEnum.INITIATOR.getCode(), BlacklistVerifyTypeEnum.MOBILE.getCode(), encryptRaiserMobile);
            black1.setHit(true);
            black1.setLimitActionIds(Lists.newArrayList());
            black1.setTypeIds(Lists.newArrayList());
            list.add(black1);

            BlacklistVerifyDto black = new BlacklistVerifyDto(BlacklistVerifyRoleEnum.DEFAULT.getCode(), BlacklistVerifyTypeEnum.MOBILE.getCode(), info);
            black.setHit(true);
            black.setLimitActionIds(Lists.newArrayList());
            black.setTypeIds(Lists.newArrayList());
            list.add(black);

            RiskStrategyHitRecord riskStrategyHitRecord = new RiskStrategyHitRecord();
            riskStrategyHitRecord.setHitPhase(BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL.getCode());
            riskStrategyHitRecord.setRiskStrategy(RiskStrategyEnum.ABNORMAL_IDENTIFY_SPOT.getCode());
            riskStrategyHitRecord.setSecondStrategy(RiskStrategySecondEnum.INITIATOR_MOBILE.getCode());
            riskStrategyHitRecord.setStatus(RiskHandleStatusEnum.PENDING.getCode());
            riskStrategyHitRecord.setResult((byte) RiskHandleResultEnum.RISK_CONFIRMING.getCode());
            riskStrategyHitRecord.setCaseId(param.getCaseId());
            riskStrategyHitRecord.setLaunchTime(new Date());

            riskStrategyHitRecord.setRiskType(getRiskTypeByHitType(hitType));

            String hitInfo = JSON.toJSONString(list);
            riskStrategyHitRecord.setHitInfo(hitInfo);

            riskHitService.uniteSaveHitRecord(riskStrategyHitRecord);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    private static final Map<String, IdentifySpotInitiatorHitEnum> enumMap = Arrays.stream(IdentifySpotInitiatorHitEnum.values())
            .collect(Collectors.toMap(IdentifySpotInitiatorHitEnum::getDesc, Function.identity(), (a,b) -> b));

    private Integer getRiskTypeByHitType(String hitType) {
        String key = "发起人手机号为" + hitType;
        IdentifySpotInitiatorHitEnum e = enumMap.getOrDefault(key, IdentifySpotInitiatorHitEnum.DEFAULT);
        return e.getCode();
    }

    /**
     * 调用薪人薪事 查询是否内部手机号
     */
    private boolean checkIsInnerUser(String mobile, String name, boolean shouldCheckName) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        if (StringUtils.isBlank(name)) {
            // 若需要校验姓名 且姓名为空则不命中
            if (shouldCheckName) {
                return false;
            }
            name = "no-care";
        }
        UserMatchInfoResponse resp = orgApiClient.getUserMatchInfo(mobile, name, "20f3GUB+lz8eUsE0rVm4lXqFJD8=");
        log.info("调用薪人薪事接口 mobile {}, name {}, resp {}", mobile, name, resp);
        if (resp == null || resp.getCode() != 0) {
            log.warn("调用SSC接口失败 resp {}", resp);
            return false;
        }
        UserMatchInfo userMatchInfo = resp.getUserMatchInfo();
        if (userMatchInfo == null) {
            log.warn("调用SSC接口 返回格式异常 resp {}", resp);
            return false;
        }
        boolean a = check(userMatchInfo.getIsInnerPhone(), true);
        if (shouldCheckName) {
            boolean b = check(userMatchInfo.getIsMatch(), false);
            return a && b;
        }
        return a;
    }

    private boolean check(MatchStatus isMatch, boolean except) {
        if (isMatch == null) {
            return !except;
        }
        return isMatch == (except ? MatchStatus.YES : MatchStatus.NO);
    }

    /**
     * 调用薪人薪事 查询是否内部紧急联系人手机号
     */
    private Boolean checkIsInnerEmergency(String mobile) {
        return true;
    }

    /**
     * 查询excel维护数据
     */
    private GetInternalStaffAndEmergencyMobileDO checkIsInnerUserManual(String encryptMobile, String raiserName, boolean shouldCheckName) {
        GetInternalStaffAndEmergencyMobileDO d = getInternalStaffAndEmergencyMobileDAO.getByStaffMobile(encryptMobile);
        if (d == null) {
            return null;
        }
        if (!shouldCheckName) {
            return d;
        }
        if (StringUtils.isBlank(d.getSelfName()) || StringUtils.isBlank(raiserName)) {
            return null;
        }
        // 姓名相同不命中
        if (StringUtils.equals(d.getSelfName(), raiserName)) {
            return null;
        }
        return d;
    }

    private GetInternalStaffAndEmergencyMobileDO checkIsInnerEmergencyUserManual(String encryptRaiserMobile, String raiserName, boolean shouldCheckName) {
        GetInternalStaffAndEmergencyMobileDO d = getInternalStaffAndEmergencyMobileDAO.getByEmergencyMobile(encryptRaiserMobile);
        if (d == null) {
            return null;
        }
        if (!shouldCheckName) {
            return d;
        }
        if (StringUtils.isBlank(d.getEmergencyName()) || StringUtils.isBlank(raiserName)) {
            return null;
        }
        // 姓名相同不命中
        if (StringUtils.equals(d.getEmergencyName(), raiserName)) {
            return null;
        }
        return d;
    }

    /**
     * <AUTHOR>
     */
    @Data
    public static class HitContext {

        @ApiModelProperty("是否命中")
        private Boolean hit;

        @ApiModelProperty("命中库类型")
        private String hitType;

    }

    @Builder
    @Data
    public static class HitRecord {

        @ApiModelProperty("调用入参")
        private RiskRaiserMobileCheckParam requestParam;

        @ApiModelProperty("待录入ID")
        private String clewId;

        @ApiModelProperty("案例ID")
        private int caseId;

        @ApiModelProperty("调用时机")
        private String channel;

        @ApiModelProperty("命中库类型")
        private String hitType;

    }

    /**
     * 获取手机号掩码 eg:18201534934 => 182****4934
     *
     * @param phoneNumber
     * @return
     */
    public static String getTelephoneMask(String phoneNumber) {
        return org.apache.commons.lang3.StringUtils.isNotBlank(phoneNumber) ?
                phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") :
                phoneNumber;
    }

}
