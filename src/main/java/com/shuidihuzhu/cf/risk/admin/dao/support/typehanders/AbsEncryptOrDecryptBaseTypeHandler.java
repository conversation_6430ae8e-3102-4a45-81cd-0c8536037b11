package com.shuidihuzhu.cf.risk.admin.dao.support.typehanders;

import com.shuidihuzhu.cf.risk.admin.util.SpringUtil;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.ibatis.type.BaseTypeHandler;

/**
 * @author: wanghui
 * @create: 2022/3/1 下午5:10
 */
public abstract class AbsEncryptOrDecryptBaseTypeHandler<T> extends BaseTypeHandler<T> {
    /**
     * 获取ShuidiCipher bean，用于解密
     * @return
     */
    protected ShuidiCipher getShuidiCipherBean(){
        return SpringUtil.getBean(ShuidiCipher.class);
    }

    /**
     * 获取OldShuidiCipher bean，用于加密
     * @return
     */
    protected OldShuidiCipher getOldShuidiCipherBean(){
        return SpringUtil.getBean(OldShuidiCipher.class);
    }
}
