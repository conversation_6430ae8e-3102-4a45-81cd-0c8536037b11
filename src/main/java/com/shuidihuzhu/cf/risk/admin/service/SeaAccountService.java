package com.shuidihuzhu.cf.risk.admin.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.model.vo.common.SimpleOrganizationVo;
import com.shuidihuzhu.client.auth.saas.feign.GroupFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.GroupTreeFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.dto.GroupMembersResultDto;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleGroupVo;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SeaAccountService {

    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private GroupTreeFeignClient groupTreeFeignClient;

    @Autowired
    private UserGroupFeignClient userGroupFeignClient;
    @Autowired
    private GroupFeignClient groupFeignClient;
    @Autowired
    private SimpleOrganizationVo simpleOrganizationVo;


    public String getName(long userId) {
        if (userId <= 0) {
            return "";
        }
        Response<AuthUserDto> accountResponse = userFeignClient.getValidAuthUserById(userId);
        log.info("call user account client resp:{}", accountResponse);
        String name = "";
        if (accountResponse.ok() && accountResponse.getData() != null) {
            name = accountResponse.getData().getUserName();
        } else {
            log.error("query sea account userId {}, filed {}", userId, accountResponse);
        }

        return name;
    }

    public Map<Long, AuthUserDto> getAuthUserDtoList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Maps.newHashMap();
        }
        Response<List<AuthUserDto>> response = userFeignClient.getAuthUserByIds(userIdList);
        List<AuthUserDto> authUserDtoList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(authUserDtoList)) {
            return Maps.newHashMap();
        }

        return authUserDtoList.stream()
                .collect(Collectors.toMap(AuthUserDto::getUserId, Function.identity(), (k1, k2) -> k2));
    }

    public String getOrganization(long userId) {
        Response<String> rpcResponse = userGroupFeignClient.getGroupNameByUserId(userId);
        log.info("call user org client resp:{}", rpcResponse);
        String organization = "";
        if (rpcResponse.ok()) {
            organization = rpcResponse.getData();
        } else {
            log.error("query sea org filed:{}", rpcResponse);
        }
        return StringUtils.isBlank(organization) ? organization : organization + "-";
    }

    public List<SimpleOrganizationVo> getOrgInfo(Integer parentOrgId, Boolean innerOrg) {
        // orgId 根节点默认是-1
        if (parentOrgId == null || parentOrgId <= -1 || innerOrg == null) {
            return getRootOrg();
        }
        Response<AuthGroupDto> dtoResponse = groupFeignClient.selectByGroupBizId(Long.valueOf(String.valueOf(parentOrgId)));
        if (dtoResponse.notOk() || dtoResponse.getData() == null) {
            return Lists.newArrayList();
        }

        long groupId = dtoResponse.getData().getId();
        Response<GroupMembersResultDto> orgMebmbersWithDb = userGroupFeignClient.getGroupMebmbers(groupId);
        if (!orgMebmbersWithDb.ok()) {
            return Lists.newArrayList();
        }

        List<SimpleGroupVo> subGroups = orgMebmbersWithDb.getData().getSubGroups();
        if (CollectionUtils.isEmpty(subGroups)) {
            return Lists.newArrayList();
        }
        List<SimpleGroupVo> groupVos = Lists.newArrayList();
        // 区分外部还是内部
        int isInner = innerOrg == true ? 1 : 2;
        for (SimpleGroupVo subGroup : subGroups) {
            int inner = getIsInner(subGroup.getGroupId());
            if (inner == isInner) {
                groupVos.add(subGroup);
            }
        }
        List<SimpleOrganizationVo> simpleOrganizationVoList = buildVoListByOrgVo(subGroups);
        simpleOrganizationVoList.forEach(v -> v.setInnerOrg(innerOrg));
        return simpleOrganizationVoList;
    }

    private int getIsInner(long groupId) {
        Response<AuthGroupDto> response = groupFeignClient.selectById(groupId);
        if (response.notOk() || response.getData() == null) {
            return 0;
        }
        return response.getData().getIsInner();
    }

    private List<SimpleOrganizationVo> getRootOrg() {
        List<SimpleOrganizationVo> simpleOrganizationVos = Lists.newArrayList();
        List<SimpleOrganizationVo> innerOrg = getInnerOrg(-1);
        simpleOrganizationVos.addAll(innerOrg);
        List<SimpleOrganizationVo> voList = getWithoutOrg(-1);
        simpleOrganizationVos.addAll(voList);
        return simpleOrganizationVos;
    }

    private List<SimpleOrganizationVo> buildVoListByOrgVo(List<SimpleGroupVo> simpleGroupVos) {
        if (CollectionUtils.isEmpty(simpleGroupVos)) {
            return Lists.newArrayList();
        }
        ArrayList<SimpleOrganizationVo> organizationVos = Lists.newArrayList();
        for (SimpleGroupVo simpleGroupVo : simpleGroupVos) {
            organizationVos.add(simpleOrganizationVo.buildVo(simpleGroupVo));
        }
        return organizationVos;
    }


    public AdminUserNameWithOrg getCurrAdminUserNameWithOrg(long adminUserId) {

        if (adminUserId > 0) {
            String userName = getName(adminUserId);
            String organization = getOrganization(adminUserId);
            return new AdminUserNameWithOrg(Math.toIntExact(adminUserId), userName, StringUtils.isBlank(organization)
                    ? organization
                    : organization.substring(0, organization.length() - 1));
        }

        return new AdminUserNameWithOrg(0, "", "");
    }

    public List<SimpleOrganizationVo> getInnerOrg(Integer orgId) {
        //获取内部的架构信息
        Response<List<AuthGroupDto>> adminRpcResponse = groupTreeFeignClient.getGroupListByParentId(Long.valueOf(String.valueOf(orgId)));
        if (adminRpcResponse.notOk() || CollectionUtils.isEmpty(adminRpcResponse.getData())) {
            return Lists.newArrayList();
        }
        ArrayList<AuthGroupDto> innerGroup = Lists.newArrayList();
        for (AuthGroupDto authGroupDto : adminRpcResponse.getData()) {
            if (authGroupDto.getIsInner().equals(1)) {
                innerGroup.add(authGroupDto);
            }
        }
        List<SimpleOrganizationVo> adminVos = buildVoListAdmin(innerGroup);
        if (CollectionUtils.isNotEmpty(adminVos)) {
            adminVos.forEach(v -> v.setInnerOrg(true));
        }
        return adminVos;
    }


    public List<SimpleOrganizationVo> getWithoutOrg(Integer orgId) {
        //获取外部的架构信息
        Response<List<AuthGroupDto>> adminRpcResponse = groupTreeFeignClient.getGroupListByParentId(Long.valueOf(String.valueOf(orgId)));
        if (adminRpcResponse.notOk() || CollectionUtils.isEmpty(adminRpcResponse.getData())) {
            return Lists.newArrayList();
        }
        ArrayList<AuthGroupDto> outGroup = Lists.newArrayList();
        for (AuthGroupDto authGroupDto : adminRpcResponse.getData()) {
            if (authGroupDto.getIsInner().equals(2)) {
                outGroup.add(authGroupDto);
            }
        }
        List<SimpleOrganizationVo> adminVos = buildVoListAdmin(outGroup);
        if (CollectionUtils.isNotEmpty(adminVos)) {
            adminVos.forEach(v -> v.setInnerOrg(false));
        }
        return adminVos;

    }


    public List<SimpleOrganizationVo> getInnerAllOrg() {
        //获取内部的架构信息
        Response<List<AuthGroupDto>> adminRpcResponse = groupFeignClient.getRootOrgGroupList();
        if (adminRpcResponse.ok()) {
            ArrayList<AuthGroupDto> innerGroup = Lists.newArrayList();
            for (AuthGroupDto authGroupDto : adminRpcResponse.getData()) {
                if (authGroupDto.getIsInner().equals(1)) {
                    innerGroup.add(authGroupDto);
                }
            }
            List<SimpleOrganizationVo> adminVos = buildVoListAdmin(innerGroup);
            if (CollectionUtils.isNotEmpty(adminVos)) {
                adminVos.forEach(v -> v.setInnerOrg(true));
            }
            return adminVos;
        }
        return Lists.newArrayList();
    }

    public List<SimpleOrganizationVo> getWithoutAllOrg() {
        //获取外部的架构信息
        Response<List<AuthGroupDto>> adminRpcResponse = groupFeignClient.getRootOrgGroupList();
        if (adminRpcResponse.ok()) {
            ArrayList<AuthGroupDto> outGroup = Lists.newArrayList();
            for (AuthGroupDto authGroupDto : adminRpcResponse.getData()) {
                if (authGroupDto.getIsInner().equals(2)) {
                    outGroup.add(authGroupDto);
                }
            }
            List<SimpleOrganizationVo> adminVos = buildVoListAdmin(outGroup);
            if (CollectionUtils.isNotEmpty(adminVos)) {
                adminVos.forEach(v -> v.setInnerOrg(false));
            }
            return adminVos;
        }
        return Lists.newArrayList();
    }


    private List<SimpleOrganizationVo> buildVoListAdmin(List<AuthGroupDto> authGroupDtos) {
        if (CollectionUtils.isEmpty(authGroupDtos)) {
            return Lists.newArrayList();
        }
        ArrayList<SimpleOrganizationVo> organizationVos = Lists.newArrayList();
        for (AuthGroupDto authGroupDto : authGroupDtos) {
            organizationVos.add(simpleOrganizationVo.buildVo(authGroupDto));

        }
        return organizationVos;
    }

    public String getInfoByMis(String mis) {
        if (StringUtils.isBlank(mis)) {
            return null;
        }
        Response<AuthUserDto> rpcResponse = userFeignClient.getByLoginName(mis);
        if (rpcResponse != null && rpcResponse.ok() && rpcResponse.getData() != null) {
            AuthUserDto authUserDto = rpcResponse.getData();
            String name = StringUtils.trimToEmpty(authUserDto.getUserName());
            String organization = getOrganization(authUserDto.getUserId());
            return organization + name;
        }
        return null;
    }

    public AdminUserNameWithOrg getInfoModelByMis(String mis) {
        if (StringUtils.isBlank(mis)) {
            return new AdminUserNameWithOrg();
        }
        AdminUserNameWithOrg adminUserNameWithOrg = new AdminUserNameWithOrg();
        Response<AuthUserDto> rpcResponse = userFeignClient.getByLoginName(mis);
        if (rpcResponse != null && rpcResponse.ok() && rpcResponse.getData() != null) {
            AuthUserDto authUserDto = rpcResponse.getData();
            String name = StringUtils.trimToEmpty(authUserDto.getUserName());
            adminUserNameWithOrg.setAdminUserId(Math.toIntExact(authUserDto.getUserId()));
            adminUserNameWithOrg.setAdminUserName(name);
            adminUserNameWithOrg.setOrg(getOrganization(authUserDto.getUserId()));
            return adminUserNameWithOrg;
        }
        return adminUserNameWithOrg;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @NotNull
    public static final class AdminUserNameWithOrg {
        private int adminUserId;
        private String adminUserName;
        private String org;

        public String getUserNameWithOrg() {
            if (StringUtils.isAnyBlank(org, adminUserName)) {
                return org + adminUserName;
            }
            return org + "-" + adminUserName;
        }
    }

}
