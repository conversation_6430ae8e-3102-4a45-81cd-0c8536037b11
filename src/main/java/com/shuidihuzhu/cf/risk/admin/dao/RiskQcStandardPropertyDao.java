package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcStandardPropertyDao {

    List<RiskQcStandardProperty> getAll();


    RiskQcStandardProperty getById(@Param("id") long id);


    List<RiskQcStandardProperty> getByIds(@Param("ids") List<Long> ids);


    List<RiskQcStandardProperty> findByNameAndLevel(@Param("firstPropertyList") List<String> firstPropertyList,
                                                    @Param("level") int level);
}
