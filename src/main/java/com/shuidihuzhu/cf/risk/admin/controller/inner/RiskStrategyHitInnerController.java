package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyCallResult;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListDataService;
import com.shuidihuzhu.cf.risk.admin.service.hit.RiskHitService;
import com.shuidihuzhu.cf.risk.client.admin.hit.RiskStrategyHitClient;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyCallResultDto;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyHitDto;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/21 14:28
 */
@RestController
@Slf4j
public class RiskStrategyHitInnerController implements RiskStrategyHitClient {

    @Resource
    private RiskHitService riskHitService;

    @Resource
    private BlackListDataService blackListDataService;

    @Override
    public Response<Void> saveHitRecord(StrategyHitDto strategyHitDto) {
        log.info("收到风控策略命中请求:{}", strategyHitDto);
        RiskStrategyHitRecord strategyHitRecord = new RiskStrategyHitRecord();
        BeanUtils.copyProperties(strategyHitDto, strategyHitRecord);
        riskHitService.uniteSaveHitRecord(strategyHitRecord);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Boolean> hadRiskHitPayee(Integer caseId) {
        log.info("提交收款人信息命中结果查询:{}", caseId);
        if (caseId == null || caseId == 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        RiskStrategyHitRecord hitRecord = riskHitService.queryOneByCaseStrategy(caseId,
                List.of(BlacklistCallPhaseEnum.SUBMIT_MATERIAL_REVIEW.getCode(), BlacklistCallPhaseEnum.SUBMIT_REVISE_PAYEE.getCode(),
                        BlacklistCallPhaseEnum.SUBMIT_ACTIVE_ACCOUNT.getCode()));
        log.info("提交收款人信息命中结果查询, resp:{}", hitRecord);
        return NewResponseUtil.makeSuccess(hitRecord != null);
    }

    @Override
    public Response<Integer> saveCallResult(StrategyCallResultDto strategyCallResultDto) {
        log.info("策略调用结果开始插入:{}", strategyCallResultDto);
        RiskStrategyCallResult riskStrategyCallResult = new RiskStrategyCallResult();
        BeanUtils.copyProperties(strategyCallResultDto, riskStrategyCallResult);
        int result = riskHitService.saveCallResult(riskStrategyCallResult);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Void> autoDelLimitTimeBlacklist() {
        long adminUserId = ContextUtil.getAdminLongUserId();
        blackListDataService.autoDeleteBlackList(adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<List<StrategyHitDto>> listRepeatPayOrderCaseByCaseId(int caseId) {
        return NewResponseUtil.makeSuccess(riskHitService.listRepeatPayOrderCaseByCaseId(caseId));
    }

}
