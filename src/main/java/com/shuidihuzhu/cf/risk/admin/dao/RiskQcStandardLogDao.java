package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcStandardLogDao {
    int addInfo(@Param("operationId")long operationId, @Param("operationName")String operationName,
                @Param("operationLog")String operationLog, @Param("qcStandardId")long qcStandardId,
                @Param("operationType")int operationType);


    List<RiskQcStandardLog> getLogByStandardId(@Param("standardId")long standardId);
}
