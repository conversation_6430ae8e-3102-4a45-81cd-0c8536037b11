package com.shuidihuzhu.cf.risk.admin.controller.risk;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.dao.GetInternalStaffAndEmergencyMobileDAO;
import com.shuidihuzhu.cf.risk.admin.model.GetInternalStaffAndEmergencyMobileDO;
import com.shuidihuzhu.cf.risk.admin.service.listener.GetInternalStaffAndEmergencyMobileListener;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;


@Validated
@Slf4j
@RestController
@RequestMapping(path = "/api/cf-risk-admin/get-internal-staff-and-emergency-mobile")
public class GetInternalStaffAndEmergencyMobileController {

    @Autowired
    private GetInternalStaffAndEmergencyMobileDAO getInternalStaffAndEmergencyMobileDAO;

    /**
     * 文件上传
     * <p>
     * 1. 创建excel对应的实体对象 参照{@link GetInternalStaffAndEmergencyMobileDO}
     * <p>
     * 2. 由于默认一行行的读取excel，所以需要创建excel一行一行的回调监听器，参照{@link }
     * <p>
     * 3. 直接读即可
     */
    @PostMapping("/upload-excel")
    @ResponseBody
    public Response<String> upload(MultipartFile file) throws IOException {
        try {
            EasyExcel.read(file.getInputStream(), GetInternalStaffAndEmergencyMobileDO.class,
                    new GetInternalStaffAndEmergencyMobileListener(getInternalStaffAndEmergencyMobileDAO)).sheet().doRead();
            return NewResponseUtil.makeSuccess("success");
        }catch (IOException e) {
            return NewResponseUtil.makeSuccess("false");
        }
    }
}
