package com.shuidihuzhu.cf.risk.admin.controller.common;


import com.shuidihuzhu.cf.risk.admin.model.vo.common.SimpleOrganizationVo;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/14
 */
@Validated
@Slf4j
@RestController
@RequestMapping(path = "/api/cf-risk-admin/common/organzation")
public class OrganizationCommonController {


    @Autowired
    private SeaAccountService seaAccountService;

    @ApiOperation(value = "获取组织架构的子信息", notes = "权限：organzation:get")
    @RequiresPermission("organzation:get")
    @PostMapping(path = "/get")
    public Response<List<SimpleOrganizationVo>> getOrg(@ApiParam("组织架构父类id")
                                                       @RequestParam(value = "orgId", defaultValue = "-1", required = false) Integer orgId,
                                                       @ApiParam("内部还是外包架构 true:内部架构 false:外包架构")
                                                       @RequestParam(value = "innerOrg", required = false) Boolean innerOrg) {
        log.info("获取组织架构的子信息 orgId：{} innerOrg:{}", orgId, innerOrg);
        return NewResponseUtil.makeSuccess(seaAccountService.getOrgInfo(orgId, innerOrg));
    }

    @ApiOperation(value = "获取内部组织架构", notes = "权限：organzation:get")
    @RequiresPermission("organzation:getInner")
    @PostMapping(path = "/inner/get")
    public Response<List<SimpleOrganizationVo>> getInnerOrg(@ApiParam("组织架构父类id")
                                                                @RequestParam(value = "orgId", defaultValue = "0", required = false) Integer orgId) {
        log.info("获取组织架构的子信息 orgId：{}", orgId);
        return NewResponseUtil.makeSuccess(seaAccountService.getInnerOrg(orgId));
    }


    @ApiOperation(value = "获取外包组织架构", notes = "权限：organzation:get")
    @RequiresPermission("organzation:getWithout")
    @PostMapping(path = "/without/get")
    public Response<List<SimpleOrganizationVo>> getWithoutOrg(@ApiParam("组织架构父类id")
                                                       @RequestParam(value = "orgId", defaultValue = "0", required = false) Integer orgId) {
        log.info("获取组织架构的子信息 orgId：{}", orgId);
        return NewResponseUtil.makeSuccess(seaAccountService.getWithoutOrg(orgId));
    }

    @ApiOperation(value = "获取内部组织架构", notes = "权限：organzation:get")
    @RequiresPermission("organzation:getInnerAll")
    @PostMapping(path = "/inner/get-all")
    public Response<List<SimpleOrganizationVo>> getInnerAllOrg() {
        return NewResponseUtil.makeSuccess(seaAccountService.getInnerAllOrg());
    }


    @ApiOperation(value = "获取外包组织架构", notes = "权限：organzation:get")
    @RequiresPermission("organzation:getWithoutAll")
    @PostMapping(path = "/without/get-all")
    public Response<List<SimpleOrganizationVo>> getWithoutAllOrg() {
        return NewResponseUtil.makeSuccess(seaAccountService.getWithoutAllOrg());
    }

}
