package com.shuidihuzhu.cf.risk.admin.dao.list;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartmentLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskListDepartmentLogDao {
    int insertSelective(RiskListDepartmentLog record);

    RiskListDepartmentLog selectByPrimaryKey(Long id);

    List<RiskListDepartmentLog> listByListDepartmentId(Long listDepartmentId);
}