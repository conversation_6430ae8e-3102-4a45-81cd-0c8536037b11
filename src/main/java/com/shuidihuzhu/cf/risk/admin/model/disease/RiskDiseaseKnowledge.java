package com.shuidihuzhu.cf.risk.admin.model.disease;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Optional;

@Data
@ApiModel("疾病知识")
public class RiskDiseaseKnowledge {

    @ExcelIgnore
    @ApiModelProperty("主键")
    private long id;

    @ExcelProperty(index = 0)
    @ApiModelProperty("序号")
    private String number;

    @ExcelProperty(index = 1)
    @ApiModelProperty("检索名称")
    private String searchName;

    @ExcelProperty(index = 2)
    @ApiModelProperty("疾病名称")
    private String diseaseName;

    @ExcelProperty(index = 3)
    @ApiModelProperty("就诊科室")
    private String cureOffice;

    @ExcelProperty(index = 4)
    @ApiModelProperty("疾病别名")
    private String diseaseAlias;

    @ExcelProperty(index = 5)
    @ApiModelProperty("疾病简介")
    private String diseaseIntro;

    @ExcelProperty(index = 6)
    @ApiModelProperty("治疗方案")
    private String curePlan;

    @ExcelProperty(index = 7)
    @ApiModelProperty("简要治疗方案")
    private String simpleCurePlan;

    @ExcelProperty(index = 8)
    @ApiModelProperty("治疗花费")
    private String cureCost;

    @ExcelProperty(index = 9)
    @ApiModelProperty("简要治疗话费")
    private String simpleCureCost;

    @ExcelProperty(index = 10)
    @ApiModelProperty("预后")
    private String prognosis;

    @ExcelProperty(index = 11)
    @ApiModelProperty("诊断疾病的图片")
    private String diagnoseImage;

    @ExcelProperty(index = 12)
    @ApiModelProperty("诊断疾病的文案")
    private String diagnoseText;

    @ApiModelProperty("疾病知识文件地址-字符串")
    private String diseaseFileAddressStr;

    @ApiModelProperty("疾病知识版本,0旧版本,1新版本")
    private int knowledgeVersion;

    @ExcelIgnore
    private Timestamp createTime;
    @ExcelIgnore
    private Timestamp updateTime;
    @ExcelIgnore
    private int isDelete;


    public RiskDiseaseKnowledge() {
        this.cureOffice = "";
        this.diseaseIntro = "";
        this.prognosis = "";
        this.diagnoseImage = "";
        this.diagnoseText = "";
        this.diseaseAlias = "";
        this.diseaseFileAddressStr = "";
    }


    public void init() {
        this.cureOffice = Optional.of(this.cureOffice).orElse("");
        this.cureCost = Optional.ofNullable(this.cureCost).orElse("");
        this.curePlan = Optional.ofNullable(this.curePlan).orElse("");
        this.simpleCurePlan = Optional.ofNullable(this.simpleCurePlan).orElse("");
        this.simpleCureCost = Optional.ofNullable(this.simpleCureCost).orElse("");
        this.diseaseIntro = Optional.ofNullable(this.diseaseIntro).orElse("");
        this.prognosis = Optional.ofNullable(this.prognosis).orElse("");
        this.diagnoseImage = Optional.ofNullable(this.diagnoseImage).orElse("");
        this.diagnoseText = Optional.ofNullable(this.diagnoseText).orElse("");
        this.diseaseAlias = Optional.ofNullable(this.diseaseAlias).orElse("");
        this.diseaseFileAddressStr = Optional.ofNullable(this.diseaseFileAddressStr).orElse("");
    }
}
