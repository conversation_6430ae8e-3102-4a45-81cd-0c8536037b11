package com.shuidihuzhu.cf.risk.admin.model.enums;


/**
 * @Auther: subing
 * @Date: 2020/6/13
 */
public enum  RiskQcStandardOperateType {
    // 配置相关
    ENABLE(0, "启用"),
    DISABLE(1, "弃用"),
    DELETE(2, "删除"),
    UP(3, "上移"),
    DOWN(4, "下移"),
    ADD(5, "新增"),
    ;


    int code;
    String description;

    RiskQcStandardOperateType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (RiskQcStandardOperateType value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
