package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultConfigBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcResultConfigDao;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResultConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/16
 */
@Service
public class RiskQcResultConfigBizImpl implements RiskQcResultConfigBiz {
    @Autowired
    private RiskQcResultConfigDao riskQcResultConfigDao;

    @Override
    public List<RiskQcResultConfig> getAll() {
        return riskQcResultConfigDao.getAll();
    }
}
