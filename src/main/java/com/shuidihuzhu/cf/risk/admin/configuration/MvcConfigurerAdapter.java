package com.shuidihuzhu.cf.risk.admin.configuration;

import com.shuidihuzhu.client.auth.saas.interceptor.AuthSaasLoginInterceptor;
import com.shuidihuzhu.client.auth.saas.interceptor.AuthSaasPermissionInterceptor;
import com.shuidihuzhu.common.web.filter.LogRequestFilter;
import com.shuidihuzhu.common.web.interceptor.OptionalSessionKeyValidateInterceptor;
import com.shuidihuzhu.common.web.interceptor.SessionKeyValidateInterceptor;
import com.shuidihuzhu.common.web.interceptor.SigValidateInterceptor;
import com.shuidihuzhu.pf.common.v2.interceptor.PageInterceptor;
import com.thetransactioncompany.cors.CORSConfiguration;
import com.thetransactioncompany.cors.CORSConfigurationException;
import com.thetransactioncompany.cors.CORSFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.Collections;
import java.util.Properties;

/**
 * Created by sven on 2019/5/29.
 *
 * <AUTHOR>
 */
@Configuration
public class MvcConfigurerAdapter extends WebMvcConfigurerAdapter {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
    }

    @Bean
    public FilterRegistrationBean logRequestFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new LogRequestFilter());
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 2);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean corsFilter() throws CORSConfigurationException {
        Properties prop = new Properties();
        prop.setProperty("cors.maxAge", "3600");
        CORSConfiguration corsConfig = new CORSConfiguration(prop);
        CORSFilter filter = new CORSFilter(corsConfig);
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(filter);
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 3);
        return filterRegistrationBean;
    }

    @Bean
    public PageInterceptor pageInterceptor(){
        return new PageInterceptor();
    }

//    @Bean
//    public AuthSaasLoginInterceptor authSaasLoginInterceptor(){
//        return new AuthSaasLoginInterceptor();
//    }
//
//    @Bean
//    public AuthSaasPermissionInterceptor authSaasPermissionInterceptor(){
//        return new AuthSaasPermissionInterceptor();
//    }
}
