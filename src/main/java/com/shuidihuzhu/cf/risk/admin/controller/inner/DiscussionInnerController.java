package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.biz.DiscussionBiz;
import com.shuidihuzhu.cf.risk.client.admin.DiscussionAdminClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class DiscussionInnerController implements DiscussionAdminClient {
    @Autowired
    private DiscussionBiz discussionBiz;

    @Override
    public Response<Boolean> hasDiscussion(int caseId) {
        return NewResponseUtil.makeSuccess(discussionBiz.hasDiscussion(caseId));
    }
}
