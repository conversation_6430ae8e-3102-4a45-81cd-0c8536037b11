package com.shuidihuzhu.cf.risk.admin.model.qc1v1;

import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.common.web.util.MD5Util;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * @Auther: subing
 * @Date: 2020/8/11
 */
@Data
public class RiskQcCallRecords {
    /**
     * 手机号
     */
    private String mobile;
    private Date startTime;
    private Date endTime;

    @ApiModelProperty("客户接听时间")
    private Date answerTime;

    /**
     * 时长
     */
    private Integer duration;
    /**
     * 时长
     */
    private Integer totalDuration;
    private String callUrl;
    /**
     * 接通状态
     */
    private Integer phoneStatus;


    public static RiskQcCallRecords buildRecords(CfClewCallRecordsDO cfClewCallRecordsDO) {
        if (Objects.isNull(cfClewCallRecordsDO)){
            return null;
        }
        // 真正通话时长 = (坐席挂断时间 - 客户接听时间) // 备注 没有客户挂断时间所以只能粗算
        Integer phoneStatus = cfClewCallRecordsDO.getPhoneStatus();
        Date answerTime = cfClewCallRecordsDO.getAnswerTime();
        long answerTimestamp = answerTime.getTime();
        long cnoEndTimestamp = cfClewCallRecordsDO.getCnoEndTime().getTime();
        long realTotalDuration = 0;
        boolean callSuccess = phoneStatus == 200;
        if (callSuccess) {
            realTotalDuration = (cnoEndTimestamp - answerTimestamp) / 1000;
        }
        RiskQcCallRecords records = new RiskQcCallRecords();
        records.setAnswerTime(callSuccess ? answerTime : null);
        records.setMobile(cfClewCallRecordsDO.getEncryptCustomerNumber());
        records.setStartTime(cfClewCallRecordsDO.getCnoStartTime());
        records.setEndTime(cfClewCallRecordsDO.getCnoEndTime());
        records.setDuration(cfClewCallRecordsDO.getTotalDuration());
        records.setTotalDuration((int) realTotalDuration);
        records.setCallUrl(cfClewCallRecordsDO.getCosFile());
        records.setPhoneStatus(phoneStatus);
        return records;
    }
}
