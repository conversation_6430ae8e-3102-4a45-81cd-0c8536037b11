package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcLogBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcLogDao;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcLog;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/17
 */
@Service
public class RiskQcLogBizImpl implements RiskQcLogBiz {
    @Autowired
    private RiskQcLogDao riskQcLogDao;

    @Override
    public int addLog(RiskQcLog riskQcLog) {
        if (riskQcLog == null) {
            return 0;
        }
        riskQcLog.setOperationName(StringUtils.trimToEmpty(riskQcLog.getOperationName()));
        riskQcLog.setOperationLog(StringUtils.trimToEmpty(riskQcLog.getOperationLog()));
        return riskQcLogDao.addLog(riskQcLog);
    }

    @Override
    public List<RiskQcLog> getByWorkOrderId(long workOrderId, List<Integer> types) {
        if (workOrderId <= 0 || CollectionUtils.isEmpty(types)) {
            return Lists.newArrayList();
        }
        return riskQcLogDao.getByWorkOrderId(workOrderId, types);
    }
}
