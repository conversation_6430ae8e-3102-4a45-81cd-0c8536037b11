package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/16 15:17
 */
@Data
@ApiModel(description = "黑名单分类")
public class BlacklistClassifyVo {

    @ApiModelProperty(value = "分类id", required = true)
    private Long id;

    @ApiModelProperty(value = "分类名称", required = true)
    private String name;

    @ApiModelProperty("黑名单类型关联的限制动作，1，2级是没有值的。如果用户选择多个，前端需要自己去做去重处理")
    private List<BlacklistTypeActionRefDto> actions;

    @ApiModelProperty("黑名单类型id，与三级分类是一一对应的，也就是三级分类该值不为空")
    private Long typeId;

    @ApiModelProperty(value = "分类状态")
    private String status;

}
