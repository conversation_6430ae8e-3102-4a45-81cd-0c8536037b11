package com.shuidihuzhu.cf.risk.admin.controller.qc;

import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard;

import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardLog;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskMaterialQcStandardVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcFirstStandardVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcStandardVo;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcStandardService;
import com.shuidihuzhu.client.auth.saas.annotation.NoLoginRequired;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
@RestController
@RequestMapping("/api/cf-risk-admin/qc/standard")
public class RiskQcStandardController {
    @Autowired
    private RiskQcStandardService riskQcStandService;

    @NoLoginRequired
    @PostMapping(path = "get-all-standard")
    @ApiOperation("返回一级&&二级配置")
    public Response<List<RiskQcStandardVo>> getAllStandard(@RequestParam(value = "isUse", defaultValue = "1") int isUse,
                                                           @RequestParam(value = "standardType", defaultValue = "1")
                                                                   int standardType,
                                                           @RequestParam(value = "parentId", defaultValue = "-1") long parentId,
                                                           @RequestParam(defaultValue = "0")int useScene,
                                                           @RequestParam(defaultValue = "0") int secondStandardType) {
        List<RiskQcStandardVo> riskQcStandardVos =
                riskQcStandService.getAllStandard(isUse, standardType, parentId, useScene, secondStandardType == 0
                        ? null : List.of(secondStandardType));
        return NewResponseUtil.makeSuccess(riskQcStandardVos);
    }

    @PostMapping(path = "get-first-standard")
    @ApiOperation("返回一级配置")
    public Response<List<RiskQcFirstStandardVo>> getFirstStandard(@RequestParam(value = "isUse", defaultValue = "1") int isUse,
                                                                  @RequestParam(value = "standardType") int standardType,
                                                                  @RequestParam(defaultValue = "0") int secondStandardType) {
        return NewResponseUtil.makeSuccess(riskQcStandService.getFirstStandard(isUse, standardType, secondStandardType));
    }

    @PostMapping(path = "up-or-down")
    @ApiOperation("配置上移下移")
    public Response<Boolean> upOrDown(@RequestParam(value = "upId") long upId,
                                      @RequestParam(value = "downId") long downId,
                                      @RequestParam int type) {
        return NewResponseUtil.makeSuccess(riskQcStandService.upOrDown(upId, downId, type));
    }

    @PostMapping(path = "change-use-status")
    @ApiOperation("修改使用状态")
    public Response<Boolean> changeUseStatus(@RequestParam long id, @RequestParam int isUse) {
        return riskQcStandService.changeUseStatus(id, isUse);
    }

    @PostMapping(path = "add-first-standard")
    @ApiOperation("增加一级标准")
    public Response<Integer> addFirstStandard(@RequestParam String standardName,
                                              @RequestParam(defaultValue = "1") int standardType,
                                              @RequestParam(defaultValue = "0") int secondStandardType) {
        return riskQcStandService.addFirstStandard(standardName, standardType, secondStandardType);
    }

    @PostMapping(path = "add-standard")
    @ApiOperation("增加二级标准接口")
    public Response<Integer> addStandard(@RequestParam long parentId, @RequestParam String standardName,
                                         @RequestParam long firstProperty, @RequestParam long secondProperty,
                                         @RequestParam String useScene,
                                         @RequestParam(defaultValue = "1") int standardType,
                                         @RequestParam(defaultValue = "0")int secondStandardType) {
        if (StringUtils.isBlank(useScene)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return riskQcStandService.addStandard(parentId, standardName, firstProperty, secondProperty, useScene, standardType, secondStandardType);
    }

    @PostMapping(path = "property/get-all")
    @ApiOperation("返回所有属性")
    public Response<Map<String, List<RiskQcStandardProperty>>> getProperty(@RequestParam(defaultValue = "1") int useScene) {
        return NewResponseUtil.makeSuccess(riskQcStandService.getProperty(useScene));
    }

    @PostMapping(path = "delete-standard")
    @ApiOperation("删除配置")
    public Response<Integer> deleteStandard(@RequestParam long id) {
        return riskQcStandService.deleteStandard(id);
    }

    @PostMapping(path = "get-log")
    @ApiOperation("查看日志")
    public Response<List<RiskQcStandardLog>> getLog(@RequestParam  long id){
        return NewResponseUtil.makeSuccess(riskQcStandService.getLog(id));
    }

    @PostMapping(path = "material/get-all-standard")
    public Response<Map<String, RiskMaterialQcStandardVo>> getAllStandardByGroup(@RequestParam int useScene){
        return NewResponseUtil.makeSuccess(riskQcStandService.getAll(useScene));
    }

    @PostMapping(path = "fuzzy-query")
    public Response<List<RiskQcStandard>> fuzzyQuery(@RequestParam("secondQuestionDesc") String secondQuestionDesc) {
        if (StringUtils.isBlank(secondQuestionDesc)) {
            return null;
        }
        return NewResponseUtil.makeSuccess(riskQcStandService.fuzzyQuery(secondQuestionDesc));
    }


}
