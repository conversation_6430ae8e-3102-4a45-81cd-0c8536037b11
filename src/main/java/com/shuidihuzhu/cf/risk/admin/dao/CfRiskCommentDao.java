package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.CfRiskComment;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface CfRiskCommentDao {

    List<CfRiskComment> findByParam(@Param("param") Map<String, Object> param, PageRequest pageRequest);

    int updateDelById(long id);

    List<CfRiskComment> findByDiscussionId(@Param("discussionId")long discussionId);

}
