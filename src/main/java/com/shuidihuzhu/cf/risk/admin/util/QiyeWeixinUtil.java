package com.shuidihuzhu.cf.risk.admin.util;

import brave.Tracer;
import brave.propagation.TraceContext;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020-02-29
 **/
@Slf4j
@Component
public class QiyeWeixinUtil {

    /**
     * 风控测试报警
     */
    private static final String BUSINESS_TEST_GROUP_ID = "wx-alarm-prod-*************";
    /**
     * 风控线上报警
     */
    private static final String BUSINESS_ONLINE_GROUP_ID = "wx-alarm-prod-*************";
    /**
     * 黑名单报警
     */
    private static final String BLACKLIST_ONLINE_GROUP_ID = "wx-alarm-prod-*************";

    @Value("${spring.application.name:}")
    private String appName;
    @Autowired
    private AlarmClient alarmClient;
    @Autowired
    private EnvLoadUtil envLoadUtil;
    @Autowired
    private Tracer tracer;

    /**
     * 上报报警到企业微信-业务群
     * @param errorIntro 错误简述
     * @param errorDetail 错误详情
     */
    public void launchBizAlarm(String errorIntro, String errorDetail) {
        doAlarm(errorIntro, errorDetail, BUSINESS_ONLINE_GROUP_ID);
    }

    /**
     * 上报黑名单报警到业务群
     * @param errorIntro 错误简述
     * @param errorDetail 错误详情
     */
    public void sendBlacklistAlarm(String errorIntro, String errorDetail) {
        doAlarm(errorIntro, errorDetail, BLACKLIST_ONLINE_GROUP_ID);
    }

    private void doAlarm(String errorIntro, String errorDetail, String onlineGroupId){
        String traceId = "";
        try {
            TraceContext traceContext = tracer.currentSpan().context();
            traceId = traceContext.traceIdString() + "," + traceContext.spanIdString();
        } catch (Exception e) {
            log.error("", e);
        }
        String groupId = BUSINESS_TEST_GROUP_ID;
        if(envLoadUtil.isOnline()) {
            groupId = onlineGroupId;
        }
        StringBuilder content = new StringBuilder(errorIntro);
        content.append("\n环境：").append(envLoadUtil.getEnv());
        content.append("\n服务名称：").append(appName);
        content.append("\ntraceId：").append(traceId);
        content.append("\n错误详情：").append(errorDetail);
        content.append("\n请相关人员关注");

        try {
            alarmClient.sendByGroup(groupId, content.toString());
        } catch (Exception e) {
            log.error("", e);
        }
    }
}
