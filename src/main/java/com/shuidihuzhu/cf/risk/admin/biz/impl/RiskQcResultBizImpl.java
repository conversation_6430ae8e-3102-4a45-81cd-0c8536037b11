package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcResultDao;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult;
import com.google.common.collect.Lists;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/16
 */
@Service
public class RiskQcResultBizImpl implements RiskQcResultBiz {
    @Autowired
    private RiskQcResultDao riskQcResultDao;

    @Override
    public List<RiskQcResult> getQcResultByTime(String qcUniqueCode, long startTime, long endTime) {
        if (startTime < 0 || endTime < 0 || startTime > endTime){
            return Lists.newArrayList();
        }
        return riskQcResultDao.getQcResultByTime(qcUniqueCode, DateUtil.getYmdhmsFromTimestamp(startTime), DateUtil.getYmdhmsFromTimestamp(endTime));
    }

    @Override
    public int add(long firstQcResultId, long secondQcResultId,
                   String problemDescribe, long wordOrderId, String qcUniqueCode, int resultType, long qcId) {
        if (wordOrderId <=0){
            return 0;
        }
        return riskQcResultDao.add(firstQcResultId, secondQcResultId, problemDescribe, wordOrderId, qcUniqueCode, resultType, qcId);
    }

    @Override
    public RiskQcResult getByWorkOrderId(long wordOrderId) {
        if (wordOrderId < 0){
            return null;
        }
        return riskQcResultDao.getByWorkOrderId(wordOrderId);
    }

    @Override
    public int updateInfo(long firstQcResultId, long secondQcResultId, String problemDescribe, long wordOrderId) {
        if (wordOrderId <=0){
            return 0;
        }
        return riskQcResultDao.updateInfo(firstQcResultId, secondQcResultId, problemDescribe, wordOrderId);
    }

    @Override
    public List<RiskQcResult> findByWorkOrderIds(List<Long> wordOrderIds) {
        if (CollectionUtils.isEmpty(wordOrderIds)) {
            return Lists.newArrayList();
        }
        return riskQcResultDao.findByWorkOrderIds(wordOrderIds);

    }
}
