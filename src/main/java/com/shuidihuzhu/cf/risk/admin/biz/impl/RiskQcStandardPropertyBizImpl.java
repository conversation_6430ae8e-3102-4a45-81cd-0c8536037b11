package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandardPropertyBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcStandardPropertyDao;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/6/13
 */
@Service
public class RiskQcStandardPropertyBizImpl implements RiskQcStandardPropertyBiz {
    @Autowired
    private RiskQcStandardPropertyDao riskQcStandardPropertyDao;

    @Override
    public List<RiskQcStandardProperty> getAll() {
        return riskQcStandardPropertyDao.getAll();
    }

    @Override
    public RiskQcStandardProperty getById(long id) {
        if (id < 0){
            return null;
        }
        return riskQcStandardPropertyDao.getById(id);
    }

    @Override
    public List<RiskQcStandardProperty> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return riskQcStandardPropertyDao.getByIds(ids);
    }

    @Override
    public List<RiskQcStandardProperty> findByNameAndLevel(List<String> firstPropertyList, int level) {
        if (CollectionUtils.isEmpty(firstPropertyList) || level <= 0) {
            return Lists.newArrayList();
        }
        return riskQcStandardPropertyDao.findByNameAndLevel(firstPropertyList, level);
    }
}
