package com.shuidihuzhu.cf.risk.admin.model;

import lombok.Data;

/**
 * id	BIGINT	主键
 * case_id	bigint	案例id
 * approval_count int	同意人数
 *  discussion_id	BIGINT	决议id
 * oppose_count int	反对人数
 * comment_count	int	评论人数
 */
@Data
public class DiscussionStat {
    private long id;
    private int caseId;
    private long discussionId;
    private int approvalCount;
    private int opposeCount;
    private int commentCount;
}
