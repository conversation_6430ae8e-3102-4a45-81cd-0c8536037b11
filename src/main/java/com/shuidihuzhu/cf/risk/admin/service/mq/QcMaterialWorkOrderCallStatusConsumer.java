package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcSearchIndexDao;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.model.enums.MaterialWorkOrderCallStatusEnum;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-10-14 14:41
 **/
@Service
@RocketMQListener(id = RiskMQTagCons.QC_MATERIAL_WORK_ORDER_CALL_STATUS,
        tags = RiskMQTagCons.QC_MATERIAL_WORK_ORDER_CALL_STATUS,
        topic = MQTopicCons.CF)
@Slf4j
public class QcMaterialWorkOrderCallStatusConsumer implements MessageListener<Map<String, Object>> {

    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private RiskQcSearchIndexDao riskQcSearchIndexDao;
    @Autowired
    private UserFeignClient userFeignClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Map<String, Object>> mqMessage) {
        Map<String, Object> map = mqMessage.getPayload();
        log.info("QcMaterialWorkOrderCallStatusConsumer map:{}", JSON.toJSONString(map));

        WorkOrderVO workOrderVO = JSON.parseObject(map.get("workOrderVO").toString(), WorkOrderVO.class);
        long newWorkOrderId = Long.parseLong(map.get("newWorkOrderId").toString());

        if (Objects.isNull(workOrderVO)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        var operatorId = workOrderVO.getOperatorId();
        Response<AuthUserDto> authRpcResponse = userFeignClient.getAuthUserById(operatorId);
        if (!authRpcResponse.ok() || authRpcResponse.getData() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = crowdfundingFeignClient.getCaseInfoById(workOrderVO.getCaseId());
        if (crowdfundingInfoFeignResponse.notOk() || Objects.isNull(crowdfundingInfoFeignResponse.getData())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoFeignResponse.getData();
        String mis = authRpcResponse.getData().getLoginName();
        var mobile = this.getMobile(crowdfundingInfo);
        var payeeMobile = crowdfundingInfo.getPayeeMobile();
        Date handleTime = workOrderVO.getHandleTime();
        //如果收款人手机号和发起人不是一个人，那么就合并两个手机号的通话记录
        List<CfClewCallRecordsDO> callRecordsDOList = Lists.newArrayList();
        if (mobile.equals(payeeMobile)) {
            callRecordsDOList.addAll(this.getCallRecord(mis, mobile, handleTime, workOrderVO.getUpdateTime()));
        } else {
            callRecordsDOList.addAll(this.getCallRecord(mis, mobile, handleTime, workOrderVO.getUpdateTime()));
            callRecordsDOList.addAll(this.getCallRecord(mis, payeeMobile, handleTime, workOrderVO.getUpdateTime()));
        }
        if (CollectionUtils.isEmpty(callRecordsDOList)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //过滤出当前工单对应案例的通话记录
        List<CfClewCallRecordsDO> callRecordsDOS = callRecordsDOList.stream()
                .filter(cfClewCallRecordsDO -> cfClewCallRecordsDO.getCaseId() == workOrderVO.getCaseId()).collect(Collectors.toList());
        int callStatus;
        if (callRecordsDOS.stream().anyMatch(cfClewCallRecordsDO -> cfClewCallRecordsDO.getPhoneStatus() == 200)) {
            callStatus = MaterialWorkOrderCallStatusEnum.EFFECTIVE_CALL.getCode();
        } else if (callRecordsDOS.stream().allMatch(cfClewCallRecordsDO -> cfClewCallRecordsDO.getPhoneStatus() != 200)) {
            callStatus = MaterialWorkOrderCallStatusEnum.INVALID_CALL.getCode();
        } else {
            callStatus = MaterialWorkOrderCallStatusEnum.NOT_CALL.getCode();
        }
        riskQcSearchIndexDao.updateCallStatus(workOrderVO.getCaseId(), newWorkOrderId, callStatus);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private List<CfClewCallRecordsDO> getCallRecord(String mis, String mobile, Date handleTime, Date finishTime) {

        Response<List<CfClewCallRecordsDO>> callRecordByMisAndPhone = cfClewtrackTaskFeignClient.listCallRecordByMisAndPhone(
                mis, mobile, null, handleTime.getTime());

        if (callRecordByMisAndPhone.ok() && CollectionUtils.isNotEmpty(callRecordByMisAndPhone.getData())) {
            // 10分钟的毫秒数
            long ms = 600000;
            long time = finishTime.getTime() + ms;
            List<CfClewCallRecordsDO> cfClewCallRecordsDOS = callRecordByMisAndPhone.getData();
            return cfClewCallRecordsDOS.stream().filter(cfClewCallRecordsDO ->
                    cfClewCallRecordsDO.getCreateTime().getTime() <= time).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    /**
     * 获取发起人手机号
     *
     * @param crowdfundingInfo
     * @return
     */
    public String getMobile(CrowdfundingInfo crowdfundingInfo) {
        String mobile = "";
        if (Objects.nonNull(crowdfundingInfo)) {
            UserInfoModel userInfoModel = userInfoDelegateService.getUserInfoByUserId(crowdfundingInfo.getUserId());
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                mobile = userInfoModel.getCryptoMobile();
            } else {
                FeignResponse<CfInfoExt> cfInfoExtFeignResponse = crowdfundingFeignClient.getCfInfoExtByCaseId(crowdfundingInfo.getId());
                if (Objects.nonNull(cfInfoExtFeignResponse) && cfInfoExtFeignResponse.ok() && Objects.nonNull(cfInfoExtFeignResponse.getData())) {
                    CfInfoExt cfInfoExt = cfInfoExtFeignResponse.getData();
                    mobile = cfInfoExt.getCryptoRegisterMobile();
                }
            }
        }
        return mobile;
    }
}
