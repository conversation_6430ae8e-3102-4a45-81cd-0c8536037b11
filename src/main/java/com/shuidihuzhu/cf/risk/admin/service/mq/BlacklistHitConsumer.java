package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enhancer.utils.MaskCodeUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistTypeDao;
import com.shuidihuzhu.cf.risk.admin.dao.hit.RiskStrategyHitRecordDao;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistType;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord;
import com.shuidihuzhu.cf.risk.admin.service.hit.RiskHitService;
import com.shuidihuzhu.cf.risk.admin.util.QiyeWeixinUtil;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTopicCons;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyRoleEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistExtendDto;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 **/
@Component
@RocketMQListener(id = CfRiskMQTagCons.BLACKLIST_HIT, tags = CfRiskMQTagCons.BLACKLIST_HIT, topic = CfRiskMQTopicCons.CF_RISK_TOPIC)
@Slf4j
public class BlacklistHitConsumer implements MessageListener<BlacklistExtendDto> {

    private static final long LEAVE_TIME = 10 * 1000;

    @Resource
    private RiskStrategyHitRecordDao riskStrategyHitRecordDao;
    @Resource
    private QiyeWeixinUtil qiyeWeixinUtil;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;

    private static ShuidiCipher shuidiCipher;
    private static RiskBlacklistTypeDao riskBlacklistTypeDao;
    @Autowired
    public void setShuidiCipher(ShuidiCipher shuidiCipher) {
        BlacklistHitConsumer.shuidiCipher = shuidiCipher;
    }
    @Autowired
    public void setRiskBlacklistTypeDao(RiskBlacklistTypeDao riskBlacklistTypeDao) {
        BlacklistHitConsumer.riskBlacklistTypeDao = riskBlacklistTypeDao;
    }

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<BlacklistExtendDto> mqMessage) {
        log.info("收到命中黑名单mq消息：{}", mqMessage);

        BlacklistExtendDto extendDto = mqMessage.getPayload();

        String key = "blacklist_hit_mq_" + extendDto.getCaseId() + "_" + extendDto.getBlacklistCallPhase();
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                return ConsumeStatus.RECONSUME_LATER;
            }

            //风控命中记录表：同一个案例 同一个用户信息 同一个触发动作 不做重复报警记录及通知
            if (BlacklistCallPhaseEnum.needCheckHandleRepeat(extendDto.getBlacklistCallPhase())) {
                if (riskHitService.hadHit(RiskStrategyEnum.BLACKLIST, RiskStrategySecondEnum.DEFAULT, true,
                        extendDto.getCaseId(), extendDto.getBlacklistCallPhase(), extendDto.getHitVerifyDtos())) {
                    log.info("黑名单重复命中，不再记录及通知，caseId:{}, blacklistCallPhase:{}",
                            extendDto.getCaseId(), extendDto.getBlacklistCallPhase());
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
            }

            Set<Long> hitActionIds = extendDto.getHitVerifyDtos().stream()
                    .filter(BlacklistVerifyDto::isHit)
                    .flatMap(blacklistVerifyDto -> blacklistVerifyDto.getLimitActionIds().stream())
                    .collect(Collectors.toSet());

            doConsume(extendDto, hitActionIds);
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void doConsume(BlacklistExtendDto extendDto, Set<Long> hitActionIds){
        //发送企业微信
        if (LimitActionEnum.needSendWxMsg(hitActionIds)) {
            qiyeWeixinUtil.sendBlacklistAlarm("命中风控规则主动通知", assembleInfo(extendDto, hitActionIds));
        }

        //保存信息
        if (LimitActionEnum.needSaveRecord(hitActionIds)) {
            RiskStrategyHitRecord riskStrategyHitRecord = new RiskStrategyHitRecord();
            riskStrategyHitRecord.setCaseId(extendDto.getCaseId());
            riskStrategyHitRecord.setLaunchTime(DateUtil.getStr2LDate(extendDto.getCaseInitiateTime()));
            riskStrategyHitRecord.setHitPhase(extendDto.getBlacklistCallPhase());
            riskStrategyHitRecord.setRiskStrategy(RiskStrategyEnum.BLACKLIST.getCode());
            List<BlacklistVerifyDto> verifyDtos = extendDto.getHitVerifyDtos().stream()
                    .filter(BlacklistVerifyDto::isHit)
                    .peek(blacklistVerifyDto -> {
                        if (BlacklistVerifyTypeEnum.needEncrypt(blacklistVerifyDto.getVerifyType())) {
                            blacklistVerifyDto.setVerifyData(oldShuidiCipher.aesEncrypt(blacklistVerifyDto.getVerifyData()));
                        }
                    })
                    .collect(Collectors.toList());
            riskStrategyHitRecord.setHitInfo(JSON.toJSONString(verifyDtos));
            riskHitService.saveHitRecord(riskStrategyHitRecord);
        }
    }

    private String assembleInfo(BlacklistExtendDto extendDto, Collection<Long> hitActionIds){
        String action = Joiner.on("、").join(LimitActionEnum.ids2Names(hitActionIds));
        StringBuilder sb = new StringBuilder("\n");
        sb.append("触发时机：").append(BlacklistCallPhaseEnum.fromCode(extendDto.getBlacklistCallPhase()).getDesc()).append("\n");
        sb.append("命中的风控策略名称：【黑名单】").append("\n");
        sb.append("命中动作类型：【").append(action).append("】").append("\n");
        sb.append("触发时间：").append(DateUtil.getCurrentDateStr()).append("\n");
    if (extendDto.getCaseId() > 0) {
        sb.append("案例id:").append(extendDto.getCaseId()).append("\n");
    }
        sb.append("引导用户发起渠道:用户自主发起").append("\n");

        sb.append(riskHitService.assembleHitInfo(extendDto.getHitVerifyDtos(), false));

        sb.append("命中的信息类型：").append(Joiner.on("、").join(extendDto.getHitVerifyDtos().stream()
                .filter(BlacklistVerifyDto::isHit)
                .sorted(Comparator.comparingInt(BlacklistVerifyDto::getVerifyType))
                .map(blacklistVerifyDto -> BlacklistVerifyRoleEnum.fromCode(blacklistVerifyDto.getVerifyRole()).getDesc() +
                        BlacklistVerifyTypeEnum.fromCode(blacklistVerifyDto.getVerifyType()).getDesc())
                .collect(Collector.of((Supplier<Set<String>>) LinkedHashSet::new, Set::add,
                        (left, right) -> {
                            if (left.size() < right.size()) {
                                right.addAll(left); return right;
                            } else {
                                left.addAll(right); return left;
                            }
                        })))).append("\n");

        sb.append("命中的信息所属的黑名单类型：").append(assembleHitBlacklistType(extendDto.getHitVerifyDtos())).append("\n");

        return sb.toString();
    }

    public static String decideEncrypt(BlacklistVerifyDto blacklistVerifyDto, boolean needDecrypt){
        if (needDecrypt && BlacklistVerifyTypeEnum.needEncrypt(blacklistVerifyDto.getVerifyType())) {
            String verifyData = shuidiCipher.decrypt(blacklistVerifyDto.getVerifyData());
            if (blacklistVerifyDto.getVerifyType() == BlacklistVerifyTypeEnum.MOBILE.getCode()) {
                return MaskCodeUtil.maskPhone(verifyData);
            }
            if (blacklistVerifyDto.getVerifyType() == BlacklistVerifyTypeEnum.ID_CARD.getCode()) {
                return MaskCodeUtil.maskSelfCard(verifyData);
            }
            if (blacklistVerifyDto.getVerifyType() == BlacklistVerifyTypeEnum.BORN_CARD.getCode()) {
                return MaskCodeUtil.maskSelfCard(verifyData);
            }
        }

        return blacklistVerifyDto.getVerifyData();
    }

    public static String assembleHitBlacklistType(List<BlacklistVerifyDto> verifyDtos){
        Set<Long> typeIds = verifyDtos.stream()
                .filter(BlacklistVerifyDto::isHit)
                .flatMap(blacklistVerifyDto -> blacklistVerifyDto.getTypeIds().stream()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(typeIds)) {
            return "";
        }
        List<RiskBlacklistType> riskBlacklistTypes = riskBlacklistTypeDao.listByIds(typeIds);
        return riskBlacklistTypes.stream().map(RiskBlacklistType::getTypeName)
                        .collect(Collectors.joining("、"));
    }

}
