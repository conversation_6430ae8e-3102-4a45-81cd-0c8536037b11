package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcSearchIndexDao {

    int insertOne(RiskQcSearchIndex riskQcSearchIndex);

    RiskQcSearchIndex getByWorkOrderId(@Param("workOrderId") long workOrderId);

    int updateByWorkOrderId(@Param("workOrderId") long workOrderId,
                            @Param("qcResult") int qcResult,
                            @Param("questionType") String questionType,
                            @Param("firstPropertyIds") String firstPropertyIds,
                            @Param("qcResultSecond") int qcResultSecond);

    List<RiskQcSearchIndex> getByWorkOrderIds(@Param("workOrderIds") List<Long> workOrderIds);

    int updateServiceStageByTaskId(@Param("taskId") int taskId,
                                   @Param("workOrderId") long workOrderId,
                                   @Param("serviceStage") int serviceStage);

    int updateMaterialId(@Param("materialId") long materialId, @Param("id") long id);



    List<RiskQcSearchIndex> getAllByPage(@Param("id") long id);

    int updateMaterialIdByTaskId(@Param("materialId") long materialId, @Param("taskId") long taskId,
                                 @Param("qcType") int qcType);

    int updateCaseIdAndUserId(@Param("taskId") long taskId, @Param("qcType") int qcType,
                              @Param("caseId") long caseId, @Param("userId") long userId);

    int updateRuleNameByWorkOrderId(@Param("workOrderId") long workOrderId, @Param("ruleName") String ruleName);

    List<RiskQcSearchIndex> selectbyIdAndSize(@Param("id") long id, @Param("size") int size);

    int updateById(@Param("id") long id, @Param("userId") long userId);

    int updateCallTaskStatusByTaskId(@Param("callTaskStatus") int callTaskStatus, @Param("taskId") long taskId,
                                     @Param("qcType") int qcType);

    int updateLabelByTaskId(@Param("firstLevelLabel") long firstLevelLabel,
                            @Param("twoLevelLabel") long twoLevelLabel,
                            @Param("taskId") long taskId,
                            @Param("qcType") int qcType);


    int addCallRiskQcSearchIndex(RiskQcSearchIndex riskQcSearchIndex);

    int addMaterialRiskQcSearchIndex(RiskQcSearchIndex riskQcSearchIndex);

    int updateCallStatus(@Param("caseId") long caseId, @Param("workOrderId") long workOrderId, @Param("callStatus") int callStatus);

    int updateOrgByWorkOrderId(@Param("workOrderId") long workOrderId, @Param("organization") String organization);
}
