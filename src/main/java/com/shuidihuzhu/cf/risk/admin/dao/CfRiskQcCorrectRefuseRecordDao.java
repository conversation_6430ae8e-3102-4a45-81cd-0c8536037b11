package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.CfRiskCommentExt;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcCorrectRefuseRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/7
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface CfRiskQcCorrectRefuseRecordDao {

    int insert(@Param("workOrderId") long workOrderId,@Param("correctRefuse") String correctRefuse,@Param("operationType")int operationType);

    List<RiskQcCorrectRefuseRecord> getCorrectRefuseByWorkOrderId(@Param("workOrderId") long workOrderId);
}
