package com.shuidihuzhu.cf.risk.admin.controller;


import com.shuidihuzhu.cf.risk.admin.model.Discussion;
import com.shuidihuzhu.cf.risk.admin.model.enums.DiscussionReasonEnum;
import com.shuidihuzhu.cf.risk.admin.biz.DiscussionBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/cf-risk-admin/discussion")
public class DiscussionController {

    @Autowired
    DiscussionBiz discussionBiz;

    @ApiOperation("开启评议通道")
    @RequestMapping(path = "/open", method = RequestMethod.POST)
    @RequiresPermission("discussion:open")
    public Response open(@RequestBody Discussion discussion) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        return discussionBiz.openDiscussion(Math.toIntExact(adminUserId), discussion);
    }

    @ApiOperation("关闭评议通道")
    @RequestMapping(path = "/close", method = RequestMethod.POST)
    @RequiresPermission("discussion:close")
    public Response close(int caseId, String infoUuid) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        return discussionBiz.closeDiscussion(Math.toIntExact(adminUserId), caseId, infoUuid);
    }

    @ApiOperation("获取评议状态")
    @RequestMapping(path = "/get-status", method = RequestMethod.POST)
    public Response getStatus(int caseId) {
        return NewResponseUtil.makeSuccess(discussionBiz.getStatus(caseId));
    }

    @ApiOperation("检查当前案例开启评议是否合法")
    @RequestMapping(path = "/check-case", method = RequestMethod.POST)
    public Response checkCase(int caseId, String infoUuid) {
        String result = discussionBiz.checkCase(caseId, infoUuid);
        return StringUtils.isBlank(result) ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeResponse(99999, result, "");
    }

    @ApiOperation("获取评议通道原因枚举列表")
    @RequestMapping(path = "/reason-enum-list", method = RequestMethod.POST)
    public Response reasonEnumList() {
        return NewResponseUtil.makeSuccess(Arrays.stream(DiscussionReasonEnum.values()).collect(Collectors.toMap(DiscussionReasonEnum::getDescription, DiscussionReasonEnum::getCode)));
    }

    @ApiOperation("获取案例评议申请信息")
    @RequestMapping(path = "/get-discussion-info", method = RequestMethod.POST)
    public Response getDiscussionInfo(int caseId) {
        return NewResponseUtil.makeSuccess(discussionBiz.findByCaseId(caseId));
    }

    @ApiOperation("获取案例评议申请记录")
    @RequestMapping(path = "/get-discussion-info-list", method = RequestMethod.POST)
    public Response getDiscussionInfoList(int caseId) {
        return NewResponseUtil.makeSuccess(discussionBiz.listByCaseId(caseId));
    }

    @ApiOperation("获取评议统计信息")
    @RequestMapping(path = "/discussion-stat-info")
    public Response getRecordCount(long discussionId){
        return NewResponseUtil.makeSuccess(discussionBiz.getInfoById(discussionId));
    }

}
