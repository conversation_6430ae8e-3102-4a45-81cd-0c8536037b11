package com.shuidihuzhu.cf.risk.admin.service.recording.support;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.WorkOrderCallRecordingRelBiz;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.service.recording.AbsWorkOrderRecordingHandler;
import com.shuidihuzhu.cf.risk.admin.util.SpringUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2022/3/28 下午4:43
 * 举报工单 WorkOrderType.REPORT_TYPES 录音获取 处理 存储
 */
@Component
@Slf4j
public class WorkOrderRecordingHandlerReport extends AbsWorkOrderRecordingHandler {
    @Autowired
    private WorkOrderCallRecordingRelBiz workOrderCallRecordingRelBiz;
    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;
    @Override
    public List<WorkOrderRecordingModel.CallRecordModel> listCallRecordModels(long workOrderId) {
        List<String> uniqueIds = workOrderCallRecordingRelBiz.selectByWorkOrderId(workOrderId);
        if (CollectionUtils.isEmpty(uniqueIds)) {
            log.info("listCallRecordModels empty record workOrderId {}", workOrderId);
            return Lists.newArrayList();
        }
        Response<List<ClewCallRecordModel>> callRecordsByUniqueIds = cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(uniqueIds);
        log.info("cfClewtrackFeignClient.getClewCallRecordsByUniqueIds response:{}", callRecordsByUniqueIds);
        if (callRecordsByUniqueIds.notOk() || CollectionUtils.isEmpty(callRecordsByUniqueIds.getData())) {
            return Lists.newArrayList();
        }
        return callRecordsByUniqueIds.getData().stream()
                .map(WorkOrderRecordingModel.CallRecordModel::createByClewCallRecordModel)
                .collect(Collectors.toList());
    }

    @Override
    public AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum() {
        return AiAsrDelegate.HandleTypeEnum.HANDLE_REPORT_RECORD;
    }

    @Override
    public WorkOrderType getWorkOrderType() {
        return null;
    }
}
