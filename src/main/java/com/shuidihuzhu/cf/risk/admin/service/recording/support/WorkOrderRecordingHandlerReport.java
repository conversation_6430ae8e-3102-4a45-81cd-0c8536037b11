package com.shuidihuzhu.cf.risk.admin.service.recording.support;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.WorkOrderCallRecordingRelBiz;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.service.recording.AbsWorkOrderRecordingHandler;
import com.shuidihuzhu.cf.risk.admin.util.SpringUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 举报工单录音处理器
 *
 * 该处理器专门负责处理举报类工单（WorkOrderType.REPORT_TYPES）的录音相关业务：
 *
 * 业务场景：
 * - 举报工单是用户或内部人员对某些违规行为、服务问题等进行举报而产生的工单
 * - 举报内容可能涉及通话录音，需要对这些录音进行分析和质检
 * - 通过AI语音识别技术分析录音内容，识别违规行为或服务问题
 *
 * 数据关联设计：
 * - 举报工单与通话录音通过中间关系表（work_order_call_recording_rel）进行关联
 * - 关系表存储工单ID和录音唯一标识（recording_unique_id）的映射关系
 * - 支持一个工单关联多个录音记录的场景
 *
 * 主要功能：
 * 1. 根据举报工单ID查询关联的录音唯一标识列表
 * 2. 根据录音唯一标识批量获取详细的通话录音信息
 * 3. 将录音数据转换为标准格式供AI分析使用
 * 4. 支持举报场景专用的AI分析和风控检测
 *
 * 数据流向：
 * 举报工单 -> 工单录音关系表 -> 录音唯一标识 -> 通话录音详情
 *
 * <AUTHOR>
 * @create 2022/3/28 下午4:43
 */
@Component
@Slf4j
public class WorkOrderRecordingHandlerReport extends AbsWorkOrderRecordingHandler {

    /** 工单与通话录音关系业务服务，用于查询工单关联的录音标识 */
    @Autowired
    private WorkOrderCallRecordingRelBiz workOrderCallRecordingRelBiz;

    /** 线索跟踪服务客户端，用于根据录音标识获取详细的通话录音信息 */
    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;
    /**
     * 获取举报工单的通话录音记录列表
     *
     * 该方法专门处理举报工单的录音获取逻辑，采用间接关联的方式：
     * 1. 通过工单录音关系表查询工单关联的录音唯一标识列表
     * 2. 根据录音唯一标识批量获取详细的通话录音信息
     * 3. 将通话录音数据转换为标准的CallRecordModel格式
     *
     * 设计说明：
     * - 举报工单与录音的关联关系相对复杂，不是直接关联
     * - 通过中间关系表（work_order_call_recording_rel）建立映射关系
     * - 支持一个举报工单关联多个不同的通话录音
     * - 录音可能来自不同的通话场景和时间点
     *
     * 业务场景：
     * - 用户举报某次服务过程中的问题，可能涉及多次通话
     * - 内部举报某个服务人员的行为，需要分析相关的通话录音
     * - 质检人员需要听取相关录音来核实举报内容的真实性
     *
     * @param workOrderId 举报工单ID
     * @return 通话录音记录模型列表，包含录音URL、通话时长、接通状态等信息
     */
    @Override
    public List<WorkOrderRecordingModel.CallRecordModel> listCallRecordModels(long workOrderId) {
        // 步骤1: 查询工单关联的录音唯一标识列表
        // 从工单录音关系表中获取该举报工单关联的所有录音的唯一标识
        List<String> uniqueIds = workOrderCallRecordingRelBiz.selectByWorkOrderId(workOrderId);
        if (CollectionUtils.isEmpty(uniqueIds)) {
            log.info("listCallRecordModels: no recording unique ids found for workOrderId {}", workOrderId);
            return Lists.newArrayList();
        }

        log.info("listCallRecordModels: found {} recording unique ids for workOrderId {}", uniqueIds.size(), workOrderId);

        // 步骤2: 根据录音唯一标识批量获取通话录音详情
        // 调用线索跟踪服务，根据录音唯一标识列表批量查询录音详细信息
        Response<List<ClewCallRecordModel>> callRecordsByUniqueIds = cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(uniqueIds);
        log.info("cfClewtrackFeignClient.getClewCallRecordsByUniqueIds response: {}", callRecordsByUniqueIds);

        // 步骤3: 检查远程调用结果
        if (callRecordsByUniqueIds.notOk() || CollectionUtils.isEmpty(callRecordsByUniqueIds.getData())) {
            log.warn("listCallRecordModels: failed to get call records or empty result for uniqueIds: {}", uniqueIds);
            return Lists.newArrayList();
        }

        // 步骤4: 数据转换
        // 将ClewCallRecordModel转换为标准的CallRecordModel格式
        // 包括解密手机号、计算实际通话时长、格式化时间等处理
        return callRecordsByUniqueIds.getData().stream()
                .map(WorkOrderRecordingModel.CallRecordModel::createByClewCallRecordModel)
                .collect(Collectors.toList());
    }

    /**
     * 获取AI处理类型枚举
     *
     * 返回举报记录的AI处理类型，该类型决定了：
     * 1. 使用举报场景专用的风控词库进行敏感词检测
     * 2. AI分析会重点关注违规行为、服务问题等举报相关内容
     * 3. 分析结果会按照举报处理的业务流程进行后续处理
     *
     * HANDLE_REPORT_RECORD 专门用于举报工单的录音分析，
     * 会重点识别违规言论、服务态度问题、操作不当等举报相关内容。
     *
     * @return AI处理类型枚举，用于举报记录分析场景
     */
    @Override
    public AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum() {
        return AiAsrDelegate.HandleTypeEnum.HANDLE_REPORT_RECORD;
    }

    /**
     * 获取工单类型
     *
     * 注意：举报工单处理器返回null，因为举报工单包含多种类型（REPORT_TYPES）。
     *
     * 设计说明：
     * - 举报工单不是单一的工单类型，而是一个工单类型集合
     * - WorkOrderType.REPORT_TYPES 包含多种具体的举报类型
     * - 因此这里返回null，表示该处理器处理多种举报相关的工单类型
     * - 具体的工单类型判断在业务层面进行处理
     *
     * @return null，表示处理多种举报类型的工单
     */
    @Override
    public WorkOrderType getWorkOrderType() {
        return null;
    }
}
