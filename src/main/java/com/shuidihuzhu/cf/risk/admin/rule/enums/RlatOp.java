package com.shuidihuzhu.cf.risk.admin.rule.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 等式条件
 * <AUTHOR>
 * @date 2020-02-26
 **/
@AllArgsConstructor
@Getter
public enum RlatOp {
    EQUALS("等于", "=="),
    NOT_EQUALS("不等于", "!="),
    LESS_THEN("小于", "<"),
    LESS_THEN_EQUALS("小于或等于", "<="),
    GREATER_THEN("大于", ">"),
    GREATER_THEN_EQUALS("大于或等于", ">="),
    IN("在集合", ""),
    NOT_IN("不在集合", ""),
    CONTAIN("包含", ""),
    NOT_CONTAIN("不包含", ""),
    //todo houys 后期开放开放功能
    /*EQUALS_IGNORE_CASE("等于（不区分大小写）", ""),
    NOT_EQUALS_IGNORE_CASE("不等于（不区分大小写）", ""),
    START_WITH("开始于", ""),
    NOT_START_WITH("不开始于", ""),
    END_WITH("结束于", ""),
    NOT_END_WITH("不结束于", ""),
    NULL("为空", ""),
    NOT_NULL("不为空", ""),
    MATCH("匹配正则表达式", ""),
    NOT_MATCH("不匹配正则表达式", ""),*/
    ;

    public static Map<String, String> nameDescMap(){
        return Arrays.stream(values()).collect(Collectors.toMap(RlatOp::name, RlatOp::getName, (s, s2) -> s , LinkedHashMap::new));
    }

    String name;
    String script;

}
