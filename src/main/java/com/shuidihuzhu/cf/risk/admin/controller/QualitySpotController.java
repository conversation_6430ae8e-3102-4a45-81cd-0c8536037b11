package com.shuidihuzhu.cf.risk.admin.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotLevelConfBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotStrategyBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQualitySpotTypeBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotScheduleTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.quality.sampling.QualitySpotStrategyQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.*;
import com.shuidihuzhu.cf.risk.admin.rule.enums.RlatOp;
import com.shuidihuzhu.cf.risk.admin.service.RiskQualitySpotWorkOrderService;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotLevelConfService;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotStrategyService;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.model.vo.RiskQualitySpotRuleVo;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/15 20:29
 */
@Validated
@Slf4j
@RestController
@RequestMapping(path = "/api/cf-risk-admin/quality/spot")
public class QualitySpotController {

    @Resource
    private QualitySpotStrategyService qualitySpotStrategyService;
    @Resource
    private QualitySpotStrategyBiz qualitySpotStrategyBiz;
    @Resource
    private QualitySpotLevelConfBiz qualitySpotLevelConfBiz;
    @Resource
    private QualitySpotLevelConfService qualitySpotLevelConfService;
    @Resource
    private RiskQualitySpotTypeBiz riskQualitySpotTypeBiz;
    @Resource
    private RiskQualitySpotWorkOrderService riskQualitySpotWorkOrderService;

    @ApiOperation(value = "抽检策略-保存策略", notes = "权限：qualitySpotStrategy:save")
    @RequiresPermission("qualitySpotStrategy:save")
    @PostMapping(path = "/strategy/save")
    public Response<RiskQualitySpotStrategyDetailVo> strategySave(@Valid RiskQualitySpotStrategyDetailVo detailVo) {
        log.info("抽检策略-保存策略请求入参：{}", detailVo);
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            qualitySpotStrategyService.saveStrategy(detailVo, adminUserId);
        } catch (IllegalArgumentException e) {
            log.warn("Illegal argument exp:{}", e.getMessage());
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_ERROR.getCode(), e.getMessage(), null);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "抽检策略-更新策略", notes = "权限：qualitySpotStrategy:update")
    @RequiresPermission("qualitySpotStrategy:update")
    @PostMapping(path = "/strategy/update")
    public Response<RiskQualitySpotStrategyDetailVo> strategyUpdate(@Valid RiskQualitySpotStrategyDetailVo detailVo) {
        log.info("抽检策略-保存策略请求入参：{}", detailVo);
        if (detailVo.getId() <= 0){
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_STRATEGY_Id_ERROR);
        }
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            qualitySpotStrategyService.updateStrategy(detailVo, adminUserId);
        } catch (IllegalArgumentException e) {
            log.warn("Illegal argument exp:{}", e.getMessage());
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_ERROR.getCode(), e.getMessage(), null);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "抽检策略-查看策略", notes = "权限：qualitySpotStrategy:get")
    @RequiresPermission("qualitySpotStrategy:get")
    @PostMapping(path = "/strategy/get")
    public Response<RiskQualitySpotStrategyDetailVo> strategyGet(
            @ApiParam(value = "策略id", required = true) @RequestParam(value = "id")
            @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long id) {
        log.info("抽检策略-查看策略请求入参：{}", id);
        return NewResponseUtil.makeSuccess(qualitySpotStrategyBiz.getById(id));
    }

    @ApiOperation(value = "抽检策略-查询允许配置的适用范围", notes = "权限：qualitySpotRemaining:scope")
    @RequiresPermission("qualitySpotRemaining:scope")
    @PostMapping(path = "/strategy/remaining/scope")
    public Response<Collection<Integer>> strategyRemainingScope(
            @ApiParam(value = "适用场景id", required = true) @RequestParam(value = "scene")
            @NotNull(message = "适用场景不能为空") @Min(value = 1, message = "适用场景不能小于1") Long scene) {
        log.info("抽检策略-查询允许配置的适用范围请求入参：{}", scene);
        List<RiskQualitySpotStrategy> spotStrategies = qualitySpotStrategyBiz.listByValidScene(scene);
        Set<Integer> used = spotStrategies.stream().map(strategy ->
                QualitySpotScheduleTypeEnum.codeString2CodeList(strategy.getStrategyScope()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        if (used.contains(QualitySpotScheduleTypeEnum.EVERY_DAY.getCode())) {
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }
        return NewResponseUtil.makeSuccess(CollectionUtils.subtract(QualitySpotScheduleTypeEnum.codeEnumMap.keySet(), used));
    }

    @ApiOperation(value = "抽检策略列表查询", notes = "权限：qualitySpotStrategy:list")
    @RequiresPermission("qualitySpotStrategy:list")
    @PostMapping(path = "/strategy/list")
    public Response<PageResult<RiskQualitySpotStrategyVo>> strategyList(@Valid QualitySpotStrategyQuery strategyQuery) {
        log.info("{}请求入参：{}", this.getClass().getSimpleName(), strategyQuery);
        return NewResponseUtil.makeSuccess(qualitySpotStrategyService.queryStrategyList(strategyQuery));
    }

    @ApiOperation(value = "抽检策略-启用", notes = "权限：qualitySpotStrategy:enable")
    @RequiresPermission("qualitySpotStrategy:enable")
    @PostMapping(path = "/strategy/enable")
    public Response<Object> strategyEnable(
            @ApiParam(value = "策略id",required = true)
            @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long id) {
        log.info("抽检策略-启用请求入参：{}", id);
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            qualitySpotStrategyService.enableStrategy(id, adminUserId);
        } catch (IllegalArgumentException e) {
            log.warn("Illegal argument exp:{}", e.getMessage());
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_ERROR.getCode(), e.getMessage(), null);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "抽检策略-弃用", notes = "权限：qualitySpotStrategy:disable")
    @RequiresPermission("qualitySpotStrategy:disable")
    @PostMapping(path = "/strategy/disable")
    public Response<Object> strategyDisable(
            @ApiParam(value = "策略id",required = true)
            @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long id) {
        log.info("抽检策略-弃用请求入参：{}", id);
        long adminUserId = ContextUtil.getAdminLongUserId();
        qualitySpotStrategyService.disableStrategy(id, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "抽检策略-查看操作日志", notes = "权限：qualitySpotStrategy:logList")
    @RequiresPermission("qualitySpotStrategy:logList")
    @PostMapping(path = "/strategy/log-list")
    public Response<List<RiskQualitySpotStrategyLogVo>> strategyLogList(@ApiParam(value = "策略id",required = true)
             @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") @RequestParam("strategyId")Long strategyId) {
        log.info("抽检策略-查看操作日志请求入参, strategyId:{}", strategyId);
        return NewResponseUtil.makeSuccess(qualitySpotStrategyService.queryStrategyLogList(strategyId));
    }

    @ApiOperation(value = "抽检策略-查看抽检量级列表", notes = "权限：qualitySpotStrategyLevel:list")
    @RequiresPermission("qualitySpotStrategyLevel:list")
    @PostMapping(path = "/strategy/level/list")
    public Response<List<RiskQualitySpotLevelConfVo>> strategyLevelList(@NotNull(message = "质检对象不能为空") @RequestParam(value = "firstScene", required = false)
                                                                            @ApiParam(value = "质检对象", defaultValue = "0") Long firstScene,
                                                                        @NotNull(message = "二级工单类型不能为空") @RequestParam(value ="secondScene", required = false)
                                                                        @ApiParam(value = "二级工单类型", defaultValue = "0") Long secondScene) {
        if (firstScene <= 0) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return NewResponseUtil.makeSuccess(qualitySpotLevelConfBiz.listWithAllLatestValidScene(firstScene, secondScene));
    }

    @ApiOperation(value = "抽检策略-查看抽检次日生效量级", notes = "权限：qualitySpotStrategyLevel:getScene")
    @RequiresPermission("qualitySpotStrategyLevel:getScene")
    @PostMapping(path = "/strategy/level/get-scene")
    public Response<RiskQualitySpotLevelConfVo> strategyLevelGetScene(
            @NotNull(message = "二级工单类型不能为空") @Min(value = 1, message = "二级工单类型不能小于1")
            @ApiParam(value = "二级工单类型", required = true)  @RequestParam("scene")Long scene) {
        log.info("抽检策略-查看抽检次日生效量级请求入参：{}", scene);
        return NewResponseUtil.makeSuccess(qualitySpotStrategyBiz.getLatestLevelConf(scene));
    }


    @ApiOperation(value = "抽检策略-查看抽检量级", notes = "权限：qualitySpotStrategyLevel:get")
    @RequiresPermission("qualitySpotStrategyLevel:get")
    @PostMapping(path = "/strategy/level/get")
    public Response<RiskQualitySpotLevelConfVo> strategyLevelGet(
            @ApiParam(value = "抽检量级id", required = true)  @RequestParam("id")
            @NotNull(message = "id不能为空") @Min(value = 1, message = "id值不能小于1") Long id) {
        log.info("抽检策略-查看抽检量级请求入参：{}", id);
        return NewResponseUtil.makeSuccess(qualitySpotLevelConfBiz.getByLevelId(id));
    }

    @ApiOperation(value = "抽检策略-抽检量级保存修改", notes = "权限：qualitySpotStrategyLevel:update")
    @RequiresPermission("qualitySpotStrategyLevel:update")
    @PostMapping(path = "/strategy/level/update")
    public Response<RiskQualitySpotLevelConfVo> strategyLevelUpdate(@Valid RiskQualitySpotLevelConfUpdateVo confUpdateVo) {
        log.info("抽检策略-抽检量级保存修改请求入参：{}", confUpdateVo);
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            qualitySpotLevelConfService.updateLevelConf(confUpdateVo, adminUserId);
        } catch (IllegalArgumentException e) {
            log.warn("Illegal argument exp:{}", e.getMessage());
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_ERROR.getCode(), e.getMessage(), null);
        }
        return NewResponseUtil.makeSuccess(null);
    }


    @ApiOperation(value = "抽检策略-查看抽检量级修改历史", notes = "权限：qualitySpotStrategyLevel:historyList")
    @RequiresPermission("qualitySpotStrategyLevel:historyList")
    @PostMapping(path = "/strategy/level/update-history-list")
    public Response<List<RiskQualitySpotLevelConfLogVo>> strategyLevelHistoryList(
            @NotNull(message = "二级工单类型不能为空") @Min(value = 1, message = "二级工单类型不能小于1")
                    @ApiParam(value = "二级工单类型", required = true) Integer scene) {
        log.info("抽检策略-查看抽检量级修改历史请求入参：{}", scene);
        return NewResponseUtil.makeSuccess(qualitySpotLevelConfBiz.listByConfId(scene));
    }

    /**
     * ***************************************************************
     * ***************************规则源数据部分**************************
     * ***************************************************************
     */
    @ApiOperation(value = "规则配置元数据-关系运算符列表", notes = "key,value形式，key为实际请求需要的code码，value用于显示；" +
            "权限：qualitySpotRule:relationalList")
    @RequiresPermission("qualitySpotRule:relationalList")
    @PostMapping(path = "/rule/relational/list")
    public Response<Map<String, String>> ruleRelationalList() {
        return NewResponseUtil.makeSuccess(RlatOp.nameDescMap());
    }

    @ApiOperation(value = "规则配置元数据-用于配置规则时可选的对象属性列表", notes = "权限：qualitySpotRule:entityProps")
    @RequiresPermission("qualitySpotRule:entityProps")
    @PostMapping(path = "/rule/entity/prop")
    public Response<List<RuleEntityProp>> ruleEntityProp(@ApiParam(value = "场景id")@RequestParam(value = "scene", defaultValue = "3") long scene) {
        if ( QualitySpotSceneEnum.fromCode(scene) == null){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(RuleEntityProp.fieldNameDescMap(scene));
    }

    @ApiOperation(value = "抽检策略-查看场景id", notes = "需要区分权限权")
    @PostMapping(path = "/scene/list/get")
    public Response<List<RiskQualitySpotType>> getRuleVoList(@ApiParam(value = "质检对象id 默认为0")
                                                               @RequestParam(value = "parentId", defaultValue = "0") Long parentId) {
        log.info("抽检策略-查看策略请求入参：{}", parentId);
        return NewResponseUtil.makeSuccess(riskQualitySpotTypeBiz.getListByParentId(parentId));
    }


    @RequiresPermission("qualitySpotRule:workOrderRuleGet")
    @ApiOperation(value = "工单策略配置-查看场景id", notes = "需要区分权限权")
    @PostMapping(path = "work-order/scene/rule/get")
    public Response<List<RiskQualitySpotRuleVo>> getRuleVoList(@ApiParam(value = "场景id 线下顾问:3 微信1v1:4") @RequestParam(value = "scene") long scene) {
        log.info("抽检策略-查看策略请求入参：{}", scene);
        return NewResponseUtil.makeSuccess(riskQualitySpotWorkOrderService.getRuleVoList(scene));
    }

    @RequiresPermission("qualitySpotRule:workOrderUserRuleUpdate")
    @ApiOperation(value = "保存修改配置规则工单接口", notes = "需要区分权限权")
    @PostMapping(path = "work-order/user/rule/add-or-update")
    public Response<Boolean> addOrUpdateUserRule(@Valid @RequestBody() RiskQualitySpotWorkOrderDetailVo riskQualitySpotWorkOrderDetailVo) {
        log.info("抽检策略-保存和更新策略请求入参：{}", JSON.toJSONString(riskQualitySpotWorkOrderDetailVo));
        if (riskQualitySpotWorkOrderDetailVo.getRiskQualitySpotRuleDetailVos().size() > 10){
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_WORK_RODER_RULE_SIZE_ERROR);
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        return riskQualitySpotWorkOrderService.saveOrUpdate(riskQualitySpotWorkOrderDetailVo);
    }

    @RequiresPermission("qualitySpotRule:workOrderUserRuleGet")
    @ApiOperation(value = "工单策略配置-查看规则配置详情", notes = "需要区分权限权")
    @PostMapping(path = "work-order/user/rule/get")
    public Response<RiskQualitySpotWorkOrderDetailVo> getUserRule(@ApiParam(value = "场景id 线下顾问:3 微信1v1:4") @RequestParam(value = "scene") long scene,
                                                                  @ApiParam(value = "操作人id") @RequestParam(value = "operatorId") long operatorId) {
        log.info("抽检策略-查看策略请求入参：{} operatorId:{}", scene, operatorId);
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        if (operatorId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return riskQualitySpotWorkOrderService.getUserRule(operatorId, scene);
    }

}
