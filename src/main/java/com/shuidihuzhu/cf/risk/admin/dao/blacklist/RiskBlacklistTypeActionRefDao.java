package com.shuidihuzhu.cf.risk.admin.dao.blacklist;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataActionRef;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeActionRef;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.Collection;
import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskBlacklistTypeActionRefDao {
    int insertSelective(RiskBlacklistTypeActionRef record);

    int saveBatch(List<RiskBlacklistTypeActionRef> records);

    int deleteByIds(List<Long> ids);

    RiskBlacklistTypeActionRef selectByPrimaryKey(Long id);

    List<RiskBlacklistTypeActionRef> listByTypeIds(Collection<Long> typeIds);

    int updateTypeActionRefById(RiskBlacklistTypeActionRef riskBlacklistTypeActionRef);




}