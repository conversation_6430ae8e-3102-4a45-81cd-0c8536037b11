package com.shuidihuzhu.cf.risk.admin.model;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/2/6
 * id	BIGINT	主键
 * case_id	VARCHAR	案例id
 * user_id	BIGINT	用户id
 * discussion_id	BIGINT	决议id
 * discussion_result	TINYINT
 * 评议结果：
 *  1：同意
 *  2：拒绝
 */
@Data
@Slf4j
public class DiscussionRecord {
    private long id;
    private long caseId;
    private long userId;
    private long discussionId;
    private int discussionResult;

}
