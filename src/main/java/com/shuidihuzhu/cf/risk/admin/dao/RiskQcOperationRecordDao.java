package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcOperationRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-01-06 12:12
 **/
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcOperationRecordDao {

    int save(RiskQcOperationRecord riskQcOperationRecord);
}
