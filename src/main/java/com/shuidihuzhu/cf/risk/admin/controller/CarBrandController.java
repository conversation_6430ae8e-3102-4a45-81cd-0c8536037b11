package com.shuidihuzhu.cf.risk.admin.controller;

import com.shuidihuzhu.cf.risk.admin.model.vo.CarBrandVo;
import com.shuidihuzhu.cf.risk.admin.service.CarBrandService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 轿车品牌接口
 * <AUTHOR>
 * @date 2020/6/15 20:29
 */
@Validated
@Slf4j
@RestController
@RequestMapping(path = "/api/cf-risk-admin/car/brand")
public class CarBrandController {

    @Resource
    private CarBrandService carBrandService;

    @ApiOperation(value = "根据名称模糊查询车品牌-列表")
    @PostMapping(path = "/list/limit")
    public Response<List<CarBrandVo>> listLimit(@ApiParam("车品牌") String carName) {
        log.info("根据名称模糊查询车品牌-列表，请求入参：{}", carName);
        return NewResponseUtil.makeSuccess(carBrandService.listByLimit50(carName));
    }

}
