package com.shuidihuzhu.cf.risk.admin.model.vo.list;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/7/28 16:46
 */
@Data
public class ListDepartmentBase {

    @NotNull(message = "名单类型不能为空")
    @Range(min = 1, max = 3)
    @ApiModelProperty("名单类型：1 黑名单，2 灰名单，3 白名单")
    private Byte listType;

    @ApiModelProperty("医院id")
    private Integer hospitalId;

    @ApiModelProperty("医院code码")
    private String hospitalCode;

    @NotBlank(message = "医院名称不能为空")
    @ApiModelProperty("医院名称")
    private String hospitalName;

    @NotBlank(message = "区号不能为空")
    @ApiModelProperty("座机区号")
    private String areaCode;

    @NotBlank(message = "座机号不能为空")
    @ApiModelProperty("座机号")
    private String landline;

    @ApiModelProperty("座机分机号")
    private String extension;

    @NotBlank(message = "医院所在省份不能为空")
    @ApiModelProperty("医院所在省份")
    private String province;

    @NotBlank(message = "医院所在省份不能为空")
    @ApiModelProperty("医院所在城市")
    private String city;

    @NotBlank(message = "科室不能为空")
    @Length(min = 1, max = 500)
    @ApiModelProperty("科室")
    private String departments;

}
