package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcAssignRecordBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcAssignRecordDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcAssignRecord;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RiskQcAssignRecordBizImpl implements RiskQcAssignRecordBiz {

    @Autowired
    private RiskQcAssignRecordDao assignRecordDao;

    @Override
    public int addList(List<RiskQcAssignRecord> recordList) {
        if (CollectionUtils.isEmpty(recordList)){
            return 0;
        }
        return assignRecordDao.addList(recordList);
    }

    @Override
    public int countByUniqueCode(String uniqueCode, int orderType, String startTime, String endTime) {
        if (StringUtils.isBlank(uniqueCode)){
            return 0;
        }
        Integer count = assignRecordDao.countRangeByCode(uniqueCode, orderType, startTime, endTime);
        return count == null ? 0 : count;
    }
}
