package com.shuidihuzhu.cf.risk.admin.cache.list;

import com.shuidihuzhu.cf.risk.admin.cache.CacheRefreshService;
import com.shuidihuzhu.cf.risk.admin.cache.constant.CacheRefreshTopic;
import com.shuidihuzhu.cf.risk.admin.service.list.ListDepartmentService;
import com.shuidihuzhu.cf.risk.model.admin.list.ListDepartmentDto;
import com.shuidihuzhu.common.web.util.cache.AbstractCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ListDepartmentCache extends AbstractCache<List<String>, List<ListDepartmentDto>> {

    @Resource
    private CacheRefreshService<List> cacheRefreshService;
    @Resource
    private ListDepartmentService departmentService;

    @PostConstruct
    public void init(){
        cacheRefreshService.subscribeTopic(CacheRefreshTopic.LIST_DEPARTMENT_MODIFY, (channel, message) -> {
            refresh(message);
            try {
                //为了让数据立刻刷新
                getValue(message);
            } catch (ExecutionException e) {
                log.error("", e);
            }
        }, List.class);
    }

    public List<ListDepartmentDto> listDepartmentDtos(String province, String city){
        try {
            return getValue(List.of(province, city));
        } catch (ExecutionException e) {
            log.error("", e);
        }
        return Collections.emptyList();
    }

    /**
     * @param keys constant
     * @return {@code Map<String, Map<String, RiskCityAreaDto>>}
     */
    @Override
    protected List<ListDepartmentDto> queryData(List<String> keys) {
        log.info("ListDepartmentCache setCache!");

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        List<ListDepartmentDto> listDepartmentDtos = departmentService.listByAreaLandline(keys.get(0), keys.get(1));

        stopWatch.stop();

        log.info("ListDepartmentCache size:{}, cost:{}", listDepartmentDtos.size(), stopWatch.getTotalTimeMillis());

        return listDepartmentDtos;
    }

    @Override
    public void invalidateAll() {
        super.invalidateAll();
    }

    public void refresh(List<String> keys) {
        refreshCache(keys);
    }

    public String getLocalCacheStat() {
        return getCacheStat().toString();
    }

    @Override
    protected int getExpireAfterWriteMin() {
        return 5;
    }

    @Override
    protected int getRefreshAfterWriteMin() {
        return 3;
    }

}
