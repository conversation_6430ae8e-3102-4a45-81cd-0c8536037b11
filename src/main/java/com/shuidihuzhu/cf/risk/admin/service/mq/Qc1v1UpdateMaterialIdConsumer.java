package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcSearchIndexDao;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * @author: lix<PERSON><PERSON><PERSON>
 * @create: 2020-08-20 19:53
 **/
@Service
@RocketMQListener(id = CfClientMQTagCons.CF_CLUE_FUWU_CREATE_REPORT_MSG,
        tags = CfClientMQTagCons.CF_CLUE_FUWU_CREATE_REPORT_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class Qc1v1UpdateMaterialIdConsumer implements MessageListener<Map<Long, Long>> {

    @Autowired
    private RiskQcSearchIndexDao riskQcSearchIndexDao;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Map<Long, Long>> mqMessage) {
        var payload = mqMessage.getPayload();
        if (Objects.isNull(payload)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        for (Map.Entry<Long, Long> longLongEntry : payload.entrySet()) {
            Long taskId = longLongEntry.getKey();
            Long materialId = longLongEntry.getValue();
            log.info("Qc1v1UpdateMaterialIdConsumer taskId:{},materialId:{}", taskId, materialId);
            riskQcSearchIndexDao.updateMaterialIdByTaskId(materialId, taskId, QcTypeEnum.WX_1V1.getCode());
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
