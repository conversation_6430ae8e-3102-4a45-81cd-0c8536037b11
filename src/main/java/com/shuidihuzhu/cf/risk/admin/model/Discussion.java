package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.cf.risk.model.DiscussionDTO;
import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 评议实体
 * case_id	VARCHAR	案例id
 * title	VARCHAR	标题
 * desc	VARCHAR	描述
 * images	VARCHAR	图片列表
 * reason	int	原因枚举
 * other_reason	VARCHAR	其他原因
 * other_fund_reason	VARCHAR	其他款项原因
 * status	TINYINT	决议状态（0关闭，1开启，2结束）
 * check_status	TINTINT	审核状态（0下发，1提交未审核，2通过，3拒绝）
 * close_hour	INT	关闭时间 （小时）
 * close_time	TIMESTAMP	具体关闭时间
 */
@Data
public class Discussion implements PageHasId {
    private long id;
    private int caseId;
    private String infoUuid;
    private String title;
    private String description;
    private String images;
    private int reason;
    private String otherReason;
    private String otherFundReason;
    private int status;
    private int checkStatus;
    private int closeHour;
    private Timestamp closeTime;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;
    //0代录入1不代录入
    private int insteadStatus;
    private String insteadTitle;
    private String insteadDescription;
    private String insteadImages;
    //冗余字段
    private Timestamp checkTime;
    private String refuseReason;
    private int applyTime;
    private String operator;

    public DiscussionDTO convertDiscussionDTO(){
        DiscussionDTO discussionDTO = new DiscussionDTO();
        discussionDTO.setId(id);
        discussionDTO.setCaseId(caseId);
        discussionDTO.setInfoUuid(infoUuid);
        discussionDTO.setTitle(title);
        discussionDTO.setDescription(description);
        discussionDTO.setImages(images);
        discussionDTO.setReason(reason);
        discussionDTO.setOtherReason(otherReason);
        discussionDTO.setOtherFundReason(otherFundReason);
        discussionDTO.setStatus(status);
        discussionDTO.setCheckStatus(checkStatus);
        discussionDTO.setCloseHour(closeHour);
        discussionDTO.setCloseTime(closeTime);
        discussionDTO.setRefuseReason(refuseReason);
        return discussionDTO;
    }
}
