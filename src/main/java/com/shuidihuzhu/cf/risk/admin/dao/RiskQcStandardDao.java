package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcStandardDao {

    List<RiskQcStandard> getAllByType(@Param("isUse") int isUse, @Param("standardType") int standardType,
                                      @Param("secondStandardTypes")List<Integer> secondStandardTypes);


    int addStandard(RiskQcStandard riskQcStandard);


    int updateSort(@Param("sort") int sort, @Param("id") long id);


    List<RiskQcStandard> getByIds(@Param("ids") List<Long> ids);

    int updateUseById(@Param("isUse") int isUse, @Param("id") long id);


    List<RiskQcStandard> getByParentId(@Param("parentId") long parentId, @Param("isUse") int isUse);


    RiskQcStandard getLastByLevel(@Param("level") int level, @Param("parentId") Long parentId);

    int deleteInfo(@Param("id") long id);


    RiskQcStandard getById(@Param("id") long id);

    int updateSecondaryUseStatus(@Param("useStatus")int useStatus, @Param("id")long id);


    RiskQcStandard getByName(@Param("standardName") String standardName,
                             @Param("standardType") int standardType,
                             @Param("secondStandardType") int secondStandardType,
                             @Param("level")int level);



    List<RiskQcStandard> fuzzyQuery(@Param("standardName") String standardName, @Param("level") int level);
}
