package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.client.adminpure.constants.MQTagPureAdminCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcOrderCaiLiaoCreateService;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcOrderHighRiskCreateService;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrderCreateParam;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Service
@RocketMQListener(id = CfClientMQTagCons.QC_HIGH_RISK_WORK_ORDER_CREATE,
        tags = CfClientMQTagCons.QC_HIGH_RISK_WORK_ORDER_CREATE,
        topic = MQTopicCons.CF)
@Slf4j
public class QcHighRiskWorkOrderCreateConsumer implements MessageListener<QcWorkOrderCreateParam> {

    @Autowired
    private QcOrderHighRiskCreateService qcOrderHighRiskCreateService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<QcWorkOrderCreateParam> mqMessage) {
        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.RECONSUME_LATER;
        }
        QcWorkOrderCreateParam qcWorkOrderCreateParam = mqMessage.getPayload();
        return qcOrderHighRiskCreateService.createHighRiskWorkOrder(qcWorkOrderCreateParam);
    }

}
