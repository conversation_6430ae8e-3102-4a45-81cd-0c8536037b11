package com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 质检抽检周期类型枚举
 * <AUTHOR>
 * @date 2020/6/15 21:11
 */
@AllArgsConstructor
@Getter
public enum QualitySpotScheduleTypeEnum {

    MONDAY(2, "星期一"),
    TUESDAY(3, "星期二"),
    WEDNESDAY(4, "星期三"),
    THURSDAY(5, "星期四"),
    FRIDAY(6, "星期五"),
    SATURDAY(7, "星期六"),
    SUNDAY(1, "星期日"),
    EVERY_DAY(0, "每天"),
    ;

    public static String codes2Desc(String codes){
        if (StringUtils.isBlank(codes)) {
            return "";
        }
        List<String> descList = Lists.newArrayList();
        for (String code : Splitter.on(",").trimResults().omitEmptyStrings().split(codes)) {
            descList.add(codeEnumMap.get(Integer.valueOf(code)).getDesc());
        }
        return Joiner.on(",").join(descList);
    }

    public static String codes2Desc(Collection<Integer> codes){
        if (CollectionUtils.isEmpty(codes)) {
            return "";
        }
        StringBuilder descSb = new StringBuilder();
        for (Integer code : codes) {
            QualitySpotScheduleTypeEnum typeEnum = codeEnumMap.get(Integer.valueOf(code));
            if (typeEnum == null) {
                throw new IllegalArgumentException("未知的使用场景类型");
            }
            descSb.append(typeEnum.getDesc()).append(",");
        }
        return descSb.substring(0, descSb.length() - 1);
    }

    public static String codes2JoinString(Collection<Integer> codes){
        if (CollectionUtils.isEmpty(codes)) {
            return "";
        }
        StringBuilder descSb = new StringBuilder();
        for (Integer code : codes) {
            QualitySpotScheduleTypeEnum typeEnum = codeEnumMap.get(Integer.valueOf(code));
            if (typeEnum == null) {
                throw new IllegalArgumentException("未知的使用场景类型");
            }
            descSb.append(typeEnum.getCode()).append(",");
        }
        return descSb.substring(0, descSb.length() - 1);
    }

    public static List<Integer> codeString2CodeList(String codes){
        if (StringUtils.isBlank(codes)) {
            return Collections.emptyList();
        }
        List<Integer> codeList = Lists.newArrayList();
        for (String code : Splitter.on(",").trimResults().omitEmptyStrings().split(codes)) {
            QualitySpotScheduleTypeEnum typeEnum = codeEnumMap.get(Integer.valueOf(code));
            if (typeEnum == null) {
                throw new IllegalArgumentException("未知的使用场景类型");
            }
            codeList.add(typeEnum.code);
        }
        return codeList;
    }

    private final int code;
    private final String desc;

    public static final Map<Integer, QualitySpotScheduleTypeEnum> codeEnumMap =
            Arrays.stream(values()).collect(Collectors.toMap(QualitySpotScheduleTypeEnum::getCode, Function.identity()));

}
