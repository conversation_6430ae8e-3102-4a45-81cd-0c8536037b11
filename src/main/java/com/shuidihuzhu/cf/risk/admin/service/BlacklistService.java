package com.shuidihuzhu.cf.risk.admin.service;

import com.shuidihuzhu.cf.risk.client.risk.BlacklistVerifyClient;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/7 11:28
 */
@Slf4j
@Validated
@Service
public class BlacklistService {

    @Resource
    private BlacklistVerifyClient blacklistVerifyClient;

    public List<BlacklistVerifyDto> verifyDataList(@NotNull @Size(min = 1) List<BlacklistVerifyDto> verifyDtos){
        log.info("调用黑名单验证, params:{}", verifyDtos);
        Response<List<BlacklistVerifyDto>> response = blacklistVerifyClient.verify(verifyDtos);
        if (response.notOk()) {
            log.error("调用黑名单验证失败, resp:{}", response);
            return verifyDtos;
        }
        log.info("调用黑名单验证, resp:{}", response);

        return response.getData();
    }

}
