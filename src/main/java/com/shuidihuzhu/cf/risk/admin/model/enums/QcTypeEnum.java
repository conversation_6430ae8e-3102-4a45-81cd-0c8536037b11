package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;

@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_base_info", columnName = "qc_type"),
        @DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_search_index", columnName = "qc_type")}
)
@AllArgsConstructor
public enum QcTypeEnum {

    BD(1, "线下顾问"),

    WX_1V1(2, "微信1V1"),

    CALL(3, "外呼"),

    MATERIAL(4, "材审"),

    INTERNAL_AUDIT(5, "内审"),


    ;

    @Getter
    private int code;
    @Getter
    private String desc;

}
