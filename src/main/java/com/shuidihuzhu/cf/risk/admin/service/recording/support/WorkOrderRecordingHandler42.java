package com.shuidihuzhu.cf.risk.admin.service.recording.support;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcCallRecords;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcTotalCallRecords;
import com.shuidihuzhu.cf.risk.admin.service.recording.AbsWorkOrderRecordingHandler;
import com.shuidihuzhu.cf.risk.admin.util.SpringUtil;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueCallRecordSumModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2022/3/28 下午4:43
 * qc_wx_1v1 微信1v1质检工单 录音获取 处理 存储
 */
@Component
@Slf4j
public class WorkOrderRecordingHandler42 extends AbsWorkOrderRecordingHandler {
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Override
    public List<WorkOrderRecordingModel.CallRecordModel> listCallRecordModels(long workOrderId) {
        Response<List<WorkOrderVO>> listResponse = handleFeignResponse("cfQcWorkOrderClient.queryQcByIds", cfQcWorkOrderClient.queryQcByIds(Lists.newArrayList(workOrderId)));
        if (CollectionUtils.isEmpty(listResponse.getData())) {
            return Lists.newArrayList();
        }
        Response<List<CfClueInfoModel>> cfClueInfoResponse = handleFeignResponse("cfClewtrackTaskFeignClient.listCfClueInfo", cfClewtrackTaskFeignClient.listCfClueInfo(listResponse.getData().stream().map(WorkOrderVO::getTaskId).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(cfClueInfoResponse.getData())) {
            return Lists.newArrayList();
        }
        CfClueInfoModel cfClueInfoModel = cfClueInfoResponse.getData().get(0);
        final List<CfClewCallRecordsDO> callRecords = getCallRecords(cfClueInfoModel.getTaskId());
        if (CollectionUtils.isEmpty(callRecords)) {
            return Lists.newArrayList();
        }
        return callRecords.stream()
                .map(WorkOrderRecordingModel.CallRecordModel::createByClewCallRecordDO)
//                .map(item -> WorkOrderRecordingModel.CallRecordModel(item.getMobile(),item.getCallUrl(),item.getPhoneStatus(),DateUtil.formatDateTime(item.getStartTime()), DateUtil.formatDateTime(item.getEndTime()), DateUtil.formatDateTime(item.getAnswerTime()), item.getDuration(), item.getTotalDuration()))
                .collect(Collectors.toList());
    }
//
//    public RiskQcTotalCallRecords buildRiskQcTotalCallRecords(String mis, String encryptPhone,
//                                                              String encryptSecondPhone, Date time){
//        Response<CfClueCallRecordSumModel> response = handleFeignResponse("cfClewtrackTaskFeignClient.getCallRecordSummaryByMisAndPhone",cfClewtrackTaskFeignClient.getCallRecordSummaryByMisAndPhone(mis, encryptPhone, encryptSecondPhone, time == null ? null : time.getTime()));
//        if (response.getData() == null) {
//            return null;
//        }
//        CfClueCallRecordSumModel cfClueCallRecordSumModel = response.getData();
//        return RiskQcTotalCallRecords.buildInfo(cfClueCallRecordSumModel)
//                .buildRiskQcCallRecords(getCallRecords(mis, encryptPhone, encryptSecondPhone, time));
//    }


    private List<CfClewCallRecordsDO> getCallRecords(Long taskId) {
        Response<List<CfClewCallRecordsDO>> response = handleFeignResponse("cfClewtrackTaskFeignClient.listCallRecord",
                cfClewtrackTaskFeignClient.listCallRecord(taskId));
        List<CfClewCallRecordsDO> cfClewCallRecordsDOS = response.getData();
        if (CollectionUtils.isEmpty(cfClewCallRecordsDOS)) {
            return Lists.newArrayList();
        }
        return cfClewCallRecordsDOS.stream().sorted(Comparator.comparing(CfClewCallRecordsDO::getCnoStartTime).reversed())
                .collect(Collectors.toList());
    }
//
//    private List<RiskQcCallRecords> getCallRecords(String mis, String encryptPhone, String encryptSecondPhone, Date time) {
//        Response<List<CfClewCallRecordsDO>> response = handleFeignResponse("cfClewtrackTaskFeignClient.listCallRecordByMisAndPhone",cfClewtrackTaskFeignClient.listCallRecordByMisAndPhone(mis, encryptPhone, encryptSecondPhone, time == null ? null : time.getTime()));
//        if (CollectionUtils.isEmpty(response.getData())) {
//            return Lists.newArrayList();
//        }
//        List<RiskQcCallRecords> riskQcCallRecords = Lists.newArrayList();
//        List<CfClewCallRecordsDO> cfClewCallRecordsDOS = response.getData();
//        for (CfClewCallRecordsDO cfClewCallRecordsDO : cfClewCallRecordsDOS){
//            RiskQcCallRecords records = RiskQcCallRecords.buildRecords(cfClewCallRecordsDO);
//            if (records != null) {
//                records.setMobile(SpringUtil.getBean(ShuidiCipher.class).decrypt(records.getMobile()));
//                riskQcCallRecords.add(records);
//            }
//        }
//        return riskQcCallRecords.stream().sorted(Comparator.comparing(RiskQcCallRecords::getStartTime).reversed()).collect(Collectors.toList());
//    }


    @Override
    public AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum() {
        return AiAsrDelegate.HandleTypeEnum.HANDLE_WX_1V1_QC_RECHECK;
    }

    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.qc_wx_1v1;
    }
}
