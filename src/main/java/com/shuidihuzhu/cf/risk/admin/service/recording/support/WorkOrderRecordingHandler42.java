package com.shuidihuzhu.cf.risk.admin.service.recording.support;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcCallRecords;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcTotalCallRecords;
import com.shuidihuzhu.cf.risk.admin.service.recording.AbsWorkOrderRecordingHandler;
import com.shuidihuzhu.cf.risk.admin.util.SpringUtil;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewCallRecordsDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueCallRecordSumModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 微信1v1质检工单录音处理器
 *
 * 该处理器专门负责处理微信1v1质检工单（WorkOrderType.qc_wx_1v1）的录音相关业务：
 *
 * 业务场景：
 * - 微信1v1服务是指服务人员通过微信与用户进行一对一沟通服务
 * - 在服务过程中可能涉及电话沟通，需要对这些通话录音进行质检
 * - 质检内容包括服务态度、专业性、合规性等方面
 *
 * 主要功能：
 * 1. 根据质检工单ID获取对应的微信1v1任务信息
 * 2. 查询任务相关的所有通话录音记录
 * 3. 将录音数据转换为标准格式供AI分析使用
 * 4. 支持AI语音识别和风控词汇检测
 *
 * 数据流向：
 * 质检工单 -> 微信1v1任务 -> 线索信息 -> 通话录音记录
 *
 * <AUTHOR>
 * @create 2022/3/28 下午4:43
 */
@Component
@Slf4j
public class WorkOrderRecordingHandler42 extends AbsWorkOrderRecordingHandler {

    /** 线索跟踪任务服务客户端，用于获取微信1v1任务和通话记录信息 */
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;

    /** 质检工单服务客户端，用于查询工单基本信息 */
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    /**
     * 获取微信1v1质检工单的通话录音记录列表
     *
     * 该方法专门处理微信1v1质检工单（qc_wx_1v1）的录音获取逻辑：
     * 1. 根据工单ID查询工单基本信息，获取关联的任务ID
     * 2. 根据任务ID查询线索信息，获取具体的任务详情
     * 3. 根据任务ID查询该任务下的所有通话录音记录
     * 4. 将通话录音数据转换为标准的CallRecordModel格式
     *
     * 业务背景：微信1v1质检是针对微信一对一服务场景的质检工单，
     * 需要获取服务人员与用户的通话录音进行质检分析。
     *
     * @param workOrderId 微信1v1质检工单ID
     * @return 通话录音记录模型列表，包含录音URL、通话时长、接通状态等信息
     */
    @Override
    public List<WorkOrderRecordingModel.CallRecordModel> listCallRecordModels(long workOrderId) {
        // 步骤1: 根据工单ID查询工单基本信息
        // 获取工单详情，主要是为了提取关联的任务ID（taskId）
        Response<List<WorkOrderVO>> listResponse = handleFeignResponse("cfQcWorkOrderClient.queryQcByIds",
                cfQcWorkOrderClient.queryQcByIds(Lists.newArrayList(workOrderId)));
        if (CollectionUtils.isEmpty(listResponse.getData())) {
            log.warn("listCallRecordModels: workOrderId {} not found", workOrderId);
            return Lists.newArrayList();
        }

        // 步骤2: 根据任务ID查询线索信息
        // 从工单信息中提取任务ID列表，查询对应的线索详情
        Response<List<CfClueInfoModel>> cfClueInfoResponse = handleFeignResponse("cfClewtrackTaskFeignClient.listCfClueInfo",
                cfClewtrackTaskFeignClient.listCfClueInfo(listResponse.getData().stream()
                        .map(WorkOrderVO::getTaskId)
                        .collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(cfClueInfoResponse.getData())) {
            log.warn("listCallRecordModels: no clue info found for workOrderId {}", workOrderId);
            return Lists.newArrayList();
        }

        // 步骤3: 获取第一个线索信息（通常一个工单对应一个线索）
        CfClueInfoModel cfClueInfoModel = cfClueInfoResponse.getData().get(0);

        // 步骤4: 根据任务ID查询通话录音记录
        // 获取该任务下的所有通话录音，按时间倒序排列
        final List<CfClewCallRecordsDO> callRecords = getCallRecords(cfClueInfoModel.getTaskId());
        if (CollectionUtils.isEmpty(callRecords)) {
            log.info("listCallRecordModels: no call records found for taskId {}", cfClueInfoModel.getTaskId());
            return Lists.newArrayList();
        }

        // 步骤5: 数据转换
        // 将CfClewCallRecordsDO转换为标准的CallRecordModel格式
        // 包括解密手机号、计算实际通话时长、格式化时间等
        return callRecords.stream()
                .map(WorkOrderRecordingModel.CallRecordModel::createByClewCallRecordDO)
                .collect(Collectors.toList());
    }


    /**
     * 根据任务ID获取通话录音记录列表
     *
     * 该方法从线索跟踪系统中获取指定任务的所有通话录音记录：
     * 1. 调用线索跟踪服务获取通话记录数据
     * 2. 按照通话开始时间倒序排列（最新的通话在前）
     * 3. 返回排序后的通话录音记录列表
     *
     * 业务说明：每个微信1v1任务可能包含多次通话，
     * 质检人员需要按时间顺序查看所有通话录音进行质检。
     *
     * @param taskId 微信1v1任务ID，对应线索跟踪系统中的任务标识
     * @return 按时间倒序排列的通话录音记录列表
     */
    private List<CfClewCallRecordsDO> getCallRecords(Long taskId) {
        // 调用线索跟踪服务获取指定任务的通话记录
        Response<List<CfClewCallRecordsDO>> response = handleFeignResponse("cfClewtrackTaskFeignClient.listCallRecord",
                cfClewtrackTaskFeignClient.listCallRecord(taskId));

        List<CfClewCallRecordsDO> cfClewCallRecordsDOS = response.getData();
        if (CollectionUtils.isEmpty(cfClewCallRecordsDOS)) {
            log.info("getCallRecords: no call records found for taskId {}", taskId);
            return Lists.newArrayList();
        }

        // 按通话开始时间倒序排列，确保最新的通话记录在前面
        // 这样质检人员可以优先查看最近的通话录音
        return cfClewCallRecordsDOS.stream()
                .sorted(Comparator.comparing(CfClewCallRecordsDO::getCnoStartTime).reversed())
                .collect(Collectors.toList());
    }
//
//    private List<RiskQcCallRecords> getCallRecords(String mis, String encryptPhone, String encryptSecondPhone, Date time) {
//        Response<List<CfClewCallRecordsDO>> response = handleFeignResponse("cfClewtrackTaskFeignClient.listCallRecordByMisAndPhone",cfClewtrackTaskFeignClient.listCallRecordByMisAndPhone(mis, encryptPhone, encryptSecondPhone, time == null ? null : time.getTime()));
//        if (CollectionUtils.isEmpty(response.getData())) {
//            return Lists.newArrayList();
//        }
//        List<RiskQcCallRecords> riskQcCallRecords = Lists.newArrayList();
//        List<CfClewCallRecordsDO> cfClewCallRecordsDOS = response.getData();
//        for (CfClewCallRecordsDO cfClewCallRecordsDO : cfClewCallRecordsDOS){
//            RiskQcCallRecords records = RiskQcCallRecords.buildRecords(cfClewCallRecordsDO);
//            if (records != null) {
//                records.setMobile(SpringUtil.getBean(ShuidiCipher.class).decrypt(records.getMobile()));
//                riskQcCallRecords.add(records);
//            }
//        }
//        return riskQcCallRecords.stream().sorted(Comparator.comparing(RiskQcCallRecords::getStartTime).reversed()).collect(Collectors.toList());
//    }


    /**
     * 获取AI处理类型枚举
     *
     * 返回微信1v1质检复检的AI处理类型，该类型决定了：
     * 1. 使用哪套风控词库进行敏感词检测
     * 2. AI分析的策略和重点关注的内容
     * 3. 分析结果的处理和存储方式
     *
     * HANDLE_WX_1V1_QC_RECHECK 专门用于微信1v1服务场景的质检分析，
     * 会重点关注服务质量、沟通效果、合规性等方面。
     *
     * @return AI处理类型枚举，用于微信1v1质检复检场景
     */
    @Override
    public AiAsrDelegate.HandleTypeEnum getAiHandleTypeEnum() {
        return AiAsrDelegate.HandleTypeEnum.HANDLE_WX_1V1_QC_RECHECK;
    }

    /**
     * 获取工单类型
     *
     * 返回该处理器负责处理的工单类型：微信1v1质检工单。
     * 该类型用于：
     * 1. 工单路由：系统根据工单类型选择对应的处理器
     * 2. 业务区分：不同类型的工单有不同的处理逻辑
     * 3. 权限控制：不同角色对不同类型工单的操作权限
     *
     * @return 微信1v1质检工单类型
     */
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.qc_wx_1v1;
    }
}
