package com.shuidihuzhu.cf.risk.admin.consumer;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingOrderFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.risk.admin.dao.DiscussionDao;
import com.shuidihuzhu.cf.risk.admin.dao.DiscussionRecordDao;
import com.shuidihuzhu.cf.risk.admin.dao.DiscussionStatDao;
import com.shuidihuzhu.cf.risk.admin.delegate.ShortUrlDelegate;
import com.shuidihuzhu.cf.risk.admin.model.Discussion;
import com.shuidihuzhu.cf.risk.admin.model.DiscussionRecord;
import com.shuidihuzhu.cf.risk.admin.model.DiscussionStat;
import com.shuidihuzhu.cf.risk.admin.service.MsgClientV2Service;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.client.constant.MQTagCons;
import com.shuidihuzhu.client.model.event.DiscussionEndEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RocketMQListener(id = MQTagCons.NOT_DISCUSSION_MSG,
        tags = MQTagCons.NOT_DISCUSSION_MSG,
        topic = MQTopicCons.CF)
public class DiscussionSendSmsConsmer implements MessageListener<DiscussionEndEvent>{
    @Autowired
    private DiscussionDao discussionDao;
    @Autowired
    private CrowdfundingOrderFeignClient cfOrderClient;
    @Autowired
    private DiscussionStatDao discussionStatDao;
    @Autowired
    private DiscussionRecordDao discussionRecordDao;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private CfFirstApproveClient cfFirstApproveClient;
    @Resource
    private ShortUrlDelegate shortUrlDelegate;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<DiscussionEndEvent> consumerMessage) {
        log.info("mq接收");
        DiscussionEndEvent e = consumerMessage.getPayload();
        Discussion discussion = discussionDao.findById(e.getDiscussionId());
        CrowdfundingInfo crowdfundingInfo = crowdfundingFeignClient.getCaseInfoById((int) e.getCaseId()).getData();
        //获取患者姓名
        String patientRealName = "";
        CfFirsApproveMaterial cfFirsApproveMaterial = cfFirstApproveClient.selectFirstApproveByCaseId((int) e.getCaseId()).getData();
        if(cfFirsApproveMaterial != null) {
            patientRealName = cfFirsApproveMaterial.getPatientRealName();
        }
        //给捐款人发消息
        List<Long> userIds = getNotDiscussionUserId(discussion);
        List<UserInfoModel> userInfoModels = userInfoDelegateService.getUserInfoByUserIdBatch(userIds);
        log.info("mq接收发短信 start userinfo:",userInfoModels);
        String url = crowdfundingInfo.getInfoId();
        for(UserInfoModel userInfoModel : userInfoModels){
            Map<Integer, String> wxParams = Maps.newHashMap();
            wxParams.put(1, url);
            wxParams.put(2, crowdfundingInfo.getTitle());
            wxParams.put(3, patientRealName);
            wxParams.put(5, discussion.getTitle());
            Map<Integer, String> msgParams = Maps.newHashMap();
            msgParams.put(1, url);
            msgParams.put(2, crowdfundingInfo.getTitle());
            msgParams.put(3, patientRealName);
            msgParams.put(4, getTrackUrl(crowdfundingInfo.getInfoId()));
            msgParams.put(5,discussion.getTitle());
            Map<String, Map<Integer, String>> msgMap = Maps.newHashMap();
            msgMap.put(userInfoModel.getCryptoMobile(), msgParams);
            Map<Long, Map<Integer, String>> wxMap = Maps.newHashMap();
            wxMap.put(userInfoModel.getUserId(),wxParams);
            msgClientV2Service.sendWxParamsMsg("PVP0673",wxMap);
            msgClientV2Service.sendSmsParamsMsg("YJE1021", msgMap, true);
            log.info("发短信 userid:",userInfoModel);
        }
        return ConsumeStatus.CONSUME_SUCCESS;

    }

    //获取未评议人id
    private List<Long> getNotDiscussionUserId(Discussion discussion){
        DiscussionStat discussionStat = discussionStatDao.findById(discussion.getId());
        List<DiscussionRecord> discussionRecords = discussionRecordDao.getListByDiscussionId(discussionStat.getDiscussionId());
        List<Long> userIds = discussionRecords.stream()
                .map(DiscussionRecord::getUserId)
                .collect(Collectors.toList());
        List<Long> absentUserIds = listCompare(getCfOrderUserIds(discussionStat.getCaseId()), userIds);
        return absentUserIds;
    }
    //获取捐款人id
    private List<Long> getCfOrderUserIds(int caseId) {
        int offset = 0;
        int limit = 1000;
        FeignResponse<List<CrowdfundingOrder>> orderFeignResponse = cfOrderClient.getListByInfoId(caseId,
                offset, limit);
        List<CrowdfundingOrder> crowdFundingOrderList = Lists.newArrayList();
        if (orderFeignResponse.ok()) {
            crowdFundingOrderList = orderFeignResponse.getData();
        }
        Set<Long> userIds = Sets.newHashSet();
        while (!org.apache.commons.collections4.CollectionUtils.isEmpty(crowdFundingOrderList)) {
            List<Long> uids = crowdFundingOrderList.stream()
                    .map(CrowdfundingOrder::getUserId)
                    .collect(Collectors.toList());
            userIds.addAll(uids);

            offset += crowdFundingOrderList.size();
            orderFeignResponse = cfOrderClient.getListByInfoId(caseId, offset, limit);
            if (orderFeignResponse.ok()) {
                crowdFundingOrderList = orderFeignResponse.getData();
            }
        }
        return Lists.newArrayList(userIds);
    }
    private List<Long> listCompare(List<Long> userIds, List<Long> discussionUserIds){
        Map<Long,Integer> map = new HashMap<>(discussionUserIds.size());
        List<Long> absentUserIds = Lists.newArrayList();
        for(long userId : discussionUserIds){
            map.put(userId, 1);
        }
        for(long userId : userIds){
            if(map.get(userId) == null){
                absentUserIds.add(userId);
            }
        }
        return absentUserIds;
    }
    private String getTrackUrl(String infoUuid) {
        String url = "https://www.shuidichou.com/cf/discussion/detail/" + infoUuid;
        String shortUrl = shortUrlDelegate.process(url);
        if (StringUtils.isNotEmpty(shortUrl)) {
            return shortUrl;
        }
        return StringUtils.EMPTY;
    }
}
