package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcHospitalDeptService;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeMqModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentNameChangeMqModel;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RocketMQListener(id = "cf-risk-admin-GROWTHTOOL_CLASSIFY_DEPARTMENT_NAME_CHANGE_APPROVE",
        tags = "GROWTHTOOL_CLASSIFY_DEPARTMENT_NAME_CHANGE_APPROVE",
        group = "cf-risk-admin-GROWTHTOOL_CLASSIFY_DEPARTMENT_NAME_CHANGE_APPROVE",
        topic = MQTopicCons.CF)
@Slf4j
public class DeptModifyConsumer extends MaliBaseMQConsumer<DepartmentNameChangeMqModel> implements MessageListener<DepartmentNameChangeMqModel> {

    @Autowired
    private QcHospitalDeptService qcHospitalDeptService;

    @Override
    protected boolean handle(ConsumerMessage<DepartmentNameChangeMqModel> consumerMessage) {
        DepartmentNameChangeMqModel payload = consumerMessage.getPayload();
        log.info("DeptModifyConsumer payload:{}", payload);
        qcHospitalDeptService.onDeptChange(payload);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
