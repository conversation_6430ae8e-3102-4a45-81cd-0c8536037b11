package com.shuidihuzhu.cf.risk.admin.service;

import com.shuidihuzhu.cf.risk.admin.dao.RiskCarBrandDao;
import com.shuidihuzhu.cf.risk.admin.model.vo.CarBrandVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/17 19:58
 */
@Slf4j
@Service
public class CarBrandService {

    @Resource
    private RiskCarBrandDao carBrandDao;

    public List<CarBrandVo> listByLimit50(String carName){
        return carBrandDao.listByLimit50(carName).stream().map(CarBrandVo::new).collect(Collectors.toList());
    }

}
