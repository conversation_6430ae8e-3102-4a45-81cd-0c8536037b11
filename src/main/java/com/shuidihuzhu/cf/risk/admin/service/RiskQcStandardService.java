package com.shuidihuzhu.cf.risk.admin.service;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandExtBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.enums.*;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskMaterialQcStandardVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcFirstStandardVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcStandardDetailVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcStandardVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandardLogBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandardPropertyBiz;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardExt;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardLog;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/6/12
 */
@Service
@Slf4j
public class RiskQcStandardService {
    @Autowired
    private RiskQcStandBiz riskQcStandBiz;
    @Autowired
    private RiskQcStandExtBiz riskQcStandExtBiz;
    @Autowired
    private RiskQcStandardPropertyBiz riskQcStandardPropertyBiz;
    @Autowired
    private RiskQcStandardLogBiz riskQcStandardLogBiz;
    @Autowired
    private SeaAccountService seaAccountService;


    /**
     * 返回一级&&二级配置
     *
     * @param isUse
     * @param standardType
     * @param parentId
     * @return
     */
    public List<RiskQcStandardVo> getAllStandard(int isUse, int standardType, long parentId,
                                                 int useScene, List<Integer> secondStandardType) {
        List<RiskQcStandard> riskQcStandards =
                riskQcStandBiz.getAllByType(isUse, standardType, secondStandardType);
        return parentId <= 0 ? getIsUseInfo(riskQcStandards, useScene) : getInfoByPatient(riskQcStandards, parentId, isUse, useScene);
    }

    private List<RiskQcStandardVo> getIsUseInfo(List<RiskQcStandard> riskQcStandards, int useScene) {
        List<RiskQcStandard> firstRiskQcStandards = riskQcStandards.stream().filter(t -> t.getLevel() == RiskQcStandardLevelEnum.ONE_LEVEL.getCode()).collect(Collectors.toList());
        List<RiskQcStandard> secondRiskQcStandards = riskQcStandards.stream().filter(t -> t.getLevel() == RiskQcStandardLevelEnum.TWO_LEVEL.getCode()).collect(Collectors.toList());
        List<Long> standardIds = secondRiskQcStandards.stream().map(RiskQcStandard::getId).collect(Collectors.toList());
        List<RiskQcStandardExt> riskQcStandardExts = riskQcStandExtBiz.getByStandardIds(standardIds);
        riskQcStandardExts = useScene == 0 ? riskQcStandardExts : riskQcStandardExts.stream()
                .filter(t -> t.getUseScene() == useScene).collect(Collectors.toList());
        return mergeInfo(firstRiskQcStandards, secondRiskQcStandards, riskQcStandardExts);
    }

    /**
     * 根据parentId返回标准
     *
     * @param riskQcStandards
     * @param parentId
     * @param isUse
     * @return
     */
    private List<RiskQcStandardVo> getInfoByPatient(List<RiskQcStandard> riskQcStandards, long parentId,
                                                    int isUse, int useScene) {
        RiskQcStandard firstQcStandard = riskQcStandBiz.getById(parentId);
        if (firstQcStandard == null) {
            return Lists.newArrayList();
        }
        List<RiskQcStandard> secondRiskQcStandards = riskQcStandards.stream().filter(t -> t.getParentId() == parentId && t.getIsUse() == isUse).collect(Collectors.toList());
        List<Long> standardIds = secondRiskQcStandards.stream().map(RiskQcStandard::getId).collect(Collectors.toList());
        List<RiskQcStandardExt> riskQcStandardExts = riskQcStandExtBiz.getByStandardIds(standardIds);
        riskQcStandardExts = useScene == 0 ? riskQcStandardExts : riskQcStandardExts.stream()
                .filter(t -> t.getUseScene() == useScene).collect(Collectors.toList());
        return mergeInfo(Lists.newArrayList(firstQcStandard), secondRiskQcStandards, riskQcStandardExts);
    }

    /**
     * 返回一级配置
     *
     * @param isUse
     * @param standardType
     * @return
     */
    public List<RiskQcFirstStandardVo> getFirstStandard(int isUse, int standardType, int secondStandardType) {
        List<RiskQcStandard> riskQcStandards =
                riskQcStandBiz.getAllByType(isUse, standardType, secondStandardType == 0 ? null : List.of(secondStandardType));
        List<RiskQcStandard> firstRiskQcStandards = riskQcStandards.stream().filter(t -> t.getLevel() == RiskQcStandardLevelEnum.ONE_LEVEL.getCode()).sorted(Comparator.comparing(RiskQcStandard::getSort)).collect(Collectors.toList());
        List<RiskQcFirstStandardVo> riskQcFirstStandardVos = Lists.newArrayList();
        firstRiskQcStandards.forEach(riskQcStandard -> {
            riskQcFirstStandardVos.add(new RiskQcFirstStandardVo(riskQcStandard.getId(), riskQcStandard.getStandardName()));
        });
        return riskQcFirstStandardVos;
    }


    //合并数据
    private List<RiskQcStandardVo> mergeInfo(List<RiskQcStandard> firstRiskQcStandards,
                                             List<RiskQcStandard> secondRiskQcStandards,
                                             List<RiskQcStandardExt> riskQcStandardExts) {
        List<RiskQcStandardVo> riskQcStandardVos = Lists.newArrayList();
        List<RiskQcStandardProperty> riskQcStandardProperties = riskQcStandardPropertyBiz.getAll();
        Map<Long, RiskQcStandardProperty> riskQcStandardPropertyMap = riskQcStandardProperties.stream().collect(Collectors.toMap(RiskQcStandardProperty::getId, Function.identity(), (old, news) -> news));
        for (RiskQcStandard firstRiskQcStandard : firstRiskQcStandards) {
            List<RiskQcStandard> secondMergeInfoList = secondRiskQcStandards.stream().filter(t -> t != null && t.getParentId() == firstRiskQcStandard.getId()).collect(Collectors.toList());
            // if (CollectionUtils.isNotEmpty(secondMergeInfoList)) {
            riskQcStandardVos.add(new RiskQcStandardVo(firstRiskQcStandard.getStandardName(), firstRiskQcStandard.getId(), buildVo(secondMergeInfoList, riskQcStandardExts, riskQcStandardPropertyMap), firstRiskQcStandard.getSort(),
                    firstRiskQcStandard.getStandardType(), firstRiskQcStandard.getSecondStandardType()));
            // }
        }
        riskQcStandardVos =
                riskQcStandardVos.stream().sorted(Comparator.comparing(RiskQcStandardVo::getSort)).collect(Collectors.toList());
        return riskQcStandardVos;
    }

    //构建数据
    private List<RiskQcStandardDetailVo> buildVo(List<RiskQcStandard> secondRiskQcStandards,
                                                 List<RiskQcStandardExt> riskQcStandardExts,
                                                 Map<Long, RiskQcStandardProperty> riskQcStandardPropertyMap) {
        List<RiskQcStandardDetailVo> riskQcStandardDetailVos = Lists.newArrayList();
        for (RiskQcStandard secondRiskQcStandard : secondRiskQcStandards) {
            List<RiskQcStandardExt> riskQcStandardExtList =
                    riskQcStandardExts.stream().filter(t -> t != null && t.getQcStandardId() == secondRiskQcStandard.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(riskQcStandardExtList)) {
                RiskQcStandardExt riskQcStandardExt = riskQcStandardExtList.get(0);
                RiskQcStandardProperty riskQcStandardProperty = riskQcStandardPropertyMap.get(riskQcStandardExt.getFirstPropertyId());
                String firstProperty = riskQcStandardProperty != null ? riskQcStandardProperty.getProperty() : "";
                RiskQcStandardProperty qcStandardProperty = riskQcStandardPropertyMap.get(riskQcStandardExt.getSecondPropertyId());
                String secondProperty = qcStandardProperty != null ? qcStandardProperty.getProperty() : "";
                RiskQcStandardDetailVo e = new RiskQcStandardDetailVo(secondRiskQcStandard.getId(), secondRiskQcStandard.getStandardName(), firstProperty + "-" + secondProperty, riskQcStandardExt.getUseScene(), secondRiskQcStandard.getSort());
                e.setUseSceneList(riskQcStandardExtList.stream().map(RiskQcStandardExt::getUseScene).collect(Collectors.toList()));
                riskQcStandardDetailVos.add(e);
            }
        }
        riskQcStandardDetailVos =
                riskQcStandardDetailVos.stream().sorted(Comparator.comparing(RiskQcStandardDetailVo::getSort)).collect(Collectors.toList());
        return riskQcStandardDetailVos;
    }

    /**
     * 上下移动
     *
     * @param upId
     * @param downId
     * @param type
     * @return
     */
    public boolean upOrDown(long upId, long downId, int type) {
        List<RiskQcStandard> riskQcStandards = riskQcStandBiz.getByIds(Lists.newArrayList(upId, downId));
        if (CollectionUtils.size(riskQcStandards) != Lists.newArrayList(upId, downId).size()) {
            return false;
        }
        int upSort = riskQcStandards.stream().filter(t -> t.getId() == downId).collect(Collectors.toList()).get(0).getSort();
        int downSort = riskQcStandards.stream().filter(t -> t.getId() == upId).collect(Collectors.toList()).get(0).getSort();
        int result = riskQcStandBiz.updateSort(upSort, upId);
        int result1 = riskQcStandBiz.updateSort(downSort, downId);
        if (result > 0 && result1 > 0) {
            if (RiskQcStandardOperateType.UP.getDescription().equals(RiskQcStandardOperateType.findOfCode(type))) {
                addLog(RiskQcStandardOperateType.UP, downId);
            } else {
                addLog(RiskQcStandardOperateType.DOWN, upId);
            }
        }
        return result > 0 && result1 > 0;
    }

    /**
     * 修改使用状态
     *
     * @param id
     * @param status
     * @return
     */
    public Response<Boolean> changeUseStatus(long id, int status) {
        if (CollectionUtils.isNotEmpty(riskQcStandBiz.getByParentId(id, RiskQcStandardUseEnum.IS_USE.getCode()))) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_STANDARD_LABEL);
        }
        int result = riskQcStandBiz.updateUseById(status, id);
        if (status == RiskQcStandardUseEnum.IS_USE.getCode() && result > 0) {
            addLog(RiskQcStandardOperateType.ENABLE, id);
        } else if (status == RiskQcStandardUseEnum.IS_NOT_USE.getCode() && result > 0) {
            addLog(RiskQcStandardOperateType.DISABLE, id);
        }
        return NewResponseUtil.makeSuccess(result > 0);
    }

    /**
     * 增加一级标准
     *
     * @param standardName
     * @return
     */
    public Response<Integer> addFirstStandard(String standardName, int standardType, int secondStandardType) {
        if (riskQcStandBiz.getByName(standardName, standardType, secondStandardType, RiskQcStandardLevelEnum.ONE_LEVEL.getCode())
                != null) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_FIRST_STANDARD_REPEAT);
        }
        RiskQcStandard riskQcStandard = riskQcStandBiz.getLastByLevel(RiskQcStandardLevelEnum.ONE_LEVEL.getCode(), null);
        int sort = 1;
        if (riskQcStandard != null) {
            sort = riskQcStandard.getSort() + 1;
        }
        RiskQcStandard qcStandard = new RiskQcStandard(standardName, standardType, secondStandardType, 0, RiskQcStandardLevelEnum.ONE_LEVEL.getCode(), sort, RiskQcStandardUseEnum.IS_USE.getCode());
        int result = riskQcStandBiz.addStandard(qcStandard);
        if (result > 0) {
            addLog(RiskQcStandardOperateType.ADD, qcStandard.getId());
        }
        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 增加二级标准接口
     *
     * @param parentId
     * @param standardName
     * @param firstProperty
     * @param secondProperty
     * @param useScene
     * @return
     */
    public Response<Integer> addStandard(long parentId, String standardName,
                                         long firstProperty, long secondProperty, String useScene, int standardType,
                                         int secondStandardType) {
        RiskQcStandard standard = riskQcStandBiz.getById(parentId);
        if (standard == null || standard.getIsUse() == RiskQcStandardUseEnum.IS_NOT_USE.getCode()) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_FIRST_STANDARD_NOT_USE);
        }
        if (riskQcStandBiz.getByName(standardName, standardType, secondStandardType, RiskQcStandardLevelEnum.TWO_LEVEL.getCode())
                != null) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_SECOND_STANDARD_REPEAT);
        }
        RiskQcStandard riskQcStandard = riskQcStandBiz.getLastByLevel(RiskQcStandardLevelEnum.TWO_LEVEL.getCode(), parentId);
        int sort = 1;
        if (riskQcStandard != null) {
            sort = riskQcStandard.getSort() + 1;
        }
        List<Integer> useSceneList = Arrays.stream(useScene.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        int result = 0;
        RiskQcStandard qcStandard = new RiskQcStandard(standardName, standardType, secondStandardType, parentId, RiskQcStandardLevelEnum.TWO_LEVEL.getCode(), sort, RiskQcStandardUseEnum.IS_USE.getCode());
        riskQcStandBiz.addStandard(qcStandard);
        for (Integer useScenes : useSceneList) {
            result = riskQcStandExtBiz.add(qcStandard.getId(), firstProperty, secondProperty, useScenes);
            if (result > 0) {
                addLog(RiskQcStandardOperateType.ADD, qcStandard.getId());
            }
            sort++;
        }
        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 返回所有属性
     *
     * @return
     */
    public Map<String, List<RiskQcStandardProperty>> getProperty(int useScene) {
        Map<String, List<RiskQcStandardProperty>> result = Maps.newHashMap();
        List<RiskQcStandardProperty> riskQcStandardProperties = riskQcStandardPropertyBiz.getAll();
        riskQcStandardProperties =
                riskQcStandardProperties.stream().filter(t -> t.getPropertyType() == useScene).collect(Collectors.toList());
        result.put("firstLevel", riskQcStandardProperties.stream().filter(t -> t.getLevel() == RiskQcStandardLevelEnum.ONE_LEVEL.getCode()).collect(Collectors.toList()));
        result.put("secondLevel", riskQcStandardProperties.stream().filter(t -> t.getLevel() == RiskQcStandardLevelEnum.TWO_LEVEL.getCode()).collect(Collectors.toList()));
        return result;
    }

    /**
     * 删除配置
     *
     * @param id
     * @return
     */
    public Response<Integer> deleteStandard(long id) {
        RiskQcStandard riskQcStandard = riskQcStandBiz.getById(id);
        if (riskQcStandard == null) {
            return NewResponseUtil.makeSuccess(0);
        }
        if (riskQcStandard.getSecondaryUseStatus() > 0) {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_STANDARD_DELETE);
        }
        int result = riskQcStandBiz.deleteInfo(id);
        if (result > 0) {
            addLog(RiskQcStandardOperateType.DELETE, id);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 添加日志
     *
     * @param riskQcStandardOperateType
     * @param qcStandardId
     */
    private void addLog(RiskQcStandardOperateType riskQcStandardOperateType, long qcStandardId) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        String operation = seaAccountService.getOrganization(adminUserId) + seaAccountService.getName(adminUserId);
        riskQcStandardLogBiz.addInfo(adminUserId, operation, riskQcStandardOperateType.getDescription(), qcStandardId, riskQcStandardOperateType.getCode());
    }

    //查询日志
    public List<RiskQcStandardLog> getLog(long qcStandardId) {
        return riskQcStandardLogBiz.getLogByStandardId(qcStandardId);
    }


    public Map<String, RiskMaterialQcStandardVo> getAll(int useScene) {
        List<RiskQcStandardVo> riskQcStandardVos = this.getAllStandard(RiskQcStandardUseEnum.IS_USE.getCode(),
                RiskQcStandardTypeEnum.MATERIAL.getCode(), 0, useScene, RiskQcSecondStandardTypeEnum.CODE_LIST);
        return this.buildVo(riskQcStandardVos);
    }

    public Map<String, RiskMaterialQcStandardVo> buildVo(List<RiskQcStandardVo> riskQcStandardVos) {
        Map<String, RiskMaterialQcStandardVo> maps = Maps.newHashMap();
        if (CollectionUtils.isEmpty(riskQcStandardVos)) {
            return maps;
        }
        RiskQcSecondStandardTypeEnum.ENUM_LIST.forEach((code, riskQcSecondStandardTypeEnum) -> {
            List<RiskQcStandardVo> filterInfos = riskQcStandardVos.stream().filter(t -> t.getSecondStandardType() == code).collect(Collectors.toList());
            maps.put(riskQcSecondStandardTypeEnum.toString(), new RiskMaterialQcStandardVo(code, riskQcSecondStandardTypeEnum.getDescription(), filterInfos));
        });
        return maps;
    }

    public List<RiskQcStandard> fuzzyQuery(String secondQuestionDesc) {
        return riskQcStandBiz.fuzzyQuery(secondQuestionDesc.trim(), RiskQcStandardLevelEnum.TWO_LEVEL.getCode());
    }

    public Response<Integer> multiAddScene(String standardIds, String useScene) {
        if (StringUtils.isBlank(standardIds)) {
            return NewResponseUtil.makeSuccess(0);
        }
        String[] idArr = StringUtils.split(standardIds, ",");
        int success = 0;
        for (String idStr : idArr) {
            Long id = Long.valueOf(StringUtils.trim(idStr));
            RiskQcStandard standard = riskQcStandBiz.getById(id);
            if (standard == null) {
                log.warn("multiAddScene id not found {}", id);
                continue;
            }
            List<RiskQcStandardExt> extList = riskQcStandExtBiz.getByStandardIds(Lists.newArrayList(id));
            long firstPropertyId = extList.get(0).getFirstPropertyId();
            long secondPropertyId = extList.get(0).getSecondPropertyId();
            log.info("multiAddScene {} ,f {}, s {}", standard, firstPropertyId, secondPropertyId);
            List<Integer> useSceneList = Arrays.stream(useScene.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            for (Integer scene : useSceneList) {
                riskQcStandExtBiz.add(id, firstPropertyId, secondPropertyId, scene);
            }
            success++;
        }
        return NewResponseUtil.makeSuccess(success);
    }
}
