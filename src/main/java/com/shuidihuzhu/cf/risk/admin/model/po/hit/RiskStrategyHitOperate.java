package com.shuidihuzhu.cf.risk.admin.model.po.hit;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
public class RiskStrategyHitOperate {
    /**
     * 主键
     */
    private Long id;

    /**
     * 风控策略命中记录id
     */
    private Long hitRecordId;

    /**
     * 风险核实结果，RiskHandleResultEnum
     */
    private Byte result;

    /**
     * 处理动作，RiskHandleActionEnum
     */
    private String action;

    /**
     * 操作人
     */
    private Long operateId;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 按钮id 可以是-1
     */
    private String radio;

    /**
     * 重复案例id
     */
    private Integer repeatCaseId;

    /**
     * 主动停止筹款原因
     */
    private String activeStop;

    /**
     *  其他原因
     */
    private String otherReason;

}