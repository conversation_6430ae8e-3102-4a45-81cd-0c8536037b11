package com.shuidihuzhu.cf.risk.admin.configuration;

import com.shuidihuzhu.common.jdbc.readwrite.separation.group.ReadWriteSeparationDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * @author: cuikexiang
 */
@Configuration
public class ReadWriteSeparationDataSourceConfiguration {

    @Resource(name = RiskAdminDS.CF_RISK_DATASOURCE)
    private DataSource riskMasterDatasource;

    @Resource(name = RiskAdminDS.CF_RISK_SLAVE_DATASOURCE)
    private DataSource riskSlaveDatasource;




    @Bean(name = RiskAdminDS.CF_RISK_RW)
    public DataSource riskDatasource() {
        return new ReadWriteSeparationDataSource(riskSlaveDatasource,riskMasterDatasource);
    }
}
