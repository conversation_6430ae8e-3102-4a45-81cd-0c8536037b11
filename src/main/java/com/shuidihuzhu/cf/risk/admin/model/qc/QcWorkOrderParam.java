package com.shuidihuzhu.cf.risk.admin.model.qc;

import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-19 17:57
 **/
@Data
public class QcWorkOrderParam {

    //------------------------
    //------公共搜索参数-------
    //------------------------
    /**
     * 质检对象
     */
    private int qcType;
    /**
     * 工单类型
     */
    private int orderType;
    /**
     * 组织架构
     */
    private String organization;
    /**
     * 被质检人姓名
     */
    private String qcByName;
    /**
     * 创建时间开始范围
     */
    private long createStartTime;
    /**
     * 创建时间结束范围
     */
    private long createEndTime;
    /**
     * 工单id
     */
    private long workOrderId;
    /**
     * 案例id
     */
    private long caseId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 操作人id
     */
    private long operatorId;
    /**
     * 分配类型
     */
    private int assignType;
    /**
     * 处理结果
     */
    private int handleResult;
    /**
     * 质检结果
     */
    private int qcResult;
    /**
     * 二级质检结果
     */
    private int qcResultSecond;
    /**
     * 问题类型
     */
    private int questionType;
    /**
     * 处理时间开始范围
     */
    private long handleStartTime;
    /**
     * 处理时间结束范围
     */
    private long handleEndTime;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 待录入id
     */
    private long materialId;

    /**
     * 二级问题类型
     */
    private int secondQuestionType;

    /**
     * 录音类型
     */
    private int callType;

    //------------------------
    //------微信1v1搜索参数-----
    //------------------------
    /**
     * 登记手机号
     */
    private String registerMobile;
    /**
     * 服务环节
     */
    private int serviceStage;
    /**
     * 工作内容
     */
    private int jobContent;


    //------------------------
    //------外呼搜索参数-----
    //------------------------
    /**
     * 外呼服务一级标签
     */
    private long firstLevelLabel;
    /**
     * 外呼服务二级标签
     */
    private long twoLevelLabel;
    /**
     * 外呼服务线索渠道
     */
    private String callCluesChannel;
    /**
     * 外呼服务任务状态
     */
    private int callTaskStatus;

    private int firstPropertyId;


    //------------------------
    //------材审搜索参数-----
    //------------------------
    /**
     * 材审工单通话状态
     */
    private int callStatus;
    /**
     * 材审工单处理状态
     */
    private int materialHandleResult;

    @ApiModelProperty("疑似无效质检工单 {0: 未确定, 1: 无效, 2: 有效}")
    private Integer seemInvalidOrder;

    @ApiModelProperty("是否远程发起 {1: 是, 0: 不是}")
    private Integer remoteRaise;

    private int pageNum;
    private int pageSize;

    @ApiModelProperty("模糊查询语音转移结果")
    private String asrResult;

    @ApiModelProperty("是否有科室归一结果")
    private Boolean deptClassifySuccess;

    @ApiModelProperty("科室质检-院区搜索")
    private String deptHospitalName;

    public static QcWorkOrderParam buildParam(WorkOrderListParam workOrderListParam, int pageNum,
                                              String mobile, String qcByName){
        QcWorkOrderParam qcWrokOrderParam = new QcWorkOrderParam();
        qcWrokOrderParam.setPageSize(workOrderListParam.getPageSize());
        qcWrokOrderParam.setPageNum(pageNum);
        qcWrokOrderParam.setOperatorId(workOrderListParam.getUserId());
        qcWrokOrderParam.setOrderType(workOrderListParam.getOrderType());
        qcWrokOrderParam.setHandleResult(StringUtils.isNotBlank(workOrderListParam.getHandleResult()) ?
                Integer.parseInt(workOrderListParam.getHandleResult()) : HandleResultEnum.later_doing.getType());
        qcWrokOrderParam.setRegisterMobile(mobile);
        qcWrokOrderParam.setQcByName(qcByName);
        qcWrokOrderParam.setQcType(QcTypeEnum.WX_1V1.getCode());
        qcWrokOrderParam.setServiceStage(-1);
        qcWrokOrderParam.setJobContent(-1);
        return qcWrokOrderParam;
    }
}

