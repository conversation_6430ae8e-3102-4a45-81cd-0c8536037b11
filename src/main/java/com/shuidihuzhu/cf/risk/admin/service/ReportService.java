package com.shuidihuzhu.cf.risk.admin.service;

import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPublicSentimentInfoDao;
import com.shuidihuzhu.cf.risk.admin.model.vo.MarkReportVO;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.admin.client.AdminReportClient;
import com.shuidihuzhu.client.cf.admin.model.AdminMarkReport;
import com.shuidihuzhu.client.cf.admin.model.AdminMarkReportParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class ReportService {

    @Autowired
    private AdminReportClient adminReportClient;
    @Autowired
    private RiskPublicSentimentInfoDao infoDao;

    public int markReport(MarkReportVO markReportVO){
        if (markReportVO == null){
            return 0;
        }
        long psId = markReportVO.getPsId();
        AdminMarkReportParam adminMarkReportParam = markReportVO.convertParam();
        adminMarkReportParam.setOperatorId(Math.toIntExact(ContextUtil.getAdminLongUserId()));
        Response<Map<String, Integer>> response = adminReportClient.markReport(adminMarkReportParam);
        if (!response.notOk() && response.getData() != null){
            Integer reportId = response.getData().get("reportId");
            return infoDao.updateReportIdById(reportId == null ? 0 : reportId, adminMarkReportParam.getCaseId(), psId);
        }
        log.info("标记举报失败 psId:{}", psId);
        return 0;
    }

    public AdminMarkReport getReport(long psId){
        RiskPublicSentimentInfo info = infoDao.getInfoById(psId);
        if (info == null){
            return null;
        }
        if (info.getCaseId() > 0 && info.getReportId() > 0){
            return getReport(info.getCaseId(), info.getReportId());
        }
        return null;
    }

    public AdminMarkReport getReport(int caseId, int reportId){
        Response<AdminMarkReport> adminMarkReportResponse = adminReportClient.getMarkReportInfo(caseId, reportId);
        if (adminMarkReportResponse.notOk() || adminMarkReportResponse.getData() == null){
            return null;
        }
        return adminMarkReportResponse.getData();
    }
}
