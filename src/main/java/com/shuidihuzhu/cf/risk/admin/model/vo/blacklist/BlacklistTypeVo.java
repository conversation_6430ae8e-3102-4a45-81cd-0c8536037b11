package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.risk.admin.model.enums.CommonStatusEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistType;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@NoArgsConstructor
@ApiModel(description = "黑名单类型")
public class BlacklistTypeVo {

    public BlacklistTypeVo(RiskBlacklistType riskBlacklistType, List<Long> actions) {
        BeanUtils.copyProperties(riskBlacklistType, this);
        this.status = CommonStatusEnum.fromCode(riskBlacklistType.getStatus()).getDesc();
        this.updateTime = DateUtil.getDate2LStr(riskBlacklistType.getUpdateTime());
        this.actionNames = Joiner.on("，").join(LimitActionEnum.ids2Names(actions));
    }

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("限制动作名称")
    private String typeName;

    @ApiModelProperty("限制动作")
    private String actionNames;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("最新操作人")
    private String operateName;
    @ApiModelProperty("最新操作时间")
    private String updateTime;

}
