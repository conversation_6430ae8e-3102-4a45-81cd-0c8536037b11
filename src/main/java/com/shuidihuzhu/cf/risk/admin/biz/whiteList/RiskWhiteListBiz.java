package com.shuidihuzhu.cf.risk.admin.biz.whiteList;

import com.github.pagehelper.Page;
import com.shuidihuzhu.cf.risk.admin.model.query.whiteList.WhiteListQuery;
import com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
public interface RiskWhiteListBiz {

    int save(RiskWhiteListDto riskWhiteListDto);

    int update(RiskWhiteListDto riskWhiteListDto);

    int updateExpireTime(Date expireTime, long id, String operator);

    RiskWhiteListDto getById(long id);

    Page<RiskWhiteListDto> getList(WhiteListQuery whiteListQuery);

    RiskWhiteListDto getByCipherMobileAndType(String mobile, byte type);

    RiskWhiteListDto getByCipherIdCardAndType(String idCard, byte type);

}
