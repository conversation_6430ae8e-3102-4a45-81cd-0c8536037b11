package com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling;

import lombok.Data;

import java.util.Date;

@Data
public class RiskQualitySpotStrategyLog {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 关联策略id
     */
    private Long strategyId;

    /**
     * 操作类型：1 新建策略；2 弃用；3 启用；4 编辑启用时间；5 编辑适用范围
     */
    private Integer operateType;

    /**
     * 最新操作人id
     */
    private Long operateId;

    /**
     * 最新操作人
     */
    private String operateName;

    /**
     * 是否删除0 否，1 是
     */
    private Byte isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}