package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealResultModel;

import java.util.List;

public interface RiskQcAppealResultBiz {

    int addInfo(long workOrderId, String appealInfo, int appealResult, long qcId);

    RiskQcAppealResultModel getByWorkOrderId(long workOrderId);

    List<RiskQcAppealResultModel> findByWorkOrderIds(List<Long> workOrderIdList);
}

