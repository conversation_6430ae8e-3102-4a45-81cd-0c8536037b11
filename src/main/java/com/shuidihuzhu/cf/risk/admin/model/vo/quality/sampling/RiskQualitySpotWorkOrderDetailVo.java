package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig;
import com.shuidihuzhu.client.cf.workorder.model.vo.RiskQualitySpotRuleDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/21
 */
@Data
@ApiModel("工单分配保存详情")
public class RiskQualitySpotWorkOrderDetailVo {

    @ApiModelProperty("规则的id")
    private long id;

    @NotNull(message = "操作人id不能为空")
    @ApiModelProperty("操作人id")
    private Long operatorId;

    @NotNull(message = "场景id不能为空")
    @ApiParam(value = "场景id 线下顾问:3 微信1v1:4")
    private int scene;

    /**
     * {@link com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotWorkOrderDistributionEnum}
     */
    //@NotNull(message = "工单分配类型不能为空")
    @Min(value = 0, message = "工单分配类型不能小于1")
    @ApiModelProperty("工单分配类型 1:随机分单 2:按照配置分单")
    private int workOrderDistributionType;

    /**
     * {@link com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotWorkOrderRangeLimitEnum}
     */
    //@NotNull(message = "领单范围限制不能为空")
    @Min(value = 0, message = "领单范围限制不能小于1")
    @ApiModelProperty("领单范围限制 1: 只领取  2：有限")
    private int getWorkOrderRangeLimit;

    @ApiModelProperty("规则list")
    private List<RiskQualitySpotRuleAdminDetailVo> riskQualitySpotRuleDetailVos = Lists.newArrayList();


    public static RiskQualitySpotWorkOrderDetailVo buildVo(RiskQualitySpotWorkOrderUserConfig userConfig) {
        RiskQualitySpotWorkOrderDetailVo detailVo = new RiskQualitySpotWorkOrderDetailVo();
        if (userConfig == null){
            return detailVo;
        }
        detailVo.setId(userConfig.getId());
        detailVo.setScene(userConfig.getScene());
        detailVo.setWorkOrderDistributionType(userConfig.getDistributionType());
        detailVo.setGetWorkOrderRangeLimit(userConfig.getRangeLimit());
        if (StringUtils.isNotBlank(userConfig.getRuleInfo())){
            detailVo.setRiskQualitySpotRuleDetailVos(JSON.parseArray(userConfig.getRuleInfo(), RiskQualitySpotRuleAdminDetailVo.class));
        }
        return detailVo;
    }
}
