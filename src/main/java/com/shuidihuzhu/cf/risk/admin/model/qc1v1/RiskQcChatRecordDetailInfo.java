package com.shuidihuzhu.cf.risk.admin.model.qc1v1;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskQcChatRecordDetailInfo {
    /**
     * 聊天时间
     */
    private Date chatTime;
    /**
     * 聊天内容
     */
    private String content;
    /**
     * 类型
     */
    private int contentType;
    /**
     * 关键词
     */
    private List<RiskQcChatKeywordsInfo> riskQcChatKeywordsInfos = new ArrayList<>();

    /*
    text(文本)/image(图片)/revoke(撤回)/agree(同意会话聊天内容)/disagree(不同意会话聊天内容)/voice(语音)/video(视频)/card(名片)/location(位置)/emotion(表情)/file(文件)/link(链接)/weapp(小程序)
     */
    private String contentTag;

    /**
     * 是否撤回
     */
    private int revoke;

    public static RiskQcChatRecordDetailInfo buildInfo(ChatMessageVo chatMessageVo) {
        if (chatMessageVo == null) {
            return null;
        }
        RiskQcChatRecordDetailInfo riskQcChatRecordDetailInfo = new RiskQcChatRecordDetailInfo();
        riskQcChatRecordDetailInfo.setChatTime(new Timestamp(chatMessageVo.getMsgTime()));
        riskQcChatRecordDetailInfo.setRevoke("recall".equals(chatMessageVo.getSendStatus()) ? 1 : 0);
        final String contentType = chatMessageVo.getContentType();
        switch (contentType){
            case "voice":
            case "video":
                riskQcChatRecordDetailInfo.setContent(MapUtils.isEmpty(chatMessageVo.getContentExt()) ? null :
                        String.valueOf(chatMessageVo.getContentExt().getOrDefault("url", "")));
                break;
            case "link":
            case "weapp":
            case "location":
            case "agree":
            case "disagree":
            case "card":
            case "emotion":
                riskQcChatRecordDetailInfo.setContent(JSON.toJSONString(chatMessageVo.getContentExt()));
                break;
            case "file":
                riskQcChatRecordDetailInfo.setContent(MapUtils.isEmpty(chatMessageVo.getContentExt()) ? null :
                        String.valueOf(chatMessageVo.getContentExt().getOrDefault("filename", "")));
                break;
            default:
                riskQcChatRecordDetailInfo.setContent(chatMessageVo.getContent());
                break;
        }

        riskQcChatRecordDetailInfo.setContentType(chatMessageVo.getSenderType());
        riskQcChatRecordDetailInfo.setContentTag(chatMessageVo.getContentType());
        return riskQcChatRecordDetailInfo;
    }

    public RiskQcChatRecordDetailInfo buildRiskQcChatKeywordsInfo(List<RiskQcChatKeywordsInfo> riskQcChatKeywordsInfos) {
        this.riskQcChatKeywordsInfos = riskQcChatKeywordsInfos;
        return this;
    }
}
