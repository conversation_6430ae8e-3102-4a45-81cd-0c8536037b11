package com.shuidihuzhu.cf.risk.admin.biz.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/12
 */
public interface RiskQualitySpotTypeBiz {

    List<RiskQualitySpotType> findAllParentById(long typeId);

    List<RiskQualitySpotType> getListByParentId(long parentId);

    List<RiskQualitySpotType> findById(List<Long> ids);
}
