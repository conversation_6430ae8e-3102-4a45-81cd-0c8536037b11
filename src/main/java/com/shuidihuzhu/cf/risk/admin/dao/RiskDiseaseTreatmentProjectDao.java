package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskDiseaseTreatmentProjectDao {

    int clearAll();

    int sync(List<RiskDiseaseTreatmentProject> treatmentProjects);

    List<RiskDiseaseTreatmentProject> getAll();

    int deleteByDiseaseId(@Param("diseaseId") long id);

    int saveList(List<RiskDiseaseTreatmentProject> treatmentProjects);


    List<RiskDiseaseTreatmentProject> findByDiseaseId(@Param("diseaseId") long diseaseId);

    int deleteByIdList(List<Long> needDeleteIdList);

    int update(RiskDiseaseTreatmentProject treatmentProject);

    List<RiskDiseaseTreatmentProject> findByProjectName(@Param("projectName") String projectName);

}
