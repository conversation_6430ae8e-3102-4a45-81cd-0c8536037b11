package com.shuidihuzhu.cf.risk.admin.model.enums;

public enum RiskQcDisponseActionEnum {
    //
    SUBMIT(1, "提交"),
    LATER_DISPOSE(2, "稍后处理"),
    ;

    int code;
    String description;

    RiskQcDisponseActionEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code) {
        for (RiskQcDisponseActionEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return "";
    }
}
