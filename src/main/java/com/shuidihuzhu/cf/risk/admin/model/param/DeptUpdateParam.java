package com.shuidihuzhu.cf.risk.admin.model.param;

import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeItemModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeptUpdateParam {

    private long operatorId;
    private long workOrderId;
    private List<DepartmentChangeItemModel> departmentDetails;
}
