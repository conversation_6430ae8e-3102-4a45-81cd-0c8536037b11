package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * @Auther: subing
 * @Date: 2020/6/19
 */
@Data
public class InitiateVo {
    private Integer remoteRaise;
    private Integer communicationWay;
    private Integer accidentType;
    private Integer patientIdentity;
    private Integer accidentDuty;
    private String accidentDetail;
    private String paidAmount;
    private String willPayAmount;
    private Integer companyCrowdfundingPermit;
    private Integer hasToPatientOwn;
    private Long id;
    private Integer weChatFriendNum;
    private Integer hasCriminal;
    private Integer criminalType;
    private String otherCriminal;


    public static InitiateVo buildVo(PreposeMaterialModel.MaterialInfoVo materialInfoVo){
        InitiateVo initiateVo = new InitiateVo();
        BeanUtils.copyProperties(materialInfoVo, initiateVo);
        initiateVo.setPaidAmount(PreposeMaterialVo.buildValue(materialInfoVo.getPaidAmount()));
        if (materialInfoVo.getWillPayAmount() != null){
            initiateVo.setWillPayAmount(PreposeMaterialVo.buildValue(materialInfoVo.getWillPayAmount()));
        }else if (materialInfoVo.getWillPayAmountArea() !=null){
            PreposeMaterialModel.FinancialPayAmountAreaEnum financialAssetsAmountAreaEnum = valueOfCode(materialInfoVo.getWillPayAmountArea());
            initiateVo.setWillPayAmount(financialAssetsAmountAreaEnum == null ? "" : financialAssetsAmountAreaEnum.getDesc());
        }

        return initiateVo;
    }

    public static PreposeMaterialModel.FinancialPayAmountAreaEnum valueOfCode(int code) {
        PreposeMaterialModel.FinancialPayAmountAreaEnum[] values = PreposeMaterialModel.FinancialPayAmountAreaEnum.values();
        for (PreposeMaterialModel.FinancialPayAmountAreaEnum payAmountAreaEnum : values) {
            if (payAmountAreaEnum.getCode() == code) {
                return payAmountAreaEnum;
            }
        }
        return null;
    }
}
