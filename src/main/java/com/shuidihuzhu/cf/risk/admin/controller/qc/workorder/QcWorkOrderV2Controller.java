package com.shuidihuzhu.cf.risk.admin.controller.qc.workorder;


import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.risk.admin.model.constant.QcOrderConst;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcWorkOrderCreateService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api("新工单接口")
@Slf4j
@RestController
@RequestMapping("/api/cf-risk-admin/qc/work-order/v2")
public class QcWorkOrderV2Controller {

    @Resource
    private QcWorkOrderCreateService qcWorkOrderCreateService;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @ApiOperation(value = "手动触发案例初审通过创建质检工单逻辑")
    @RequiresPermission("qc:trigger-create-order")
    @PostMapping(path = "/trigger-create-order")
    public OperationResult<Void> triggerCreateOrder(@ApiParam("案例id") @RequestParam int caseId,
                                                    @RequestParam int userId) throws Exception {
        return qcWorkOrderCreateService.promoteQcWorkOrderCreate(caseId, userId);
    }

    @ApiOperation(value = "查询该案例所有材审相关质检工单列表")
    @PostMapping(path = "/get-qc-material-order-list-by-case-id")
    public Response<List<WorkOrderVO>> getQcMaterialOrderListByCaseId(@ApiParam("案例id") @RequestParam int caseId,
                                                                      @RequestParam int userId) throws Exception {
        return cfWorkOrderClient.queryByCaseAndTypes(caseId, QcOrderConst.QC_MATERIAL_ORDER_OF_CASE_LIST);
    }


    @ApiOperation(value = "绑定工单和通话记录关系")
    @PostMapping(path = "/bind-workOrderId-recordingUniqueId")
    public Response<Void> bindWorkOrderIdRecordingUniqueId(@ApiParam("workOrderId") @RequestParam Long workOrderId,
                                                                        @ApiParam("recordingUniqueId") @RequestParam String recordingUniqueId) {

        Long userId  =  ContextUtil.getAdminLongUserId();
        return qcWorkOrderCreateService.bindWorkOrderIdRecordingUniqueId(workOrderId,recordingUniqueId,userId);
    }



}
