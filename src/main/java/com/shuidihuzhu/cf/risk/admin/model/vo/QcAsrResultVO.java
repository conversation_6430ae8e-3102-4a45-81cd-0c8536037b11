package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QcAsrResultVO {
    @ApiModelProperty("业务唯一id")
    private Long materialId;
    @ApiModelProperty("业务处理方式")
    private AiAsrDelegate.HandleTypeEnum handleTypeEnum;

    @ApiModelProperty("工单id")
    private Long workOrderId;

    private String id;

    private Result result;

    private String input_url;

    @Data
    public static class Result {
        private List<Sentence> sentences;
    }

    @Data
    public static class Sentence {
        private String text;
        private int begin_time;
        private int end_time;
        private String speaker_type;
    }

}
