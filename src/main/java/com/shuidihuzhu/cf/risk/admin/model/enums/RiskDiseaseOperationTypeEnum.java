package com.shuidihuzhu.cf.risk.admin.model.enums;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
public enum  RiskDiseaseOperationTypeEnum {
    SAVE(1, "新增"),
    UPDATE(2, "编辑"),
    DELETE(3, "删除"),
    ;

    int code;
    String description;

    RiskDiseaseOperationTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (RiskDiseaseOperationTypeEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
