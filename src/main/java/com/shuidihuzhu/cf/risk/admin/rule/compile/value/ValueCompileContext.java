package com.shuidihuzhu.cf.risk.admin.rule.compile.value;

import com.shuidihuzhu.cf.risk.admin.rule.model.Arithmetic;
import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionData;
import com.shuidihuzhu.cf.risk.admin.rule.enums.ArithOp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/19 15:14
 */
@Slf4j
@Component
public class ValueCompileContext {

    @Resource(name = "constantValueCompile")
    private AbstractCompileChain valueCompile;

    public String spliceArithmetic(CriterionData criterionData){
        String value = valueCompile.compileValue(criterionData);
        Arithmetic arithmetic = criterionData.getArithmetic();
        if (arithmetic != null) {
            value = value + doArithmetic(arithmetic);
        }
        return value;
    }

    private String doArithmetic(Arithmetic arithmetic){
        ArithOp arithOp = arithmetic.getOp();
        String snippet = valueCompile.compileValue(arithmetic.getValue());
        String arithResult = arithOp.getScript() + snippet;
        Arithmetic nextArith = arithmetic.getArithmetic();
        if (nextArith != null) {
            arithResult = arithResult + doArithmetic(nextArith);
        }

        return arithResult;
    }

}
