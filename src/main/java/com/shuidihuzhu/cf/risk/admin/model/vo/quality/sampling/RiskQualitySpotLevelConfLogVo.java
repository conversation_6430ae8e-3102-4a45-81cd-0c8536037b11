package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConfLog;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "抽检量级修改历史实体")
public class RiskQualitySpotLevelConfLogVo {

    public RiskQualitySpotLevelConfLogVo(RiskQualitySpotLevelConfLog levelConfLog) {
        this.setModifyContent(levelConfLog.getModifyContent());
        this.setModifyReason(levelConfLog.getModifyReason());
        this.setOperateName(levelConfLog.getOperateName());
        this.setParseTime(DateUtil.getDate2LStr(levelConfLog.getParseTime()));
        this.setUpdateTime(DateUtil.getDate2LStr(levelConfLog.getUpdateTime()));
    }

    @ApiModelProperty("修改原因备注")
    private String modifyReason;

    @ApiModelProperty("修改情况")
    private String modifyContent;

    @ApiModelProperty("开始生效时间")
    private String parseTime;

    @ApiModelProperty("修改人")
    private String operateName;

    @ApiModelProperty("修改时间")
    private String updateTime;

}