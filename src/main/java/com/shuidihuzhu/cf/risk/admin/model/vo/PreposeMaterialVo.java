package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.AuthenticityIndicator;
import com.shuidihuzhu.cf.enums.partner.IdCardType;
import com.shuidihuzhu.cf.risk.admin.model.ProposeDiseaseNameInfo;
import com.shuidihuzhu.cf.risk.admin.util.MaskUtil;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Auther: subing
 * @Date: 2020/6/18
 */
@Data
public class PreposeMaterialVo {
    private String patientName;
    private Integer patientIdCardType;
    private String patientIdCard;
    private NumberMaskVo patientIdCardMask;
    private String diseaseDetail;
    private String hospital;
    private String department;
    private String diseaseName;
    private String houseSellingAmount;
    private Integer houseSellingCount;
    private Integer carNum;
    private Integer carSellingCount;
    private Integer carHasSell;
    private String carAmountArea;
    private String carSellingAmount;
    private String homeIncomeArea;
    private Integer hasFinancialAssets;
    private Integer financialAssetsAmountArea;
    private String financialAssetsAmount;
    private boolean homeOwningAmountStatus;
    private String homeOwningAmount;
    private Integer homeOwningAmountArea;
    private Integer livingAllowance;
    private Integer hasPoverty;
    private Integer hasRaise;
    private String raiseAmount;
    private String remainAmount;
    private Integer useForMedical;
    private Integer medicalInsurance;
    private Integer medicalInsuranceRateArea;
    private Integer hasPersonalInsurance;
    private Integer hasCarInsurance;
    private Integer govRelief;
    private Integer otherRelief;
    private String title;
    private String content;
    private String pictureUrl;
    private String raiseMobile;
    private NumberMaskVo raiseMobileMask;
    private Integer raisePatientRelation;
    private String raiseName;
    private String selfCryptoIdcard;
    private NumberMaskVo selfCryptoIdcardMask;
    private String preAuditImageUrl;
    private List<Integer> riskLabels;
    private String expectCost;
    private String targetAmount;
    private String povertyImg;
    private String allowanceImg;
    private String treatmentInfo;
    private String rpTreatmentInfo;
    private String patientRpDisease;
    private Integer selfOccupiedHouse;
    private Integer medicalInsuranceType;
    private Integer specialReport;
    private Integer specialReportType = 0;
    private String specialReportReason;
    private String raiseCityName;

    //其他房
    /**
     * 房屋数量
     */
    private Integer houseNum;
    /**
     * 房产总价值，手动填写
     */
    private Integer houseValue;
    /**
     * 房产净值（其他房产）
     */
    private Integer houseNetWorthArea;
    /**
     * @see PreposeMaterialModel.HouseSellingAmountAreaEnum
     * 房产总价值区间
     */
    private String houseAmountArea;
    /**
     * 变卖状态
     * @see PreposeMaterialModel.HasSellEnum
     * 未变卖、已变卖、变卖中
     */
    private Integer houseHasSell;


    //自建房房
    /**
     * {@link PreposeMaterialModel.HouseNumEnum}
     */
    @ApiModelProperty("房屋数量")
    private Integer selfHouseNum;
    /**
     * {@link PreposeMaterialModel.HouseSellingAmountAreaEnum}
     */
    @ApiModelProperty("房产总价值区间")
    private String selfHouseAmountArea;

    @ApiModelProperty("手动填写的房屋价值")
    private Integer selfHouseValue;
    /**
     * {@link PreposeMaterialModel.HasSellEnum}
     */
    @ApiModelProperty("是否变卖")
    private Integer selfHouseHasSell;
    /**
     * {@link PreposeMaterialModel.HouseNumEnum}
     */
    @ApiModelProperty("变卖中房产数量")
    private Integer selfHouseSellingCount;
    /**
     * {@link PreposeMaterialModel.HouseSellingAmountAreaEnum}
     */
    @ApiModelProperty("变卖中房产价值区间")
    private String selfHouseSellingAmountArea;

    @ApiModelProperty("手动填写的房屋价值")
    private Integer selfHouseSellingAmount;

    @ApiModelProperty("是否有自建房")
    private boolean isSelfHouse;

    @ApiModelProperty("患者婚姻状况 PatientMarriedEnum")
    private Integer patientMaritalStatus;
    @ApiModelProperty("患者有几位子女已结婚")
    private Integer marriedChildrenCount;
    @ApiModelProperty("患者已婚子女状况 EconomicSituationEnum")
    private List<Integer> marriedChildrenStatus;
    @ApiModelProperty("患者父母状况 EconomicSituationEnum")
    private List<Integer> patientParentStatus;
    @ApiModelProperty("净值阈值 单位：万")
    private Integer netWorthThreshold;

    @ApiModelProperty("增信信息(新)")
    private AuthenticityIndicator authenticityIndicator;



    public static PreposeMaterialVo buildPreposeVo(PreposeMaterialModel.MaterialInfoVo materialInfoVo, com.shuidihuzhu.cf.enhancer.utils.MaskUtil maskUtil) {
        if (Objects.isNull(materialInfoVo)) {
            return null;
        }
        PreposeMaterialVo preposeMaterialVo = new PreposeMaterialVo();
        if (materialInfoVo.getVersion() >= PreposeMaterialModel.VersionEnum.VERSION_4_0.getVersionCode()) {
            preposeMaterialVo.setSelfHouse(true);
        }
        BeanUtils.copyProperties(materialInfoVo, preposeMaterialVo);
        preposeMaterialVo.setPovertyImg(StringUtils.trimToNull(preposeMaterialVo.getPovertyImg()));
        preposeMaterialVo.setAllowanceImg(StringUtils.trimToNull(preposeMaterialVo.getAllowanceImg()));
        preposeMaterialVo.setExpectCost(buildValue(materialInfoVo.getExpectCost()));
        preposeMaterialVo.setTargetAmount(buildValue(materialInfoVo.getTargetAmount()));
        int amount = materialInfoVo.getFinancialAssetsAmount() == null ? 0 : materialInfoVo.getFinancialAssetsAmount();
        preposeMaterialVo.setFinancialAssetsAmount(buildValue(amount));
        preposeMaterialVo.setRaiseAmount(buildValue(materialInfoVo.getRaiseAmount()));
        preposeMaterialVo.setRemainAmount(buildValue(materialInfoVo.getRemainAmount()));
        if (materialInfoVo.getHasDebt() != null && materialInfoVo.getHasDebt() == 1) {
            if (materialInfoVo.getHomeOwningAmount() != null && materialInfoVo.getHomeOwningAmount() > 0) {
                preposeMaterialVo.setHomeOwningAmount(buildValue(materialInfoVo.getHomeOwningAmount()));
            } else if (materialInfoVo.getHomeOwningAmountArea() != null) {
                preposeMaterialVo.setHomeOwningAmount(PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(materialInfoVo.getHomeOwningAmountArea()).getDesc());
            }
        }
        updateInfo(preposeMaterialVo, materialInfoVo);
        List<Integer> riskLabels = materialInfoVo.getRiskLabels();
        if (CollectionUtils.isNotEmpty(riskLabels) && riskLabels.contains(PreposeMaterialModel.MaterialRiskLabel.GOVERNMENT_BAILOUT.getCode())) {
            preposeMaterialVo.setGovRelief(1);
        }
        if (CollectionUtils.isNotEmpty(riskLabels) && riskLabels.contains(PreposeMaterialModel.MaterialRiskLabel.HELP_FROM_OTHER_CHANNELS.getCode())) {
            preposeMaterialVo.setOtherRelief(1);
        }
        preposeMaterialVo.setOtherRelief(materialInfoVo.getHasSupply());
        preposeMaterialVo.setHomeOwningAmountStatus(materialInfoVo.getHasDebt() != null && materialInfoVo.getHasDebt() == 1);
        int type  = Optional.ofNullable(preposeMaterialVo.getRaisePatientRelation()).orElse(-1);
        if (type == PreposeMaterialModel.RaiserPatientRelation.SELF.getCode()
                ||type == PreposeMaterialModel.RaiserPatientRelation.SELF_VERSION_2.getCode() ||
                type == PreposeMaterialModel.RaiserPatientRelation.SELF_VERSION_3.getCode()) {
            preposeMaterialVo.setSelfCryptoIdcard(Objects.nonNull(preposeMaterialVo.getPatientIdCardType()) && preposeMaterialVo.getPatientIdCardType() == 1 ? preposeMaterialVo.getPatientIdCard() : preposeMaterialVo.getSelfCryptoIdcard());
        }
        preposeMaterialVo.setTreatmentInfo(getJsonInfo(materialInfoVo.getTreatmentInfo()));
        preposeMaterialVo.setRpTreatmentInfo(getJsonInfo(materialInfoVo.getRpTreatmentInfo()));

        preposeMaterialVo.setPatientMaritalStatus(materialInfoVo.getPatientMaritalStatus());
        preposeMaterialVo.setMarriedChildrenCount(materialInfoVo.getMarriedChildrenCount());
        preposeMaterialVo.setMarriedChildrenStatus(materialInfoVo.getMarriedChildrenStatus());
        preposeMaterialVo.setPatientParentStatus(materialInfoVo.getPatientParentStatus());
        preposeMaterialVo.setNetWorthThreshold(materialInfoVo.getNetWorthThreshold());

        preposeMaterialVo.setPatientIdCardMask(maskUtil.buildByDecryptStrAndType(preposeMaterialVo.getPatientIdCard(), DesensitizeEnum.IDCARD));
        preposeMaterialVo.setPatientIdCard(null);
        // 出生证情况
        if(StringUtils.isBlank(preposeMaterialVo.getPatientIdCardMask().getMaskNumber())){
            preposeMaterialVo.getPatientIdCardMask().setMaskNumber(preposeMaterialVo.getPatientIdCard());
        }
        preposeMaterialVo.setRaiseMobileMask(maskUtil.buildByDecryptPhone(preposeMaterialVo.getRaiseMobile()));
        preposeMaterialVo.setRaiseMobile(null);
        preposeMaterialVo.setSelfCryptoIdcardMask(maskUtil.buildByDecryptStrAndType(preposeMaterialVo.getSelfCryptoIdcard(), DesensitizeEnum.IDCARD));
        preposeMaterialVo.setSelfCryptoIdcard(null);
        // 出生证情况
        if(StringUtils.isBlank(preposeMaterialVo.getSelfCryptoIdcardMask().getMaskNumber())){
            preposeMaterialVo.getSelfCryptoIdcardMask().setMaskNumber(preposeMaterialVo.getSelfCryptoIdcard());
        }


        return preposeMaterialVo;
    }

    public static String buildValue(Integer amount) {
        if (amount != null && amount > 0) {
            BigDecimal bg = new BigDecimal(amount);
            bg = bg.divide(BigDecimal.valueOf(10000));
            double f = bg.setScale(2, RoundingMode.HALF_DOWN).doubleValue();
            return f + "万元";
        }
        return null;
    }

    private static void updateInfo(PreposeMaterialVo preposeMaterialVo, PreposeMaterialModel.MaterialInfoVo materialInfoVo) {
        if (materialInfoVo.getCarValue() == null) {
            preposeMaterialVo.setCarAmountArea(materialInfoVo.getCarAmountArea() != null ? PreposeMaterialModel.CarSellingAmountAreaEnum.valueOfCode(materialInfoVo.getCarAmountArea()).getDesc() : null);
        } else {
            preposeMaterialVo.setCarAmountArea(buildValue(materialInfoVo.getCarValue()));
        }
        if (materialInfoVo.getHouseValue() == null) {
            preposeMaterialVo.setHouseAmountArea(materialInfoVo.getHouseAmountArea() == null ? null : PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(materialInfoVo.getHouseAmountArea()).getDesc());
        } else {
            preposeMaterialVo.setHouseAmountArea(buildValue(materialInfoVo.getHouseValue()));
        }
        //自建房价值区间转换字符串
        if (materialInfoVo.getSelfHouseValue() == null) {
            preposeMaterialVo.setSelfHouseAmountArea(materialInfoVo.getSelfHouseAmountArea() == null ? null : PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(materialInfoVo.getSelfHouseAmountArea()).getDesc());
        } else {
            preposeMaterialVo.setSelfHouseAmountArea(buildValue(materialInfoVo.getSelfHouseValue()));
        }
        //自建房变卖价值区间转换字符串
        if (materialInfoVo.getSelfHouseSellingAmount() == null) {
            preposeMaterialVo.setSelfHouseSellingAmountArea(materialInfoVo.getSelfHouseSellingAmountArea() == null ? null : PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(materialInfoVo.getSelfHouseSellingAmountArea()).getDesc());
        } else {
            preposeMaterialVo.setSelfHouseSellingAmountArea(buildValue(materialInfoVo.getSelfHouseSellingAmount()));
        }
        preposeMaterialVo.setCarSellingAmount(materialInfoVo.getCarSellingAmount() == null && materialInfoVo.getCarSellingAmountArea() != null ? PreposeMaterialModel.CarSellingAmountAreaEnum.valueOfCode(materialInfoVo.getCarSellingAmountArea()).getDesc() : buildValue(materialInfoVo.getCarSellingAmount()));
        if (materialInfoVo.getHouseSellingAmount() == null) {
            preposeMaterialVo.setHouseSellingAmount(materialInfoVo.getHouseSellingAmountArea() == null ? null :
                    PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(materialInfoVo.getHouseSellingAmountArea()).getDesc());
        } else {
            preposeMaterialVo.setHouseSellingAmount(buildValue(materialInfoVo.getHouseSellingAmount()));
        }
        if (materialInfoVo.getHomeIncome() == null) {
            preposeMaterialVo.setHomeIncomeArea(materialInfoVo.getHomeIncomeArea() == null ? null : PreposeMaterialModel.HomeIncomeAreaEnum.valueOfCode(materialInfoVo.getHomeIncomeArea()).getDesc());
        } else {
            preposeMaterialVo.setHomeIncomeArea(buildValue(materialInfoVo.getHomeIncome()));
        }
    }


    private static String getJsonInfo(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(content);
        String jsonString = jsonObject.getString("info");
        if (StringUtils.isBlank(jsonString)){
            return null;
        }
        List<ProposeDiseaseNameInfo> proposeDiseaseNameInfos =
                JSONObject.parseObject(jsonString, new TypeReference<List<ProposeDiseaseNameInfo>>(){});
        if (CollectionUtils.isEmpty(proposeDiseaseNameInfos)){
            return null;
        }
        StringBuffer stringBuffer = new StringBuffer();
        proposeDiseaseNameInfos.forEach(proposeDiseaseNameInfo -> {
            stringBuffer.append("【").append(proposeDiseaseNameInfo.getSpecialDiseaseName()).append("】");
            if (StringUtils.isNotBlank(proposeDiseaseNameInfo.getShowValue()) && StringUtils.contains(proposeDiseaseNameInfo.getShowValue(), ";")){
                String[] strings =  StringUtils.trimToEmpty(proposeDiseaseNameInfo.getShowValue()).split(";");
                stringBuffer.append(strings[1]);
            }else {
                stringBuffer.append(StringUtils.trimToEmpty(proposeDiseaseNameInfo.getShowValue()));
            }
            stringBuffer.append("\n");
        });
        return stringBuffer.toString();
    }

}
