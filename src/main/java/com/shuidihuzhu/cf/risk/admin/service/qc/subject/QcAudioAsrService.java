package com.shuidihuzhu.cf.risk.admin.service.qc.subject;

import com.shuidihuzhu.cf.risk.admin.model.vo.AsrSentenceVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcAsrResultVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcVideoVo;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface QcAudioAsrService {
    Response<Void> handleBDRecord(long workOrderId, boolean useZiyanAsr);

    Response<Void> onAudioAsrShuidiCallback(QcAsrResultVO resultVO);

    Response<Void> onAudioAsrCallback(QcAsrResultVO resultVO);

    void handleWorkOrderRecording(Long workOrderId, Integer workOrderType);

    void handleWorkOrderRecording(Long workOrderId);

    List<AsrSentenceVO> getNewSentenceVOList(int length, List<AsrSentenceVO> sentenceVOList);
}
