package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.cf.risk.admin.model.enums.RiskRuleResult;

import com.shuidihuzhu.cf.risk.admin.model.enums.RiskRuleCondition;
import com.shuidihuzhu.client.auth.saas.annotation.NoLoginRequired;
import lombok.Data;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-29 11:37
 **/
@NoLoginRequired
@Data
public class RiskRuleModel {
    /**
     * 当前值
     */
    private int currentVaule;
    /**
     * 阈值
     */
    private List<Integer> threshold;
    /**
     * 计算方式
     */
    private RiskRuleCondition riskRuleCondition;
    /**
     * 计算结果
     */
    private RiskRuleResult riskRuleResult;

    public RiskRuleModel(int currentVaule, List<Integer> threshold, RiskRuleCondition riskRuleCondition,
                         RiskRuleResult riskRuleResult) {
        this.currentVaule = currentVaule;
        this.threshold = threshold;
        this.riskRuleCondition = riskRuleCondition;
        this.riskRuleResult = riskRuleResult;
    }
}
