package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData;
import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Data
@ApiModel("疾病库信息Vo")
public class RiskDiseaseInfoVO implements PageHasId {

    @ApiModelProperty("疾病库id")
    private long id;

    @ApiModelProperty("疾病类别名称")
    private String diseaseClassName ;

    @ApiModelProperty("疾病医学名称")
    private String medicalName ;

    @ApiModelProperty("疾病归一规则")
    private String diseaseMergeRule ;

    @ApiModelProperty("用户常用口语")
    private String normalName ;

    @ApiModelProperty("类型")
    private int raiseType;

    @ApiModelProperty("治疗方案描述")
    private String treatMethodList ;

    @ApiModelProperty("是否删除0否1是")
    private int isDelete;

    //@ApiModelProperty("治疗方案详情")
    //private List<RiskDiseaseTreatmentProjectVO> treatmentProjectList;

    public static RiskDiseaseInfoVO build(RiskDiseaseData riskDiseaseData) {
        if (riskDiseaseData == null) {
            return null;
        }
        RiskDiseaseInfoVO diseaseInfoVO = new RiskDiseaseInfoVO();
        diseaseInfoVO.setNormalName(riskDiseaseData.getNormalName());
        diseaseInfoVO.setDiseaseMergeRule(riskDiseaseData.getDiseaseMergeRule());
        diseaseInfoVO.setMedicalName(riskDiseaseData.getMedicalName());
        diseaseInfoVO.setRaiseType(riskDiseaseData.getRaiseType());
        diseaseInfoVO.setDiseaseClassName(riskDiseaseData.getDiseaseClassName());
        diseaseInfoVO.setId(riskDiseaseData.getId());
        diseaseInfoVO.setIsDelete(riskDiseaseData.getIsDelete());
        return diseaseInfoVO;
    }
}
