package com.shuidihuzhu.cf.risk.admin.serviceexception;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
public class ServiceExceptionUtils {

    public static ServiceException createViewException(String msg) {
        return new ViewServiceException(msg);
    }

    public static <T> Response<T> handleResponse(Supplier<Response<T>> function) {
        Response<T> r;
        try {
            r = function.get();
        } catch (ViewServiceException viewServiceException) {
            log.warn("handleResponse viewException functionName {}", function.getClass().getSimpleName(), viewServiceException);
            return NewResponseUtil.makeFail(viewServiceException.getViewMsg());
        }

        return r;
    }
}
