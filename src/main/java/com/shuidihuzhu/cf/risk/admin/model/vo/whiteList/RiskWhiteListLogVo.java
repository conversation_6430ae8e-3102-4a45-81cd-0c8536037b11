package com.shuidihuzhu.cf.risk.admin.model.vo.whiteList;

import com.shuidihuzhu.cf.risk.admin.model.dto.whitelist.RiskWhiteListLog;
import com.shuidihuzhu.cf.risk.admin.model.enums.list.WhiteOperateTypeEnum;
import com.shuidihuzhu.common.web.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("白名单日志")
public class RiskWhiteListLogVo {

    @ApiModelProperty("操作人姓名")
    private String operateName;

    @ApiModelProperty("操作时间")
    private String operateTime;

    @ApiModelProperty("操作人备注")
    private String comment;

    public static RiskWhiteListLogVo  buildVo(RiskWhiteListLog listLog) {
        RiskWhiteListLogVo listLogVo = new  RiskWhiteListLogVo();
        listLogVo.setOperateName(listLog.getOperateName());
        WhiteOperateTypeEnum operateTypeEnum =  WhiteOperateTypeEnum.findOfCode(listLog.getOperateType());
        listLogVo.setComment(operateTypeEnum == null ? "" : operateTypeEnum.getDesc() + listLog.getOtherInfo());
        listLogVo.setOperateTime(DateUtil.formatDateTime(listLog.getCreateTime()));
        return listLogVo;
    }

}
