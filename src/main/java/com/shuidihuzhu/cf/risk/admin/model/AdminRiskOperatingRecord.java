package com.shuidihuzhu.cf.risk.admin.model;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020-05-15
 **/
@Data
public class AdminRiskOperatingRecord {

    private long id;
    private int infoId;
    private long workOrderId;
    private int adminUserId;
    private int bizType;
    private int risk;
    private String riskLevel;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

}
