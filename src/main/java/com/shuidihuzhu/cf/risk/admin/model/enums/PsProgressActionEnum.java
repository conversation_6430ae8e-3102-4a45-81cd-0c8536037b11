package com.shuidihuzhu.cf.risk.admin.model.enums;

public enum  PsProgressActionEnum {
    PUBLISH(1, "发布"),
    INFO_ENTERING(2, "信息录入"),
    FOLLOW(3, "发酵跟进"),
    ;


    private int code;
    private String description;

    PsProgressActionEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (PsProgressActionEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
