package com.shuidihuzhu.cf.risk.admin.model.dto.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistClassify;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/17 11:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RiskBlacklistClassifyEnhance extends RiskBlacklistClassify {

    private List<String> namePath;

}
