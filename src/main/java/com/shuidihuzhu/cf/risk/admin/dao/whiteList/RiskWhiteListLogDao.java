package com.shuidihuzhu.cf.risk.admin.dao.whiteList;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.dto.whitelist.RiskWhiteListLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskWhiteListLogDao {

    int saveLog(RiskWhiteListLog buildRiskWhiteListLog);


    List<RiskWhiteListLog> findById(@Param("whiteListId") long whiteListId);
}
