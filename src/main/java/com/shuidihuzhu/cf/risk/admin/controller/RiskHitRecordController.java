package com.shuidihuzhu.cf.risk.admin.controller;


import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.model.enums.BlackActionLimitTimeType;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleActionEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.hit.RiskHandleStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.RiskRecordRecordQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.RiskHitOperateVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.StrategyHitContentVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.StrategyHitLogVo;
import com.shuidihuzhu.cf.risk.admin.service.hit.RiskHitService;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import netscape.javascript.JSObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/15 20:03
 */
@Validated
@Slf4j
@RequiresPermission("risk:view")
@RestController
@RequestMapping(path = "/api/cf-risk-admin/risk/record")
public class RiskHitRecordController {

    @Resource
    private RiskHitService riskHitService;
    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @ApiOperation(value = "风控策略命中记录-列表")
    @PostMapping(path = "/list")
    public Response<StrategyHitContentVo> list(RiskRecordRecordQuery recordQuery) throws Exception {
        log.info("风控策略命中记录-列表，请求入参：{}", recordQuery);
        return NewResponseUtil.makeSuccess(riskHitService.queryRiskHitRecord(recordQuery));
    }

    @ApiOperation(value = "风控策略命中记录-查看操作日志")
    @PostMapping(path = "/list/log")
    public Response<List<StrategyHitLogVo>> listLog(@NotNull @Min(value = 1) Long hitRecordId) {
        log.info("风控策略命中记录-查看操作日志，请求入参：{}", hitRecordId);
        return NewResponseUtil.makeSuccess(riskHitService.queryRecordLogs(hitRecordId));
    }

    @RequiresPermission("risk:handle")
    @ApiOperation(value = "风控策略命中记录-处理命中结果")
    @PostMapping(path = "/handle")
    public Response<List<Long>> handle(@Valid @RequestBody() RiskHitOperateVo riskHitOperateVo) {
        log.info("风控策略命中记录-处理命中结果，请求入参：{}", riskHitOperateVo);
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            return NewResponseUtil.makeSuccess(riskHitService.handleHitInfo(riskHitOperateVo, adminUserId));
        } catch (IllegalArgumentException e) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_ERROR.getCode(), e.getMessage(), null);
        }
    }

    @RequiresPermission("risk:handle-result-edit")
    @ApiOperation(value = "风控策略命中记录-更新处理结果")
    @PostMapping(path = "/handle/update")
    public Response<List<Long>> handleUpdate(@Valid @RequestBody() RiskHitOperateVo riskHitOperateVo) {
        log.info("风控策略命中记录-修改命中结果，请求入参：{}", riskHitOperateVo);
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            return NewResponseUtil.makeSuccess(riskHitService.updateHandleHitInfo(riskHitOperateVo, adminUserId));
        } catch (IllegalArgumentException e) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_ERROR.getCode(), e.getMessage(), null);
        }
    }

    @ApiOperation(value = "风控策略命中记录-查看处理结果")
    @PostMapping(path = "/handle/log")
    public Response<List<RiskHitOperateVo>> handleLog(@NotNull @Min(value = 1) Long hitRecordId) {
        log.info("风控策略命中记录-查看处理结果，请求入参：{}", hitRecordId);
        return NewResponseUtil.makeSuccess(riskHitService.queryOperateHistory(hitRecordId));
    }

    @ApiOperation(value = "风控策略命中记录-查看最近一次处理结果")
    @PostMapping(path = "/handle/log/least")
    public Response<RiskHitOperateVo> handleLogLeast(@NotNull @Min(value = 1) Long hitRecordId) {
        log.info("风控策略命中记录-查看处理结果，请求入参：{}", hitRecordId);
        List<RiskHitOperateVo> riskHitOperateVos = riskHitService.queryOperateHistory(hitRecordId);
        if (CollectionUtils.isNotEmpty(riskHitOperateVos)) {
            return NewResponseUtil.makeSuccess(riskHitOperateVos.get(0));
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "风控策略命中记录-命中时机")
    @PostMapping(path = "/handle/phase")
    public Response<Map<Integer, String>> handlePhase() {
        return NewResponseUtil.makeSuccess(BlacklistCallPhaseEnum.recordUsableKeyVal());
    }

    @ApiOperation(value = "风控策略命中记录-风控核实结果枚举")
    @PostMapping(path = "/handle/result")
    public Response<Map<Integer, String>> handleResult() {
        return NewResponseUtil.makeSuccess(RiskHandleResultEnum.usableKeyVal());
    }

    @ApiOperation(value = "风控策略命中记录-风控处理动作枚举")
    @PostMapping(path = "/handle/action")
    public Response<Map<Integer, String>> handleAction() {
        return NewResponseUtil.makeSuccess(RiskHandleActionEnum.usableKeyVal());
    }

    @ApiOperation(value = "风控策略命中记录-风控限制动作枚举")
    @PostMapping(path = "/limit/action")
    public Response<Map<Long, String>> limitAction() {
        return NewResponseUtil.makeSuccess(LimitActionEnum.usableKeyVal());
    }

    @ApiOperation(value = "风控策略命中记录-风控处理状态枚举")
    @PostMapping(path = "/handle/status")
    public Response<Map<Integer, String>> handleStatus() {
        return NewResponseUtil.makeSuccess(RiskHandleStatusEnum.usableKeyVal());
    }

    @ApiOperation(value = "风控策略命中记录-风控策略枚举")
    @PostMapping(path = "/handle/strategy")
    public Response<Map<Integer, String>> handleStrategy() {
        return NewResponseUtil.makeSuccess(RiskStrategyEnum.usableKeyVal());
    }

    @ApiOperation(value = "风控策略命中记录-风控二级策略枚举")
    @PostMapping(path = "/handle/strategy-second")
    public Response<Map<Integer, String>> handleSecondStrategy(@RequestParam(value = "strategyId", defaultValue = "0", required = false) int strategyId) {
        return NewResponseUtil.makeSuccess(RiskStrategySecondEnum.getRiskStrategyByOneLevel(strategyId));
    }
    @ApiOperation(value = "风控策略命中-限制时长枚举")
    @PostMapping(path = "/handle/black-action-limit-time-type")
    public Response<Map<Integer, String>> blackActionLimitTimeType() {
        return NewResponseUtil.makeSuccess(Arrays.stream(BlackActionLimitTimeType.values()).filter(riskStrategyEnum -> riskStrategyEnum.getCode()>0)
                .collect(Collectors.toMap(BlackActionLimitTimeType::getCode, BlackActionLimitTimeType::getDesc)));
    }

    @ApiOperation(value = "根据caseId查询infoId")
    @PostMapping(path = "/obtain-info-id/by/case-id")
    public Response<String> getInfoIdByCaseId(@NotNull @Min(value = 1) Integer caseId) {
        FeignResponse<CrowdfundingInfo> resp = crowdfundingFeignClient.getCaseInfoById(caseId);
        if (resp.notOk()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(resp.getData().getInfoId());
    }

    /*@PostMapping(path = "/wash-data")
    public Response<Void> washData(String uuid) {
        log.info("线上问题批量修数据:{}", uuid);
        if (!Objects.equals(uuid, "db8a078f97fe4a7faac05af&*a612")) {
            return null;
        }
        riskHitService.washDataHandle();
        return NewResponseUtil.makeSuccess(null);
    }*/

}
