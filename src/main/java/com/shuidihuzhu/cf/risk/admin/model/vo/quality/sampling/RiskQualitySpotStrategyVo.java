package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotScheduleTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling.QualitySpotStrategyStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@Data
@ApiModel(description = "抽检策略配置实体")
public class RiskQualitySpotStrategyVo {

    public RiskQualitySpotStrategyVo(RiskQualitySpotStrategy riskQualitySpotStrategy){
        this.setId(riskQualitySpotStrategy.getId());
        this.setStrategyName(riskQualitySpotStrategy.getStrategyName());
        this.setStrategyScope(QualitySpotScheduleTypeEnum.codes2Desc(riskQualitySpotStrategy.getStrategyScope()));
        this.setStrategyParseTime(DateUtil.getDate2LStr(riskQualitySpotStrategy.getStrategyParseTime()));
        this.setStrategyExpireTime(
                Objects.equals(riskQualitySpotStrategy.getStatus(), QualitySpotStrategyStatusEnum.DISABLE.getCode())
                        ? DateUtil.getDate2LStr(riskQualitySpotStrategy.getStrategyExpireTime())
                        : "");
        this.setStatus(Objects.requireNonNull(QualitySpotStrategyStatusEnum.fromCode(riskQualitySpotStrategy.getStatus())).getDesc());
        this.setOperateName(riskQualitySpotStrategy.getOperateName());
        this.setOperateTime(DateUtil.getDate2LStr(riskQualitySpotStrategy.getUpdateTime()));
        this.setStrategyScene(riskQualitySpotStrategy.getSceneInfo());
    }

    @ApiModelProperty("序号")
    private Long id;

    @ApiModelProperty("策略名称")
    private String strategyName;

    @ApiModelProperty("策略适用场景")
    private String strategyScene;

    @ApiModelProperty("策略适用范围")
    private String strategyScope;

    @ApiModelProperty("策略生效时间")
    private String strategyParseTime;

    @ApiModelProperty("策略失效时间")
    private String strategyExpireTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("最新操作人")
    private String operateName;

    @ApiModelProperty("最新策略操作时间")
    private String operateTime;



}