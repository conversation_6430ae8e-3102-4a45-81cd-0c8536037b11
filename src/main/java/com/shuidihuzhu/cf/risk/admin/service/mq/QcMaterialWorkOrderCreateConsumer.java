package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.service.qc.workorder.QcOrderCaiLiaoCreateService;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-08-10 16:20
 **/
@Service
@RocketMQListener(id = RiskMQTagCons.QC_MATERIAL_WORK_ORDER_CREATE,
        tags = RiskMQTagCons.QC_MATERIAL_WORK_ORDER_CREATE,
        topic = MQTopicCons.CF)
@Slf4j
public class QcMaterialWorkOrderCreateConsumer implements MessageListener<WorkOrderResultChangeEvent> {

    @Autowired
    private QcOrderCaiLiaoCreateService qcOrderCaiLiaoCreateService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WorkOrderResultChangeEvent> mqMessage) {
        WorkOrderResultChangeEvent workOrderResultChangeEvent = mqMessage.getPayload();
        return qcOrderCaiLiaoCreateService.touchCreateCaiLiaoQcOrder(workOrderResultChangeEvent);
    }

}
