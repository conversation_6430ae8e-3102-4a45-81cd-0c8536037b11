package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(description = "黑名单数据-编辑用户信息")
public class BlacklistDataUserEditVo extends BlackListDataBaseInfo{

    @NotNull(message = "黑名单数据id不能为空")
    @Min(value = 1, message = "黑名单数据id不能小于1")
    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "操作原因不能为空")
    @Length(min = 5, max = 200, message = "操作原因长度必须在[5~200]之间")
    @ApiModelProperty("操作原因")
    private String operateReason;

}
