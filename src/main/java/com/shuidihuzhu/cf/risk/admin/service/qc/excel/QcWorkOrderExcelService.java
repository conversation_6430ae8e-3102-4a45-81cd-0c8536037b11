package com.shuidihuzhu.cf.risk.admin.service.qc.excel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.adminpure.feign.OrderCallFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.call.CfCallOutRecordMsg;
import com.shuidihuzhu.cf.client.base.enums.BaseErrorCodeEnum;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.data.platform.util.ExportUtil;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandExtBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcStandardPropertyBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcStandardUseSceneEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.*;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcCalculateResult;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.shuidihuzhu.cf.risk.admin.model.vo.excel.QcHighRiskExcelVO;
import com.shuidihuzhu.cf.risk.admin.service.Risk1v1QcDetailService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcDetailService;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/19  7:43 下午
 */
@Service
@Slf4j
public class QcWorkOrderExcelService {

    @Resource
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Resource
    private SeaAccountService accountService;
    @Resource
    private OrderCallFeignClient orderCallFeignClient;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private QcWorkOrderExcelBuildService buildService;
    @Resource
    private Risk1v1QcDetailService risk1v1QcDetailService;
    @Resource
    private RiskQcStandBiz riskQcStandBiz;
    @Resource
    private RiskQcStandExtBiz riskQcStandExtBiz;
    @Resource
    private RiskQcStandardPropertyBiz riskQcStandardPropertyBiz;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    public interface IOrderExcelDataLoader<T> {
        List<T> load(List<WorkOrderVO> orderList);
    }

    public void getExcelList(QcWorkOrderParam qcWorkOrderParam, long adminUserId) {
        int orderType = qcWorkOrderParam.getOrderType();
        if (orderType == WorkOrderType.qc_high_risk_quality_inspection.getType()) {
            getExportData(adminUserId, qcWorkOrderParam, this::getHighRiskOrderExcelVo, QcHighRiskExcelVO.class);
        }
    }

    private <T> void getExportData(long adminUserId, QcWorkOrderParam qcWorkOrderParam,
                                   QcWorkOrderExcelService.IOrderExcelDataLoader<T> excelDataLoader,
                                   Class<T> clazz) {

        if (Objects.isNull(qcWorkOrderParam)) {
            return;
        }

        List<Long> workOrderIds = buildService.getWorkOrderIdByEs(qcWorkOrderParam);
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return;
        }

        List<T> qcWorkOrderExcelVos = Lists.newArrayList();
        List<List<Long>> workOrderIdsGroup = Lists.partition(workOrderIds, 200);
        for (List<Long> subWorkOrderIds : workOrderIdsGroup) {
            Response<List<WorkOrderVO>> response = cfQcWorkOrderClient.queryQcByIds(subWorkOrderIds);
            List<WorkOrderVO> workOrderVOList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
            if (CollectionUtils.isEmpty(workOrderVOList)) {
                return;
            }
            qcWorkOrderExcelVos.addAll(excelDataLoader.load(workOrderVOList));
        }

        String fileName = WorkOrderType.getFromType(qcWorkOrderParam.getOrderType()).getMsg() + "导出_" + System.currentTimeMillis();
        exportUtil.export(adminUserId, qcWorkOrderExcelVos, clazz, fileName);
    }

    private List<QcHighRiskExcelVO> getHighRiskOrderExcelVo(List<WorkOrderVO> workOrderVOList) {


        List<Long> qcIds = workOrderVOList.stream().map(WorkOrderVO::getQcId).collect(Collectors.toList());
        List<Long> workOrderIds = workOrderVOList.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        List<Long> userIds = workOrderVOList.stream().map(WorkOrderVO::getOperatorId).collect(Collectors.toList());

        //获取质检信息
        Map<Long, RiskQcBaseInfo> qcBaseInfoMap = buildService.getQcBaseInfoMap(qcIds);

        //获取index表
        Map<Long, RiskQcSearchIndex> qcSearchIndexMap = buildService.getQcSearchIndexMap(workOrderIds);

        //获取用户信息
        Map<Long, AuthUserDto> authUserDtoMap = accountService.getAuthUserDtoList(userIds);

        //获取质检结果
        Map<Long, RiskQcResult> riskQcResultMap = buildService.getRiskQcResultMap(workOrderIds);

        //问题录音信息
        Map<Long, List<QcRecordingProblemsVo>> callMap = buildService.getRecordingProblemsByWorkOrderIds(workOrderIds);

        List<QcHighRiskExcelVO> qcWorkOrderExcelVos = Lists.newArrayList();
        for (WorkOrderVO workOrderVO : workOrderVOList) {
            long workOrderId = workOrderVO.getWorkOrderId();

            QcHighRiskExcelVO v = new QcHighRiskExcelVO();
            v.setWorkOrderId(workOrderId);
            v.setCaseId(workOrderVO.getCaseId());
            v.setQcDate(DateUtil.getDate2SStr2(workOrderVO.getFinishTime()));

            AuthUserDto authUserDto = authUserDtoMap.get(workOrderVO.getOperatorId());
            String name = StringUtils.EMPTY;
            if (Objects.nonNull(authUserDto) && StringUtils.isNotBlank(authUserDto.getUserName())) {
                name = authUserDto.getUserName();
            }
            v.setOperatorName(name);

            RiskQcBaseInfo riskQcBaseInfo = qcBaseInfoMap.get(workOrderVO.getQcId());
            if (Objects.isNull(riskQcBaseInfo)) {
                continue;
            }
            RiskQcSearchIndex index = qcSearchIndexMap.get(workOrderId);
            if (Objects.isNull(index)) {
                continue;
            }

            v.setTargetName(index.getQcByName());
            v.setTargetOrgName(index.getOrganization());

            Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(index.getSourceWorkOrderId());
            WorkOrderVO orderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
            if (Objects.nonNull(orderVO)) {
                v.setHandleResultMsg(HandleResultEnum.getFromType(orderVO.getHandleResult()).getMsg() + "-" + WorkOrderType.getFromType(orderVO.getOrderType()).getMsg());
            }

            //获取质检结果
            RiskQcResult riskQcResult = riskQcResultMap.get(workOrderId);
            if (Objects.isNull(riskQcResult)) {
                qcWorkOrderExcelVos.add(v);
                continue;
            }

            RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), new TypeReference<RiskQcResultVo>() {
            });
            if (Objects.isNull(riskQcResultVo)) {
                qcWorkOrderExcelVos.add(v);
                continue;
            }
            v.setPreInfoRemark(riskQcResultVo.getPreInfoRemark());
            v.setAddCreditRemark(riskQcResultVo.getAddCreditRemark());
            v.setImgWordRemark(riskQcResultVo.getImgWordRemark());
            v.setLowIncomeRemark(riskQcResultVo.getLowIncomeRemark());
            v.setOtherRemark(riskQcResultVo.getUserWriteRemark());
            v.setReviseOpinion(riskQcResultVo.getSuggest());

            List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos = RiskQcResultVo.getList(riskQcResultVo.getMaterialQcResultOption());
            if (CollectionUtils.isEmpty(riskMaterialQcStandardVos)) {
                qcWorkOrderExcelVos.add(v);
                continue;
            }

            List<QcHighRiskExcelVO> list = buildQcHighRiskExcelVO(v, riskMaterialQcStandardVos, callMap, index, workOrderId);

            qcWorkOrderExcelVos.addAll(list);
        }
        return qcWorkOrderExcelVos;
    }


    private List<QcHighRiskExcelVO> buildQcHighRiskExcelVO(QcHighRiskExcelVO v, List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos, Map<Long, List<QcRecordingProblemsVo>> callMap, RiskQcSearchIndex index, long workOrderId) {
        List<QcHighRiskExcelVO> qcWorkOrderExcelVos = Lists.newArrayList();
        getCallMsg(v, callMap, workOrderId, index, qcWorkOrderExcelVos);
        List<Long> ids = Lists.newArrayList();
        getQcResultMsg(v, riskMaterialQcStandardVos, ids, qcWorkOrderExcelVos);
        getQcResult(v, ids, qcWorkOrderExcelVos);
        return qcWorkOrderExcelVos;
    }

    /**
     * 获取通话录音信息
     *
     * @param v
     * @param callMap
     * @param workOrderId
     * @param qcWorkOrderExcelVos
     */
    private void getCallMsg(QcHighRiskExcelVO v, Map<Long, List<QcRecordingProblemsVo>> callMap, long workOrderId, RiskQcSearchIndex index, List<QcHighRiskExcelVO> qcWorkOrderExcelVos) {
        //获取录音信息
        OperationResult<CfCallOutRecordMsg> operationResult = orderCallFeignClient.selectHighRiskCallRecords(index.getSourceWorkOrderId(), index.getWorkOrderId());
        List<CfCallOutRecordMsg.CallOutDetail> callOutDetailList = Optional.ofNullable(operationResult).filter(y ->
                y.getCode() == BaseErrorCodeEnum.SUCCESS.getCode()).map(OperationResult::getData).map(CfCallOutRecordMsg::getCallOutDetails).orElse(Lists.newArrayList());

        List<QcRecordingProblemsVo> qcRecordingProblemsVos = callMap.get(workOrderId);
        if (CollectionUtils.isNotEmpty(qcRecordingProblemsVos)) {
            for (QcRecordingProblemsVo qcRecordingProblemsVo : qcRecordingProblemsVos) {
                long tapingId = qcRecordingProblemsVo.getTapingId();
                String problemIdStr = qcRecordingProblemsVo.getProblemIds();
                if (StringUtils.isNotEmpty(problemIdStr)) {
                    List<Long> problemIdList = Splitter.on(",").splitToList(problemIdStr).stream().map(Long::valueOf).collect(Collectors.toList());
                    List<RiskQcStandard> riskQcStandards = riskQcStandBiz.getByIds(problemIdList);
                    List<Long> parentIds = riskQcStandards.stream().map(RiskQcStandard::getParentId).collect(Collectors.toList());
                    List<RiskQcStandard> riskQcStandardList = riskQcStandBiz.getByIds(parentIds);
                    List<RiskQcStandardExt> riskQcStandardExts = riskQcStandExtBiz.getByStandardIds(problemIdList);
                    List<RiskQcStandardProperty> riskQcStandardProperties = riskQcStandardPropertyBiz.getAll();
                    String customerPhone = StringUtils.EMPTY;
                    String callDuration = StringUtils.EMPTY;
                    if (CollectionUtils.isNotEmpty(callOutDetailList)) {
                        CfCallOutRecordMsg.CallOutDetail callOutDetail = callOutDetailList.stream().filter(y -> y.getCallRecordId() == tapingId).findFirst().orElse(null);
                        if (Objects.nonNull(callOutDetail)) {
                            customerPhone = callOutDetail.getMobile();
                            callDuration = callOutDetail.getDuration() + "s";
                        }
                    }
                    for (RiskQcStandard riskQcStandard : riskQcStandards) {
                        String standardName = riskQcStandardList.stream().filter(y -> y.getId() == riskQcStandard.getParentId()).map(RiskQcStandard::getStandardName).findFirst().orElse("");
                        RiskQcStandardExt riskQcStandardExt = riskQcStandardExts.stream().filter(y -> y.getQcStandardId() == riskQcStandard.getId()).findFirst().orElse(null);
                        String determinantAttribute = StringUtils.EMPTY;
                        if (Objects.nonNull(riskQcStandardExt)) {
                            String firstProperty = riskQcStandardProperties.stream()
                                    .filter(y -> y.getId() == riskQcStandardExt.getFirstPropertyId())
                                    .map(RiskQcStandardProperty::getProperty)
                                    .findFirst()
                                    .orElse("");
                            String secondProperty = riskQcStandardProperties.stream()
                                    .filter(y -> y.getId() == riskQcStandardExt.getSecondPropertyId())
                                    .map(RiskQcStandardProperty::getProperty)
                                    .findFirst()
                                    .orElse("");
                            determinantAttribute = firstProperty + "-" + secondProperty;
                        }

                        QcHighRiskExcelVO qcHighRiskExcelVO = new QcHighRiskExcelVO();
                        BeanUtils.copyProperties(v, qcHighRiskExcelVO);
                        qcHighRiskExcelVO.setErrorModule("通话录音");
                        qcHighRiskExcelVO.setErrorType(standardName);
                        qcHighRiskExcelVO.setSpecificError(riskQcStandard.getStandardName());
                        qcHighRiskExcelVO.setDeterminantAttribute(determinantAttribute);
                        qcHighRiskExcelVO.setCustomerPhone(customerPhone);
                        qcHighRiskExcelVO.setCallDuration(callDuration);
                        qcWorkOrderExcelVos.add(qcHighRiskExcelVO);
                    }
                }
            }
        }
    }


    /**
     * 获取质检结果详细细节  问题id
     *
     * @param v
     * @param riskMaterialQcStandardVos
     * @param ids
     * @param qcWorkOrderExcelVos
     */
    private void getQcResultMsg(QcHighRiskExcelVO v, List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos, List<Long> ids, List<QcHighRiskExcelVO> qcWorkOrderExcelVos) {
        for (RiskMaterialQcStandardVo riskMaterialQcStandardVo : riskMaterialQcStandardVos) {
            List<RiskQcStandardVo> riskQcStandardVoList = riskMaterialQcStandardVo.getRiskQcStandardVo();
            for (RiskQcStandardVo riskQcStandardVo : riskQcStandardVoList) {
                List<RiskQcStandardDetailVo> riskQcStandardDetailVos = riskQcStandardVo.getRiskQcStandardSecondVos();
                for (RiskQcStandardDetailVo riskQcStandardDetailVo : riskQcStandardDetailVos) {
                    if (riskQcStandardDetailVo.isCheck()) {
                        QcHighRiskExcelVO qcHighRiskExcelVO = new QcHighRiskExcelVO();
                        BeanUtils.copyProperties(v, qcHighRiskExcelVO);
                        qcHighRiskExcelVO.setSpecificError(riskQcStandardDetailVo.getStandardName());
                        qcHighRiskExcelVO.setErrorModule(riskMaterialQcStandardVo.getDesc());
                        qcHighRiskExcelVO.setErrorType(riskQcStandardVo.getStandardName());
                        qcHighRiskExcelVO.setDeterminantAttribute(riskQcStandardDetailVo.getProperty());
                        qcWorkOrderExcelVos.add(qcHighRiskExcelVO);
                        ids.add(riskQcStandardDetailVo.getId());
                    }
                }
            }
        }
    }

    /**
     * 获取质检结果
     *
     * @param v
     * @param ids
     * @param qcWorkOrderExcelVos
     */
    private void getQcResult(QcHighRiskExcelVO v, List<Long> ids, List<QcHighRiskExcelVO> qcWorkOrderExcelVos) {
        RiskQcCalculateResult riskQcCalculateResult = risk1v1QcDetailService.getResult(ids, RiskQcStandardUseSceneEnum.HIGH_RISK_QUALITY_INSPECTION.getCode());
        if (CollectionUtils.isNotEmpty(qcWorkOrderExcelVos)) {
            for (QcHighRiskExcelVO qcHighRiskExcelVO : qcWorkOrderExcelVos) {
                qcHighRiskExcelVO.setIsEligibility(RiskQcResultEnum.findOfCode(riskQcCalculateResult.getQcResult()));
                qcHighRiskExcelVO.setCriticalErrorCount(riskQcCalculateResult.getCriticalError());
                qcHighRiskExcelVO.setNonCriticalErrorCount(riskQcCalculateResult.getNonCriticalError());
                qcHighRiskExcelVO.setIsFault(riskQcCalculateResult.getQcResult() == RiskQcResultEnum.QUALIFIED.getType() ? "否" : "是");
            }
        } else {
            v.setIsEligibility(RiskQcResultEnum.findOfCode(riskQcCalculateResult.getQcResult()));
            v.setCriticalErrorCount(riskQcCalculateResult.getCriticalError());
            v.setNonCriticalErrorCount(riskQcCalculateResult.getNonCriticalError());
            v.setIsFault(riskQcCalculateResult.getQcResult() == RiskQcResultEnum.QUALIFIED.getType() ? "否" : "是");
            qcWorkOrderExcelVos.add(v);
        }
    }
}
