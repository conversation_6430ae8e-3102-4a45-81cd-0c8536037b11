package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcSearchIndexDao;
import com.shuidihuzhu.cf.risk.admin.model.QcCreateWorkOrderModel;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.service.mq.producer.MqProducer;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-15 15:30
 **/
@Service
@Slf4j
public class RiskQcSearchIndexBizImpl implements RiskQcSearchIndexBiz {

    @Autowired
    private RiskQcSearchIndexDao riskQcSearchIndexDao;
    @Autowired
    private MqProducer producer;
    @Resource
    private OldShuidiCipher oldShuidiCipher;

    @Override
    public int addSearchIndex(RiskQcSearchIndex riskQcSearchIndex, int workOrderType) {
        RiskQcSearchIndex searchIndex = riskQcSearchIndexDao.getByWorkOrderId(riskQcSearchIndex.getWorkOrderId());
        if (Objects.nonNull(searchIndex)) {
            return 0;
        }
        int i = riskQcSearchIndexDao.insertOne(riskQcSearchIndex);
        producer.sendDelayOfQcWorkOrderCreate4RecodingMsg(new QcCreateWorkOrderModel(workOrderType, riskQcSearchIndex.getWorkOrderId(),riskQcSearchIndex.getSourceWorkOrderId()));
        return i;
    }

    @Override
    public int updateByWorkOrderId(long workOrderId, int qcResultId, String questionTypeIds, String firstPropertyIds, int qcResultSecond) {
        return riskQcSearchIndexDao.updateByWorkOrderId(workOrderId, qcResultId, questionTypeIds, firstPropertyIds, qcResultSecond);
    }

    @Override
    public RiskQcSearchIndex getByWorkOrderId(long workOrderId) {
        return riskQcSearchIndexDao.getByWorkOrderId(workOrderId);
    }

    @Override
    public List<RiskQcSearchIndex> getByWorkOrderIds(List<Long> workOrderIds) {
        return riskQcSearchIndexDao.getByWorkOrderIds(workOrderIds);
    }

    @Override
    public int updateServiceStageByTaskId(int taskId, long workOrderId, int serviceStage) {
        return riskQcSearchIndexDao.updateServiceStageByTaskId(taskId, workOrderId, serviceStage);
    }

    @Override
    public int updateCaseIdAndUserId(long taskId, int qcType, long caseId, long userId) {
        return riskQcSearchIndexDao.updateCaseIdAndUserId(taskId, qcType, caseId, userId);
    }

    @Override
    public int updateRuleNameByWorkOrderId(long workOrderId, String ruleName) {
        return riskQcSearchIndexDao.updateRuleNameByWorkOrderId(workOrderId, ruleName);
    }

    @Override
    public int addCallRiskQcSearchIndex(int orderType, RiskQcSearchIndex riskQcSearchIndex) {
        int i = riskQcSearchIndexDao.addCallRiskQcSearchIndex(riskQcSearchIndex);
        producer.sendDelayOfQcWorkOrderCreate4RecodingMsg(new QcCreateWorkOrderModel(orderType, riskQcSearchIndex.getWorkOrderId(),riskQcSearchIndex.getSourceWorkOrderId()));
        return i;
    }

    @Override
    public int addMaterialRiskQcSearchIndex(int orderType, RiskQcSearchIndex riskQcSearchIndex) {
        int i = riskQcSearchIndexDao.addMaterialRiskQcSearchIndex(riskQcSearchIndex);
        producer.sendDelayOfQcWorkOrderCreate4RecodingMsg(new QcCreateWorkOrderModel(orderType, riskQcSearchIndex.getWorkOrderId(),riskQcSearchIndex.getSourceWorkOrderId()));
        return i;
    }

    @Override
    public int updateOrgByWorkOrderId(long workOrderId, String org) {
        return riskQcSearchIndexDao.updateOrgByWorkOrderId(workOrderId, org);
    }
}
