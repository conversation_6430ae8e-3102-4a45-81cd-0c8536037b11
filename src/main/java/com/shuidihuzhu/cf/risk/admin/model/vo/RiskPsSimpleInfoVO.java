package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class RiskPsSimpleInfoVO {

    private long id;
    private int status;//状态
    private String infoSourceStr;//信息来源
    private String infoFeedBackStr;//信息反馈形式
    private String publicSentimentInfoTypeStr;//信息类型
    private Timestamp infoEnteringTime;//信息录入时间
    private Timestamp publishTime;//发布时间

    public static RiskPsSimpleInfoVO convertVO(RiskPublicSentimentInfo info){
        RiskPsSimpleInfoVO vo = new RiskPsSimpleInfoVO();
        vo.setId(info.getId());
        vo.setStatus(info.getStatus());
        vo.setInfoEnteringTime(info.getCreateTime());
        vo.setPublishTime(info.getPublishTime());
        return vo;
    }
}
