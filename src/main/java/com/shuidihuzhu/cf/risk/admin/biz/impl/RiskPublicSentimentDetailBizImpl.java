package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskPublicSentimentDetailBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskPublicSentimentDetailDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/2/20
 */
@Service
@Slf4j
public class RiskPublicSentimentDetailBizImpl implements RiskPublicSentimentDetailBiz {

    @Autowired
    private RiskPublicSentimentDetailDao riskPublicSentimentDetailDao;

    @Override
    public RiskPublicSentimentDetail add(String riskPublicSentimentDetailJson, long id) {
        RiskPublicSentimentDetail riskPublicSentimentDetail = buildDetail(riskPublicSentimentDetailJson);
        if (riskPublicSentimentDetail == null) {
            return null;
        }
        riskPublicSentimentDetail.setPublicSentimentId(id);
        riskPublicSentimentDetailDao.add(riskPublicSentimentDetail);
        return riskPublicSentimentDetail;
    }

    @Override
    public int updateByPublicSentimentId(String riskPublicSentimentDetailJson, long id) {
        RiskPublicSentimentDetail riskPublicSentimentDetail = buildDetail(riskPublicSentimentDetailJson);
        if (riskPublicSentimentDetail == null) {
            return 0;
        }
        riskPublicSentimentDetail.setPublicSentimentId(id);
        return riskPublicSentimentDetailDao.updateByPublicSentimentId(riskPublicSentimentDetail);
    }

    @Override
    public RiskPublicSentimentDetail getByPublicSentimentId(long publicSentimentId) {
        if (publicSentimentId <= 0){
            return null;
        }
        return riskPublicSentimentDetailDao.getByPublicSentimentId(publicSentimentId);
    }

    @Override
    public List<RiskPublicSentimentDetail> getByPublicSentimentIds(List<Long> publicSentimentIds) {
        if (CollectionUtils.isEmpty(publicSentimentIds)) {
            return Lists.newArrayList();
        }
        return riskPublicSentimentDetailDao.getByPublicSentimentIds(publicSentimentIds);
    }


    private RiskPublicSentimentDetail buildDetail(String riskPublicSentimentDetailJson) {
        RiskPublicSentimentDetail riskPublicSentimentDetail = null;
        try {
            riskPublicSentimentDetail = JSONObject.parseObject(riskPublicSentimentDetailJson, RiskPublicSentimentDetail.class);
        } catch (Exception e) {
            log.error("", e);
        }
        return riskPublicSentimentDetail;
    }
}
