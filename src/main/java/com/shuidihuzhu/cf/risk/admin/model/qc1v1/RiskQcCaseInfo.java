package com.shuidihuzhu.cf.risk.admin.model.qc1v1;

import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.admin.model.CfBasePreMsgStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bouncycastle.est.CACertsResponse;

import java.util.Date;

/**
 * @Auther: subing
 * @Date: 2020/8/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskQcCaseInfo {
    private Integer caseId;
    private String infoId;
    private String title;
    private Integer firstApproveStatus;
    private Integer targetAmount;
    private Integer raiseAmount;
    private Integer shareCount;
    private Integer donateCount;
    //材审状态
    private Integer itemInfoApproveStatus;
    //发起时间
    private Date initiateTime;
    //代录入初审通过状态
    private String preMsgStatusDesc;
    private String firstApproveStatusDesc;


    public static RiskQcCaseInfo riskQcCaseInfo(CrowdfundingInfo crowdfundingInfo, CfInfoExt cfInfoExt, CfInfoStat cfInfoStat) {
        RiskQcCaseInfo riskQcCaseInfo = new RiskQcCaseInfo();
        if (crowdfundingInfo != null) {
            riskQcCaseInfo.setCaseId(crowdfundingInfo.getId());
            riskQcCaseInfo.setTitle(crowdfundingInfo.getTitle());
            riskQcCaseInfo.setTargetAmount(crowdfundingInfo.getTargetAmount() / 100);
            riskQcCaseInfo.setRaiseAmount(crowdfundingInfo.getAmount() / 100);
            riskQcCaseInfo.setDonateCount(crowdfundingInfo.getDonationCount());
            riskQcCaseInfo.setInitiateTime(crowdfundingInfo.getCreateTime());
            riskQcCaseInfo.setItemInfoApproveStatus(crowdfundingInfo.getStatus().value());
            riskQcCaseInfo.setInfoId(crowdfundingInfo.getInfoId());
        }
        if (cfInfoExt != null){
            riskQcCaseInfo.setFirstApproveStatus(cfInfoExt.getFirstApproveStatus());
            riskQcCaseInfo.setFirstApproveStatusDesc(FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus()).getApproveMsg());
        }
        if (cfInfoStat != null){
            riskQcCaseInfo.setShareCount(cfInfoStat.getShareCount());
        }
        return riskQcCaseInfo;
    }

    public RiskQcCaseInfo setPreMsgStatusDesc(String desc){
        this.preMsgStatusDesc = desc;
        return this;
    }

}
