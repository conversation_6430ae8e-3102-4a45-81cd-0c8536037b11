package com.shuidihuzhu.cf.risk.admin.model.qc1v1;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueCallRecordSumModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/8/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskQcTotalCallRecords {
    /**
     * 有效通话总数
     */
    private Integer totalValidCalls;

    /**
     * 呼出记录
     */
    private Integer outGoingRecords;

    /**
     * 呼叫坐席
     */
    private String callSeats;

    /**
     * 通话时长
     */
    private Integer totalDuration;

    /**
     * 通话记录
     */
    private List<RiskQcCallRecords> riskQcCallRecords = Lists.newArrayList();

    public static RiskQcTotalCallRecords buildInfo(CfClueCallRecordSumModel cfClueCallRecordSumModel){
        RiskQcTotalCallRecords riskQcTotalCallRecords = new RiskQcTotalCallRecords();
        riskQcTotalCallRecords.setTotalValidCalls(cfClueCallRecordSumModel.getConnectCount());
        riskQcTotalCallRecords.setOutGoingRecords(cfClueCallRecordSumModel.getCallCount());
        riskQcTotalCallRecords.setCallSeats(cfClueCallRecordSumModel.getClientName());
        riskQcTotalCallRecords.setTotalDuration(0);
//        riskQcTotalCallRecords.setTotalDuration(cfClueCallRecordSumModel.getTotalDuration());
        return riskQcTotalCallRecords;
    }

    public RiskQcTotalCallRecords buildRiskQcCallRecords(List<RiskQcCallRecords> riskQcCallRecords){
        this.riskQcCallRecords = riskQcCallRecords;
        this.totalDuration = 0;
        if (CollectionUtils.isEmpty(riskQcCallRecords)) {
            return this;
        }
        totalDuration = riskQcCallRecords.stream().map(RiskQcCallRecords::getTotalDuration).reduce(0, Integer::sum);
        return this;
    }
}
