package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseOperationRecord;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
public interface RiskDiseaseOperationRecordBiz {

    int save(RiskDiseaseOperationRecord operationRecord);

    List<RiskDiseaseOperationRecord> findListByDiseaseId(long diseaseId);

    RiskDiseaseOperationRecord getLastOneByDiseaseId(long diseaseId);
}
