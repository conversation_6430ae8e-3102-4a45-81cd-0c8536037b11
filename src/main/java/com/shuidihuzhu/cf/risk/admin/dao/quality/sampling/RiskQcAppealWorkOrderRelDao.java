package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcAppealWorkOrderRel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2020/11/17
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcAppealWorkOrderRelDao {

    int save(@Param("qcWorkOrderId") long qcWorkOrderId, @Param("appealWorkOrderId") long appealWorkOrderId);


    RiskQcAppealWorkOrderRel getByAppealWorkOrderId(@Param("appealWorkOrderId") long appealWorkOrderId);

}
