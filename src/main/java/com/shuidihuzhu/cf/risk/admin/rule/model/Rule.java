package com.shuidihuzhu.cf.risk.admin.rule.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-26
 **/
@Data
public class Rule {

    private String name;
    private int priority;
    private int status;
    private JSONObject extData;
    private Integer executeModelValue;
    private String dataScopeString;

    private CriterionGroup preconditionGroup;
    private CriterionGroup criterionGroup;
    private CriterionAction thenAction;
    private CriterionAction elseAction;

}
