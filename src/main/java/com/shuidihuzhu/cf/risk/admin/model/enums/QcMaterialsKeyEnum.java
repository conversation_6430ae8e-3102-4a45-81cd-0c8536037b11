package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;

@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_materials_info", columnName = "materials_key")},
        valueName = "key")
@AllArgsConstructor
public enum QcMaterialsKeyEnum {

    RECORDING(1, "recording", "录音"),
    ORG_ID(2, "orgid", "组织结构id"),
    ORDER_ID(3, "orderId", "工单id"),
    DETAIL_SNAPSHOT(4, "snapshot", "材审工单detail快照"),
    COMPLAINT_MATERIAL(5, "complaintMaterial", "申诉材料"),
    ORIGINAL_WORK_ORDER(6, "originalOrderId", "工单对应的原工单id"),
    HAS_REPEAT(7, "hasRepeat", "是否生成复检");

    @Getter
    private int code;
    @Getter
    private String key;
    @Getter
    private String desc;

}
