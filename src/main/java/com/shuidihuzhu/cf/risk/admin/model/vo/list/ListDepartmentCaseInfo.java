package com.shuidihuzhu.cf.risk.admin.model.vo.list;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/28 17:32
 */
@ApiModel(description = "提交过改号码的案例信息")
@Data
public class ListDepartmentCaseInfo {

    @ApiModelProperty("caseId")
    private Integer caseId;

    @ApiModelProperty("案例发起时间")
    private String launchTime;

    @ApiModelProperty("医院名称")
    private String hospitalName;

    @ApiModelProperty("医院所在省份")
    private String province;

    @ApiModelProperty("医院所在城市")
    private String city;

    @ApiModelProperty("科室")
    private String departments;
    @ApiModelProperty("发起人手机号")
    private String mobile;
    @ApiModelProperty("发起人姓名")
    private String name;
    @ApiModelProperty("发起人身份证号")
    private String idCard;
    @ApiModelProperty("患者手机号")
    private String patientName;
    @ApiModelProperty("患者证件类型")
    private String patientIdType;
    @ApiModelProperty("患者证件号码")
    private String patientIdNumber;
    @ApiModelProperty("收款人手机号")
    private String payeePhone;
    @ApiModelProperty("收款人姓名")
    private String payeeName;
    @ApiModelProperty("收款人身份证号")
    private String payeeIdCard;

    @ApiModelProperty("对公打款开户支行")
    private String hospitalBankBranch;

    @ApiModelProperty("对公打款银行卡号")
    private String hospitalBankCard;

    @ApiModelProperty("对公打款账户名称")
    private String hospitalAccountName;

    @ApiModelProperty("对公打款住院号")
    private String hospitalNum;

    @ApiModelProperty("对公打款床号")
    private String bedNum;

    @ApiModelProperty("对公打款科室")
    private String payeeDepartment;

    @ApiModelProperty("收款关系类型: 9 对公打款，12 慈善机构，其他 个人")
    private Byte relationType;

}
