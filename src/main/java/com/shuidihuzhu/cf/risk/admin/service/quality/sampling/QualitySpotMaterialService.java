package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotLevelConfBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotJobConfDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotMaterialsAuditDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotRuleInfo;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotExecuteModelEnum;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingOperationFeignClient;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.AssignTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/10/15
 */
@Service
@Slf4j
public class QualitySpotMaterialService {

    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private CrowdfundingOperationFeignClient crowdfundingOperationFeignClient;
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private QualitySpotStrategyService qualitySpotStrategyService;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private QualitySpotLevelConfBiz qualitySpotLevelConfBiz;

    @Async
    public void spotMaterial(Long qcWorkOrderId, RiskQcBaseInfo riskQcBaseInfo, WorkOrderVO workOrderVO) {
        //获取工单规则
        List<QualitySpotJobConfDto> qualitySpotJobConfDtos =
                qualitySpotStrategyService.listJobScopeConf(QualitySpotSceneEnum.MATERIALS_AUDIT_QUALITY_WORK_ORDER.getCode());
        if (CollectionUtils.isEmpty(qualitySpotJobConfDtos)) {
            log.info("qualitySpotJobConfDtos is empty");
            return;
        }
        log.info("spotMaterial qcWorkOrderId:{} riskQcBaseInfo:{} workOrderVO:{}", qcWorkOrderId, JSON.toJSONString(riskQcBaseInfo),
                JSON.toJSONString(workOrderVO));
        QualitySpotJobConfDto qualitySpotJobConfDto = qualitySpotJobConfDtos.get(0);
        //检查当天的质检数量是否已经到达上限
        if (checkStrategyCount(qualitySpotJobConfDto)) {
            return;
        }
        QualitySpotMaterialsAuditDto qualitySpotMaterialsAuditDto = buildQualitySpotMaterialsAuditDto(qcWorkOrderId, riskQcBaseInfo, workOrderVO);
        if (qualitySpotMaterialsAuditDto == null) {
            return;
        }
        for (QualitySpotRuleInfo ruleInfo : qualitySpotJobConfDto.getRuleInfoList()) {
            if (checkRuleSampling(ruleInfo, qualitySpotJobConfDto)){
                continue;
            }
            qualitySpotMaterialsAuditDto =
                    qualitySpotStrategyService.doQualitySpotMaterialStrategy(ruleInfo.getRuleId(), qualitySpotMaterialsAuditDto);
            //分配工单
            if (!qualitySpotMaterialsAuditDto.isHit()) {
                continue;
            }
            //记录命中规则名称,命中规则id
            riskQcSearchIndexBiz.updateRuleNameByWorkOrderId(qualitySpotMaterialsAuditDto.getWorkOrderId(), ruleInfo.getRuleName());
            cfQcWorkOrderClient.addExtValue(qualitySpotMaterialsAuditDto.getWorkOrderId(),
                    OrderExtName.ruleId.getName(), Long.toString(ruleInfo.getRuleId()));
            //更新工单分配类型
            cfQcWorkOrderClient.updateAssignType(Lists.newArrayList(qualitySpotMaterialsAuditDto.getWorkOrderId()), AssignTypeEnum.MUST_ASSIGN.getCode());
            //统计工单信息
            updateCountRedis(qualitySpotMaterialsAuditDto, ruleInfo);
            break;
        }

    }

    private void updateCountRedis(QualitySpotMaterialsAuditDto qualitySpotMaterialsAuditDto, QualitySpotRuleInfo ruleInfo) {
        long scene = QualitySpotSceneEnum.MATERIALS_AUDIT_QUALITY_WORK_ORDER.getCode();
        // 被质检人当天必须分配工单数量
        redissonHandler.incrAndSetTimeWhenNotExists(getCheckerDayCountKey(scene, qualitySpotMaterialsAuditDto.getUniqueCode()),
                RedissonHandler.ONE_DAY);
        //被质检人最近1h内必须分配的工单量（自然1h)
        redissonHandler.incrAndSetTimeWhenNotExists(getCheckerHourCountKey(scene, qualitySpotMaterialsAuditDto.getUniqueCode()),
                RedissonHandler.ONE_HOUR);
        // 最近1h内必须分配的工单量（自然1h）
        redissonHandler.incrAndSetTimeWhenNotExists(getAllHourCountKey(scene), RedissonHandler.ONE_HOUR);
        // 总规则统计
        redissonHandler.incrAndSetTimeWhenNotExists(getRuleCountKey(ruleInfo.getRuleId()),
                RedissonHandler.ONE_DAY);
        // 总策略统计
        //查询数量
        StrategyCount strategyCount = redissonHandler.get(getStrategyCountKey(scene) , StrategyCount.class);
        if (strategyCount == null) {
            List<RiskQualitySpotLevelConfVo> riskQualitySpotLevelConfVos =
                    qualitySpotLevelConfBiz.listWithAllCurrentValidScene(scene);
            if (CollectionUtils.isEmpty(riskQualitySpotLevelConfVos)) {
                log.error("riskQualitySpotLevelConfVos is empty, scene:{}", scene);
                return;
            }
            strategyCount = new StrategyCount(0, riskQualitySpotLevelConfVos.get(0).getSamplingLevel(),
                    DateUtil.getYMDStringByDate(new Date()));
        }
        strategyCount.setCurrentCount(strategyCount.getCurrentCount() + 1);
        redissonHandler.setEX(getStrategyCountKey(scene) ,
                strategyCount, RedissonHandler.ONE_DAY);

    }

    private boolean checkRuleSampling(QualitySpotRuleInfo ruleInfo, QualitySpotJobConfDto qualitySpotJobConfDto) {
        if (qualitySpotJobConfDto.getExecuteMode() == QualitySpotExecuteModelEnum.PRIORITY.getCode()) {
            return false;
        }
        int count =  getCountByKey(getRuleCountKey(ruleInfo.getRuleId()));
        QualitySpotExecuteModelEnum executeModelEnum = QualitySpotExecuteModelEnum.fromCode(qualitySpotJobConfDto.getExecuteMode());
        switch (executeModelEnum) {
            case PRIORITY:
                return false;
            case PERCENTAGE:
            case FINAL_COUNT:
                return count >= ruleInfo.getRuleSamplingLevel();
            default:
                return true;
        }

    }

    private QualitySpotMaterialsAuditDto buildQualitySpotMaterialsAuditDto(Long qcWorkOrderId, RiskQcBaseInfo riskQcBaseInfo, WorkOrderVO workOrderVO) {
        QualitySpotMaterialsAuditDto qualitySpotMaterialsAuditDto = new QualitySpotMaterialsAuditDto();
        qualitySpotMaterialsAuditDto.setWorkOrderId(qcWorkOrderId);
        qualitySpotMaterialsAuditDto.setWorkOrderDealResult(workOrderVO.getHandleResult());
        qualitySpotMaterialsAuditDto.setCheckerName(riskQcBaseInfo.getQcByName());
        qualitySpotMaterialsAuditDto.setUniqueCode(riskQcBaseInfo.getQcUniqueCode());
        // 已筹金额
        FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse =
                crowdfundingFeignClient.getCaseInfoById((int) riskQcBaseInfo.getCaseId());
        if (crowdfundingInfoFeignResponse.isFailOrNullData()){
            return null;
        }
        if (StringUtils.isBlank(workOrderVO.getCaseUuid())){
            workOrderVO.setCaseUuid(crowdfundingInfoFeignResponse.getData().getInfoId());
        }
        // 审核驳回次数
        Response<String> operationResponse = crowdfundingOperationFeignClient.getByInfoId(workOrderVO.getCaseUuid());
        if (operationResponse.notOk() || StringUtils.isBlank(operationResponse.getData())) {
            log.error("operationResponse:{}", JSON.toJSONString(operationResponse));
            return null;
        }
        CrowdfundingOperation crowdfundingOperation = JSON.parseObject(operationResponse.getData(), CrowdfundingOperation.class);
        qualitySpotMaterialsAuditDto.setMaterialsRejectCount(crowdfundingOperation.getRefuseCount());
        // 举报次数
        FeignResponse<List<CrowdfundingReport>> reportResponse =
                crowdfundingFeignClient.getCrowdfundingReportsByInfoIds(Lists.newArrayList(workOrderVO.getCaseId()));
        if (reportResponse.notOk()) {
            log.error("reportResponse:{}", JSON.toJSONString(operationResponse));
            return null;
        }
        qualitySpotMaterialsAuditDto.setReportCount(reportResponse.getData().size());
        var crowdfundingInfo = crowdfundingInfoFeignResponse.getData();
        if (Objects.nonNull(crowdfundingInfo)) {
            qualitySpotMaterialsAuditDto.setHasAmount(crowdfundingInfo.getAmount() / 100);
        }
        long scene = QualitySpotSceneEnum.MATERIALS_AUDIT_QUALITY_WORK_ORDER.getCode();
        //被质检人当天必须分配工单数量
        qualitySpotMaterialsAuditDto.setDayAllotWorkOrderCount( getCountByKey(
                getCheckerDayCountKey(scene, qualitySpotMaterialsAuditDto.getUniqueCode())));
        //被质检人最近1h内必须分配的工单量（自然1h)
        qualitySpotMaterialsAuditDto.setCheckerHourAllotWorkOrderCount( getCountByKey(
                getCheckerHourCountKey(scene, qualitySpotMaterialsAuditDto.getUniqueCode())));
        // 最近1h内必须分配的工单量（自然1h）
        qualitySpotMaterialsAuditDto.setHourAllotWorkOrderCount(getCountByKey(getAllHourCountKey(scene)));
        return qualitySpotMaterialsAuditDto;
    }

    private int getCountByKey(String key) {
        if (log.isDebugEnabled()){
            log.debug("key:{}", key);
        }
        Integer count = redissonHandler.get(key, Integer.class);
        if (count == null) {
            return 0;
        }
        return count;
    }

    private boolean checkStrategyCount(QualitySpotJobConfDto confDto) {
        StrategyCount strategyCount =
                redissonHandler.get(getStrategyCountKey(confDto.getScene()), StrategyCount.class);
        if (strategyCount == null) {
            List<RiskQualitySpotLevelConfVo> riskQualitySpotLevelConfVos =
                    qualitySpotLevelConfBiz.listWithAllCurrentValidScene(confDto.getScene());
            if (CollectionUtils.isEmpty(riskQualitySpotLevelConfVos)) {
                log.error("riskQualitySpotLevelConfVos is empty, scene:{}", confDto.getScene());
                return true;
            }
            //查询数量
            strategyCount = new StrategyCount(0, riskQualitySpotLevelConfVos.get(0).getSamplingLevel(),
                    DateUtil.getYMDStringByDate(new Date()));
            redissonHandler.setEX(getStrategyCountKey(confDto.getScene()) , strategyCount, RedissonHandler.ONE_DAY);
        }
        return strategyCount.getCurrentCount() >= strategyCount.getTargetCount();
    }

    /**
     * 任务策略的key
     */
    private String getStrategyCountKey(long scene) {
        return DateUtil.getYMDStringByDate(new Date()) + "_" + scene;
    }


    /**
     * 规则每天的工单的key
     */
    private String getRuleCountKey(long ruleId) {
        return DateUtil.getYMDStringByDate(new Date()) + "_" + ruleId;
    }

    /**
     * 被质检人当天质检工单量 key
     * @param scene
     */
    private String getCheckerDayCountKey(long scene, String userKey) {
        return DateUtil.getYMDStringByDate(new Date()) + "_" + scene + "_" + userKey;
    }

    /**
     * 被质检人1小时内质检工单量 key
     */
    private String getCheckerHourCountKey(long scene,  String userKey) {
        return com.shuidihuzhu.common.util.DateUtil.getCurFormatDate("yyyyMMddHH") + "_" + scene + "_" + userKey;
    }

    /**
     * 1小时内质检工单量 key
     */
    private String getAllHourCountKey(long scene) {
        return com.shuidihuzhu.common.util.DateUtil.getCurFormatDate("yyyyMMddHH") + "_" + scene;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class StrategyCount {
        private int currentCount;
        private int targetCount;
        private String currentDate;
    }
}
