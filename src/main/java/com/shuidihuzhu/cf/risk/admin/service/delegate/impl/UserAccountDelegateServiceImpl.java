package com.shuidihuzhu.cf.risk.admin.service.delegate.impl;

import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserAccountDelegateService;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/26 10:56 AM
 */
@Slf4j
@Service
public class UserAccountDelegateServiceImpl implements UserAccountDelegateService {

    @Resource
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;

    @Override
    public MobileUserIdModel getUserIdByMobile(String mobile) {
        return simpleUserAccountServiceClient.getUserIdByMobile(mobile);
    }
}
