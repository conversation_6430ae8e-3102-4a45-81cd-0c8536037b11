package com.shuidihuzhu.cf.risk.admin.controller.qc;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcCalculateResult;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcChatRecordDetailInfo;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcChatTotalCount;
import com.shuidihuzhu.cf.risk.admin.model.qc1v1.RiskQcTotalCallRecords;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQc1v1DetailInfoVo;
import com.shuidihuzhu.cf.risk.admin.service.Risk1v1QcDetailService;
import com.shuidihuzhu.client.cf.growthtool.client.CfDiseaseTherapyFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCalcCostModel;
import com.shuidihuzhu.client.cf.growthtool.param.DiseaseParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.kratos.client.feign.WxChatMessageFeignClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/8/7
 */
@RestController
@RequestMapping(path = "/api/cf-risk-admin/qc-1v1/detail")
@Slf4j
public class Risk1v1QcDetailController {
    @Autowired
    private Risk1v1QcDetailService risk1v1QcDetailService;
    @Autowired
    private CfDiseaseTherapyFeignClient cfDiseaseTherapyFeignClient;

    @PostMapping(path = "get-calculate-info")
    public Response<RiskQcCalculateResult> getCalculateInfo(@RequestParam String ids, @RequestParam(required = false) Integer scene) {
        if (StringUtils.isBlank(ids)) {
            return NewResponseUtil.makeSuccess(new RiskQcCalculateResult(RiskQcResultEnum.QUALIFIED.getType(), 0, 0, 0));
        }
        List<Long> longIds = Splitter.on(",").splitToList(ids).stream().map(Long::valueOf).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(risk1v1QcDetailService.getResult(longIds, scene));
    }


    @PostMapping(path = "get-clew-task-info")
    @ApiOperation("获取服务任务信息")
    public Response<RiskQc1v1DetailInfoVo> getClewTaskInfo(@RequestParam long clewTaskId,
                                                           @RequestParam(required = false, defaultValue = "0")long workOrderId) {
        log.info("getClewTaskInfo clewTaskId:{} workOrderId:{}", clewTaskId, workOrderId);
        RiskQc1v1DetailInfoVo riskQc1v1DetailInfo = risk1v1QcDetailService.getClewTaskInfo(clewTaskId, workOrderId);
        return NewResponseUtil.makeSuccess(riskQc1v1DetailInfo);
    }

    @PostMapping(path = "get-call-record")
    @ApiOperation("返回通话信息")
    public Response<RiskQcTotalCallRecords> getCallRecord(@RequestParam long clewTaskId,
                                                          @RequestParam(defaultValue = "2") int qcType) {
        log.info("getCallRecord clewTaskId:{}", clewTaskId);
        RiskQcTotalCallRecords riskQcTotalCallRecords = (qcType == QcTypeEnum.WX_1V1.getCode()) ?
                risk1v1QcDetailService.getCallRecord(clewTaskId) : risk1v1QcDetailService.getCallRecordByBesides(clewTaskId);
        return NewResponseUtil.makeSuccess(riskQcTotalCallRecords);
    }


    @PostMapping(path = "get-chat-record")
    @ApiOperation("返回聊天记录信息")
    public Response<List<RiskQcChatRecordDetailInfo>> getChatRecord(@RequestParam long clewTaskId, @RequestParam int pageNum,
                                                              @RequestParam(defaultValue = "10") int pageSize,
                                                              @RequestParam String searchTime) {
        log.info("getChatRecord clewTaskId:{},pageNum:{},pageSize:{},searchTime:{}", clewTaskId, pageNum, pageSize, searchTime);
        if (Objects.isNull(DateUtil.parseDateTime(searchTime))){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<RiskQcChatRecordDetailInfo> riskQcChatRecord =
                risk1v1QcDetailService.getChatSearch(clewTaskId, pageNum, pageSize, searchTime);
        return NewResponseUtil.makeSuccess(riskQcChatRecord);
    }

    @PostMapping(path = "get-chat-record-count")
    @ApiOperation("返回聊天记录数量")
    public Response<List<RiskQcChatTotalCount>> getChatRecordCount(@RequestParam long clewTaskId){
        List<RiskQcChatTotalCount> riskQcChatRecord = risk1v1QcDetailService.getChatRecordCount(clewTaskId);
        return NewResponseUtil.makeSuccess(riskQcChatRecord);
    }

    @PostMapping(path = "calc-cost-log")
    @ApiOperation("返回花费记录")
    public Response<List<CfCalcCostModel>> calcCostLog(@RequestParam long clewTaskId) {
        log.info("calcCostLog clewTaskId:{}", clewTaskId);
        DiseaseParam diseaseParam = new DiseaseParam();
        diseaseParam.setTaskId(clewTaskId);
        return cfDiseaseTherapyFeignClient.calcCostLog(diseaseParam);
    }


}
