package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.DiscussionCheckRecord;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;


public interface DiscussionCheckBiz {

    Response pass(int userId, int caseId, String infoUuid);

    Response refuse(int userId, int caseId, String infoUuid, String reason);

    PageResponse<DiscussionCheckRecord> refuseList(int caseId, String pageJson);

}
