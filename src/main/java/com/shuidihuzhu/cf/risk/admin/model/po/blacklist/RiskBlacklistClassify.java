package com.shuidihuzhu.cf.risk.admin.model.po.blacklist;

import lombok.Data;

import java.util.Date;

@Data
public class RiskBlacklistClassify {
    /**
     * 主键
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 分类层级
     */
    private Integer level;

    /**
     * 层级路径
     */
    private String levelPath;

    /**
     * 状态，0 启用 1 弃用
     */
    private Byte status;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}