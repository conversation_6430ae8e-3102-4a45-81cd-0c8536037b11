package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConf;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQualitySpotLevelConfDao {
    int insertSelective(RiskQualitySpotLevelConf record);

    RiskQualitySpotLevelConf selectByPrimaryKey(Long id);

    int updateSamplingLevelById(@Param("id") Long id, @Param("samplingLevel") Integer samplingLevel);

    List<RiskQualitySpotLevelConf> selectBySceneAndExpireTime(@Param("scene") Long scene, @Param("expireTime") String expireTime);

    int updateExpireTimeById(@Param("id") Long id, @Param("expireTime") String expireTime);

    List<RiskQualitySpotLevelConf> listWithAllValidScene(@Param("scenes") List<Long> scenes, @Param("parseTime") Date parseTime);

    List<RiskQualitySpotLevelConf> listWithAllValid(@Param("parseTime") Date parseTime);

}