package com.shuidihuzhu.cf.risk.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.whiteList.WhiteListQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.whiteList.RiskWhiteListAddVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.whiteList.RiskWhiteListLogVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.whiteList.RiskWhiteListVo;
import com.shuidihuzhu.cf.risk.admin.service.whitelist.RiskWhiteListService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/7
 */
@Slf4j
@RequiresPermission("white-list:list")
@RestController
@RequestMapping(path = "/api/cf-risk-admin/white-list/")
public class WhiteListController {

    @Autowired
    private RiskWhiteListService riskWhiteListService;

    @ApiOperation("添加白名单")
    @RequiresPermission("white-list:update")
    @PostMapping("/add")
    public Response<Void> save(RiskWhiteListAddVo riskWhiteListAddVo) {
        log.info("riskWhiteListAddVo add:{}", JSONObject.toJSONString(riskWhiteListAddVo));
        if (StringUtils.isBlank(riskWhiteListAddVo.getIdCard()) && StringUtils.isBlank(riskWhiteListAddVo.getPhoneNumber())){
            return NewResponseUtil.makeError(RiskAdminErrorCode.ID_CARD_OR_PHONE_MUST_EXIST);
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0){
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        return riskWhiteListService.add(riskWhiteListAddVo, adminUserId);
    }

    @ApiOperation("更新白名单")
    @RequiresPermission("white-list:update")
    @PostMapping("/update")
    public  Response<Void> update(RiskWhiteListAddVo riskWhiteListAddVo) {
        log.info("riskWhiteListAddVo update:{}", JSONObject.toJSONString(riskWhiteListAddVo));
        if (StringUtils.isBlank(riskWhiteListAddVo.getIdCard()) && StringUtils.isBlank(riskWhiteListAddVo.getPhoneNumber())){
            return NewResponseUtil.makeError(RiskAdminErrorCode.ID_CARD_OR_PHONE_MUST_EXIST);
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0){
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        return riskWhiteListService.update(riskWhiteListAddVo, adminUserId);
    }

    @ApiOperation("编辑有效期")
    @RequiresPermission("white-list:update")
    @PostMapping("/update-expire-time")
    public  Response<Void> updateExpireTime(@ApiParam("有效期")@RequestParam(value ="expireTime" ) String expireTime,
                                     @ApiParam("白名单序号")@RequestParam(value ="id" ) long id) {
        log.info("riskWhiteListAddVo updateExpireTime:{} id:{}", expireTime, id);
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0){
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        Date expireTimeDate = DateUtil.getDateFromLongString(expireTime);
        return riskWhiteListService.updateExpireTime(expireTimeDate, id,adminUserId);
    }

    @ApiOperation("获取信息回显")
    @RequiresPermission("white-list:get")
    @PostMapping("/get")
    public  Response<RiskWhiteListVo> get(@ApiParam("白名单序号")@RequestParam(value ="id" ) long id) {
        log.info("riskWhiteListAddVo get:{}",  id);
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        return riskWhiteListService.get(id);
    }


    @ApiOperation("获取操作日志")
    @RequiresPermission("white-list:get")
    @PostMapping("/log/get")
    public  Response<List<RiskWhiteListLogVo>> getLog(@ApiParam("白名单序号")@RequestParam(value ="id" ) long id) {
        log.info("riskWhiteListAddVo getLog:{}",  id);
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        return riskWhiteListService.getLog(id);
    }


    @ApiOperation("获取白名单列表")
    @RequiresPermission("white-list:get")
    @PostMapping("/list/get")
    public  Response<PageResult<RiskWhiteListVo>> getList(@RequestBody WhiteListQuery whiteListQuery) {
        log.info("riskWhiteListAddVo getList:{}",  "");
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        return riskWhiteListService.getList(whiteListQuery);
    }

    @ApiOperation(value = "获取白名单type", notes = "需要区分权限权")
    @PostMapping(path = "/type/get")
    public Response<List<Long>> getTypeList() {
        long adminUserId = ContextUtil.getAdminLongUserId();
        String authSaasAppCode = AuthSaasContext.getAuthAppCode();
        return NewResponseUtil.makeSuccess(riskWhiteListService.getWhiteType(adminUserId, authSaasAppCode));
    }

}
