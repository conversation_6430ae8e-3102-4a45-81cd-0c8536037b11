package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTaskModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoStatModel;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderOrgRel;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-08-10 16:20
 **/
@Service
@RocketMQListener(id = RiskMQTagCons.QC_CALL_WORK_ORDER_CREATE,
        tags = RiskMQTagCons.QC_CALL_WORK_ORDER_CREATE,
        topic = MQTopicCons.CF)
@Slf4j
public class QcCallWorkOrderCreateConsumer implements MessageListener<CfClueInfoStatModel> {

    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Autowired
    private UserGroupFeignClient userGroupFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfClueInfoStatModel> mqMessage) {
        CfClueInfoStatModel cfClueInfoStatModel = mqMessage.getPayload();
        CfClewTaskDO cfClewTaskDO = cfClueInfoStatModel.getCfClewTaskDO();
        log.info("QcCallWorkOrderCreateConsumer cfClewTaskDO:{}", JSONObject.toJSONString(cfClewTaskDO));

        Long clewId = cfClewTaskDO.getId();
        String lockName = "qcLock_" + QcTypeEnum.CALL.getCode() + "_" + clewId + "_"
                + cfClueInfoStatModel.getCfClewTaskDO().getUserId();

        String identifier = "";
        try {
            identifier = cfRiskRedissonHandler.tryLock(lockName, 3 * 1000L, 30 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                return ConsumeStatus.RECONSUME_LATER;
            }
            //检查是否已生成工单
            Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(clewId.intValue(),
                    WorkOrderType.qc_call.getType());
            if (lastWorkOrder.ok() && Objects.nonNull(lastWorkOrder.getData())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Response<List<CfClewTaskModel>> clewTaskListResponse = cfClewtrackTaskFeignClient.getClewTaskModel(List.of(clewId));
            if (clewTaskListResponse == null || clewTaskListResponse.notOk()) {
                throw new RuntimeException("rpc fail");
            }
            //创建质检基本信息
            RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
            riskQcBaseInfo.setTaskId(clewId);
            riskQcBaseInfo.setQcType(QcTypeEnum.CALL.getCode());
            riskQcBaseInfo.setOrderType(WorkOrderType.qc_call.getType());
            riskQcBaseInfo.setQcUniqueCode(cfClewTaskDO.getUserId());
            riskQcBaseInfo.setQcByName(cfClewTaskDO.getUserName());
            riskQcBaseInfoBiz.addQc(riskQcBaseInfo);

            //记录组织结构id
            long userId = this.getUserIdByMis(cfClewTaskDO.getUserId());
            long orgId = this.getOrgIdByUserId(userId, cfClewTaskDO.getUserId());

            RiskQcMaterialsInfo riskQcMaterialsInfo = new RiskQcMaterialsInfo();
            riskQcMaterialsInfo.setQcId(riskQcBaseInfo.getId());
            riskQcMaterialsInfo.setMaterialsKey(QcMaterialsKeyEnum.ORG_ID.getKey());
            riskQcMaterialsInfo.setMaterialsValue(Long.toString(orgId));
            riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfo);


            //执行工单创建逻辑
            QcWorkOrder qcWorkOrder = new QcWorkOrder();
            qcWorkOrder.setCaseId(clewId.intValue());
            qcWorkOrder.setQcId(riskQcBaseInfo.getId());
            qcWorkOrder.setOrderType(WorkOrderType.qc_call.getType());
            qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
            qcWorkOrder.setComment("生成外呼质检工单");
            Response<Long> clientQcWorkOrder = cfQcWorkOrderClient.createQcWorkOrder(qcWorkOrder);

            if (clientQcWorkOrder.ok() && Objects.nonNull(clientQcWorkOrder.getData())) {
                log.info("QcCallWorkOrderCreate id:{}", clientQcWorkOrder.getData());
                //调用质检操作记录接口，记录操作记录
                String content = "生成外呼质检工单,工单ID【" + clientQcWorkOrder.getData() + "】";
                riskQcLogService.addLog(RiskQcOperationTypeEnum.CREATE_COMMON_WORK_ORDER, clientQcWorkOrder.getData(), content);
                String organization = this.getOrganization(userId, cfClewTaskDO.getUserId());
                // 添加搜索索引字段聚合表
                RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
                riskQcSearchIndex.setTaskId(clewId);
                riskQcSearchIndex.setQcId(riskQcBaseInfo.getId());
                riskQcSearchIndex.setWorkOrderId(clientQcWorkOrder.getData());
                riskQcSearchIndex.setQcType(QcTypeEnum.CALL.getCode());
                riskQcSearchIndex.setOrganization(organization);
                riskQcSearchIndex.setQcUniqueCode(cfClewTaskDO.getUserId());
                riskQcSearchIndex.setQcByName(cfClewTaskDO.getUserName());

                CfClewTaskModel cfClueInfoModel = clewTaskListResponse.getData().get(0);
                riskQcSearchIndex.setRegisterMobileEncrypt(Optional.ofNullable(oldShuidiCipher.aesEncrypt(cfClueInfoModel.getMobile())).orElse(StringUtils.EMPTY));
                riskQcSearchIndex.setJobContent(cfClueInfoModel.getWorkContentType());
                riskQcSearchIndex.setCallCluesChannel(cfClueInfoModel.getPrimaryChannel());
                riskQcSearchIndex.setCallTaskStatus(cfClueInfoModel.getShowPageNameEnum().getCode());
                riskQcSearchIndex.setFirstLevelLabel(cfClueInfoModel.getFirstTag() == null ? 0 : cfClueInfoModel.getFirstTag());
                riskQcSearchIndex.setTwoLevelLabel(cfClueInfoModel.getSecondTag() == null ? 0 : cfClueInfoModel.getSecondTag());
                riskQcSearchIndexBiz.addCallRiskQcSearchIndex(qcWorkOrder.getOrderType(), riskQcSearchIndex);

                this.addOrgId(cfClewTaskDO.getUserId(), userId, clientQcWorkOrder.getData());
                cfQcWorkOrderClient.addExtValue(clientQcWorkOrder.getData(), OrderExtName.qcUserName.getName(), cfClewTaskDO.getUserName());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

        } catch (Exception e) {
            log.error("QcCallWorkOrderCreateConsumer.consumeMessage error", e);
        } finally {
            if (StringUtils.isNotEmpty(identifier)) {
                cfRiskRedissonHandler.unLock(lockName, identifier);
            }
        }
        return ConsumeStatus.RECONSUME_LATER;
    }

    private void addOrgId(String mis, long userId, long workOrderId) {
        List<AuthGroupDto> adminOrganizations = null;
        if (mis.startsWith("SD")) {
            Response<List<AuthGroupDto>> superiorOrgByUserIdWithDb = userGroupFeignClient.getUserOrgs(userId);
            if (superiorOrgByUserIdWithDb.ok()) {
                adminOrganizations = superiorOrgByUserIdWithDb.getData();
            }
        } else {
            Response<List<AuthGroupDto>> superiorOrgByUserIdWithDb = userGroupFeignClient.getUserOrgs(userId);
            if (superiorOrgByUserIdWithDb.ok()) {
                adminOrganizations = superiorOrgByUserIdWithDb.getData();
            }
        }
        if (CollectionUtils.isNotEmpty(adminOrganizations)) {
            Set<Long> orgIds = Sets.newHashSet();
            for (AuthGroupDto authGroupDto : adminOrganizations) {
                orgIds.add(authGroupDto.getGroupBizId());
            }
            var workOrderOrgRels = orgIds.stream().map(orgId -> {
                WorkOrderOrgRel workOrderOrgRel = new WorkOrderOrgRel();
                workOrderOrgRel.setWorkOrderId(workOrderId);
                workOrderOrgRel.setOrgId(Math.toIntExact(orgId));
                return workOrderOrgRel;
            }).collect(Collectors.toList());
            cfQcWorkOrderClient.addOrgId(workOrderOrgRels);
        }
    }

    private String getOrganization(long userId, String mis) {
        Response<String> authRpcResponse = userGroupFeignClient.getGroupNameByUserId(userId);
        if (authRpcResponse.notOk()) {
            return "";
        }
        return authRpcResponse.getData();
    }

    private long getOrgIdByUserId(long userId, String mis) {
        Response<AuthGroupDto> userOrgInfo = userGroupFeignClient.selectByUserId(userId);
        if (userOrgInfo.notOk()) {
            return 0;
        }
        final AuthGroupDto data = userOrgInfo.getData();
        if (data == null) {
            return 0;
        }
        return data.getGroupBizId();
    }

    private long getUserIdByMis(String mis) {
        //外包
        if (mis.startsWith("SD")) {
            Response<AuthUserDto> userAccountByMis = userFeignClient.getByLoginName(mis);
            if (userAccountByMis.ok()) {
                return userAccountByMis.getData().getUserId();
            }
            return 0;
        }
        //内部
        Response<AuthUserDto> userAccountByMis = userFeignClient.getByLoginName(mis);
        if (userAccountByMis.ok() && userAccountByMis.getData() != null && userAccountByMis.getData().getUserId() != null) {
            return userAccountByMis.getData().getUserId();
        }
        return 0;
    }
}
