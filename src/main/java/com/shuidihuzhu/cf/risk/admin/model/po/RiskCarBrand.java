package com.shuidihuzhu.cf.risk.admin.model.po;

import lombok.Data;

import java.util.Date;
@Data
public class RiskCarBrand {
    /**
     * 主键
     */
    private Long id;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌类型：0 未知，1 普通品牌，2 知名豪车
     */
    private Byte brandType;

    /**
     * 车辆品牌首字母
     */
    private String initial;

    /**
     * 车牌标识
     */
    private String signPicture;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}