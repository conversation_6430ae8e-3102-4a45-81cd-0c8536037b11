package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseDataBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseKnowledgeBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseKnowledgeDao;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseKnowledge;
import com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseCheckDto;
import com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseKnowledgeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DiseaseKnowledgeBizImpl implements RiskDiseaseKnowledgeBiz {

    @Autowired
    private RiskDiseaseKnowledgeDao diseaseKnowledgeDao;
    @Autowired
    private RiskDiseaseDataBiz riskDiseaseDataBiz;

    @Override
    public void handleList(List<RiskDiseaseKnowledge> diseaseKnowledgeList) {
        if (CollectionUtils.isEmpty(diseaseKnowledgeList)) {
            return;
        }
        int row = 0;
        log.info("DiseaseKnowledgeBiz handleList size:{}", diseaseKnowledgeList.size());
        for (RiskDiseaseKnowledge diseaseKnowledge : diseaseKnowledgeList) {
            row++;
            if (row < 2) {
                continue;
            }
            String number = diseaseKnowledge.getNumber();
            if (StringUtils.isBlank(number)) {
                continue;
            }
            if (("number").equals(number.trim())) {
                continue;
            }
            if (!number.trim().contains("-")) {
                RiskDiseaseData diseaseData = riskDiseaseDataBiz.getById(Long.parseLong(number.trim()));
                diseaseKnowledge.setDiseaseName(diseaseData.getDiseaseClassName());
            }
            Long id = diseaseKnowledgeDao.existByNumber(number);
            diseaseKnowledge.init();
            if (id == null || id <= 0L) {
                log.info("DiseaseKnowledgeBiz handleList add number:{}", number);
                diseaseKnowledgeDao.add(diseaseKnowledge);
            } else {
                log.info("DiseaseKnowledgeBiz handleList update number:{}", number);
                diseaseKnowledgeDao.update(diseaseKnowledge);
            }

        }
    }


    @Override
    public long addOrUpdate(DiseaseKnowledgeDto diseaseKnowledgeDto) {
        if (diseaseKnowledgeDto == null) {
            return 0;
        }
        RiskDiseaseKnowledge diseaseKnowledge = new RiskDiseaseKnowledge();
        BeanUtils.copyProperties(diseaseKnowledgeDto, diseaseKnowledge);
        diseaseKnowledge.init();
        if (diseaseKnowledgeDto.getId() == 0L) { //新增
            String number = diseaseKnowledgeDto.getNumber();
            DiseaseKnowledgeDto diseaseKnowledgeDB = diseaseKnowledgeDao.findByNumber(number);
            if (diseaseKnowledgeDB != null) {
                return 0;
            }
            if (!StringUtils.contains(number, "-")) {
                RiskDiseaseData diseaseData = riskDiseaseDataBiz.getById(Long.parseLong(number));
                if (diseaseData == null) {
                    return 0;
                } else {
                    diseaseKnowledge.setDiseaseName(diseaseData.getDiseaseClassName());
                }
            }
            int addRes = diseaseKnowledgeDao.add(diseaseKnowledge);
            return addRes > 0 ? diseaseKnowledge.getId() : 0L;
        } else { //更新
            int updateRes = diseaseKnowledgeDao.updateById(diseaseKnowledge);
            return updateRes > 0 ? diseaseKnowledge.getId() : 0L;
        }
    }

    @Override
    public DiseaseKnowledgeDto detail(long id) {
        return diseaseKnowledgeDao.findById(id);
    }

    @Override
    public Map<Integer, List<DiseaseKnowledgeDto>> list(String diseaseName, String startTime, String endTime, int limit, int offset) {
        List<DiseaseKnowledgeDto> diseaseKnowledgeList = diseaseKnowledgeDao.listByParam(diseaseName, startTime, endTime, limit, offset);
        Map<Integer, List<DiseaseKnowledgeDto>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(diseaseKnowledgeList)) {
            return null;
        }
        result.put(diseaseKnowledgeDao.countByParam(diseaseName, startTime, endTime), diseaseKnowledgeList);
        return result;
    }

    @Override
    public int del(long id) {
        return diseaseKnowledgeDao.updateDelById(id);
    }

    @Override
    public DiseaseCheckDto getCheckDetail(String number) {
        if (StringUtils.isBlank(number)) {
            return null;
        }
        DiseaseCheckDto checkDto = new DiseaseCheckDto();
        checkDto.setNumber(number);
        DiseaseKnowledgeDto diseaseKnowledge = diseaseKnowledgeDao.findByNumber(number);
        if (diseaseKnowledge != null) {
            checkDto.setRepeat(true);
        }
        if (StringUtils.contains(number, "-")) {
            checkDto.setLevel(2);
        } else {
            checkDto.setLevel(1);
            long id = 0L;
            try {
                id = Long.parseLong(number);
            } catch (NumberFormatException e) {
                log.error("", e);
            }
            if (id > 0L) {
                RiskDiseaseData diseaseData = riskDiseaseDataBiz.getById(id);
                checkDto.setDiseaseName(diseaseData == null ? "" : diseaseData.getDiseaseClassName());
            }
        }
        return checkDto;
    }

    @Override
    public List<String> findDiseaseNormList(String diseaseNorm) {
        List<RiskDiseaseData> riskDiseaseDataList = riskDiseaseDataBiz.findDiseaseNormList(diseaseNorm);
        if (CollectionUtils.isEmpty(riskDiseaseDataList)) {
            return null;
        }
        List<String> diseaseNormList = riskDiseaseDataList.stream().map(RiskDiseaseData::getDiseaseClassName).collect(Collectors.toList());

        if ("急性髓系白血病".contains(diseaseNorm)) {
            diseaseNormList.add("急性髓系白血病");
        }

        if ("急性淋巴细胞白血病".contains(diseaseNorm)) {
            diseaseNormList.add("急性淋巴细胞白血病");
        }

        return diseaseNormList;
    }
}
