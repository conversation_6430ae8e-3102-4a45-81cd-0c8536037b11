package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskPsSimpleInfoVO;

import java.util.List;
import java.util.Map;

public interface RiskPublicSentimentInfoBiz {
    int add(String riskPublicSentimentInfoJson, String detailInformationJson, long adminUserId);

    int updateById(String riskPublicSentimentInfoJson, long id, String detailInformationJson, long adminUserId);

    RiskPublicSentimentInfo getInfoById(long id);

    Map<String, Object> getInfoList(int infoSource, int infoFeedBack, int infoClassify, String startTime,
                                    String endTime, String publicSentimentInfoType, int solution, int caseId,
                                    int pageNo, int pageSize, String lastOperator, String disposeStatus, String infoFeedBackOther);

    List<RiskPsSimpleInfoVO> getInfoByCaseId(int caseId);

    int countPsByCaseId(int caseId);

    List<String> getByInfoFeedBack();


}
