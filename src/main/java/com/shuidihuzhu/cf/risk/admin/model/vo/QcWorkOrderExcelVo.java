package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.util.Date;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-07-10 13:35
 **/
@Data
public class QcWorkOrderExcelVo extends BaseRowModel {

    @ExcelProperty(value = "顾问姓名", index = 0)
    private String bdName;
    @ExcelProperty(value = "顾问所在组织", index = 1)
    private String organization;
    @ExcelProperty(value = "案例ID", index = 2)
    private long caseId;
    @ExcelProperty(value = "代录入ID", index = 3)
    private long materialId;
    @ExcelProperty(value = "患者姓名", index = 4)
    private String patientName;
    @ExcelProperty(value = "发起人电话号码", index = 5)
    private String mobile;
    @ExcelProperty(value = "总录音时长", index = 6)
    private int totalDuration;
    @ExcelProperty(value = "一级质检结果", index = 7)
    private String firstQcResult;
    @ExcelProperty(value = "二级质检结果", index = 8)
    private String secondQcResult;
    @ExcelProperty(value = "高压线(对应问题描述)", index = 9)
    private String highTensionLine;
    @ExcelProperty(value = "一类违规(对应问题描述)", index = 10)
    private String oneType;
    @ExcelProperty(value = "二类违规(对应问题描述)", index = 11)
    private String twoType;
    @ExcelProperty(value = "服务问题(对应问题描述)", index = 12)
    private String serviceProblems;
    @ExcelProperty(value = "录音备注", index = 13)
    private String recordingNotes;
    @ExcelProperty(value = "其他备注", index = 14)
    private String otherNotes;
    @ExcelProperty(value = "质检员", index = 15)
    private String operatorName;
    @ExcelProperty(value = "质检工单创建时间", index = 16)
    private Date createTime;
    @ExcelProperty(value = "质检工单分配时间", index = 17)
    private Date handleTime;
    @ExcelProperty(value = "质检工单处理时间", index = 18)
    private Date updateTime;
}
