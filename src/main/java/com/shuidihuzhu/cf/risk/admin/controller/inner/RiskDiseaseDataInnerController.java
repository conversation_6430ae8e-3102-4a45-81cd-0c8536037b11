package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseDataBiz;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseDataPortion;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/11
 */

@RestController
@Slf4j
public class RiskDiseaseDataInnerController {


    @Resource
    private RiskDiseaseDataBiz riskDiseaseDataBiz;

    //http://cf-risk-admin.cf-risk.svc/innerapi/cf-risk-admin/disease/get-all-disease-rule
    @ApiOperation("获取所有疾病归一规则,AI组使用")
    @PostMapping(path = "/innerapi/cf-risk-admin/disease/get-all-disease-rule")
    public Response<List<RiskDiseaseDataPortion>> getAllDiseaseRule() {

        List<RiskDiseaseDataPortion> allDiseaseRule = riskDiseaseDataBiz.getAllDiseaseRule();
        return CollectionUtils.isEmpty(allDiseaseRule) ? NewResponseUtil.makeFail(null) : NewResponseUtil.makeSuccess(allDiseaseRule);
    }
}
