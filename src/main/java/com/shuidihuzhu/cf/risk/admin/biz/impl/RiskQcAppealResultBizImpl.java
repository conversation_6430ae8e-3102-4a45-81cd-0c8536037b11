package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcAppealResultBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcAppealResultDao;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealResultModel;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/11/16
 */
@Service
public class RiskQcAppealResultBizImpl implements RiskQcAppealResultBiz {

    @Autowired
    private RiskQcAppealResultDao riskQcAppealResultDao;

    @Override
    public int addInfo(long workOrderId, String appealInfo, int appealResult, long qcId) {
        if (workOrderId <= 0){
            return 0;
        }
        return riskQcAppealResultDao.addInfo(workOrderId, appealInfo, appealResult, qcId);
    }

    @Override
    public RiskQcAppealResultModel getByWorkOrderId(long workOrderId) {
        if (workOrderId <= 0){
            return null;
        }
        return riskQcAppealResultDao.getByWorkOrderId(workOrderId);
    }

    @Override
    public List<RiskQcAppealResultModel> findByWorkOrderIds(List<Long> workOrderIdList) {
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return Lists.newArrayList();
        }
        return riskQcAppealResultDao.findByWorkOrderIds(workOrderIdList);
    }
}
