package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.clinet.event.center.constants.MQTagCons;
import com.shuidihuzhu.cf.clinet.event.center.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfPatientBaseInfoVo;
import com.shuidihuzhu.cf.risk.admin.biz.*;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQcAppealWorkOrderRelBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcAppealProblemStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcAppealResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.*;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealProblemModel;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealResultModel;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcResultVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcStandardDetailVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcStandardVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcVideoVo;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotResultIssueRequest;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.cf.growthtool.model.ReportRelation;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestNoticeModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/13
 */
@Service
@Slf4j
public class QualitySpotResultIssueInnerService {


    @Autowired
    private RiskQcStandardPropertyBiz qcStandardPropertyBiz;
    @Autowired
    private RiskQcStandExtBiz qcStandExtBiz;
    @Autowired
    private RiskQcResultBiz qcResultBiz;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private RiskQcCheckedVideoInfoBiz riskQcCheckedVideoInfoBiz;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private AuthorFeignClient authorFeignClient;
    @Autowired
    private RiskQcAppealResultBiz riskQcAppealResultBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private RiskQcAppealWorkOrderRelBiz appealWorkOrderRelBiz;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;


    @Value("${qc.appeal.issue-normal-standard:合规问题}")
    private String qcAppealIssueNormalStandard;
    @Value("${qc.appeal.issue-serious-standard:一类违规,二类违规,高压线}")
    private String qcAppealIssueSeriousStandard;

    private List<String> getQcAppealIssueNormalStandardList() {
        return Splitter.on(",").splitToList(qcAppealIssueNormalStandard);
    }

    private List<String> getQcAppealIssueSeriousStandardList() {
        return Splitter.on(",").splitToList(qcAppealIssueSeriousStandard);
    }


    public List<Long> offlineIssue(QualitySpotResultIssueRequest qualitySpotResultIssueRequest) {
        if (qualitySpotResultIssueRequest.isNormal()) {
            return offlineNormalIssue(qualitySpotResultIssueRequest.getWorkOrderVOS());
        }
        return offlineSeriousnessIssue(qualitySpotResultIssueRequest.getWorkOrderVOS());
    }

    private List<Long> offlineSeriousnessIssue(List<WorkOrderVO> workOrderVOS) {
        List<Long> seriousnessQcStandardId = getQcStandardId(getQcAppealIssueSeriousStandardList(), 1, 1);
        //查询对应的质检结果
        List<RiskQcResult> riskQcResultList = qcResultBiz.findByWorkOrderIds(workOrderVOS.stream()
                .map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(riskQcResultList)) {
            return Lists.newArrayList();
        }
        //过滤工单的结果
        riskQcResultList = filterIssueProperty(riskQcResultList, seriousnessQcStandardId, Lists.newArrayList());
        if (CollectionUtils.isEmpty(riskQcResultList)) {
            return Lists.newArrayList();
        }
        //下发MQ
        senQcResultMQ(riskQcResultList, workOrderVOS,
                CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.SERIOUS_ISSUE,
                CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.FIRST_NOTICE);
        return riskQcResultList.stream().map(RiskQcResult::getWorkOrderId).collect(Collectors.toList());
    }

    /**
     * 质检结果中勾选的问题描述，对应的一级属性是合规问题，且不包含一类违规、二类违规、高压线, 服务问题不支持申诉
     *
     * @param workOrderVOS
     * @return
     */
    private List<Long> offlineNormalIssue(List<WorkOrderVO> workOrderVOS) {
        //获取属性对应的id
        List<Long> normalQcStandardId = getQcStandardId(getQcAppealIssueNormalStandardList(), 1, 1);
        List<Long> seriousnessQcStandardId = getQcStandardId(getQcAppealIssueSeriousStandardList(), 1, 1);
        //查询对应的质检结果
        List<RiskQcResult> riskQcResultList = qcResultBiz.findByWorkOrderIds(workOrderVOS.stream()
                .map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(riskQcResultList)) {
            return Lists.newArrayList();
        }
        //过滤工单的结果
        riskQcResultList = filterIssueProperty(riskQcResultList, normalQcStandardId, seriousnessQcStandardId);
        if (CollectionUtils.isEmpty(riskQcResultList)) {
            return Lists.newArrayList();
        }
        log.info("riskQcResultList:{}", riskQcResultList.stream().map(RiskQcResult::getWorkOrderId).collect(Collectors.toList()));
        //下发MQ
        senQcResultMQ(riskQcResultList, workOrderVOS,
                CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE,
                CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.FIRST_NOTICE);
        return riskQcResultList.stream().map(RiskQcResult::getWorkOrderId).collect(Collectors.toList());
    }

    /**
     * 下发质检结果的MQ
     *
     * @param riskQcResultList
     * @param workOrderVOS
     * @param issueType
     * @param noticeType
     */
    private void senQcResultMQ(List<RiskQcResult> riskQcResultList,
                               List<WorkOrderVO> workOrderVOS,
                               CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum issueType,
                               CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum noticeType) {

        Map<Long, WorkOrderVO> workOrderVOMap = workOrderVOS.stream().collect(Collectors.toMap(WorkOrderVO::getWorkOrderId, Function.identity()));
        List<Integer> caseIds = workOrderVOS.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());
        List<Long> workOrderIds = workOrderVOS.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        Map<Long, Long> reportMap = getReportIdMap(workOrderIds);
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = getInfoMap(caseIds);
        //患者姓名获取
        Map<Integer, String> payeeNameMap = getPayeeNameMap(caseIds);
        //获取顾问姓名
        Map<Long, String> qcBaseInfoMap = getLongRiskQcBaseInfoMap(workOrderVOS);
        //获取问题录音
        List<RiskQcCheckedVideoInfo> checkIdList = riskQcCheckedVideoInfoBiz.findByWorkOrderId(Lists.newArrayList(workOrderVOMap.keySet()));
        Map<Long, String> checkIdMap = checkIdList.stream().collect(Collectors.toMap(RiskQcCheckedVideoInfo::getWorkOrderId,
                RiskQcCheckedVideoInfo::getCheckedId));

        for (RiskQcResult riskQcResult : riskQcResultList) {
            WorkOrderVO workOrderVO = workOrderVOMap.get(riskQcResult.getWorkOrderId());
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(workOrderVO.getCaseId());
            //直接主体
            CfGwReplaceInputQualityTestNoticeModel cfGwReplaceInputQualityTestNoticeModel = new CfGwReplaceInputQualityTestNoticeModel();
            cfGwReplaceInputQualityTestNoticeModel.setIssueType(issueType);
            cfGwReplaceInputQualityTestNoticeModel.setNoticeType(noticeType);
            //质检结果主体
            CfGwReplaceInputQualityTestNoticeModel.QualityTestNoticeInfo qualityTestNoticeInfo = new CfGwReplaceInputQualityTestNoticeModel.QualityTestNoticeInfo();
            cfGwReplaceInputQualityTestNoticeModel.setQualityTestNoticeInfo(qualityTestNoticeInfo);
            qualityTestNoticeInfo.setCaseId(workOrderVO.getCaseId());
            qualityTestNoticeInfo.setInfoUuid(crowdfundingInfo.getInfoId());
            qualityTestNoticeInfo.setCaseTitle(crowdfundingInfo.getTitle());
            qualityTestNoticeInfo.setReportId(reportMap.getOrDefault(workOrderVO.getWorkOrderId(), 0L));
            qualityTestNoticeInfo.setCreateTime(new Date());
            qualityTestNoticeInfo.setWorkOrderId(workOrderVO.getWorkOrderId());
            qualityTestNoticeInfo.setPatientName(payeeNameMap.getOrDefault(workOrderVO.getCaseId(), ""));
            qualityTestNoticeInfo.setGwName(qcBaseInfoMap.getOrDefault(workOrderVO.getQcId(), ""));
            //问题详情
            RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), RiskQcResultVo.class);
            if (riskQcResultVo == null) {
                continue;
            }
            qualityTestNoticeInfo.setAudioRemark(riskQcResultVo.getVoiceRemark());
            qualityTestNoticeInfo.setOtherRemark(riskQcResultVo.getUserWriteRemark());
            qualityTestNoticeInfo.setQCTime(riskQcResult.getCreateTime());
            //问题录音
            qualityTestNoticeInfo.setIssueAudios(findNeedAudios(riskQcResult.getQcId(),
                    workOrderVO.getQcId(), checkIdMap.get(riskQcResult.getWorkOrderId())));
            //问题信息
            qualityTestNoticeInfo.setIssueInfos(formatTransferByQcResult(riskQcResultVo));
            producer.send(new Message<>(MQTopicCons.CF, CfClientMQTagCons.CF_GW_REPLACE_INPUT_QUALITY_TEST_NOTICE,
                    MQTagCons.USER_EVENT_FOR_EVENT_CENTER + "_" + riskQcResult.getId(), cfGwReplaceInputQualityTestNoticeModel));
            riskQcLogService.addLog(RiskQcOperationTypeEnum.ISSUE_QC_RESULT, riskQcResult.getWorkOrderId(),"下发质检结果");
        }

    }


    /**
     * 下发申诉结果的MQ
     *
     * @param workOrderVOS
     * @param issueType
     * @param noticeType
     */
    public void sendQcAppealResultMQ(List<WorkOrderVO> workOrderVOS,
                                     CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum issueType,
                                     CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum noticeType) {
        List<Long> workOrderIds = workOrderVOS.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
        log.info("workOrderVOS Ids ：{} issueType:{} noticeType:{}", JSON.toJSONString(workOrderIds), issueType, noticeType);
        List<Integer> caseIds = workOrderVOS.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());
        Map<Long, Long> reportMap = getReportIdMap(workOrderIds);
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = getInfoMap(caseIds);
        //患者姓名获取
        Map<Integer, String> payeeNameMap = getPayeeNameMap(caseIds);
        //获取顾问姓名
        Map<Long, String> qcBaseInfoMap = getLongRiskQcBaseInfoMap(workOrderVOS);
        //获取申诉结果信息
        List<RiskQcAppealResultModel> riskQcAppealResultModels = riskQcAppealResultBiz.findByWorkOrderIds(workOrderIds);
        Map<Long, RiskQcAppealResultModel> modelMap = riskQcAppealResultModels.stream()
                .collect(Collectors.toMap(RiskQcAppealResultModel::getWorkOrderId, Function.identity()));


        for (WorkOrderVO workOrderVO : workOrderVOS) {
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(workOrderVO.getCaseId());
            //直接主体
            CfGwReplaceInputQualityTestNoticeModel cfGwReplaceInputQualityTestNoticeModel = new CfGwReplaceInputQualityTestNoticeModel();
            cfGwReplaceInputQualityTestNoticeModel.setIssueType(issueType);
            cfGwReplaceInputQualityTestNoticeModel.setNoticeType(noticeType);
            //质检结果主体
            CfGwReplaceInputQualityTestNoticeModel.QualityTestNoticeInfo qualityTestNoticeInfo = new CfGwReplaceInputQualityTestNoticeModel.QualityTestNoticeInfo();
            cfGwReplaceInputQualityTestNoticeModel.setQualityTestNoticeInfo(qualityTestNoticeInfo);
            qualityTestNoticeInfo.setCaseId(workOrderVO.getCaseId());
            qualityTestNoticeInfo.setInfoUuid(crowdfundingInfo.getInfoId());
            qualityTestNoticeInfo.setCaseTitle(crowdfundingInfo.getTitle());
            qualityTestNoticeInfo.setReportId(reportMap.getOrDefault(workOrderVO.getWorkOrderId(), 0L));
            qualityTestNoticeInfo.setCreateTime(new Date());
            qualityTestNoticeInfo.setWorkOrderId(workOrderVO.getWorkOrderId());
            qualityTestNoticeInfo.setPatientName(payeeNameMap.getOrDefault(workOrderVO.getCaseId(), ""));
            qualityTestNoticeInfo.setGwName(qcBaseInfoMap.getOrDefault(workOrderVO.getQcId(), ""));

            //获取质检结果
            RiskQcAppealResultModel riskQcAppealResultModel = modelMap.get(workOrderVO.getWorkOrderId());
            if (riskQcAppealResultModel == null) {
                log.error("riskQcAppealResultModel is error");
                continue;
            }
            RiskQcAppealInfoModel riskQcAppealInfoModel =
                    JSON.parseObject(riskQcAppealResultModel.getAppealInfo(), RiskQcAppealInfoModel.class);
            //质检动作
            qualityTestNoticeInfo.setQCAction(QcAppealResultEnum.findOfCode(riskQcAppealResultModel.getAppealResult()));
            //审批时间
            qualityTestNoticeInfo.setQCTime(riskQcAppealResultModel.getCreateTime());
            qualityTestNoticeInfo.setQCRemark(riskQcAppealInfoModel.getComment());
            qualityTestNoticeInfo.setIssueInfos(buildAppealProblem(riskQcAppealInfoModel.getRiskQcAppealProblemModels()));
            //问题录音
           fullQcResultInfo(workOrderVO, qualityTestNoticeInfo);
            log.info("cfGwReplaceInputQualityTestNoticeModel:{}", JSON.toJSONString(cfGwReplaceInputQualityTestNoticeModel));
            //问题信息
            producer.send(new Message<>(MQTopicCons.CF, CfClientMQTagCons.CF_GW_REPLACE_INPUT_QUALITY_TEST_NOTICE,
                    MQTagCons.USER_EVENT_FOR_EVENT_CENTER + "_" + riskQcAppealResultModel.getId(), cfGwReplaceInputQualityTestNoticeModel));
            issueAppealResultLog(workOrderVO.getWorkOrderId(), issueType, noticeType);
        }
    }

    /**
     * 下发申诉结果 日志
     */
    private void issueAppealResultLog(long appealWorkOrderId,
                                      CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum issueType,
                                      CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum noticeType) {
        RiskQcAppealWorkOrderRel appealWorkOrderRel = appealWorkOrderRelBiz.getByAppealWorkOrderId(appealWorkOrderId);
        if (appealWorkOrderRel == null || appealWorkOrderRel.getQcWorkOrderId() <= 0L) {
            log.info("appealWorkOrderId:{} illegal, issueAppealResultLog fail", appealWorkOrderId);
            return;
        }
        String log = "";
        switch (issueType) {
            case DEFAULT_ISSUE:
                log = "普通申诉工单";
                break;
            case SERIOUS_ISSUE:
                log = "严重问题申诉工单";
                break;
            default:
                break;
        }
        if (noticeType == CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.Third_NOTICE) {
            log = "二次申诉工单";
        }
        riskQcLogService.addLog(RiskQcOperationTypeEnum.ISSUE_QC_RESULT, appealWorkOrderRel.getQcWorkOrderId(), log + "，下发申诉判定结果");
    }

    private void fullQcResultInfo(WorkOrderVO workOrderVO, CfGwReplaceInputQualityTestNoticeModel.QualityTestNoticeInfo qualityTestNoticeInfo) {
        //获取初始的质检工单
        RiskQcAppealWorkOrderRel workOrderRel = appealWorkOrderRelBiz.getByAppealWorkOrderId(workOrderVO.getWorkOrderId());
        if (workOrderRel == null) {
            return;
        }
        long qcWorkOrderId = workOrderRel.getQcWorkOrderId();
        RiskQcResult riskQcResult = qcResultBiz.getByWorkOrderId(qcWorkOrderId);
        if (riskQcResult == null) {
            return;
        }
        RiskQcCheckedVideoInfo riskQcCheckedVideoInfo  = riskQcCheckedVideoInfoBiz.getByWorkOrderId(qcWorkOrderId);
        qualityTestNoticeInfo.setIssueAudios(findNeedAudios(riskQcResult.getQcId(),
                workOrderVO.getQcId(), riskQcCheckedVideoInfo == null? "" : riskQcCheckedVideoInfo.getCheckedId()));
        RiskQcResultVo riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), RiskQcResultVo.class);
        if (riskQcResultVo == null) {
            return;
        }
        qualityTestNoticeInfo.setAudioRemark(riskQcResultVo.getVoiceRemark());
        qualityTestNoticeInfo.setOtherRemark(riskQcResultVo.getUserWriteRemark());

    }

    private List<CfGwReplaceInputQualityTestNoticeModel.IssueInfo> buildAppealProblem(List<RiskQcAppealProblemModel> riskQcAppealProblemModels) {
        return riskQcAppealProblemModels.stream()
                .filter(v -> v.getStatus() != QcAppealProblemStatusEnum.PASS.getCode()).map(this::buildIssueInfo).collect(Collectors.toList());
    }

    private CfGwReplaceInputQualityTestNoticeModel.IssueInfo buildIssueInfo(RiskQcAppealProblemModel riskQcAppealProblemModel){
        CfGwReplaceInputQualityTestNoticeModel.IssueInfo issueInfo = new CfGwReplaceInputQualityTestNoticeModel.IssueInfo();
        issueInfo.setProperty(riskQcAppealProblemModel.getProperty());
        issueInfo.setRemark(riskQcAppealProblemModel.getProblem());
        issueInfo.setFeedbackStatus(riskQcAppealProblemModel.getStatus());
        return issueInfo;
    }


    private Map<Integer, String> getPayeeNameMap(List<Integer> caseIds) {
        FeignResponse<List<CfPatientBaseInfoVo>> listFeignResponse = authorFeignClient.selectPatientBaseInfoByCaseIds(caseIds);
        if (listFeignResponse.isFailOrNullData()) {
            log.info("listFeignResponse:{}", JSON.toJSONString(listFeignResponse));
            listFeignResponse = authorFeignClient.selectPatientBaseInfoByCaseIds(caseIds);
        }
        if (listFeignResponse.isFailOrNullData()) {
            log.error("listFeignResponse:{}", JSON.toJSONString(listFeignResponse));
            return Maps.newHashMap();
        }
        return listFeignResponse.getData().stream().collect(Collectors.toMap(CfPatientBaseInfoVo::getCaseId,
                CfPatientBaseInfoVo::getPatientName));
    }

    @NotNull
    private Map<Long, String> getLongRiskQcBaseInfoMap(List<WorkOrderVO> workOrderVOS) {
        List<RiskQcBaseInfo> riskQcBaseInfos = riskQcBaseInfoBiz.getByIds(workOrderVOS.stream()
                .map(WorkOrderVO::getQcId).collect(Collectors.toList()));
        return riskQcBaseInfos.stream()
                .collect(Collectors.toMap(RiskQcBaseInfo::getId, RiskQcBaseInfo::getQcByName, (k1, k2) -> k2));
    }

    /**
     * 转化问题详情
     *
     * @param riskQcResultVo
     * @return
     */
    private List<CfGwReplaceInputQualityTestNoticeModel.IssueInfo> formatTransferByQcResult(RiskQcResultVo riskQcResultVo) {
        List<RiskQcStandardDetailVo> riskQcStandardDetailVos = riskQcResultVo.getQcResultOption().stream()
                .map(RiskQcStandardVo::getRiskQcStandardSecondVos)
                .reduce(Lists.newArrayList(), (all, one) -> {
                    all.addAll(one);
                    return all;
                });
        //获取服务问题的标准id
        List<Long> serviceIds = getQcStandardId(Lists.newArrayList("服务问题"), 1, 1);
        riskQcStandardDetailVos = riskQcStandardDetailVos.stream()
                .filter(v -> !serviceIds.contains(v.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(riskQcStandardDetailVos)) {
            return Lists.newArrayList();
        }
        List<CfGwReplaceInputQualityTestNoticeModel.IssueInfo> issueInfoList = Lists.newArrayListWithCapacity(riskQcStandardDetailVos.size());
        for (RiskQcStandardDetailVo detailVo : riskQcStandardDetailVos) {
            CfGwReplaceInputQualityTestNoticeModel.IssueInfo issueInfo = new CfGwReplaceInputQualityTestNoticeModel.IssueInfo();
            issueInfo.setRemark(detailVo.getStandardName());
            List<String> propertyList = Splitter.on("-").splitToList(detailVo.getProperty());
            if (CollectionUtils.isNotEmpty(propertyList)) {
                issueInfo.setProperty(propertyList.get(0));
            }
            issueInfo.setAllProperty(detailVo.getProperty());
            issueInfoList.add(issueInfo);
        }
        return issueInfoList;
    }

    /**
     * 获取该条质检的问题录音
     *
     * @param qcId
     * @param workOrderQcId
     * @param checkIds
     * @return
     */
    private List<CfGwReplaceInputQualityTestNoticeModel.IssueAudio> findNeedAudios(long qcId, long workOrderQcId, String checkIds) {
        //获取qcID
        if (qcId <= 0) {
            qcId = workOrderQcId;
        }
        if (qcId <= 0) {
            log.error("findNeedAudios qcId is 0 !");
            return Lists.newArrayList();
        }
        //获取全部的录音
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos = riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.RECORDING.getKey());
        if (CollectionUtils.isEmpty(riskQcMaterialsInfos)) {
            return Lists.newArrayList();
        }
        List<RiskQcVideoVo> riskQcVideoVos = riskQcMaterialsInfos.stream()
                .filter(v -> StringUtils.isNotBlank(v.getMaterialsValue()))
                .map(v -> {
                    RiskQcVideoVo riskQcVideoVo = JSONObject.parseObject(v.getMaterialsValue(), RiskQcVideoVo.class);
                    riskQcVideoVo.setId(v.getId());
                    return riskQcVideoVo;
                })
                .collect(Collectors.toList());
        //若问题录音为空则返回的录音
        if (StringUtils.isBlank(checkIds)) {
            return riskQcVideoVos.stream().map(this::buildIssueAudio).collect(Collectors.toList());
        }
        List<Long> checkIdList = Splitter.on(",").splitToList(checkIds).stream()
                .map(Long::parseLong).collect(Collectors.toList());
        //获取勾选的问题录音
        return riskQcVideoVos.stream().filter(v -> checkIdList.contains(v.getId()))
                .map(this::buildIssueAudio).collect(Collectors.toList());
    }


    private CfGwReplaceInputQualityTestNoticeModel.IssueAudio buildIssueAudio(RiskQcVideoVo riskQcVideoVo) {
        CfGwReplaceInputQualityTestNoticeModel.IssueAudio issueAudio = new CfGwReplaceInputQualityTestNoticeModel.IssueAudio();
        issueAudio.setAudioUrl(riskQcVideoVo.getVideoUrl());
        issueAudio.setCreateTime(DateUtil.parseDateTime(riskQcVideoVo.getCreatTime()));
        return issueAudio;
    }

    private Map<Integer, CrowdfundingInfo> getInfoMap(List<Integer> caseIds) {
        FeignResponse<List<CrowdfundingInfo>> listFeignResponse = crowdfundingFeignClient.getCrowdfundingListById(caseIds);
        if (listFeignResponse.isFailOrNullData()) {
            log.info("listFeignResponse:{}", JSON.toJSONString(listFeignResponse));
            listFeignResponse = crowdfundingFeignClient.getCrowdfundingListById(caseIds);
        }
        if (listFeignResponse.isFailOrNullData()) {
            log.error("listFeignResponse:{}", JSON.toJSONString(listFeignResponse));
            return Maps.newHashMap();
        }
        return listFeignResponse.getData().stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));
    }

    /**
     * 根据案例id生成代录入idMap
     *
     * @param caseIds
     * @return
     */
    private Map<Integer, Long> getReportMap(List<Integer> caseIds) {
        Response<List<ReportRelation>> relationResponse = clewPreproseMaterialFeignClient.getByCaseIds(caseIds);
        //如果失败则重试一次
        if (relationResponse.notOk()) {
            log.info("relationResponse:{}", JSON.toJSONString(relationResponse));
            relationResponse = clewPreproseMaterialFeignClient.getByCaseIds(caseIds);
        }
        if (relationResponse.notOk()) {
            log.error("relationResponse:{}", JSON.toJSONString(relationResponse));
            return Maps.newHashMap();
        }
        List<ReportRelation> caseSpecialPrePoseDetails = relationResponse.getData();
        return caseSpecialPrePoseDetails.stream().collect(Collectors.toMap(ReportRelation::getCaseId,
                ReportRelation::getPreposeMaterialId, (a, b) -> b));

    }

    private Map<Long, Long> getReportIdMap(List<Long> workOrderIds) {
        List<RiskQcSearchIndex> riskQcSearchIndexList = riskQcSearchIndexBiz.getByWorkOrderIds(workOrderIds);
        if (CollectionUtils.isEmpty(riskQcSearchIndexList)) {
            return Maps.newHashMap();
        }
        return riskQcSearchIndexList.stream().collect(Collectors.toMap(RiskQcSearchIndex::getWorkOrderId,
                RiskQcSearchIndex::getMaterialId, (a, b) -> b));
    }


    /**
     * 筛选合适质检
     *
     * @param riskQcResultList    需要筛选的质检结果list
     * @param needQcStandardId    需要包含的标准id
     * @param excludeQcStandardId 需要排除的标准
     * @return
     */
    private List<RiskQcResult> filterIssueProperty(List<RiskQcResult> riskQcResultList, List<Long> needQcStandardId, List<Long> excludeQcStandardId) {
        List<RiskQcResult> resultList = Lists.newArrayList();
        for (RiskQcResult riskQcResult : riskQcResultList) {
            RiskQcResultVo riskQcResultVo;
            try{
                log.info("riskQcResult:{}", JSON.toJSONString(riskQcResult));
                 riskQcResultVo = JSON.parseObject(riskQcResult.getProblemDescribe(), RiskQcResultVo.class);
            } catch (JSONException e) {
                log.error("", e);
                continue;
            }
            if (CollectionUtils.isEmpty(riskQcResultVo.getQcResultOption())) {
                continue;
            }
            //获取服务问题的标准id
            List<Long> serviceIds =  getQcStandardId(Lists.newArrayList("服务问题"), 1, 1);
            List<RiskQcStandardDetailVo> riskQcStandardDetailVos = riskQcResultVo.getQcResultOption().stream()
                    .map(RiskQcStandardVo::getRiskQcStandardSecondVos)
                    .reduce(Lists.newArrayList(), (all, one) -> {
                        all.addAll(one);
                        return all;
                    });
            riskQcStandardDetailVos = riskQcStandardDetailVos.stream()
                    .filter(v -> !serviceIds.contains(v.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(riskQcStandardDetailVos)) {
                continue;
            }
            //获取二级标准id
            List<Long> standardIds = riskQcStandardDetailVos.stream().map(RiskQcStandardDetailVo::getId).distinct().collect(Collectors.toList());
            //判断是否包含需要标准id
            if (!standardIds.removeAll(needQcStandardId)) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(excludeQcStandardId) && standardIds.removeAll(excludeQcStandardId)) {
                continue;
            }
            resultList.add(riskQcResult);
        }
        return resultList;
    }

    private List<Long> getQcStandardId(List<String> firstPropertyList, int level, int useScene) {
        //获取属性
        List<RiskQcStandardProperty> qcStandardProperties = qcStandardPropertyBiz.findByNameAndLevel(firstPropertyList, level);
        if (CollectionUtils.isEmpty(qcStandardProperties)) {
            return Lists.newArrayList();
        }
        List<RiskQcStandardExt> riskQcStandardExts = qcStandExtBiz.findByFirstPropertyAndScene(
                qcStandardProperties.stream().map(RiskQcStandardProperty::getId).collect(Collectors.toList()), useScene);
        if (CollectionUtils.isEmpty(riskQcStandardExts)) {
            return Lists.newArrayList();
        }
        return riskQcStandardExts.stream().map(RiskQcStandardExt::getQcStandardId)
                .distinct().collect(Collectors.toList());
    }

    public List<Long> offlineAppealIssue(QualitySpotResultIssueRequest qualitySpotResultIssueRequest) {
        sendQcAppealResultMQ(qualitySpotResultIssueRequest.getWorkOrderVOS(),
                CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE,
                CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.SECOND_NOTICE);
        return  qualitySpotResultIssueRequest.getWorkOrderVOS().stream()
                .map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
    }

    @Async
    public void dealQcAppealResultMQ(long workOrderId, long caseId, int orderType) {
        log.info("workOrderId:{} problemDescription:{}", workOrderId, orderType);
        WorkOrderVO workOrderVO = new WorkOrderVO();
        workOrderVO.setWorkOrderId(workOrderId);
        workOrderVO.setCaseId((int) caseId);
        Response<List<WorkOrderExt>> response =
                cfWorkOrderClient.listExtInfos(Lists.newArrayList(workOrderId), OrderExtName.qcId.getName());
        if (response.notOk()) {
            response =  cfWorkOrderClient.listExtInfos(Lists.newArrayList(workOrderId), OrderExtName.qcId.getName());
        }
        log.info("response:{}", JSON.toJSONString(response));
        if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            workOrderVO.setQcId(Long.parseLong(response.getData().get(0).getExtValue()));
        }
        qcResultBiz.getByWorkOrderId(workOrderId);
        List<WorkOrderVO> workOrderVOS = Lists.newArrayList(workOrderVO);
        if (orderType == WorkOrderType.qc_second_complaint.getType()) {
            sendQcAppealResultMQ(workOrderVOS,
                    CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE,
                    CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.Third_NOTICE);
        } else if (orderType == WorkOrderType.qc_serious_complaint.getType()) {
            sendQcAppealResultMQ(workOrderVOS, CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.SERIOUS_ISSUE,
                    CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.SECOND_NOTICE);
        }
    }
}
