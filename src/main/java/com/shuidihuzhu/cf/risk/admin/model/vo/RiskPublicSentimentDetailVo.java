package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.google.gson.Gson;
import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail;
import lombok.Data;

import java.util.Map;

@Data
public class RiskPublicSentimentDetailVo {

    private String title;
    private String url;
    private String content;
    private String specialForwarding;
    private String videoUrl;
    private String imgUrl;
    private Map<String, Object> supplementInfo;
    private Map<String, Object> fermentationCondition;


    public static RiskPublicSentimentDetailVo buildVo(RiskPublicSentimentDetail riskPublicSentimentDetail) {
        RiskPublicSentimentDetailVo riskPublicSentimentDetailVo = new RiskPublicSentimentDetailVo();
        riskPublicSentimentDetailVo.setTitle(riskPublicSentimentDetail.getTitle());
        riskPublicSentimentDetailVo.setUrl(riskPublicSentimentDetail.getUrl());
        riskPublicSentimentDetailVo.setContent(riskPublicSentimentDetail.getContent());
        riskPublicSentimentDetailVo.setSpecialForwarding(riskPublicSentimentDetail.getSpecialForwarding());
        riskPublicSentimentDetailVo.setVideoUrl(riskPublicSentimentDetail.getVideoUrl());
        riskPublicSentimentDetailVo.setImgUrl(riskPublicSentimentDetail.getImgUrl());
        Gson gson = new Gson();
        riskPublicSentimentDetailVo.setSupplementInfo(gson.fromJson(riskPublicSentimentDetail.getSupplementInfo(), Map.class));
        riskPublicSentimentDetailVo.setFermentationCondition(gson.fromJson(riskPublicSentimentDetail.getFermentationCondition(), Map.class));
        return riskPublicSentimentDetailVo;
    }


}
