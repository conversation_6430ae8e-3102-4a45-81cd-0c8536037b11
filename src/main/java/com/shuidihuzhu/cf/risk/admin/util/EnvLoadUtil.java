package com.shuidihuzhu.cf.risk.admin.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2019-08-02
 **/
@Slf4j
@Component("envLoadUtilRisk")
public class EnvLoadUtil {

    @Autowired
    private Environment environment;

    public String getEnv() {
        return Arrays.toString(environment.getActiveProfiles());
    }

    public boolean isOnlineAndJob() {
        try {
            Set<String> activeProfiles = Arrays.stream(environment.getActiveProfiles()).collect(Collectors.toSet());
            log.info("activeProfiles:{}", activeProfiles);
            return activeProfiles.contains("production") && activeProfiles.contains("job");
        } catch (Exception e) {
            log.error("", e);
            return false;
        }
    }

    public boolean isOnline() {
        try {
            Set<String> activeProfiles = Arrays.stream(environment.getActiveProfiles()).collect(Collectors.toSet());
            log.info("activeProfiles:{}", activeProfiles);
            return activeProfiles.contains("production") && !activeProfiles.contains("canary");
        } catch (Exception e) {
            log.error("", e);
            return false;
        }
    }

    public boolean isOnlineOrCanary() {
        try {
            Set<String> activeProfiles = Arrays.stream(environment.getActiveProfiles()).collect(Collectors.toSet());
            log.info("activeProfiles:{}", activeProfiles);
            return activeProfiles.contains("production");
        } catch (Exception e) {
            log.error("", e);
            return false;
        }
    }
}

