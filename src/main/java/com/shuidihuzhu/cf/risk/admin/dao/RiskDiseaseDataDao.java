package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseDataPortion;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskDiseaseDataDao {

    int delete(@Param("diseaseId") long diseaseId);


    RiskDiseaseData getByClassName(@Param("diseaseClassName") String diseaseClassName);

    int save(RiskDiseaseData riskDiseaseData);

    int update(RiskDiseaseData riskDiseaseData);


    RiskDiseaseData getById(@Param("diseaseId") long diseaseId);


    List<RiskDiseaseData> findList(@Param("diseaseClassName") String diseaseClassName,
                                   @Param("medicalName") String medicalName,
                                   @Param("normalName") String normalName,
                                   @Param("raiseType") int raiseType,
                                   PageRequest pageRequest);

    List<RiskDiseaseData> findListV2(@Param("diseaseClassName") String diseaseClassName,
                                   @Param("isDelete") int isDelete,
                                   @Param("startCreateTime") String startCreateTime,
                                   @Param("endCreateTime") String endCreateTime,
                                   @Param("raiseType") int raiseType);

    List<RiskDiseaseData> findListByDiseaseIds(@Param("diseaseClassName") String diseaseClassName,
                                               @Param("isDelete") int isDelete,
                                               @Param("startCreateTime") String startCreateTime,
                                               @Param("endCreateTime") String endCreateTime,
                                               @Param("raiseType") int raiseType,
                                               @Param("diseaseIds") List<Long> diseaseIds);

//    int deleteByName(@Param("diseaseClassName") String diseaseClassName);

    List<RiskDiseaseDataPortion> getAllDiseaseRule();


    int clearAll();

    int sync(@Param("list") List<RiskDiseaseData> list);

    List<RiskDiseaseData> getAll();

    List<RiskDiseaseData> findDiseaseNormList(@Param("diseaseClassName") String diseaseClassName);
}
