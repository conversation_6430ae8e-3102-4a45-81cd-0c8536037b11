package com.shuidihuzhu.cf.risk.admin.dao.blacklist;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskBlacklistDataLogDao {
    int insertSelective(RiskBlacklistDataLog record);

    int insertBatch(List<RiskBlacklistDataLog> records);

    RiskBlacklistDataLog selectByPrimaryKey(Long id);

    List<RiskBlacklistDataLog> listByDataId(Long dataId);

}