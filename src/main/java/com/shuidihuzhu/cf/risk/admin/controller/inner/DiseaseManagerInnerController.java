package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.service.DiseaseManagerService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2020/5/18
 */
@RestController
@Slf4j
@RequestMapping(value = "/innerapi/cf-risk-admin/disease/manager")
@Api("疾病上传excel接口")
public class DiseaseManagerInnerController {

    @Autowired
    private DiseaseManagerService diseaseManagerBiz;

    @ApiOperation("上传疾病库信息")
    @PostMapping(path = "/excel/save")
    public Response excelSave(@ApiParam(value = "包含UserId的xlsx文件") MultipartFile file, String sheetName) {
        log.info(this.getClass().getSimpleName() + "file:{}", file.getOriginalFilename());
        if (file.isEmpty()) {
            return NewResponseUtil.makeFail("上传的文件内容为空");
        }
        log.info("sendNoJoinMsg start");
        String originFileName = file.getOriginalFilename();
        try (InputStream ins = file.getInputStream()) {
            diseaseManagerBiz.saveExcel(ins, originFileName, sheetName);
            return NewResponseUtil.makeSuccess(null);
        } catch (Exception e) {
            log.error("uploadCooperateDrawCashDetails exception", e);
        }
        return NewResponseUtil.makeSuccess(null, "执行成功");
    }

}
