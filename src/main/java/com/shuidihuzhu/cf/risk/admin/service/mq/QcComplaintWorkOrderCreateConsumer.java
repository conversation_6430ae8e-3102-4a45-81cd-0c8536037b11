package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcResultBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcDetailService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QcAppealInnerService;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.AssignTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestNoticeModel;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: lixiaoshuang
 * @create: 2020-11-16 14:40
 **/
@Service
@RocketMQListener(id = RiskMQTagCons.QC_COMPLAINT_WORK_ORDER_CREATE,
        tags = RiskMQTagCons.QC_COMPLAINT_WORK_ORDER_CREATE,
        topic = MQTopicCons.CF)
@Slf4j
public class QcComplaintWorkOrderCreateConsumer implements MessageListener<CfGwReplaceInputQualityTestFeedbackModel> {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcResultBiz riskQcResultBiz;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Autowired
    private QcAppealInnerService qcAppealInnerService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfGwReplaceInputQualityTestFeedbackModel> mqMessage) {
        CfGwReplaceInputQualityTestFeedbackModel payload = mqMessage.getPayload();
        log.info("QcComplaintWorkOrderCreateConsumer payload:{}", JSON.toJSONString(payload));

        var workOrderId = payload.getWorkOrderId();
        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        log.info("QcComplaintWorkOrderCreateConsumer workOrderVo:{}", JSON.toJSONString(response));
        if (response.notOk()) {
            return ConsumeStatus.RECONSUME_LATER;
        }
        if (Objects.isNull(response.getData())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        WorkOrderVO workOrderVO = response.getData();
        CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum issueType = payload.getIssueType();

        //判断生成工单类型
        ConsumeStatus consumeStatus = ConsumeStatus.CONSUME_SUCCESS;
        if (workOrderVO.getOrderType() == WorkOrderType.qc_common.getType()
                && issueType.getType() == CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE.getType()) {
            consumeStatus = this.createWorkOrder(WorkOrderType.qc_complaint, payload, workOrderVO);
        } else if (workOrderVO.getOrderType() == WorkOrderType.qc_common.getType()
                && issueType.getType() == CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.SERIOUS_ISSUE.getType()) {
            consumeStatus = this.createWorkOrder(WorkOrderType.qc_serious_complaint, payload, workOrderVO);
        } else if (workOrderVO.getOrderType() == WorkOrderType.qc_complaint.getType()
                && issueType.getType() == CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE.getType()) {
            consumeStatus = this.createWorkOrder(WorkOrderType.qc_second_complaint, payload, workOrderVO);
        } else {
            log.error("the type of complaint ticket is incorrect");
        }
        return consumeStatus;
    }

    public ConsumeStatus createWorkOrder(WorkOrderType workOrderType, CfGwReplaceInputQualityTestFeedbackModel cel,
                                         WorkOrderVO workOrderVO) {

        String lockName = "qcLock_" + cel.getWorkOrderId() + "_" + workOrderType.getType() + "_"
                + cel.getMis();
        String identifier = "";
        try {
            identifier = cfRiskRedissonHandler.tryLock(lockName, 3 * 1000L, 30 * 1000L);
            log.info("QcComplaintWorkOrderCreateConsumer identifier:{}", identifier);
            if (StringUtils.isBlank(identifier)) {
                return ConsumeStatus.RECONSUME_LATER;
            }
            var caseId = workOrderVO.getCaseId();
            var beforeWorkOrderId = workOrderVO.getWorkOrderId();
            var orderType = workOrderType.getType();
            //检查是否已生成工单
            Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(caseId, orderType);
            if (lastWorkOrder.ok() && Objects.nonNull(lastWorkOrder.getData())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //创建质检基本信息
            RiskQcBaseInfo riskQcBaseInfo = this.getRiskQcBaseInfo(beforeWorkOrderId, orderType);
            riskQcBaseInfoBiz.addQc(riskQcBaseInfo);

            //记录组织结构id、材审工单id
            List<RiskQcMaterialsInfo> riskQcMaterialsInfos = List.of(
                    new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.COMPLAINT_MATERIAL.getKey(),
                            JSON.toJSONString(cel)),
                    new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.ORIGINAL_WORK_ORDER.getKey(),
                            Long.toString(beforeWorkOrderId)));
            riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfos);

            //执行工单创建逻辑
            QcWorkOrder qcWorkOrder = new QcWorkOrder();
            qcWorkOrder.setCaseId(caseId);
            qcWorkOrder.setQcId(riskQcBaseInfo.getId());
            qcWorkOrder.setOrderType(orderType);
            qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
            qcWorkOrder.setComment("生成" + workOrderType.getMsg());
            qcWorkOrder.setAssignStatus(AssignTypeEnum.MUST_ASSIGN.getCode());
            Response<Long> clientQcWorkOrder = cfQcWorkOrderClient.createQcWorkOrder(qcWorkOrder);

            if (clientQcWorkOrder.ok() && Objects.nonNull(clientQcWorkOrder.getData())) {
                log.info("createQcWorkOrder id:{}", clientQcWorkOrder.getData());
                //调用质检操作记录接口，记录操作记录
                String content = "生成" + workOrderType.getMsg() + ",工单ID【" + clientQcWorkOrder.getData() + "】";
                riskQcLogService.addAppealLog(RiskQcOperationTypeEnum.CREATE_COMMON_WORK_ORDER, clientQcWorkOrder.getData(), cel.getName(), content);

                RiskQcSearchIndex beforeRiskQcSearchIndex = riskQcSearchIndexBiz.getByWorkOrderId(beforeWorkOrderId);

                // 添加搜索索引字段聚合表
                RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
                riskQcSearchIndex.setCaseId(caseId);
                riskQcSearchIndex.setQcId(riskQcBaseInfo.getId());
                riskQcSearchIndex.setWorkOrderId(clientQcWorkOrder.getData());
                riskQcSearchIndex.setQcType(QcTypeEnum.BD.getCode());
                if (Objects.nonNull(beforeRiskQcSearchIndex)) {
                    riskQcSearchIndex.setOrganization(beforeRiskQcSearchIndex.getOrganization());
                    riskQcSearchIndex.setQcUniqueCode(beforeRiskQcSearchIndex.getQcUniqueCode());
                    riskQcSearchIndex.setQcByName(beforeRiskQcSearchIndex.getQcByName());
                    riskQcSearchIndex.setRegisterMobileEncrypt(beforeRiskQcSearchIndex.getRegisterMobileEncrypt());
                    riskQcSearchIndex.setUserId(beforeRiskQcSearchIndex.getUserId());
                    riskQcSearchIndex.setMaterialId(beforeRiskQcSearchIndex.getMaterialId());
                }
                riskQcSearchIndexBiz.addSearchIndex(riskQcSearchIndex, qcWorkOrder.getOrderType());
                //生成日志记录
                qcAppealInnerService.saveBuildLog(clientQcWorkOrder.getData(), cel.getMis(), cel.getIssueType(),
                        cel.getWorkOrderId(), workOrderType.getMsg());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

        } catch (Exception e) {
            log.error("QcComplaintWorkOrderCreateConsumer.consumeMessage error", e);
        } finally {
            if (StringUtils.isNotEmpty(identifier)) {
                cfRiskRedissonHandler.unLock(lockName, identifier);
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    public RiskQcBaseInfo getRiskQcBaseInfo(long beforeOrderId, int orderType) {
        RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
        var qcId = riskQcDetailService.getQcId(beforeOrderId);
        RiskQcBaseInfo beforeRiskQcBaseInfo = riskQcBaseInfoBiz.getById(qcId);
        if (Objects.nonNull(beforeRiskQcBaseInfo)) {
            riskQcBaseInfo.setCaseId(beforeRiskQcBaseInfo.getCaseId());
            riskQcBaseInfo.setQcType(beforeRiskQcBaseInfo.getQcType());
            riskQcBaseInfo.setOrderType(orderType);
            riskQcBaseInfo.setQcUniqueCode(beforeRiskQcBaseInfo.getQcUniqueCode());
            riskQcBaseInfo.setQcByName(beforeRiskQcBaseInfo.getQcByName());
        }
        return riskQcBaseInfo;
    }
}
