package com.shuidihuzhu.cf.risk.admin.biz.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/12
 */
public interface RiskQualitySpotStrategyTypeRelBiz {

    RiskQualitySpotTypeRel save(long strategyId, long typeId, String sceneInfo);

    List<RiskQualitySpotTypeRel> findByStrategyId(long strategyId);

    List<RiskQualitySpotTypeRel> findByStrategyIdList(List<Long> strategyIdList);

    List<RiskQualitySpotTypeRel> findByTypeId(long typeId);

    int deleteByStrategyId(long strategyId);

    List<RiskQualitySpotTypeRel>  findByTypeIdList(List<Long> typeIdList);

}
