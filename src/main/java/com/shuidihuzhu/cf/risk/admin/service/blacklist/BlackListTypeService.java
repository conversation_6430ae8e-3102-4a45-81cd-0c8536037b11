package com.shuidihuzhu.cf.risk.admin.service.blacklist;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.biz.blacklist.BlacklistTypeBiz;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistTypeActionRefDao;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistTypeDao;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistTypeLogDao;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionDto;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.RiskBlacklistClassifyEnhance;
import com.shuidihuzhu.cf.risk.admin.model.enums.CommonStatusEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistClassify;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistType;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeActionRef;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeLog;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.BlacklistTypeQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.*;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:54
 */
@Service
@Slf4j
public class BlackListTypeService {

    private static final String UPDATE_BLACKLIST_TYPE_KEY = "cf_risk_admin_blacklist_type_";
    private static final String UPDATE_BLACKLIST_TYPE_ADD_KEY = "cf_risk_admin_blacklist_type_add_lock";
    private static final long UPDATE_BLACKLIST_TYPE_KEY_LEAVE_TIME = 10 * 1000;

    @Resource
    private RiskBlacklistTypeDao riskBlacklistTypeDao;
    @Resource
    private RiskBlacklistTypeActionRefDao riskBlacklistTypeActionRefDao;
    @Resource
    private RiskBlacklistTypeLogDao riskBlacklistTypeLogDao;
    @Resource
    private BlackListClassifyService classifyService;
    @Resource
    private SeaAccountService seaAccountService;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private BlacklistTypeBiz blacklistTypeBiz;
    @Resource
    private BlackListDataService dataService;

    public PageResult<BlacklistTypeVo> queryPage(BlacklistTypeQuery typeQuery){
        Page<RiskBlacklistType> blacklistTypePage = PageHelper.startPage(typeQuery.getPageNo(), typeQuery.getPageSize())
                .doSelectPage(() -> riskBlacklistTypeDao.listByOptions(typeQuery));
        List<RiskBlacklistType> result = blacklistTypePage.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return new PageResult<>(blacklistTypePage, Collections.emptyList());
        }
        List<Long> typeIds = result.stream().map(RiskBlacklistType::getId).collect(Collectors.toList());
        List<RiskBlacklistTypeActionRef> actionRefs = blacklistTypeBiz.listTypeActionRefsByTypeIds(typeIds);
        Map<Long, List<RiskBlacklistTypeActionRef>> typeIdMap = actionRefs.stream()
                .collect(Collectors.groupingBy(RiskBlacklistTypeActionRef::getTypeId));
        List<BlacklistTypeVo> blacklistTypeVos = Lists.newArrayListWithCapacity(result.size());
        for (RiskBlacklistType riskBlacklistType : result) {
            blacklistTypeVos.add(new BlacklistTypeVo(riskBlacklistType,
                    typeIdMap.get(riskBlacklistType.getId()).stream()
                            .map(RiskBlacklistTypeActionRef::getActionId).collect(Collectors.toList()))
            );
        }
        return new PageResult<>(blacklistTypePage, blacklistTypeVos);
    }

    public List<BlacklistTypeLogVo> queryTypeLog(Long typeId){
        return riskBlacklistTypeLogDao.listByTypeId(typeId).stream().map(BlacklistTypeLogVo::new).collect(Collectors.toList());
    }

    public BlacklistTypeDetailVo getDetail(Long typeId){
        //1. 查询类型
        RiskBlacklistType riskBlacklistType = riskBlacklistTypeDao.selectByPrimaryKey(typeId);
        if (riskBlacklistType == null) {
            throw new IllegalArgumentException("无效的类型id");
        }
        //2. 查询关联的动作
        List<RiskBlacklistTypeActionRef> typeActionRefs = blacklistTypeBiz.listTypeActionRefsByTypeIds(List.of(typeId));
        //3. 查询三级分类名称
        RiskBlacklistClassifyEnhance classify = classifyService.getClassifyObtain(riskBlacklistType.getClassifyId());

        BlacklistTypeDetailVo blacklistTypeDetailVo = new BlacklistTypeDetailVo();
        blacklistTypeDetailVo.setTypeActions(typeActionRefs.stream().map(RiskBlacklistTypeActionRef::getActionId).collect(Collectors.toList()));
        List<BlacklistTypeActionRefDto> actionRefDtoList = typeActionRefs.stream()
                .map(m -> {
                    BlacklistTypeActionRefDto dto = new BlacklistTypeActionRefDto();
                    dto.setActionId(m.getActionId());
                    dto.setName(LimitActionEnum.fromCode(m.getActionId()).getName());
                    dto.setLimitTimeType(m.getLimitTimeType());
                    return dto;
                })
                .collect(Collectors.toList());
        blacklistTypeDetailVo.setActionList(actionRefDtoList);
        blacklistTypeDetailVo.setLevelName(classify.getNamePath());
        return blacklistTypeDetailVo;
    }

    public Map<Long, BlacklistTypeActionDto> getActionNameByClassifyIds(List<Long> classifyIds){
        //1. 查询类型
        List<RiskBlacklistType> riskBlacklistTypes = riskBlacklistTypeDao.listByClassifyIds(classifyIds);
        if (CollectionUtils.isEmpty(riskBlacklistTypes)) {
            return Collections.emptyMap();
        }
        Map<Long, Long> classifyIdMap = riskBlacklistTypes.stream()
                .collect(Collectors.toMap(RiskBlacklistType::getClassifyId, RiskBlacklistType::getId));
        //2. 查询关联的动作
        List<RiskBlacklistTypeActionRef> typeActionRefs = blacklistTypeBiz.listTypeActionRefsByTypeIds(classifyIdMap.values());
        Map<Long, List<RiskBlacklistTypeActionRef>> typeMap = typeActionRefs.stream()
                .collect(Collectors.groupingBy(RiskBlacklistTypeActionRef::getTypeId));
        Map<Long, BlacklistTypeActionDto> classifyIdActionMap = Maps.newHashMap();
        for (Long classifyId : classifyIds) {
            Long typeId = classifyIdMap.get(classifyId);
            if (typeId != null) {
                List<RiskBlacklistTypeActionRef> typeActionRefsGroup = typeMap.get(typeId);
                List<BlacklistTypeActionRefDto> typeActionRefDtoList = typeActionRefsGroup.stream()
                        .map(m -> {
                            BlacklistTypeActionRefDto blacklistTypeActionRefDto = new BlacklistTypeActionRefDto();
                            blacklistTypeActionRefDto.setName(LimitActionEnum.fromCode(m.getActionId()).getName());
                            blacklistTypeActionRefDto.setLimitTimeType(m.getLimitTimeType());
                            blacklistTypeActionRefDto.setActionId(m.getActionId());
                            return blacklistTypeActionRefDto;
                        })
                        .collect(Collectors.toList());
                classifyIdActionMap.put(classifyId, new BlacklistTypeActionDto(typeId, typeActionRefDtoList));
                continue;
            }
            classifyIdActionMap.put(classifyId, null);
        }
        return classifyIdActionMap;
    }

    public void saveType(BlacklistTypeAddVo typeAddVo, long adminUserId){
        String key = UPDATE_BLACKLIST_TYPE_ADD_KEY;
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_BLACKLIST_TYPE_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请刷新后重试");
            }
            //检查0~2级的类型是否重复
            checkLevelNameRepeat(typeAddVo);
            StringBuilder levelPath = new StringBuilder();
            Long parentId = 0L;
            boolean haveAdd = false;
            for (int i = 0; i < typeAddVo.getLevelType().size(); i++) {
                if (typeAddVo.getLevelType().get(i) == BlacklistTypeAddVo.LevelOperateType.ADD.getCode()) {
                    haveAdd = true;
                    if (parentId > 0) {
                        RiskBlacklistClassify classify = new RiskBlacklistClassify();
                        classify.setLevel(i);
                        classify.setLevelPath(levelPath.substring(0, levelPath.length()-1));
                        classify.setParentId(parentId);
                        classify.setName(typeAddVo.getLevelNames().get(i));
                        long leafClassifyId = classifyService.saveBatchClassify(classify,
                                typeAddVo.getLevelNames().subList(i+1, typeAddVo.getLevelType().size()));
                        doSaveType(typeAddVo, leafClassifyId, adminUserId);
                    } else {
                        //新增一级分类
                        long leafClassifyId = classifyService.saveBatchClassify(typeAddVo.getLevelNames());
                        doSaveType(typeAddVo, leafClassifyId, adminUserId);
                    }
                    break;
                }

                parentId = typeAddVo.getLevelIds().get(i);
                levelPath.append(parentId).append(",");
            }
            //目前最后一级一定是新增的，所以这个逻辑目前不会走
            if (!haveAdd) {
                doSaveType(typeAddVo, typeAddVo.getLevelIds().get(typeAddVo.getLevelIds().size()-1), adminUserId);
            }
        } catch (InterruptedException e) {
            log.error("", e);
            throw new RuntimeException("服务开小差，请稍后重试");
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
    }

    private void checkLevelNameRepeat(BlacklistTypeAddVo typeAddVo){
        List<Integer> levelType = typeAddVo.getLevelType();
        for (int i = 0; i < levelType.size(); i++) {
            if (BlacklistTypeAddVo.LevelOperateType.SELECT.getCode() == levelType.get(i)) {
                continue;
            }
            //如果上一层也是ADD(第一层需要验证)，那么不做重复验证
            if (i > 0 && levelType.get(i-1) == BlacklistTypeAddVo.LevelOperateType.ADD.getCode()) {
                return;
            }
            if (classifyService.exists(i==0?0L:typeAddVo.getLevelIds().get(i-1), typeAddVo.getLevelNames().get(i))) {
                throw new IllegalArgumentException("与已有的启用态的"+(i+1)+"级黑名单类型重名");
            }
        }
    }

    private void doSaveType(BlacklistTypeAddVo typeAddVo, Long classifyId, long adminUserId){
        RiskBlacklistType riskBlacklistType = new RiskBlacklistType();
        riskBlacklistType.setClassifyId(classifyId);
        riskBlacklistType.setTypeName(Joiner.on("-").join(typeAddVo.getLevelNames()));
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        long operateId = userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId();
        String operateName = userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg();
        riskBlacklistType.setOperateId(operateId);
        riskBlacklistType.setOperateName(operateName);
        riskBlacklistTypeDao.insertSelective(riskBlacklistType);
        //1.1 保存动作
        List<RiskBlacklistTypeActionRef> typeActionRefs = Lists.newArrayListWithCapacity(typeAddVo.getTypeActions().size());
        for (RiskBlacklistTypeActionRef typeActionRef : typeAddVo.getTypeActions()) {
            typeActionRef.setTypeId(riskBlacklistType.getId());
            typeActionRefs.add(typeActionRef);
        }
        riskBlacklistTypeActionRefDao.saveBatch(typeActionRefs);
        //1.2 保存日志
        saveTypeLog(riskBlacklistType.getId(), "新增黑名单", operateId, operateName);
    }

    public void updateTypeAction(BlacklistTypeModifyVo blacklistTypeModifyVo, long adminUserId){
        String key = UPDATE_BLACKLIST_TYPE_KEY + blacklistTypeModifyVo.getTypeId();
        String identify = null;
        List<Long> actionIdsFromDb;
        List<Long> actionIds;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_BLACKLIST_TYPE_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请刷新后重试");
            }
            List<RiskBlacklistTypeActionRef> refsFromDb = blacklistTypeBiz.listTypeActionRefsByTypeIds(List.of(blacklistTypeModifyVo.getTypeId()));
            int updateData = 0;
            Map<Long, BlacklistTypeActionRefDto> blacklistTypeActionRefDtoMap = Optional.ofNullable(blacklistTypeModifyVo.getActionList())
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toMap(BlacklistTypeActionRefDto::getActionId, Function.identity(), (x, y) -> x));
            for (RiskBlacklistTypeActionRef actionRef : refsFromDb) {
                BlacklistTypeActionRefDto blacklistTypeActionRefDto = blacklistTypeActionRefDtoMap.get(actionRef.getActionId());
                if (Objects.isNull(blacklistTypeActionRefDto) || actionRef.getLimitTimeType() == blacklistTypeActionRefDto.getLimitTimeType()) {
                    continue;
                }
                actionRef.setLimitTimeType(blacklistTypeActionRefDto.getLimitTimeType());
                riskBlacklistTypeActionRefDao.updateTypeActionRefById(actionRef);
                updateData++;
            }
            if (updateData > 0) {
                log.info("BlackListTypeService updateTypeAction update limitTime success {}", blacklistTypeModifyVo);
                return;
            }
            Map<Long, Long> actionIdMapFromDb = refsFromDb.stream()
                    .collect(Collectors.toMap(RiskBlacklistTypeActionRef::getActionId, RiskBlacklistTypeActionRef::getId));
            actionIdsFromDb = new ArrayList<>(actionIdMapFromDb.keySet());
            actionIds = blacklistTypeModifyVo.getTypeActions();
            Collection<Long> needDelete = CollectionUtils.subtract(actionIdsFromDb, actionIds);
            Collection<Long> needAdd = CollectionUtils.subtract(actionIds, actionIdsFromDb);
            if (CollectionUtils.isEmpty(needDelete) && CollectionUtils.isEmpty(needAdd)) {
                throw new RuntimeException("未做任何修改");
            }
            List<RiskBlacklistTypeActionRef> typeActionRefs = Lists.newArrayListWithCapacity(needAdd.size());
            Map<Long, BlacklistTypeActionRefDto> typeActionRefDtoMap = blacklistTypeModifyVo.getActionList()
                    .stream()
                    .collect(Collectors.toMap(BlacklistTypeActionRefDto::getActionId, Function.identity(), (x, y) -> x));
            for (Long actionId : needAdd) {
                BlacklistTypeActionRefDto blacklistTypeActionRefDto = typeActionRefDtoMap.get(actionId);
                RiskBlacklistTypeActionRef typeActionRef = new RiskBlacklistTypeActionRef();
                typeActionRef.setTypeId(blacklistTypeModifyVo.getTypeId());
                typeActionRef.setActionId(actionId);
                if (Objects.nonNull(blacklistTypeActionRefDto)) {
                    typeActionRef.setLimitTimeType(blacklistTypeActionRefDto.getLimitTimeType());
                }
                typeActionRefs.add(typeActionRef);
            }
            if (CollectionUtils.isNotEmpty(typeActionRefs)) {
                riskBlacklistTypeActionRefDao.saveBatch(typeActionRefs);
            }
            if (CollectionUtils.isNotEmpty(needDelete)) {
                riskBlacklistTypeActionRefDao.deleteByIds(
                        needDelete.stream().map(actionIdMapFromDb::get).collect(Collectors.toList()));
            }
            //处理冗余数据
//            dataService.asyncDataActions(blacklistTypeModifyVo.getTypeId(), needAdd, needDelete);
        } catch (InterruptedException e) {
            log.error("", e);
            throw new RuntimeException("服务开小差，请稍后重试");
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        //记录日志
        saveTypeLog(blacklistTypeModifyVo.getTypeId(), "编辑限制动作类型：\"" +
                Joiner.on(",").join(LimitActionEnum.ids2Names(actionIdsFromDb)) + "\"修改为\"" +
                Joiner.on(",").join(LimitActionEnum.ids2Names(actionIds)) + "\"", adminUserId
        );
    }

    public void enable(Long typeId, long adminUserId){
        RiskBlacklistType riskBlacklistType = riskBlacklistTypeDao.selectByPrimaryKey(typeId);
        List<RiskBlacklistType> types = riskBlacklistTypeDao.listByEnabledTypeNames(List.of(riskBlacklistType.getTypeName()));
        if (CollectionUtils.isNotEmpty(types)) {
            throw new IllegalArgumentException("已存在相同的黑名单类型，不允许再启用");
        }
        riskBlacklistTypeDao.updateStatusById(typeId, CommonStatusEnum.ENABLE.getCode());
        classifyService.enableClassify(riskBlacklistType.getClassifyId());
        //记录日志
        saveTypeLog(typeId, CommonStatusEnum.ENABLE.getDesc(), adminUserId);
    }

    public void disable(Long typeId, long adminUserId){
        RiskBlacklistType blacklistType = riskBlacklistTypeDao.selectByPrimaryKey(typeId);
        riskBlacklistTypeDao.updateStatusById(typeId, CommonStatusEnum.DISABLE.getCode());
        classifyService.disableClassify(blacklistType.getClassifyId());
        //记录日志
        saveTypeLog(typeId, CommonStatusEnum.DISABLE.getDesc(), adminUserId);
    }

    private void saveTypeLog(Long typeId, String modifyContent, long adminUserId) {
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        saveTypeLog(typeId, modifyContent, userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId(),
                userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg());
    }

    private void saveTypeLog(Long typeId, String modifyContent, Long operateId, String operateName) {
        RiskBlacklistTypeLog typeLog = new RiskBlacklistTypeLog();
        typeLog.setOperateId(operateId);
        typeLog.setOperateName(operateName);
        typeLog.setTypeId(typeId);
        typeLog.setModifyContent(modifyContent);
        riskBlacklistTypeLogDao.insertSelective(typeLog);
    }

}
