package com.shuidihuzhu.cf.risk.admin.controller.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.BlacklistTypeQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistClassifyVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistTypeAddVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistTypeDetailVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistTypeLogVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistTypeModifyVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.BlacklistTypeVo;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListClassifyService;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListTypeService;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/15 20:03
 */
@Validated
@Slf4j
@RequiresPermission("black-list:type-manage")
@RestController
@RequestMapping(path = "/api/cf-risk-admin/blacklist/type")
public class RiskBlacklistTypeController {

    @Resource
    private BlackListTypeService blackListTypeService;
    @Resource
    private BlackListClassifyService blackListClassifyService;

    @ApiOperation(value = "黑名单类型-限制动作类型列表", notes = "key为id，value为name")
    @GetMapping(path = "/action/list-limit")
    public Response<Map<Long, String>> actionListLimit() {
        return NewResponseUtil.makeSuccess(Arrays.stream(LimitActionEnum.values())
                .collect(Collectors.toMap(LimitActionEnum::getId, LimitActionEnum::getName)));
    }

    @ApiOperation(value = "黑名单类型-列表")
    @PostMapping(path = "/list")
    public Response<PageResult<BlacklistTypeVo>> actionList(BlacklistTypeQuery typeQuery) {
        log.info("黑名单类型-列表，请求入参：{}", typeQuery);
        return NewResponseUtil.makeSuccess(blackListTypeService.queryPage(typeQuery));
    }

    @ApiOperation(value = "黑名单类型-日志列表")
    @PostMapping(path = "/log/list")
    public Response<List<BlacklistTypeLogVo>> logList(@NotNull(message = "类型id不能为空")
        @Min(value = 1, message = "类型id不能小于1") @ApiParam("类型id") Long typeId) {
        log.info("黑名单类型-日志列表，请求入参：{}", typeId);
        return NewResponseUtil.makeSuccess(blackListTypeService.queryTypeLog(typeId));
    }

    @ApiOperation(value = "黑名单类型-详情")
    @PostMapping(path = "/get")
    public Response<BlacklistTypeDetailVo> getType(@NotNull(message = "类型id不能为空")
        @Min(value = 1, message = "类型id不能小于1") @ApiParam("类型id") Long typeId) {
        log.info("黑名单类型-详情，请求入参：{}", typeId);
        return NewResponseUtil.makeSuccess(blackListTypeService.getDetail(typeId));
    }

    @ApiOperation(value = "黑名单类型-新增")
    @PostMapping(path = "/add")
    public Response add(@Valid @RequestBody() BlacklistTypeAddVo blacklistTypeAddVo) {
        log.info("黑名单类型-新增，请求入参：{}", blacklistTypeAddVo);
        if (!BlacklistTypeAddVo.LevelOperateType.legalCodes(blacklistTypeAddVo.getLevelType())) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "层级值类型非法", null);
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        blackListTypeService.saveType(blacklistTypeAddVo, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "黑名单类型-编辑")
    @PostMapping(path = "/modify")
    public Response modify(@Valid @RequestBody() BlacklistTypeModifyVo blacklistTypeModifyVo) {
        log.info("黑名单类型-编辑，请求入参：{}", blacklistTypeModifyVo);
        long adminUserId = ContextUtil.getAdminLongUserId();
        blackListTypeService.updateTypeAction(blacklistTypeModifyVo, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "黑名单类型-启用")
    @PostMapping(path = "/enable")
    public Response enable(
            @NotNull(message = "黑名单类型id不能为空") @Min(value = 1, message = "黑名单类型id不能小于1")
            @ApiParam("黑名单类型id") Long typeId) {
        log.info("黑名单类型-启用，请求入参：{}", typeId);
        long adminUserId = ContextUtil.getAdminLongUserId();
        blackListTypeService.enable(typeId, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "黑名单类型-弃用")
    @PostMapping(path = "/disable")
    public Response disable(
            @NotNull(message = "黑名单类型id不能为空") @Min(value = 1, message = "黑名单类型id不能小于1")
            @ApiParam("黑名单类型id") Long typeId) {
        log.info("黑名单类型-弃用，请求入参：{}", typeId);
        long adminUserId = ContextUtil.getAdminLongUserId();
        blackListTypeService.disable(typeId, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "黑名单类型-分类列表查询")
    @PostMapping(path = "/classify/list")
    public Response<List<BlacklistClassifyVo>> actionList(
            @NotNull(message = "分类父级id不能为空") @Min(value = 0, message = "分类父级id不能小于0")
            @ApiParam("分类父级id，取一级时传0") @RequestParam(name = "parentId", defaultValue = "0") Long parentId) {
        log.info("黑名单类型-分类列表查询，请求入参：{}", parentId);
        return NewResponseUtil.makeSuccess(blackListClassifyService.queryChildren(parentId));
    }

    @ApiOperation(value = "黑名单类型-新增分类检测(Bool类型，存在就是true)")
    @PostMapping(path = "/classify/add/check")
    public Response<Boolean> classifyAddCheck(
            @NotNull(message = "父级不能为空") @Min(value = 0, message = "父级不能小于0")
            @ApiParam("父级id") Long parentId,
            @NotBlank(message = "分类名称不能为空") @ApiParam("分类名称") String classifyName) {
        log.info("黑名单类型-新增分类检测，请求入参parentId:{}, classifyName:{}", parentId, classifyName);
        return NewResponseUtil.makeSuccess(blackListClassifyService.exists(parentId, classifyName));
    }

}
