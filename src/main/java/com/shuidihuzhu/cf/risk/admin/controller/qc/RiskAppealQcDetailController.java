package com.shuidihuzhu.cf.risk.admin.controller.qc;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQcAppealWorkOrderRelBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcAppealResultEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcDisponseActionEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcAppealWorkOrderRel;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcAppealInfoVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcAppealResultVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcCommonResultVo;
import com.shuidihuzhu.cf.risk.admin.service.RiskAppealQcDetailService;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @Auther: subing
 * @Date: 2020/11/13
 */
@RestController
@RequestMapping("/api/cf-risk-admin/qc-appeal/detail")
@Slf4j
public class RiskAppealQcDetailController {

    @Autowired
    private RiskAppealQcDetailService riskAppealQcDetailService;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private RiskQcAppealWorkOrderRelBiz riskQcAppealWorkOrderRelBiz;

    @PostMapping(path = "get-result")
    public Response<RiskQcAppealResultVo> getResult(@RequestParam long appealWorkOrderId,
                                                    @RequestParam(defaultValue = "false") boolean showFirstRecord){
        RiskQcAppealResultVo riskQcAppealResultVo = riskAppealQcDetailService.getResult(appealWorkOrderId, showFirstRecord);
        return NewResponseUtil.makeSuccess(riskQcAppealResultVo);
    }

    @PostMapping(path = "add-info")
    public Response<Integer> addInfo(@RequestParam long appealWorkOrderId,
                                     @RequestParam int appealResult,
                                     @RequestParam String appealInfo,
                                     @RequestParam int disposeAction,
                                     @RequestParam long caseId,
                                     @RequestParam int orderType){
        RiskQcAppealInfoModel qcAppealInfoModel = null;
        try {
            qcAppealInfoModel = JSONObject.parseObject(appealInfo, new TypeReference<RiskQcAppealInfoModel>() {});
        } catch (Exception e) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isBlank(QcAppealResultEnum.findOfCode(appealResult)) && disposeAction != RiskQcDisponseActionEnum.LATER_DISPOSE.getCode()){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<WorkOrderVO> workOrderVoResponse =  cfWorkOrderClient.getWorkOrderById(appealWorkOrderId);
        log.info("workOrderVoResponse:{}", workOrderVoResponse);
        if (workOrderVoResponse != null && workOrderVoResponse.ok() && Objects.nonNull(workOrderVoResponse.getData())) {
            WorkOrderVO workOrderVO = workOrderVoResponse.getData();
            if (workOrderVO.getHandleResult() == HandleResultEnum.undoing.getType()) {
                return NewResponseUtil.makeError(RiskAdminErrorCode.QC_ALLOCATION_WORK_ORDER_ERROR);
            }
            long userId = ContextUtil.getAdminLongUserId();
            if (workOrderVO.getOperatorId() != userId){
                return NewResponseUtil.makeError(RiskAdminErrorCode.QC_ALLOCATION_WORK_ORDER_ERROR);
            }
        }else {
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_HANDEL_WORK_ORDER_ERROR);
        }
        int addInfo =
                riskAppealQcDetailService.addInfo(appealWorkOrderId, appealResult, appealInfo,
                        disposeAction, caseId, orderType, qcAppealInfoModel);
        if (addInfo > 0) {
            return NewResponseUtil.makeSuccess(addInfo);
        }
        return NewResponseUtil.makeError(RiskAdminErrorCode.QC_HANDEL_WORK_ORDER_ERROR);
    }

    @PostMapping(path = "get-appeal-info")
    public Response<RiskQcAppealInfoVo> getAppealInfo(@RequestParam long appealWorkOrderId){
        RiskQcAppealInfoModel riskQcAppealInfoModel = riskAppealQcDetailService.getAppealInfo(appealWorkOrderId);
        return NewResponseUtil.makeSuccess(new RiskQcAppealInfoVo(riskQcAppealInfoModel));
    }


    @PostMapping(path = "get-common-order-result")
    public Response<RiskQcCommonResultVo> getCommonOrderResult(@RequestParam long qcWorkOrderId, @RequestParam int workOrderType){
        RiskQcCommonResultVo riskQcCommonResultVo = riskAppealQcDetailService.getInfo(qcWorkOrderId, workOrderType);
        return NewResponseUtil.makeSuccess(riskQcCommonResultVo);
    }

    @PostMapping(path = "get-qc-work-order-id")
    public Response<Long> getQcWorkOrderId(@RequestParam long appealWorkOrderId){
        RiskQcAppealWorkOrderRel riskQcAppealWorkOrderRel = riskQcAppealWorkOrderRelBiz.getByAppealWorkOrderId(appealWorkOrderId);
        if (riskQcAppealWorkOrderRel == null){
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_GAIN_WORK_ORDER_ERROR);
        }
        long qcWorkOrderId = riskQcAppealWorkOrderRel.getQcWorkOrderId();
        return NewResponseUtil.makeSuccess(qcWorkOrderId);
    }

}
