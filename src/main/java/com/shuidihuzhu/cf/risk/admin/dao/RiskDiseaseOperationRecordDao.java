package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseOperationRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskDiseaseOperationRecordDao {

    int save(RiskDiseaseOperationRecord operationRecord);

    List<RiskDiseaseOperationRecord> findListByDiseaseId(@Param("diseaseId") long diseaseId);

    RiskDiseaseOperationRecord getLastOneByDiseaseId(@Param("diseaseId") long diseaseId);
}
