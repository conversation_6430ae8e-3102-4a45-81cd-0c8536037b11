package com.shuidihuzhu.cf.risk.admin.dao.hit;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyHitDto;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/3/11 15:12
 * @Description:
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface CfRiskRepeatPayOrderCaseRecordDao {

    int insertRepeatPayOrder(StrategyHitDto strategyHitDto);

    List<StrategyHitDto> listByCaseId(@Param("caseId") int caseId);
}
