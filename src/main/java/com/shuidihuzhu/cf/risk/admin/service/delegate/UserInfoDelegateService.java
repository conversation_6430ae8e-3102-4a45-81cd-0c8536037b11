package com.shuidihuzhu.cf.risk.admin.service.delegate;

import com.shuidihuzhu.account.model.UserInfoModel;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/26 10:59 AM
 */
public interface UserInfoDelegateService {

    List<UserInfoModel> getUserInfoByUserIdBatch(List<Long> userIds);

    UserInfoModel getUserInfoByUserId(long userId);

    UserInfoModel getUserInfoByCryptoMobile(String cryptoMobile);

    UserInfoModel getUserInfoByMobile(String mobile);

}
