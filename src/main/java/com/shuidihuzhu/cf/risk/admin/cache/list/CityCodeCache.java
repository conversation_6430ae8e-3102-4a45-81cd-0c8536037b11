package com.shuidihuzhu.cf.risk.admin.cache.list;

import com.shuidihuzhu.cf.risk.admin.cache.AbstractCache;
import com.shuidihuzhu.cf.risk.admin.dao.list.RiskCityAreaDao;
import com.shuidihuzhu.cf.risk.model.RiskCityAreaDto;
import com.shuidihuzhu.common.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CityCodeCache extends AbstractCache<String, List<RiskCityAreaDto>> {

    @Resource
    private RiskCityAreaDao riskCityAreaDao;

    public RiskCityAreaDto getCityCodeDto(String province, String city){
        try {
            List<RiskCityAreaDto> areaList = getValue(getName());
            return areaList.parallelStream().filter(riskCityAreaDto ->
                    Objects.equals(province, riskCityAreaDto.getProvince()) && Objects.equals(city, riskCityAreaDto.getCity()))
                    .findAny()
                    .orElse(null);
        } catch (ExecutionException e) {
            log.error("", e);
        }
        return null;
    }

    public RiskCityAreaDto getCityCodeDtoById(Integer provinceId, Integer cityId){
        try {
            List<RiskCityAreaDto> areaList = getValue(getName());
            return areaList.parallelStream().filter(riskCityAreaDto ->
                    Objects.equals(provinceId, riskCityAreaDto.getProvinceId()) && Objects.equals(cityId, riskCityAreaDto.getCityId()))
                    .findAny()
                    .orElse(null);
        } catch (ExecutionException e) {
            log.error("", e);
        }
        return null;
    }

    public RiskCityAreaDto getCityCodeDtoByAreaCode(String areaCode){
        try {
            List<RiskCityAreaDto> areaList = getValue(getName());
            return areaList.parallelStream().filter(riskCityAreaDto ->
                    Objects.equals(areaCode, riskCityAreaDto.getAreaCode()) || Objects.equals(areaCode, riskCityAreaDto.getAreaCodeFormer()))
                    .findAny()
                    .orElse(null);
        } catch (ExecutionException e) {
            log.error("", e);
        }
        return null;
    }

    /**
     * @param key constant
     * @return {@code Map<String, Map<String, RiskCityAreaDto>>}
     */
    @Override
    protected List<RiskCityAreaDto> queryData(String key) {
        log.info("CityCodeCache setCache!");

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        List<RiskCityAreaDto> collect = riskCityAreaDao.listAll().parallelStream().map(riskCityArea -> {
            RiskCityAreaDto riskCityAreaDto = new RiskCityAreaDto();
            BeanUtils.copyProperties(riskCityArea, riskCityAreaDto);
            return riskCityAreaDto;
        }).collect(Collectors.toList());

        stopWatch.stop();

        log.info("CityCodeCache size:{}, cost:{}", collect.size(), stopWatch.getTotalTimeMillis());

        return collect;
    }

    @Override
    protected int getExpireAfterWriteSecond() {
        return 0;
    }

    @Override
    protected int getRefreshAfterWriteSecond() {
        return 3;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }

    @Override
    protected boolean needPreLoading() {
        return true;
    }
}
