package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.RiskCarBrand;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskCarBrandDao {
    int insertSelective(RiskCarBrand record);

    RiskCarBrand selectByPrimaryKey(Long id);

    List<RiskCarBrand> listByLimit50(String carName);
}