package com.shuidihuzhu.cf.risk.admin.model.enums.hit;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 风控策略枚举
 * <AUTHOR>
 * @date 2020/8/20 17:32
 */
@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_strategy_hit_record", columnName = "status")})
@AllArgsConstructor
@Getter
public enum RiskHandleStatusEnum {

    DEFAULT(-1, "未知"),
    PENDING(0, "待处理"),
    PROCESSING(1, "处理中"),
    COMPLETE(2, "处理完成"),
    ;

    public static RiskHandleStatusEnum fromCode(int code){
        return Arrays.stream(values())
                .filter(riskHandleStatusEnum -> riskHandleStatusEnum.getCode() == code).findFirst()
                .orElse(DEFAULT);
    }

    public static Map<Integer, String> usableKeyVal(){
        return Arrays.stream(values()).filter(riskHandleStatusEnum -> riskHandleStatusEnum.getCode()>=0)
                .collect(Collectors.toMap(RiskHandleStatusEnum::getCode, RiskHandleStatusEnum::getDesc));
    }

    private int code;
    private String desc;

}
