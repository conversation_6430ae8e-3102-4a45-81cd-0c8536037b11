package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseDataBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseDataDao;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseDataPortion;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Service
public class RiskDiseaseDataBizImpl implements RiskDiseaseDataBiz {

    @Autowired
    private RiskDiseaseDataDao riskDiseaseDataDao;

    @Override
    public int delete(long diseaseId) {
        if (diseaseId <= 0) {
            return 0;
        }
        return riskDiseaseDataDao.delete(diseaseId);
    }

    @Override
    public RiskDiseaseData getByClassName(String diseaseClassName) {
        if (StringUtils.isBlank(diseaseClassName)) {
            return null;
        }
        return riskDiseaseDataDao.getByClassName(diseaseClassName);
    }

    @Override
    public int save(RiskDiseaseData riskDiseaseData) {
        if (riskDiseaseData == null) {
            return 0;
        }
        return riskDiseaseDataDao.save(riskDiseaseData);
    }

    @Override
    public int update(RiskDiseaseData riskDiseaseData) {
        if (riskDiseaseData == null) {
            return 0;
        }
        return riskDiseaseDataDao.update(riskDiseaseData);
    }

    @Override
    public RiskDiseaseData getById(long diseaseId) {
        if (diseaseId <= 0 ) {
            return null;
        }
        return riskDiseaseDataDao.getById(diseaseId);
    }

    @Override
    public List<RiskDiseaseData> findList(String diseaseClassName, String medicalName, String normalName, int raiseType, PageRequest pageRequest) {
        return riskDiseaseDataDao.findList(StringUtils.trimToNull(diseaseClassName), StringUtils.trim(medicalName),
                StringUtils.trimToNull(normalName),raiseType, pageRequest);
    }

    @Override
    public List<RiskDiseaseData> findListV2(String diseaseClassName, int isDelete, String startCreateTime, String endCreateTime,
                                          int raiseType, List<Long> diseaseIds, int current, int pageSize) {
        if(CollectionUtils.isNotEmpty(diseaseIds)){
            return riskDiseaseDataDao.findListByDiseaseIds(StringUtils.trimToNull(diseaseClassName),isDelete,
                    StringUtils.trim(startCreateTime), StringUtils.trimToNull(endCreateTime),raiseType,diseaseIds);
        }
        return riskDiseaseDataDao.findListV2(StringUtils.trimToNull(diseaseClassName),isDelete,
                StringUtils.trim(startCreateTime), StringUtils.trimToNull(endCreateTime),raiseType);
    }

    @Override
    public int deleteByName(String diseaseClassName) {
        return 0;
    }

    @Override
    public List<RiskDiseaseDataPortion> getAllDiseaseRule() {
        return riskDiseaseDataDao.getAllDiseaseRule();
    }

    @Override
    public List<RiskDiseaseData> findDiseaseNormList(String diseaseClassName) {
        if (StringUtils.isBlank(diseaseClassName)) {
            return null;
        }
        return riskDiseaseDataDao.findDiseaseNormList(diseaseClassName);
    }
}
