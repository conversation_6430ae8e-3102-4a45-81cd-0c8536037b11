package com.shuidihuzhu.cf.risk.admin.service.qc.subject.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckParamV2;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordCheckFeignV2Client;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio.AiTextValidForAudioDelegate;
import com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio.AiTextValidForAudioResp;
import com.shuidihuzhu.cf.risk.admin.model.*;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.vo.AsrSentenceVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcAsrResultVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcVideoVo;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.recording.WorkOrderRecordingHandler;
import com.shuidihuzhu.cf.risk.admin.service.recording.WorkOrderRecordingHandlerRegister;
import com.shuidihuzhu.cf.risk.admin.util.CosUploadUtil;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class QcAudioAsrServiceImpl implements QcAudioAsrService {

    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;

    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;

    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;

    @Autowired
    private AiAsrDelegate aiAsrDelegate;

    @Autowired
    private AiTextValidForAudioDelegate aiTextValidForAudioDelegate;

    @Autowired
    private WorkOrderExtFeignClient workOrderExtFeignClient;

    @Value("${apollo.order.qc_common.video.valid-percent:0.1}")
    @Getter
    private float audioValidPercent;

    @Autowired
    private RiskControlWordCheckFeignV2Client riskControlWordCheckFeignV2Client;

    @Autowired
    private WorkOrderRecordingHandlerRegister workOrderRecordingHandlerRegister;

    private final static Integer TEXT_LENGTH = 60000;

    @Override
    public Response<Void> handleBDRecord(long workOrderId, boolean useZiyanAsr) {
        Response<WorkOrderVO> orderResp = cfWorkOrderClient.getWorkOrderById(workOrderId);
        WorkOrderVO order = orderResp.getData();
        long qcId = order.getQcId();

        List<RiskQcMaterialsInfo> riskQcMaterialsInfos = riskQcMaterialsInfoBiz.getMaterials(qcId,
                QcMaterialsKeyEnum.RECORDING.getKey());
        if (CollectionUtils.isEmpty(riskQcMaterialsInfos)) {
            return NewResponseUtil.makeSuccess();
        }

        for (RiskQcMaterialsInfo riskQcMaterialsInfo : riskQcMaterialsInfos) {
            if (riskQcMaterialsInfo == null) {
                continue;
            }
            RiskQcVideoVo riskQcVideoVo = JSONObject.parseObject(riskQcMaterialsInfo.getMaterialsValue(),
                    new TypeReference<>() {});
            if (riskQcVideoVo == null) {
                continue;
            }
            if (StringUtils.isBlank(riskQcVideoVo.getVideoUrl())) {
                continue;
            }
            String url = CosUploadUtil.getAsrCosSignWithUrl(riskQcVideoVo.getVideoUrl());
            aiAsrDelegate.analyse(workOrderId, riskQcMaterialsInfo.getId(), url, AiAsrDelegate.HandleTypeEnum.HANDLE_QC_BD, useZiyanAsr);
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> onAudioAsrShuidiCallback(QcAsrResultVO resultVO) {
        log.info("onAudioAsrShuidiCallback result {}", resultVO);
        if (resultVO == null) {
            return NewResponseUtil.makeSuccess();
        }
        long materialId = resultVO.getMaterialId();
        long workOrderId = resultVO.getWorkOrderId();
        RiskQcMaterialsInfo info = riskQcMaterialsInfoBiz.getById(materialId);
        if (info == null) {
            log.warn("info null");
            return NewResponseUtil.makeSuccess();
        }
        String jsonString = info.getMaterialsValue();
        RiskQcVideoVo riskQcVideoVo = buildVideoVo(resultVO, workOrderId, jsonString);

        String newValue = JSON.toJSONString(riskQcVideoVo);
        riskQcMaterialsInfoBiz.updateById(materialId, newValue);
        log.info("onAudioAsrShuidiCallback update success id {}", materialId);
        checkAllHasCallback(workOrderId, info.getQcId());
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> onAudioAsrCallback(QcAsrResultVO resultVO) {
        log.info("onAudioAsrCallback result {}", resultVO);
        if (resultVO == null) {
            return NewResponseUtil.makeSuccess();
        }
        long materialId = resultVO.getMaterialId();
        long workOrderId = resultVO.getWorkOrderId();
        RiskQcMaterialsInfo info = riskQcMaterialsInfoBiz.getById(materialId);
        if (info == null) {
            log.warn("info null");
            return NewResponseUtil.makeSuccess();
        }
        String jsonString = info.getMaterialsValue();
        RiskQcVideoVo riskQcVideoVo = buildVideoVo(resultVO, workOrderId, jsonString);

        String newValue = JSON.toJSONString(riskQcVideoVo);
        riskQcMaterialsInfoBiz.updateById(materialId, newValue);
        log.info("onAudioAsrCallback update success id {}", materialId);
        return NewResponseUtil.makeSuccess();
    }

    private RiskQcVideoVo buildVideoVo(QcAsrResultVO resultVO, long workOrderId, String jsonString) {
        RiskQcVideoVo riskQcVideoVo = JSON.parseObject(jsonString, new TypeReference<>() {});

        processWithResult(riskQcVideoVo, resultVO);

        List<String> sentences = RiskQcVideoVo.getSentencesByAdapt(riskQcVideoVo);
        String asrText = StringUtils.join(sentences, "");
        // 如果过长截取字符串
        String asrTextForExt = StringUtils.left(asrText, 1_0000);
        Von.extUpdate().saveByList(workOrderId, Lists.newArrayList(WorkOrderExt.create(workOrderId,OrderExtName.asrResult.getName(), asrTextForExt)));
        // 标红词检查
        RiskControlWordCategoryDO.RiskWordUseScene scene = RiskControlWordCategoryDO.RiskWordUseScene.QC_BD;
        RiskWordCheckParamV2 p = RiskWordCheckParamV2.createBasic(scene, asrText);
        p.setCheckAll(true);
        Response<RiskWordResult> riskWordResultResponse = riskControlWordCheckFeignV2Client.checkForTag(p);
        RiskWordResult data = riskWordResultResponse.getData();
        List<CfBaseInfoRiskHitVO.ColourTag> hitWordColourTags = data.getHitWordColourTags();
        log.debug("ColourTag resp {}", riskWordResultResponse);
        riskQcVideoVo.setHitWordColourTags(hitWordColourTags);

        // riskQcVideoVo超长截取
        riskQcVideoVo = doHandleLength(riskQcVideoVo);
        return riskQcVideoVo;
    }

    private RiskQcVideoVo doHandleLength(RiskQcVideoVo riskQcVideoVo) {
        if(JSON.toJSONString(riskQcVideoVo).getBytes(StandardCharsets.UTF_8).length <= TEXT_LENGTH) {
            return riskQcVideoVo;
        }
        int length = 0;
        List<AsrSentenceVO> sentenceVOList = riskQcVideoVo.getSentenceInfoList();
        riskQcVideoVo.setSentenceInfoList(Lists.newArrayList());
        length = JSON.toJSONString(riskQcVideoVo).getBytes(StandardCharsets.UTF_8).length;
        if(length > TEXT_LENGTH) {
            log.warn("RiskQcVideoVo 超长, riskQcVideoVo:{}", riskQcVideoVo);
            return new RiskQcVideoVo();
        }
        List<AsrSentenceVO> newSentenceVOList = getNewSentenceVOList(length, sentenceVOList);
        riskQcVideoVo.setSentenceInfoList(newSentenceVOList);
        return riskQcVideoVo;
    }

    @Override
    public List<AsrSentenceVO> getNewSentenceVOList(int length, List<AsrSentenceVO> sentenceVOList) {
        List<AsrSentenceVO> newSentenceVOList = Lists.newArrayList();
        for (AsrSentenceVO asrSentenceVO : sentenceVOList) {
            length += JSON.toJSONString(asrSentenceVO).getBytes(StandardCharsets.UTF_8).length;
            if(length > TEXT_LENGTH) {
                log.warn("sentenceVOList 超长截断, sentence:{}", asrSentenceVO);
                break;
            }
            newSentenceVOList.add(asrSentenceVO);
        }
        return newSentenceVOList;
    }

    @Override
    public void handleWorkOrderRecording(Long workOrderId) {
        handleWorkOrderRecording(workOrderId, null);
    }

    @Override
    public void handleWorkOrderRecording(Long workOrderId, Integer workOrderType) {
        if (workOrderType == null) {
            final Response<BasicWorkOrder> orderResp = Von.read().getOrderBasicInfoById(workOrderId);
            if (NewResponseUtil.isNotOk(orderResp)) {
                log.error("工单查询失败 handleWorkOrderRecording {}", workOrderId);
                return;
            }
            final BasicWorkOrder order = orderResp.getData();
            workOrderType = order.getOrderType();
        }

        WorkOrderType orderType = WorkOrderType.getFromType(workOrderType);
        if (orderType == null) {
            log.warn("WorkOrderCreateConsumer handleWorkOrderRecording orderType 无效 :{}", workOrderType);
            return;
        }
        if (workOrderType == WorkOrderType.qc_common.getType()) {
            handleBDRecord(workOrderId, true);
            return;
        }
        WorkOrderRecordingHandler workOrderRecordingHandler = workOrderRecordingHandlerRegister.getBean(orderType);
        if (workOrderRecordingHandler == null) {
            log.info("WorkOrderCreateConsumer handleWorkOrderRecording getBean is null workOrderType :{}", workOrderType);
            return;
        }
        workOrderRecordingHandler.handleWorkOrderRecording(workOrderId);
    }

    private void checkAllHasCallback(long workOrderId, long qcId) {
        log.info("checkAllHasCallback {}, {}", workOrderId, qcId);
        List<RiskQcMaterialsInfo> materials = riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.RECORDING.getKey());
        if (CollectionUtils.isEmpty(materials)) {
            return;
        }
        List<RiskQcVideoVo> videos = Lists.newArrayList();
        for (RiskQcMaterialsInfo material : materials) {
            if (material == null) {
                continue;
            }
            RiskQcVideoVo riskQcVideoVo = JSONObject.parseObject(material.getMaterialsValue(), new TypeReference<>() {});
            if (riskQcVideoVo == null) {
                continue;
            }
            videos.add(riskQcVideoVo);

            // 有录音未处理完 直接结束
            if (riskQcVideoVo.getAudioAsrStatus() == 0){
                return;
            }
        }

        // 所有都已返回结果
        log.info("该工单所有录音Asr已返回结果 {}", workOrderId);
        AiTextValidForAudioResp textResp = aiTextValidForAudioDelegate.analyse(workOrderId, videos);
        if (textResp == null) {
            log.info("AiTextValidForAudioResp null");
            return;
        }
        float prob_score = textResp.getPreb();
        boolean valid = prob_score >= audioValidPercent;
        // 给工单打标签
        int invalidOrder = !valid ? 1 : 2;

        // 有效工单再走阿里asr
        if(valid){
            log.info("有效工单再走阿里asr {}", workOrderId);
            handleBDRecord(workOrderId, false);
        }

        final Response<Void> saveByList = Von.extUpdate().saveByList(workOrderId,
                Lists.newArrayList(
                        WorkOrderExt.create(workOrderId, QcConst.OrderExt.seenInvalidOrder, invalidOrder)
                )
        );
        if (NewResponseUtil.isNotOk(saveByList)) {
            log.error("标记有效质检工单失败 order ext save fail workOrderId {}, sig {}", workOrderId, invalidOrder);
        }
    }

    private void processWithResult(RiskQcVideoVo video, QcAsrResultVO resultVO) {
        QcAsrResultVO.Result result = resultVO.getResult();
        if (result == null) {
            return;
        }
        List<QcAsrResultVO.Sentence> sentences = result.getSentences();
        if (CollectionUtils.isEmpty(sentences)) {
            log.info("empty sentences");
            video.setAudioAsrStatus(2);
            return;
        }
        List<AsrSentenceVO> vos = getSentenceIntercept(sentences);
        video.setSentenceInfoList(vos);
        video.setAudioAsrStatus(1);
    }

    private List<AsrSentenceVO> getSentenceIntercept(List<QcAsrResultVO.Sentence> sentences) {
        List<AsrSentenceVO> vos = Lists.newArrayList();
        int maxLength = 5_0000;
        int currentLength = 0;
        for (QcAsrResultVO.Sentence sentence : sentences) {
            final int length = StringUtils.length(sentence.getText());
            currentLength += length;
            if (currentLength > maxLength) {
                return vos;
            }
            vos.add(AsrSentenceVO.createByAsrResult(sentence));
        }
        return vos;
    }

    public static void main(String[] args) {
        ArrayList<String> s = Lists.newArrayList("aa", null, "bb", null);
        String join = StringUtils.join(s, "");
        System.out.println("join = " + join);
    }
}
