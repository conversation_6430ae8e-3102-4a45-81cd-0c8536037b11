package com.shuidihuzhu.cf.risk.admin.dao.blacklist;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskBlacklistTypeLogDao {
    int insertSelective(RiskBlacklistTypeLog record);

    RiskBlacklistTypeLog selectByPrimaryKey(Long id);

    List<RiskBlacklistTypeLog> listByTypeId(Long typeId);
}