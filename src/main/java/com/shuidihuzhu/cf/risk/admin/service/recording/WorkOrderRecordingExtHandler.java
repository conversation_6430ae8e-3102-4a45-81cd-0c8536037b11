package com.shuidihuzhu.cf.risk.admin.service.recording;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckParamV2;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordCheckFeignV2Client;
import com.shuidihuzhu.cf.risk.admin.dao.WorkOrderRecordingMapper;
import com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio.AiTextValidForAudioDelegate;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingDO;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingModel;
import com.shuidihuzhu.cf.risk.admin.model.vo.AsrSentenceVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcAsrResultVO;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcAudioAsrService;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.impl.QcAudioAsrServiceImpl;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2022/3/28 下午4:22
 */
@Slf4j
@Component
public class WorkOrderRecordingExtHandler{

    @Autowired
    private WorkOrderRecordingMapper workOrderRecordingMapper;
    @Autowired
    private RiskControlWordCheckFeignV2Client riskControlWordCheckFeignV2Client;
    @Autowired
    private AiTextValidForAudioDelegate aiTextValidForAudioDelegate;
    @Autowired
    private QcAudioAsrService qcAudioAsrService;
    private final static Integer TEXT_LENGTH = 60000;


    public void handleWorkOrderRecordingExt(long workOrderId, long materialId, QcAsrResultVO qcAsrResultVO) {
        WorkOrderRecordingDO workOrderRecordingDO = workOrderRecordingMapper.selectById(materialId);
        if (workOrderRecordingDO==null) {
            log.info("handleWorkOrderRecordingExt workOrderId:{} materialId:{} selectById result is null", workOrderId, materialId);
            return;
        }
        QcAsrResultVO.Result result = qcAsrResultVO.getResult();
        if (result == null) {
            log.info("handleWorkOrderRecordingExt qcAsrResultVO.getResult() is null");
            return;
        }
        workOrderRecordingDO = doHandleAsrResult(workOrderRecordingDO, qcAsrResultVO);
        workOrderRecordingDO = doHandleHitWordRecording(workOrderRecordingDO, qcAsrResultVO);
        // workOrderRecordingDO过长截取
        workOrderRecordingDO = doHandleLength(workOrderRecordingDO);
        workOrderRecordingMapper.updateWorkOrderRecordingExt(workOrderRecordingDO);
//        checkAllHasCallback(workOrderId);
    }

    private WorkOrderRecordingDO doHandleLength(WorkOrderRecordingDO workOrderRecordingDO) {
        // recordingExt过长截取
        WorkOrderRecordingModel recordingExt = workOrderRecordingDO.getRecordingExt();
        if(recordingExt == null){
            return workOrderRecordingDO;
        }
        // 判断recordingExt原本长度
        if (JSON.toJSONString(recordingExt).getBytes(StandardCharsets.UTF_8).length <= TEXT_LENGTH) {
            return workOrderRecordingDO;
        }
        log.info("handleWorkOrderRecordingExt recordingExt is too long, workOrderRecordingDO:{}", workOrderRecordingDO);
        int length = 0;
        List<AsrSentenceVO> sentenceInfoList = recordingExt.getSentenceInfoList();
        // 将recordingExt去除sentenceInfoList的长度
        recordingExt.setSentenceInfoList(Lists.newArrayList());
        length = JSON.toJSONString(recordingExt).getBytes(StandardCharsets.UTF_8).length;
        if (length > TEXT_LENGTH) {
            log.info("handleWorkOrderRecordingExt recordingExt without sentenceInfo is too long, workOrderRecordingDO:{}", workOrderRecordingDO);
            return new WorkOrderRecordingDO();
        }
        List<AsrSentenceVO> newSentenceInfoList = qcAudioAsrService.getNewSentenceVOList(length, sentenceInfoList);
        recordingExt.setSentenceInfoList(newSentenceInfoList);
        return workOrderRecordingDO;
    }


    /**
     * asr结果处理
     * @param workOrderRecordingDO
     * @param qcAsrResultVO
     * @return
     */
    private WorkOrderRecordingDO doHandleAsrResult(WorkOrderRecordingDO workOrderRecordingDO, QcAsrResultVO qcAsrResultVO) {
        QcAsrResultVO.Result result = qcAsrResultVO.getResult();
        WorkOrderRecordingModel recordingExt = workOrderRecordingDO.getRecordingExt();
        if (recordingExt == null) {
            log.info("doHandleAsrResult fail recordingExt is null");
            return workOrderRecordingDO;
        }
        List<QcAsrResultVO.Sentence> sentences = result.getSentences();
        if (CollectionUtils.isEmpty(sentences)) {
            log.info("empty sentences");
            recordingExt.setAudioAsrStatus(2);
            return workOrderRecordingDO;
        }
        String asrText = sentences.stream().map(QcAsrResultVO.Sentence::getText).collect(Collectors.joining(""));

        // 如果过长截取字符串
        asrText = StringUtils.left(asrText, 1_0000);
        Von.extUpdate().saveByList(workOrderRecordingDO.getWorkOrderId(), Lists.newArrayList(WorkOrderExt.create(workOrderRecordingDO.getWorkOrderId(), OrderExtName.asrResult.getName(), asrText)));
        List<AsrSentenceVO> vos = sentences.stream().map(AsrSentenceVO::createByAsrResult).collect(Collectors.toList());
        recordingExt.setSentenceInfoList(vos);
        recordingExt.setAudioAsrStatus(1);
        return workOrderRecordingDO;
    }

    /**
     * 语音关键词标红
     * @param workOrderRecordingDO
     * @param qcAsrResultVO
     * @return
     */
    private WorkOrderRecordingDO doHandleHitWordRecording(WorkOrderRecordingDO workOrderRecordingDO, QcAsrResultVO qcAsrResultVO) {
        WorkOrderRecordingModel recordingExt = workOrderRecordingDO.getRecordingExt();
        if (recordingExt == null) {
            log.info("doHandleHitWordRecording fail recordingExt is null");
            return workOrderRecordingDO;
        }
        QcAsrResultVO.Result result = qcAsrResultVO.getResult();
        List<String> sentences = Optional.ofNullable(result.getSentences()).orElse(Lists.newArrayList()).stream().map(QcAsrResultVO.Sentence::getText).collect(Collectors.toList());
        String asrText = StringUtils.join(sentences, "");
        // 标红词检查
        RiskWordCheckParamV2 p = RiskWordCheckParamV2.createBasic(qcAsrResultVO.getHandleTypeEnum().getRiskWordUseScene(), asrText);
        p.setCheckAll(true);
        Response<RiskWordResult> riskWordResultResponse = riskControlWordCheckFeignV2Client.checkForTag(p);
        RiskWordResult data = riskWordResultResponse.getData();
        List<CfBaseInfoRiskHitVO.ColourTag> hitWordColourTags = data.getHitWordColourTags();
        log.debug("ColourTag resp {}", riskWordResultResponse);
        recordingExt.setHitWordColourTags(hitWordColourTags);
        return workOrderRecordingDO;
    }
}
