package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardExt;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcStandardExtDao {

    List<RiskQcStandardExt> getByStandardIds(@Param("ids") List<Long> standardIds);

    int add(@Param("qcStandardId") long qcStandardId, @Param("firstProperty") long firstProperty,
            @Param("secondProperty") long secondProperty, @Param("useScene") int useScene);


    List<RiskQcStandardExt> findByFirstPropertyAndScene(@Param("firstPropertyIds") List<Long> firstPropertyIds,
                                                        @Param("useScene") int useScene);
}
