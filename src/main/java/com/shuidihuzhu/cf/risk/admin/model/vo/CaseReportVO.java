package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.enums.OldReportHandleStatusEnum;
import com.shuidihuzhu.client.cf.admin.model.AdminMarkReport;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import lombok.Data;

@Data
public class CaseReportVO {
    private int caseId; //案例id
    private int reportId; //举报id
    private long workOrderId; //举报工单id
    private int handleStatus; //处理状态
    private boolean isNew;//是否是新举报
    private String handleStatusStr;//处理状态字符串
    /**
     * 是否是失联案例
     */
    private boolean hasLost;

    public CaseReportVO(AdminMarkReport report) {
        this.caseId = report.getCaseId();
        this.reportId = report.getReportId();
        this.workOrderId = report.getWorkOrderId();
        this.handleStatus = report.getHandleStatus();
        this.hasLost =report.isHasLost();
        this.isNew = report.isNew();
        if (report.isNew()) {
            HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(report.getHandleStatus());
            this.handleStatusStr = handleResultEnum == null ? "" : handleResultEnum.getShowMsg();
        } else {
            this.handleStatusStr = OldReportHandleStatusEnum.findOfCode(report.getHandleStatus());
        }
    }
}
