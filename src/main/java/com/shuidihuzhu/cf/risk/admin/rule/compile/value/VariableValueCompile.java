package com.shuidihuzhu.cf.risk.admin.rule.compile.value;

import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionData;
import com.shuidihuzhu.cf.risk.admin.rule.enums.ValueType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/18 22:35
 */
@Component("variableValueCompile")
@Slf4j
public class VariableValueCompile extends AbstractCompileChain<CriterionData> {

    @Override
    public String compileValue(CriterionData criterionData) {
        if (criterionData.getValueType() == ValueType.VARIABLE) {
            return METHOD_VARIABLE_NAME + "." + criterionData.getVariable().getSourcePath();
        } else if(valueCompile != null) {
            return valueCompile.compileValue(criterionData);
        }
        return null;
    }

    @Resource(name = "callMethodValueCompile")
    @Override
    public void setValueCompile(AbstractCompileChain valueCompile) {
        this.valueCompile = valueCompile;
    }

}
