package com.shuidihuzhu.cf.risk.admin.controller.qc.excel;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderParam;
import com.shuidihuzhu.cf.risk.admin.service.qc.excel.QcWorkOrderExcelService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static com.shuidihuzhu.cf.risk.admin.constant.AsyncPoolConstants.HANDLE_EXCEL_POOL;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@Api("质检工单导出")
@RequestMapping(path = "/api/cf-risk-admin/qc/work-order/excel", method = RequestMethod.POST)
public class RiskQcWorkOrderExcelController {

    @Resource
    private QcWorkOrderExcelService qcWorkOrderExcelService;
    @Resource(name = HANDLE_EXCEL_POOL)
    private Executor commonExecutor;

    @RequiresPermission("cf-qc:order-export")
    @GetMapping(path = "order-list/download-excel-v1")
    public Response<String> downloadOrderList(@RequestParam String qcWorkOrderParam) {
        QcWorkOrderParam workOrderParam = null;
        try {
            workOrderParam = JSON.parseObject(qcWorkOrderParam, QcWorkOrderParam.class);
        } catch (Exception e) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        long adminUserId = ContextUtil.getAdminLongUserId();
        QcWorkOrderParam finalWorkOrderParam = workOrderParam;
        CompletableFuture.runAsync(() -> {
            qcWorkOrderExcelService.getExcelList(finalWorkOrderParam, adminUserId);
        }, commonExecutor);

        return NewResponseUtil.makeSuccess("请稍等3~5分钟，表格会发送到你的水滴应用助手");
    }

}
