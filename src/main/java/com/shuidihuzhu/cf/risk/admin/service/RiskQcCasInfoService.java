package com.shuidihuzhu.cf.risk.admin.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.material.model.materialField.*;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcCheckedVideoInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.dao.WorkOrderRecordingMapper;
import com.shuidihuzhu.cf.risk.admin.delegate.CreditRiskDelegate;
import com.shuidihuzhu.cf.risk.admin.model.*;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.param.RiskQcVideoInfoModel;
import com.shuidihuzhu.cf.risk.admin.model.vo.*;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.CfPropertyInsuranceFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.cf.risk.admin.util.CosUploadUtil;
import com.shuidihuzhu.cf.risk.admin.util.ListUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingChaiFenV2FeignClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/6/18
 */
@Service
@Slf4j
public class RiskQcCasInfoService {
    @Autowired
    private PreposeMaterialClient preposeMaterialClient;
    @Autowired
    private AuthorFeignClient authorFeignClient;
    @Autowired
    private CfPropertyInsuranceFeignClient cfPropertyInsuranceFeignClient;
    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;
    @Autowired
    private CrowdfundingChaiFenV2FeignClient crowdfundingChaiFenFeignClient;
    @Autowired
    private CfMaterialReadClient materialReadClient;
    @Autowired
    private CfRaiseMaterialClient cfRaiseMaterialClient;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Autowired
    private RiskQcCheckedVideoInfoBiz checkedVideoInfoBiz;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Autowired
    private WorkOrderRecordingMapper workOrderRecordingMapper;
    @Autowired
    private MaskUtil maskUtil;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CreditRiskDelegate creditRiskDelegate;

    public Response<Map<String, Object>> getInitiateInfo(int caseId,long workOrderId) {
        Map<String, Object> result = Maps.newHashMap();
        addPrePoseMaterial(result, caseId,workOrderId);
        result.put("creditInfo", addQcCreditInfo(caseId));
        result.put("caseVersion", getVersion(caseId));
        return NewResponseUtil.makeSuccess(result);
    }

    private void addPrePoseMaterial(Map<String, Object> result, int caseId, long workOrderId) {
        RiskQcSearchIndex riskQcSearchIndex = riskQcSearchIndexBiz.getByWorkOrderId(workOrderId);
        if (Objects.nonNull(riskQcSearchIndex)) {
            RpcResult<PreposeMaterialModel.MaterialInfoVo> rpcResult = preposeMaterialClient.selectMaterialsById(riskQcSearchIndex.getMaterialId());
            log.info("materialInfoVo:{},caseId:{}", JSON.toJSONString(rpcResult), caseId);
            if (rpcResult == null || rpcResult.isFail() || Objects.isNull(rpcResult.getData())) {
                result.put("preposeMaterial", null);
                return;
            }
            PreposeMaterialModel.MaterialInfoVo materialInfoVo = rpcResult.getData();
            final PreposeMaterialVo preposeMaterial = PreposeMaterialVo.buildPreposeVo(materialInfoVo, maskUtil);

            // 查询标红字段
//            preposeMaterial.setRiskLabels(creditRiskDelegate.getRiskLabels(caseId));

            preposeMaterial.setRaiseMobile(null);
            result.put("preposeMaterial", preposeMaterial);
            result.put("initiateInfo", InitiateVo.buildVo(materialInfoVo));
        }
    }

    public int getVersion(int caseId){
        FeignResponse<CfInfoExt> cfInfoExtFeignResponse = crowdfundingFeignClient.getCfInfoExtByCaseId(caseId);
        log.info("cfInfoExtFeignResponse:{}", cfInfoExtFeignResponse);
        if (Objects.nonNull(cfInfoExtFeignResponse) && cfInfoExtFeignResponse.ok() && Objects.nonNull(cfInfoExtFeignResponse.getData()))
        {
            CfInfoExt cfInfoExt = cfInfoExtFeignResponse.getData();
            return cfInfoExt.getCfVersion();
        }
        return -1;
    }


    public CreditInfoVo addCreditInfo(int caseId) {
        FeignResponse<CfFirsApproveMaterial> cfFirsApproveMaterialFeignResponse = authorFeignClient.getAuthorInfoByInfoId(caseId);
        CfFirsApproveMaterial cfFirsApproveMaterial = (cfFirsApproveMaterialFeignResponse == null || cfFirsApproveMaterialFeignResponse.notOk() || cfFirsApproveMaterialFeignResponse.getData() == null) ? null : cfFirsApproveMaterialFeignResponse.getData();
        RpcResult<CfPropertyInsuranceInfoModel> insuranceInfoModelRpcResult =
                materialReadClient.selectCfPropertyInsuranceInfo(caseId);
        CfPropertyInsuranceInfoModel cfPropertyInsuranceInfoModel = (insuranceInfoModelRpcResult == null || insuranceInfoModelRpcResult.isFail() || insuranceInfoModelRpcResult.getData() == null) ? null : insuranceInfoModelRpcResult.getData();
        RpcResult<CfBasicLivingGuardModel> cfBasicLivingGuardModelRpcResult = cfRaiseMaterialClient.selectLivingGuard(caseId);
        CfBasicLivingGuardModel cfBasicLivingGuardModel = (cfBasicLivingGuardModelRpcResult == null || cfBasicLivingGuardModelRpcResult.isFail() || cfBasicLivingGuardModelRpcResult.getData() == null) ? null : cfBasicLivingGuardModelRpcResult.getData();
        FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = crowdfundingFeignClient.getCaseInfoById(caseId);
        CrowdfundingInfo crowdfundingInfo = (crowdfundingInfoFeignResponse == null || crowdfundingInfoFeignResponse.notOk() ||
        crowdfundingInfoFeignResponse.getData() == null) ? null : crowdfundingInfoFeignResponse.getData();
        String mobile = "";
        if (Objects.nonNull(crowdfundingInfo)){
            UserInfoModel userInfoModel = userInfoDelegateService.getUserInfoByUserId(crowdfundingInfo.getUserId());
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                mobile = shuidiCipher.decrypt(userInfoModel.getCryptoMobile());
            } else {
                FeignResponse<CfInfoExt> cfInfoExtFeignResponse = crowdfundingFeignClient.getCfInfoExtByCaseId(caseId);
                if (Objects.nonNull(cfInfoExtFeignResponse) && cfInfoExtFeignResponse.ok() && Objects.nonNull(cfInfoExtFeignResponse.getData())){
                    CfInfoExt cfInfoExt = cfInfoExtFeignResponse.getData();
                    mobile = shuidiCipher.decrypt(cfInfoExt.getCryptoRegisterMobile());
                }
            }
        }
        log.info("CreditInfoVo caseId:{} cfFirsApproveMaterial:{}, cfPropertyInsuranceInfoModel:{}, cfBasicLivingGuardModel:{}, crowdfundingInfo:{}",
                caseId, cfFirsApproveMaterial, cfPropertyInsuranceInfoModel, cfBasicLivingGuardModel, crowdfundingInfo);
        CreditInfoVo creditInfoVo = CreditInfoVo.buildCreditInfo(cfFirsApproveMaterial, cfPropertyInsuranceInfoModel,
                cfBasicLivingGuardModel, shuidiCipher, crowdfundingInfo, mobile, maskUtil);
        addCaseInfo(caseId, creditInfoVo);

        // 查询标红字段
//        creditInfoVo.setRiskLabels(creditRiskDelegate.getRiskLabels(caseId));

        return creditInfoVo;
    }

    public CreditInfoVo addQcCreditInfo(int caseId) {

        List<String> materialNames = Lists.newArrayList();
        materialNames.add(CfBasicLivingGuardField.living_allowance);
        materialNames.add(CfBasicLivingGuardField.allowance_img);
        materialNames.add(CfBasicLivingGuardField.has_poverty);
        materialNames.add(CfBasicLivingGuardField.poverty_img);
        materialNames.add(CfInitialPropertyField.medical_insurance);
        materialNames.add(CfInitialPropertyField.life_insurance);
        materialNames.add(CfInitialPropertyField.property_insurance);
        materialNames.add(CfInitialPropertyField.patient_has_home_income);
        materialNames.add(CfInitialPropertyField.patient_home_income_user_defined);
        materialNames.add(CfInitialPropertyField.patient_home_income_range_type);
        materialNames.add(CfInitialPropertyField.patient_has_home_stock);
        materialNames.add(CfInitialPropertyField.patient_home_stock_range_type);
        materialNames.add(CfInitialPropertyField.patient_home_stock_user_defined);
        materialNames.add(CfInitialPropertyField.patient_home_debt);
        materialNames.add(CfInitialPropertyField.patient_home_debt_amount);
        materialNames.add(CfInitialPropertyField.patient_home_debt_range_type);
        materialNames.add(CfInitialPropertyField.car_total_count);
        materialNames.add(CfInitialPropertyField.car_total_value_user_defined);
        materialNames.add(CfInitialPropertyField.car_total_value_range_type);
        materialNames.add(CfInitialPropertyField.car_sale_status);
        materialNames.add(CfInitialPropertyField.car_sale_count);
        materialNames.add(CfInitialPropertyField.car_sale_value_range_type);
        materialNames.add(CfInitialPropertyField.car_sale_value_user_defined);
        materialNames.add(CfInitialPropertyField.self_built_house_property);
        materialNames.add(CfInitialPropertyField.house_total_count);
        materialNames.add(CfInitialPropertyField.house_total_value_range_type);
        materialNames.add(CfInitialPropertyField.house_total_value_user_defined);
        materialNames.add(CfInitialPropertyField.pure_value_range_type);
        materialNames.add(CfInitialPropertyField.pure_value_user_defined);
        materialNames.add(CfInitialPropertyField.house_sale_status);
        materialNames.add(CfInitialPropertyField.house_sale_count);
        materialNames.add(CfInitialPropertyField.house_sale_value_user_defined);
        materialNames.add(CfInitialPropertyField.house_sale_value_range_type);
        materialNames.add(CfInitialPropertyField.insurance_risk_label);
        materialNames.add(CfInitialPropertyField.has_raise_on_other);
        materialNames.add(CfInitialPropertyField.raise_amount_on_other);
        materialNames.add(CfInitialPropertyField.remain_amount_on_other);
        materialNames.add(CfInitialPropertyField.use_for_medical_on_other);
        materialNames.add(CfMaterialPreModifyField.baseInfoTitle);
        materialNames.add(CfMaterialPreModifyField.baseInfoContent);
        materialNames.add(CfInitialPropertyField.patient_marital_status);
        materialNames.add(CfInitialPropertyField.married_children_count);
        materialNames.add(CfInitialPropertyField.married_children_status);
        materialNames.add(CfInitialPropertyField.patient_parent_status);
        materialNames.add(CfInitialPropertyField.net_worth_threshold);
        materialNames.add(CfPatientField.patient_name);
        materialNames.add(CfPatientField.patient_id_type);
        materialNames.add(CfPatientField.patient_crypto_id_card);
        materialNames.add(CfBaseInfoField.target_amount);
        materialNames.add(CfBaseInfoField.raise_images);
        materialNames.add(CfRaiseBasicInfoField.raise_basic_user_relation_type_for_c);
        materialNames.add(CfRaiseBasicInfoField.raise_basic_self_real_name);
        materialNames.add(CfRaiseBasicInfoField.raise_basic_self_mobile);
        materialNames.add("patient_born_card");
        materialNames.add("patient_crypto_idcard");
        materialNames.add("patient_real_name");
        materialNames.add("first_approve_image_url");
        materialNames.add(CfInitialPropertyField.patient_marital_status);
        materialNames.add(CfInitialPropertyField.married_children_count);
        materialNames.add(CfInitialPropertyField.married_children_status);
        materialNames.add(CfInitialPropertyField.patient_parent_status);


        CreditInfoVo creditInfoVo = new CreditInfoVo();
        RpcResult<Map<String, String>> rpcResult = materialReadClient.selectMaterialRecordByCaseId(caseId, materialNames);
        if(rpcResult == null || rpcResult.isFail() || rpcResult.getData() == null){
            return creditInfoVo;
        }
        // 新增增信信息请求新接口
        RpcResult<CfPropertyInsuranceInfoModel> propertyInsuranceRpcResult = materialReadClient.selectMinVersionCfPropertyInsuranceInfoModelByCaseId(caseId);
        if (propertyInsuranceRpcResult == null || propertyInsuranceRpcResult.isFail() || propertyInsuranceRpcResult.getData() == null) {
            creditInfoVo = CreditInfoVo.buildCreditInfoV2(rpcResult.getData(), new CfPropertyInsuranceInfoModel(), maskUtil);
        } else {
            creditInfoVo = CreditInfoVo.buildCreditInfoV2(rpcResult.getData(), propertyInsuranceRpcResult.getData(), maskUtil);
        }

        // 补充标红字段
//        final List<Integer> riskLabels = creditRiskDelegate.getRiskLabels(caseId);
//        creditInfoVo.setRiskLabels(riskLabels);

        log.info("CreditInfoVo caseId:{} creditInfoVo:{}", caseId, creditInfoVo);
        creditInfoVo.setRaiseMobile(null);
        return creditInfoVo;
    }

    private void addCaseInfo(int caseId, CreditInfoVo creditInfoVo) {
        Response<CaseInfoApproveStageDO> stageInfoResp = caseInfoApproveStageFeignClient.getStageInfo(caseId);
        if (stageInfoResp != null && stageInfoResp.ok()) {
            CaseInfoApproveStageDO stageInfo = stageInfoResp.getData();
            if (stageInfoResp.getData() != null) {
                creditInfoVo.setTitle(StringUtils.trimToNull(stageInfo.getTitle()));
                creditInfoVo.setContent(StringUtils.trimToNull(stageInfo.getContent()));
                creditInfoVo.setPictureUrl(StringUtils.trimToNull(stageInfo.getImages()));
            } else {
                Response<String> response = crowdfundingChaiFenFeignClient.getFundingInfoById(caseId);
                if (response != null && response.ok()) {
                    CrowdfundingInfo crowdfundingInfo = ListUtil.getModelFromResponse(response, CrowdfundingInfo.class);
                    creditInfoVo.setTitle(crowdfundingInfo != null ? StringUtils.trimToNull(crowdfundingInfo.getTitle()) : null);
                    creditInfoVo.setContent(crowdfundingInfo != null ? StringUtils.trimToNull(crowdfundingInfo.getContent()) : null);
                    Response<List<String>> listResponse = crowdfundingChaiFenFeignClient.getAttachmentsByType(caseId
                            , AttachmentTypeEnum.ATTACH_CF.value());
                    List<CrowdfundingAttachmentVo> attachmentVos = (listResponse == null || CollectionUtils.isEmpty(listResponse.getData())) ? Lists.newArrayList() : ListUtil.getModelListFromResponse(listResponse, CrowdfundingAttachmentVo.class);
                    creditInfoVo.setPictureUrl(StringUtils.trimToNull(attachmentVos.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.joining(","))));
                }
            }
        }
    }

    public List<RiskQcVideoVo> getVideoInfo(long caseId, long workOrderId) {
        RiskQcBaseInfo riskQcBaseInfo = riskQcBaseInfoBiz.getByOrderType(caseId, WorkOrderType.qc_common.getType());
        if (riskQcBaseInfo == null) {
            return Lists.newArrayList();
        }
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos = riskQcMaterialsInfoBiz.getMaterials(riskQcBaseInfo.getId(),
                QcMaterialsKeyEnum.RECORDING.getKey());
        if (CollectionUtils.isEmpty(riskQcMaterialsInfos)) {
            return Lists.newArrayList();
        }
        RiskQcVideoInfoModel videoInfo = checkedVideoInfoBiz.getInfoByWorkOrderId(workOrderId);

        Set<Long> checkedIds = Sets.newHashSet();
        Set<Long> correctAsrSet = Sets.newHashSet();
        Set<Long> incorrectAsrSet = Sets.newHashSet();
        if (videoInfo != null) {
            if (CollectionUtils.isNotEmpty(videoInfo.getCheckedIds())) {
                checkedIds = Sets.newHashSet(videoInfo.getCheckedIds());
            }
            if (CollectionUtils.isNotEmpty(videoInfo.getAsrCorrectIds())) {
                correctAsrSet = Sets.newHashSet(videoInfo.getAsrCorrectIds());
            }
            if (CollectionUtils.isNotEmpty(videoInfo.getAsrIncorrectIds())) {
                incorrectAsrSet = Sets.newHashSet(videoInfo.getAsrIncorrectIds());
            }
        }

        // 判断工单是否无效录音
        boolean isInvalidOrder = checkInvalidOrder(workOrderId);

        List<RiskQcVideoVo> riskQcVideoVos = Lists.newArrayList();
        for (RiskQcMaterialsInfo riskQcMaterialsInfo : riskQcMaterialsInfos) {
            if (riskQcMaterialsInfo != null) {
                RiskQcVideoVo riskQcVideoVo = JSONObject.parseObject(riskQcMaterialsInfo.getMaterialsValue(),
                        new TypeReference<>() {});
                Boolean asrCorrect = null;
                if (riskQcVideoVo != null && StringUtils.isNotBlank(riskQcVideoVo.getVideoUrl())) {
                    String url = CosUploadUtil.getCosSignWithUrl(riskQcVideoVo.getVideoUrl());
                    riskQcVideoVo.setVideoUrl(url);
                    long materialsInfoId = riskQcMaterialsInfo.getId();
                    riskQcVideoVo.setId(materialsInfoId);
                    riskQcVideoVo.setChecked(checkedIds.contains(materialsInfoId));
                    if (correctAsrSet.contains(materialsInfoId)) {
                        asrCorrect = true;
                    }
                    if (incorrectAsrSet.contains(materialsInfoId)) {
                        asrCorrect = false;
                    }
                    riskQcVideoVo.setAsrCorrect(asrCorrect);
                    // 老数据只有sentences
                    if (CollectionUtils.isNotEmpty(riskQcVideoVo.getSentences()) && !isInvalidOrder) {
                        List<AsrSentenceVO> sentences = riskQcVideoVo.getSentences().stream()
                                .map(AsrSentenceVO::new).collect(Collectors.toList());
                        riskQcVideoVo.setSentenceInfoList(sentences);
                    }
                    if(isInvalidOrder){
                        riskQcVideoVo.setSentenceInfoList(Lists.newArrayList());
                    }
                    riskQcVideoVos.add(riskQcVideoVo);
                }
            }
        }
        return riskQcVideoVos;
    }

    private boolean checkInvalidOrder(long workOrderId) {
        // 仅普通质检工单
        boolean isInvalidOrder = false;
        Response<List<WorkOrderExt>> seemInvalidOrderList = cfWorkOrderClient.listExtInfos(Lists.newArrayList(workOrderId), QcConst.OrderExt.seenInvalidOrder);
        Map<Long, WorkOrderExt> seemInvalidOrderExtMap = getOrderIdExtMap(seemInvalidOrderList);
        if (MapUtils.isNotEmpty(seemInvalidOrderExtMap)) {
            WorkOrderExt workOrderExt = seemInvalidOrderExtMap.get(workOrderId);
            if (Objects.nonNull(workOrderExt)) {
                int seemInvalidAudio = Integer.parseInt(workOrderExt.getExtValue());
                if(seemInvalidAudio == 1){
                    isInvalidOrder = true;
                }
            }
        }
        return isInvalidOrder;
    }

    @NotNull
    private Map<Long, WorkOrderExt> getOrderIdExtMap(Response<List<WorkOrderExt>> extInfos) {
        Map<Long, WorkOrderExt> workOrderExtMap = Maps.newHashMap();
        if (extInfos.ok() && CollectionUtils.isNotEmpty(extInfos.getData())) {
            workOrderExtMap = extInfos.getData().stream()
                    .collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
        }
        return workOrderExtMap;
    }

    /**
     * 获取工单录音信息V2版本
     *
     * @param workOrderId 工单ID
     * @return 录音信息列表，包含签名后的视频URL和脱敏后的手机号
     */
    public List<WorkOrderRecordingModel> getVideoInfoV2(long workOrderId) {
        // 从数据库查询工单对应的录音记录
        // SQL: select * from work_order_recording where work_order_id = ? and is_delete = 0
        List<WorkOrderRecordingDO> workOrderRecordingDOS = workOrderRecordingMapper.listByWorkOrderId(workOrderId);

        // 使用Stream流处理录音数据
        return workOrderRecordingDOS.stream()
                // 提取录音扩展信息（WorkOrderRecordingModel对象）
                .map(WorkOrderRecordingDO::getRecordingExt)
                // 注释掉的过滤条件：只保留接通状态为200的录音
                // .filter(v -> v.getPhoneStatus() == 200)
                // 为视频URL添加签名，用于安全访问COS存储
                .map(this::videoUrlSign)
                // 对手机号进行脱敏处理，保护用户隐私
                .map(this::maskMobile)
                // 按总通话时长升序排序
                .sorted(Comparator.comparing(WorkOrderRecordingModel::getTotalDuration))
                // 收集为List返回
                .collect(Collectors.toList());
    }

    /**
     * 为录音视频URL添加签名
     *
     * 通过COS签名机制为视频URL添加访问签名，确保URL的安全性和时效性。
     * 签名后的URL可以在一定时间内被安全访问，防止未授权访问。
     *
     * @param recordingExt 录音扩展信息对象
     * @return 处理后的录音信息对象，videoUrl字段已更新为签名URL
     */
    private WorkOrderRecordingModel videoUrlSign(WorkOrderRecordingModel recordingExt) {
        // 检查视频URL是否为空
        if (StringUtils.isNotBlank(recordingExt.getVideoUrl())){
            try {
                // 使用COS工具类为URL添加签名，生成带有访问权限和时效性的安全URL
                recordingExt.setVideoUrl(CosUploadUtil.getCosSignWithUrl(recordingExt.getVideoUrl()));
            } catch (Exception e) {
                // 签名失败时记录警告日志，但不中断处理流程
                log.warn("videoUrlSign CosUploadUtil.getCosSignWithUrl fail param:{} ", recordingExt.getVideoUrl(), e);
            }
        }
        return recordingExt;
    }

    /**
     * 对手机号进行脱敏处理
     *
     * 为了保护用户隐私，将明文手机号转换为脱敏格式（如：138****1234），
     * 并清空原始手机号字段，防止敏感信息泄露。
     *
     * @param recordingExt 录音扩展信息对象
     * @return 处理后的录音信息对象，mobile字段已清空，mobileMask字段包含脱敏后的手机号
     */
    private WorkOrderRecordingModel maskMobile(WorkOrderRecordingModel recordingExt) {
        // 使用Optional链式调用进行安全的空值处理
        Optional.ofNullable(recordingExt)
                // 过滤：只处理手机号不为空的记录
                .filter(r -> StringUtils.isNotBlank(r.getMobile()))
                // 如果条件满足，执行脱敏处理
                .ifPresent(r -> {
                    // 使用脱敏工具将加密手机号解密并脱敏，生成如"138****1234"格式
                    r.setMobileMask(maskUtil.buildByDecryptPhone(r.getMobile()));
                    // 清空原始手机号字段，确保敏感信息不会被返回
                    r.setMobile(null);
                });
        return recordingExt;
    }
}
