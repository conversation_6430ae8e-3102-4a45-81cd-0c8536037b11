package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import com.google.common.base.CaseFormat;
import com.shuidihuzhu.cf.risk.annonation.admin.IgnoreClassFieldScan;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotWxDto;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/20 21:40
 */
@ApiModel(description = "规则对象实体定义，对应拼装JSON案例的字段")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RuleEntityProp {

    @ApiModelProperty("字段name")
    private String fieldName;
    @ApiModelProperty("字段备注")
    private String fieldLabel;
    @ApiModelProperty("字段类型")
    private String fieldType;

    public static List<RuleEntityProp> fieldNameDescMap(long scene) {
        return Arrays.stream(getByScene(scene))
                .filter(field -> !field.isAnnotationPresent(IgnoreClassFieldScan.class) && !field.isSynthetic())
                .map(field ->
                {
                    ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
                    return new RuleEntityProp(field.getName(), apiModelProperty.value(),
                            StringUtils.isNotBlank(apiModelProperty.dataType())
                                    ? apiModelProperty.dataType()
                                    : CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_CAMEL, field.getType().getSimpleName()));
                })
                .collect(Collectors.toList());
    }

    private static Field[] getByScene(long scene) {
        return  QualitySpotSceneEnum.fromCode(scene).getTagetClass().getDeclaredFields();
    }

    public static void main(String[] args) {
        String aLong = CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_CAMEL, "long");
        System.out.println(aLong);
        System.out.println(fieldNameDescMap(1));
        System.out.println(fieldNameDescMap(2));
    }

}
