package com.shuidihuzhu.cf.risk.admin.controller.blacklist;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.BlacklistDataQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.*;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListDataService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/7/15 20:03
 */
@Validated
@Slf4j
@RequiresPermission("black-list:list")
@RestController
@RequestMapping(path = "/api/cf-risk-admin/blacklist/data")
public class RiskBlacklistDataController {

    @Resource
    private BlackListDataService dataService;
    @Resource
    MaskUtil maskUtil;
    @Resource
    private ShuidiCipher shuidiCipher;

    @ApiOperation(value = "黑名单数据-列表")
    @PostMapping(path = "/list")
    public Response<PageResult<BlacklistDataVo>> dataList(BlacklistDataQuery blacklistDataQuery) {
        log.info("黑名单数据-列表，请求入参：{}", blacklistDataQuery);
        return NewResponseUtil.makeSuccess(dataService.queryPage(blacklistDataQuery));
    }

    @ApiOperation(value = "黑名单数据-黑名单类型详情")
    @PostMapping(path = "/type/get")
    public Response<BlacklistDataTypeDetailVo> dataTypeGet(@NotNull(message = "黑名单数据id不能为空")
        @Min(value = 1, message = "黑名单数据id不能小于1") @ApiParam("黑名单数据id") Long dataId) {
        log.info("黑名单数据-黑名单类型详情，请求入参：{}", dataId);
        return NewResponseUtil.makeSuccess(dataService.getTypeDetail(dataId));
    }

    @ApiOperation(value = "黑名单数据-黑名单数据详情")
    @PostMapping(path = "/data/get")
    public Response<BlacklistDataUserDetailVo> dataDataGet(@NotNull(message = "黑名单数据id不能为空")
        @Min(value = 1, message = "黑名单数据id不能小于1") @ApiParam("黑名单数据id") Long dataId) {
        log.info("黑名单数据-黑名单数据详情，请求入参：{}", dataId);
        return NewResponseUtil.makeSuccess(dataService.getDataDetail(dataId));
    }

    @ApiOperation(value = "黑名单数据-编辑黑名单类型")
    @PostMapping(path = "/modify/type")
    public Response dataModifyType(@Valid  @RequestBody() BlacklistDataTypeEditVo dataTypeEditVo) {
        log.info("黑名单数据-编辑黑名单类型，请求入参：{}", dataTypeEditVo);
        long adminUserId = ContextUtil.getAdminLongUserId();
        dataService.modifyDataType(dataTypeEditVo, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "黑名单数据-编辑用户信息", notes = "只需要传新增的变量即可，因为不支持修改")
    @PostMapping(path = "/modify/data")
    public Response dataModifyData(@Valid BlacklistDataUserEditVo dataTypeEditVo) {
        log.info("黑名单数据-编辑用户信息，请求入参：{}", dataTypeEditVo);
        if (StringUtils.isAllBlank(dataTypeEditVo.getUserIdAlias()+"", dataTypeEditVo.getMobile(),
                dataTypeEditVo.getIdCard(), dataTypeEditVo.getBornCard(), dataTypeEditVo.getUserName())) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "用户信息至少填一项", null);
        }

        long adminUserId = ContextUtil.getAdminLongUserId();
        dataService.modifyDataInfo(dataTypeEditVo, adminUserId);

        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "黑名单数据-新增黑名单")
    @PostMapping(path = "/add")
    public Response dataAdd(@Valid @RequestBody() BlacklistDataAddVo dataAddVo) {
        log.info("黑名单数据-新增黑名单，请求入参：{}", dataAddVo);
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            dataService.addData(dataAddVo, "新增黑名单", adminUserId);
        } catch (IllegalArgumentException e) {
            return NewResponseUtil.makeFail(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "黑名单数据-日志列表")
    @PostMapping(path = "/log/list")
    public Response<List<BlacklistDataLogVo>> dataLog(@NotNull(message = "黑名单数据id不能为空")
        @Min(value = 1, message = "黑名单数据id不能小于1") @ApiParam("黑名单数据id") Long dataId) {
        log.info("黑名单数据-日志列表，请求入参：{}", dataId);
        return NewResponseUtil.makeSuccess(dataService.queryDatLogByDataId(dataId));
    }

    @ApiOperation(value = "黑名单数据-根据userId查询绑定手机号")
    @PostMapping(path = "/query/mobile")
    public Response<String> getMobileByUserId(@NotNull(message = "userId不能为空")
                                              @Min(value = 1, message = "userId不能小于1")Long userIdAlias) {
        log.info("黑名单数据-根据userId查询绑定手机号，请求入参：{}", userIdAlias);
        UserInfoModel userInfoModel = dataService.getUserInfoByUserId(userIdAlias);
        return NewResponseUtil.makeSuccess(userInfoModel == null ? "" : shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
    }


    @ApiOperation(value = "黑名单数据-根据mobile查询用户id")
    @PostMapping(path = "/query/user-id")
    public Response<Long> getUserIdByMobile(@NotBlank(message = "mobile不能为空") @ApiParam("黑名单数据id") String mobile) {
        log.info("黑名单数据-根据mobile查询用户id，请求入参：{}", mobile);
        Long userId = dataService.getUserIdByMobile(mobile);
        return NewResponseUtil.makeSuccess(userId == 0 ? null : userId);
    }

    @ApiOperation(value = "黑名单-预审自动添加黑名单-查询黑名单信息")
    @PostMapping(path = "/auto-add/pre-trial-adoption/get-detail")
    public Response<BlacklistDataAutoAggVo> autoAddGetDetail(@ApiParam("案例id") @NotNull @Min(value = 1) Integer caseId,
                                                             @ApiParam("黑名单Id") @RequestParam(value = "blackListTypeId", required = false, defaultValue = "0") long blackListTypeId) {
        log.info("黑名单-预审自动添加黑名单-查询黑名单信息，请求入参：{}", caseId);
        BlacklistDataAutoAggVo dataAutoAggVo;
        try {
            dataAutoAggVo = dataService.autoAddPreTrialGetDetail(caseId, blackListTypeId);
            log.info("黑名单-预审自动添加黑名单-查询黑名单信息，resp：{}", dataAutoAggVo);
        } catch (IllegalArgumentException e) {
            return NewResponseUtil.makeFail(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        Optional.ofNullable(dataAutoAggVo)
                .filter(r -> CollectionUtils.isNotEmpty(r.getBlacklistDataAutoVos()))
                .ifPresent(r -> r.getBlacklistDataAutoVos().stream()
                        .filter(item -> StringUtils.isNotBlank(item.getMobile()))
                        .forEach(item -> {
                            item.setMobileMask(maskUtil.buildByDecryptPhone(item.getMobile()));
                            item.setMobile(null);
                        })
                );
        return NewResponseUtil.makeSuccess(dataAutoAggVo);
    }

    @ApiOperation(value = "黑名单-预审自动添加黑名单-添加黑名单")
    @PostMapping(path = "/auto-add/pre-trial-adoption/submit")
    public Response<Void> autoAddSubmit(@Valid @RequestBody() BlacklistDataAutoAddVo dataAutoAddVo) {
        log.info("黑名单-预审自动添加黑名单-添加黑名单，请求入参：{}", dataAutoAddVo);
        try {
            long adminUserId = ContextUtil.getAdminLongUserId();
            dataService.saveAutoAddPreTrial(dataAutoAddVo, adminUserId);
        } catch (IllegalArgumentException e) {
            log.info("autoAddSubmit IllegalArgumentException error", e);
            return NewResponseUtil.makeFail(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.info("autoAddSubmit error", e);
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "黑名单-删除用户")
    @PostMapping(path = "delete")
    public Response<Void> delete(@ApiParam("主键id") @NotNull @Min(value = 1) Integer id,
                                 @ApiParam("原因") @NotBlank String reason) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        dataService.deleteBlackListData(id, reason, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

}
