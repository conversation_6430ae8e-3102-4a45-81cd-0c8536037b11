package com.shuidihuzhu.cf.risk.admin.dao.support.typehanders;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 数据库字段加密解密
 * <AUTHOR>
 */
@MappedTypes(Void.class)
@MappedJdbcTypes(JdbcType.UNDEFINED)
public class ColumnCipherTypeHandler extends AbsEncryptOrDecryptBaseTypeHandler<String> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        if (StringUtils.isEmpty(parameter)){
            ps.setString(i, "");
        }else {
            String encryptMobile = getOldShuidiCipherBean().aesEncrypt(parameter);
            ps.setString(i, encryptMobile);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String result = rs.getString(columnName);
        if (StringUtils.isEmpty(result)){
            return result;
        }
        return getShuidiCipherBean().decrypt(result);
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String result = rs.getString(columnIndex);
        if (StringUtils.isEmpty(result)){
            return result;
        }
        return getShuidiCipherBean().decrypt(result);
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String result = cs.getString(columnIndex);
        if (StringUtils.isEmpty(result)){
            return result;
        }
        return getShuidiCipherBean().decrypt(result);
    }


}
