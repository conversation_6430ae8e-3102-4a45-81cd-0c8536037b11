package com.shuidihuzhu.cf.risk.admin.runner;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.google.common.collect.Sets;
import com.shuidihuzhu.infra.starter.elasticjob.core.AbstractSimpleJob;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * Created by sven on 18/7/24.
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractSimpleJobEnhancer extends AbstractSimpleJob {

    private static Set<String> runningJobs = Sets.newConcurrentHashSet();

    /**
     * 获取哪些job正在run
     * @return
     */
    public static Set<String> getRunningJobs(){
        return runningJobs;
    }

    @Override
    public void doExecute(ShardingContext shardingContext) {
        try {
            runningJobs.add(shardingContext.getJobName());
            doRealExecute(shardingContext);
        }catch (Throwable e){
            log.error("exception in {}", shardingContext.getJobName(), e);
        } finally {
            runningJobs.remove(shardingContext.getJobName());
        }
    }

    /**
     * 真正执行的逻辑
     * @param shardingContext
     */
    public abstract void doRealExecute(ShardingContext shardingContext);
}
