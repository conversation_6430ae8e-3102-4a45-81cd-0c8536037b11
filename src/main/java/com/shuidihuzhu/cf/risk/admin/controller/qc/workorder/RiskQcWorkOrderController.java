package com.shuidihuzhu.cf.risk.admin.controller.qc.workorder;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.data.platform.util.ExportUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCaseVisitConfigVo;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskAdminErrorCode;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderParam;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderRecord;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderType;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcWorkOrderExcelVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcWorkOrderWx1v1ExcelVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.QcWorkOrderWx1v1RepeatExcelVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcWorkOrderVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.excel.ExcelExportDataVO;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcWorkOrderService;
import com.shuidihuzhu.cf.risk.admin.service.qc.export.QcExportService;
import com.shuidihuzhu.cf.risk.model.enums.MaterialRiskLabelEnum;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderRecordClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.model.QcHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderRecordVO;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.shardingjdbc.core.constant.OrderType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.shuidihuzhu.cf.risk.admin.constant.AsyncPoolConstants.HANDLE_EXCEL_POOL;

/**
 * @author: lixiaoshuang
 * @create: 2020-06-17 15:08
 **/
@RestController
@Slf4j
@Api("质检工单")
@RequestMapping(path = "/api/cf-risk-admin/qc/work-order", method = RequestMethod.POST)
public class RiskQcWorkOrderController {

    @Autowired
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private RiskQcWorkOrderService riskQcWorkOrderService;
    @Autowired
    private CfWorkOrderRecordClient cfWorkOrderRecordClient;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;
    @Autowired
    private CfWorkOrderStaffClient cfWorkOrderStaffClient;
    @Autowired
    private QcExportService qcExportService;
    @Resource(name = HANDLE_EXCEL_POOL)
    private Executor commonExecutor;

    @Autowired
    private ExportUtil exportUtil;


    @RequestMapping(path = "transfer")
    @ApiOperation("手动分配")
    public Response workOrderTransfer(@ApiParam("工单id") @RequestParam("workOrderId") long workOrderId,
                                      @ApiParam("工单类型") @RequestParam("orderType") int orderType,
                                      @ApiParam("分配人的id") @RequestParam("targetUserId") int targetUserId) {
        log.info("CfQcWorkOrderController.workOrderTransfer workOrderId:{},orderType:{},targetUserId:{}",
                workOrderId, orderType, targetUserId);
        if (workOrderId <= 0 || orderType <= 0 || targetUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (!WorkOrderType.QC_WORK_ORDER_LIST.contains(orderType)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        return cfQcWorkOrderClient.workOrderTransfer(workOrderId, orderType, Math.toIntExact(adminUserId), targetUserId);
    }

    @RequestMapping(path = "transfer-my-self")
    @ApiOperation("分配给自己")
    public Response workOrderTransferMySelf(@ApiParam("工单id") @RequestParam("workOrderId") long workOrderId,
                                      @ApiParam("工单类型") @RequestParam("orderType") int orderType) {
        long targetUserId = ContextUtil.getAdminLongUserId();
        log.info("CfQcWorkOrderController.workOrderTransferMySelf workOrderId:{},orderType:{},targetUserId:{}",
                workOrderId, orderType, targetUserId);
        if (workOrderId <= 0 || orderType <= 0 || targetUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (!WorkOrderType.QC_WORK_ORDER_LIST.contains(orderType)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return cfQcWorkOrderClient.workOrderTransferV2(workOrderId, orderType, Math.toIntExact(ContextUtil.getAdminLongUserId()),
                targetUserId, false);
    }

    @RequestMapping(path = "release")
    @ApiOperation("释放")
    public Response workOrderRelease(@ApiParam("工单id") @RequestParam("workOrderId") long workOrderId) {
        log.info("CfQcWorkOrderController.workOrderRelease workOrderId:{}", workOrderId);
        if (workOrderId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<WorkOrderVO> workOrderVoResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (workOrderVoResponse.ok() && Objects.nonNull(workOrderVoResponse.getData())) {
            WorkOrderVO workOrderVO = workOrderVoResponse.getData();
            if (workOrderVO.getHandleResult() != HandleResultEnum.doing.getType()
                    && workOrderVO.getHandleResult() != HandleResultEnum.later_doing.getType()) {
                return NewResponseUtil.makeError(RiskAdminErrorCode.QC_WORK_ORDER_STATUS);
            }
        }
        long userId = ContextUtil.getAdminLongUserId();
        if (CollectionUtils.isEmpty(riskQcWorkOrderService.filterInfo(List.of(workOrderId)))){
            return NewResponseUtil.makeError(RiskAdminErrorCode.QC_AGAIN_WORK_ORDER_ERROR);
        }
        return cfQcWorkOrderClient.workOrderRelease(workOrderId, userId);
    }

    @RequestMapping(path = "my-order-list")
    @ApiOperation("工单工作台列表")
    public Response<PageResult<RiskQcWorkOrderVo>> myQcWorkOrderList(@RequestParam("param") String param) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        WorkOrderListParam qcParam = JSON.parseObject(param, WorkOrderListParam.class);
        if (Objects.isNull(qcParam) || !WorkOrderType.QC_WORK_ORDER_LIST.contains(qcParam.getOrderType())) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        qcParam.setUserId(adminUserId);
        PageResult<RiskQcWorkOrderVo> pageResult = riskQcWorkOrderService.myQcWorkOrderList(qcParam);
        return NewResponseUtil.makeSuccess(pageResult);
    }

    @RequestMapping(path = "my-order-list-by-search")
    @ApiOperation("工单工作台列表搜索")
    public Response<PageResult<RiskQcWorkOrderVo>> myQcWorkOrderListBySearch(@RequestParam("param") String param,
                                                                             @RequestParam(defaultValue = "") String mobile,
                                                                             @RequestParam(defaultValue = "") String qcByName,
                                                                             @RequestParam int pageNum) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        if (adminUserId <= 0) {
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        WorkOrderListParam qcParam = JSON.parseObject(param, WorkOrderListParam.class);
        if (Objects.isNull(qcParam)) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (!WorkOrderType.QC_WORK_ORDER_LIST.contains(qcParam.getOrderType())) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        qcParam.setUserId(ContextUtil.getAdminLongUserId());
        PageResult<RiskQcWorkOrderVo> pageResult =
                riskQcWorkOrderService.myQcWorkOrderListBySearch(qcParam, mobile, qcByName, pageNum);
        return NewResponseUtil.makeSuccess(pageResult);
    }


    @RequestMapping(path = "order-list")
    @ApiOperation("工单列表")
    public Response<Map<String, Object>> qcWorkOrderList(@RequestBody QcWorkOrderParam qcWorkOrderParam) {
        if (Objects.isNull(qcWorkOrderParam)) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, Object> pageResult = riskQcWorkOrderService.qcWorkOrderList(qcWorkOrderParam);
        return NewResponseUtil.makeSuccess(pageResult);
    }

    @RequestMapping(path = "operator-record")
    @ApiOperation("工单操作记录")
    public Response<List<QcWorkOrderRecord>> qcWorkOrderList(@ApiParam("工单id") @RequestParam("workOrderId") long workOrderId) {
        if (workOrderId <= 0) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<List<WorkOrderRecordVO>> listResponse = cfWorkOrderRecordClient.listByWorkOrderId(workOrderId);
        if (listResponse.notOk() || CollectionUtils.isEmpty(listResponse.getData())) {
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }
        List<WorkOrderRecordVO> workOrderRecordVOS = listResponse.getData();
        List<QcWorkOrderRecord> qcWorkOrderRecords = workOrderRecordVOS.stream().map(workOrderRecordVO -> {
            QcWorkOrderRecord qcWorkOrderRecord = new QcWorkOrderRecord();
            qcWorkOrderRecord.setOperatorName(workOrderRecordVO.getOperatorName());
            qcWorkOrderRecord.setOperateMode(workOrderRecordVO.getOperateMode());
            qcWorkOrderRecord.setOperateDesc(workOrderRecordVO.getOperateDesc());
            qcWorkOrderRecord.setCreateTime(workOrderRecordVO.getCreateTime());
            qcWorkOrderRecord.setComment(workOrderRecordVO.getComment());
            return qcWorkOrderRecord;
        }).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(qcWorkOrderRecords);
    }

    @RequestMapping(path = "get-qc-type")
    @ApiOperation("获取质检类型")
    @RequiresPermission("qc:get-qc-type")
    public Response<Map<Integer, String>> getQcType() {
        Map<Integer, String> map = Maps.newHashMap();
        for (QcTypeEnum value : QcTypeEnum.values()) {
            map.put(value.getCode(), value.getDesc());
        }
        return NewResponseUtil.makeSuccess(map);
    }

    @RequiresPermission("cf-qc:order-export")
    @RequestMapping(path = "download-excel", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation("excel下载")
    public Response<Void> downloadExcel(String startTime, String endTime, int orderType, HttpServletResponse response) {
        long adminUserId = ContextUtil.getAdminLongUserId();
        ExcelExportDataVO data = qcExportService.getList(startTime, endTime, orderType,adminUserId);
        String fileName = "质检工单" + startTime + "-" + endTime;
        final RpcResult<Void> resp = exportUtil.exportToXlsx(adminUserId, data.getList(), data.getClazz(), fileName);
        return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), resp.getData());
    }

    @RequiresPermission("cf-qc:order-export")
    @RequestMapping(path = "order-list/download-excel", method = {RequestMethod.POST, RequestMethod.GET})
    public Response<String> downloadOrderList(@RequestParam String qcWorkOrderParam, HttpServletResponse response) {
        QcWorkOrderParam workOrderParam = null;
        try {
            workOrderParam = JSON.parseObject(qcWorkOrderParam, QcWorkOrderParam.class);
        } catch (Exception e) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        QcWorkOrderParam finalWorkOrderParam = workOrderParam;

        CompletableFuture.runAsync(() -> {
            qcExportService.getDetailListV2(finalWorkOrderParam, adminUserId);
        }, commonExecutor);

        return NewResponseUtil.makeSuccess("请稍等3~5分钟，表格会发送到你的水滴应用助手");
    }

    @RequiresPermission("cf-qc:order-export")
    @RequestMapping(path = "download-wx1v1-excel", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation("微信1v1质检数据下载")
    public Response<Void> downloadWx1v1Excel(@RequestParam String qcWorkOrderParam, HttpServletResponse response) {
        log.info("downloadWx1v1Excel qcWorkOrderParam:{}", qcWorkOrderParam);
        QcWorkOrderParam workOrderParam;
        try {
            workOrderParam = JSON.parseObject(qcWorkOrderParam, QcWorkOrderParam.class);
        } catch (Exception e) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (Objects.isNull(workOrderParam)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (workOrderParam.getQcType() != QcTypeEnum.WX_1V1.getCode()
                && (workOrderParam.getOrderType() != WorkOrderType.qc_wx_1v1.getType()
                || workOrderParam.getOrderType() != WorkOrderType.qc_wx_1v1_repeat.getType())) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        workOrderParam.setPageNum(1);
        workOrderParam.setPageSize(2000);

        long adminUserId = ContextUtil.getAdminLongUserId();
        List<?> excelVos = qcExportService.getWx1v1ExcelVos(workOrderParam,adminUserId);
        Class<?> clzss = null;
        String name = "";
        if (workOrderParam.getOrderType() == WorkOrderType.qc_wx_1v1.getType()) {
            clzss = QcWorkOrderWx1v1ExcelVo.class;
            name = "微信1v1质检数据";
        } else if (workOrderParam.getOrderType() == WorkOrderType.qc_wx_1v1_repeat.getType()) {
            clzss = QcWorkOrderWx1v1RepeatExcelVo.class;
            name = "微信1v1复检数据";
        }
        final RpcResult<Void> resp = exportUtil.exportToXlsx(adminUserId, excelVos, clzss, name);
        return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), resp.getData());
    }

    @RequestMapping(path = "user-classify", method = RequestMethod.POST)
    @ApiOperation("质检工作台获取数量")
    public Response<List<QcWorkOrderType>> userClassify() {
        long userId = ContextUtil.getAdminLongUserId();
        if (userId <= 0L) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<Map<Integer, List<Integer>>> response = cfWorkOrderClient.userClassify(userId);
        Map<Integer, List<Integer>> map = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Maps.newHashMap());
        if (MapUtils.isEmpty(map)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_PCODE_PRIVILEGE);
        }

        List<QcWorkOrderType> qcWorkOrderTypes = riskQcWorkOrderService.userClassify(userId, map);
        return NewResponseUtil.makeSuccess(qcWorkOrderTypes);
    }

    @RequestMapping(path = "get-all-count")
    @ApiOperation("质检工作台刷新待分配数量")
    Response<Integer> getAllCount(@RequestParam("orderType") int orderType,
                                  @RequestParam("handleResult") int handleResult) {
        return cfQcWorkOrderClient.getAllCount(orderType, HandleResultEnum.undoing.getType());
    }

    @RequestMapping(path = "transfers")
    @ApiOperation("手动批量分配")
    Response<Integer> qcTransfers(@RequestParam(name = "workOrderIds") String workOrderIds, @RequestParam(name = "orderTypes") String orderTypes, @RequestParam long targetUserId) {
        if (StringUtils.isAnyBlank(workOrderIds, orderTypes) || targetUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        Set<Integer> orderTypeSet = Splitter.on(",").splitToList(orderTypes).stream().map(Integer::parseInt).collect(Collectors.toSet());
        int size = orderTypeSet.size();
        int orderType = orderTypeSet.stream().findFirst().orElse(0);

        if (size > 1) {
            return NewResponseUtil.makeFail("不允许同时分配多个类型的质检工单");
        }

        if (!WorkOrderType.QC_WORK_ORDER_LIST.contains(orderType)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        long adminUserId = ContextUtil.getAdminLongUserId();
        List<Long> ids = Splitter.on(",").splitToList(workOrderIds).stream().map(Long::parseLong).collect(Collectors.toList());
        return cfQcWorkOrderClient.qcTransfers(ids, orderType, adminUserId, targetUserId);
    }


    @RequestMapping(path = "releases")
    @ApiOperation("手动批量回收")
    Response<Integer> qcWorkOrderReleases(@RequestParam(name = "workOrderIds") String workOrderIds,
                                          @RequestParam long operatorId){
        if (StringUtils.isBlank(workOrderIds) || operatorId < 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        log.info("qcWorkOrderReleases workOrderIds:{}, operatorId:{}", workOrderIds, operatorId);
        List<Long> ids = Splitter.on(",").splitToList(workOrderIds).stream().map(Long::parseLong).collect(Collectors.toList());
        ids = riskQcWorkOrderService.filterInfo(ids);
        return cfQcWorkOrderClient.qcWorkOrderReleases(ids, operatorId);
    }


    @RequestMapping(path = "add-material-id")
    public Response<String> addInfo(){
        riskQcWorkOrderService.cleanInfo();
        return NewResponseUtil.makeSuccess("ok");
    }

    @RequestMapping(path = "anew-qc")
    @ApiOperation("重新质检")
    public Response<Integer> anewQc(@RequestParam long workOrderId, @RequestParam String comment){
        if (StringUtils.length(comment) < 5 || StringUtils.length(comment) > 200){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<Integer> response =
        cfQcWorkOrderClient.openAgainQcWorkOrder(workOrderId, ContextUtil.getAdminLongUserId(), comment);
        if (response != null && response.ok()){
            cfQcWorkOrderClient.updateAssignType(Lists.newArrayList(workOrderId), AssignTypeEnum.ASSIGN.getCode());
        }
       return response;
    }


    @RequestMapping(path = "create-qc-work-order")
    @ApiOperation("手动生成工单")
    public Response<Void> createQcWorkOrder(@ApiParam("生成的工单类型") @RequestParam("orderType") int orderType,
                                            @ApiParam("工单id") @RequestParam("workOrderIds") String workOrderIds,
                                            @ApiParam("工单接收人") @RequestParam(value = "assignId", defaultValue = "0") int assignId) {
        log.info("createQcWorkOrder orderType:{}, workOrderIds:{}", orderType, workOrderIds);
        if (orderType != WorkOrderType.qc_wx_1v1_repeat.getType() && orderType != WorkOrderType.qc_common_repeat.getType()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (assignId > 0) {
            Response<StaffStatus> response = cfWorkOrderStaffClient.getStaffStatus(orderType, assignId);
            if (response.notOk()) {
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }
            if (response.ok() && Objects.isNull(response.getData())) {
                return NewResponseUtil.makeFail("接收人目前没有权限");
            }
            StaffStatus staffStatus = response.getData();
            if (staffStatus.getStaffStatus() != StaffStatusEnum.online.getType() && staffStatus.getStaffStatus() != StaffStatusEnum.stop.getType()) {
                return NewResponseUtil.makeFail("接收人不在线，请更换接收人");
            }
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        riskQcWorkOrderService.createQcWorkOrder(orderType, Stream.of(workOrderIds.split(","))
                .map(Long::parseLong).collect(Collectors.toList()), assignId,adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }


    @RequestMapping(path = "update-handle-status")
    @ApiOperation("修改处理状态")
    public Response<Void> updateHandleStatus(@ApiParam("工单id") @RequestParam("workOrderId") long workOrderId,
                                             @ApiParam("处理状态") @RequestParam("handleStatus") int handleStatus,
                                             @ApiParam("备注") @RequestParam(value = "remark", defaultValue = "") String remark,
                                             @ApiParam("描述") @RequestParam(value = "describe", defaultValue = "") String describe) {
        log.info("updateHandleStatus workOrderId:{}, handleStatus:{},remark:{},describe:{}", workOrderId, handleStatus, remark, describe);
        if (workOrderId <= 0 || handleStatus != HandleResultEnum.manual_lock.getType()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (response.ok() && Objects.isNull(response.getData())) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        var workOrderVO = response.getData();

        //复检工单的日志记录到对应的原工单上
        long logWorkOrderId = 0;
        if (workOrderVO.getOrderType() == WorkOrderType.qc_common_repeat.getType()
                || workOrderVO.getOrderType() == WorkOrderType.qc_wx_1v1_repeat.getType()) {
            List<RiskQcMaterialsInfo> riskQcMaterialsInfos = riskQcMaterialsInfoBiz.getMaterials(workOrderVO.getQcId(),
                    QcMaterialsKeyEnum.ORIGINAL_WORK_ORDER.getKey());
            if (CollectionUtils.isNotEmpty(riskQcMaterialsInfos)) {
                logWorkOrderId = Long.parseLong(riskQcMaterialsInfos.get(0).getMaterialsValue());
            }
        }

        QcHandleOrderParam param = new QcHandleOrderParam();
        param.setCaseId(workOrderVO.getCaseId());
        param.setWorkOrderId(workOrderId);
        param.setHandleResult(HandleResultEnum.manual_lock.getType());
        param.setOperComment("手动关闭");
        param.setOrderType(workOrderVO.getOrderType());
        param.setUserId(ContextUtil.getAdminLongUserId());
        cfQcWorkOrderClient.handleQc(param);
        //记录日志
        StringBuilder stringBuilder = new StringBuilder();
        if (workOrderVO.getOrderType() == WorkOrderType.qc_wx_1v1_repeat.getType()){
            stringBuilder.append("复检工单手动关闭 \n备注信息：").append(remark).append("\n");
        }else if (workOrderVO.getOrderType() == WorkOrderType.qc_common_repeat.getType()){
            stringBuilder.append("复检工单手动关闭 \n复检描述：").append(remark).append("\n");
            stringBuilder.append("复检结果描述：").append(describe);
        }
        riskQcLogService.addLog(RiskQcOperationTypeEnum.MANUAL_CLOSE, logWorkOrderId, stringBuilder.toString());
        return NewResponseUtil.makeSuccess(null);
    }


}
