package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskPsProgress;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskPsProgressDao {

    int save(RiskPsProgress riskPsProgress);

    int saveList(List<RiskPsProgress> progressList);

    List<RiskPsProgress> listByPsId(@Param("psId") long psId);


    RiskPsProgress getLastProgress(@Param("psId") long psId);


    List<RiskPsProgress> getByPsId(@Param("psId") long psId);
}
