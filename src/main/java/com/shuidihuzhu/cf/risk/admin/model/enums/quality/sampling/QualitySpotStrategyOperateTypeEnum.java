package com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/6/17 18:38
 */
@AllArgsConstructor
@Getter
public enum QualitySpotStrategyOperateTypeEnum {

    CREATE(1, "新建策略"),
    DISABLE(2, "弃用"),
    ENABLE(3, "启用"),
    EDIT_ENABLE(4, "编辑启用时间"),
    EDIT_DISABLE(5, "编辑适用范围"),
    UPDATE(6, "更新策略"),
    ;

    public static QualitySpotStrategyOperateTypeEnum fromCode(int code){
        for (QualitySpotStrategyOperateTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }

        return null;
    }

    private int code;
    private String desc;
}
