package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/6/16
 */
@Data
public class RiskQcResultVo {
    private String voiceRemark;
    private String userWriteRemark;
    private List<RiskQcStandardVo> qcResultOption;
    private Map<String, RiskMaterialQcStandardVo> materialQcResultOption;
    private long firstResultId;
    private long secondResultId;
    private String suggest;
    private Date auditFinishTime;
    //复检结果是否一致
    private int agree;
    //复检备注
    private String remark;
    //复检描述
    private String describe;
    //不合格原因
    private String unqualifiedReason;
    /**
     * 录音问题信息
     */
    private List<QcRecordingProblemsVo> qcRecordingProblemsVos;
    /**
     * 前置信息备注
     */
    private String preInfoRemark;
    /**
     * 图文信息备注
     */
    private String imgWordRemark;
    /**
     * 增信信息备注
     */
    private String addCreditRemark;
    /**
     * 低保信息备注
     */
    private String lowIncomeRemark;


    public static List<RiskMaterialQcStandardVo> getList(Map<String, RiskMaterialQcStandardVo> materialQcResultOption){
        if (MapUtils.isEmpty(materialQcResultOption)){
            return Lists.newArrayList();
        }
        List<RiskMaterialQcStandardVo> riskMaterialQcStandardVos = Lists.newArrayList();
        materialQcResultOption.forEach((k, v)-> {
            riskMaterialQcStandardVos.add(v);
        });
        return riskMaterialQcStandardVos;
    }

    public void judgeRc1v1(RiskQcResultVo rcResult){
        if (CollectionUtils.isEmpty(this.getQcResultOption())
                && CollectionUtils.isEmpty(rcResult.getQcResultOption())) {
            rcResult.setAgree(9);
            return;
        }
        if (CollectionUtils.isEmpty(this.getQcResultOption())
                || CollectionUtils.isEmpty(rcResult.getQcResultOption())) {
            rcResult.setAgree(10);
            return;
        }
        //过滤掉为空的情况
        List<RiskQcStandardVo> qcResultOption = this.getQcResultOption().stream()
                .filter(r-> CollectionUtils.isNotEmpty(r.getRiskQcStandardSecondVos()))
                .collect(Collectors.toList());
        List<RiskQcStandardVo> rcResultOption = rcResult.getQcResultOption().stream()
                .filter(r-> CollectionUtils.isNotEmpty(r.getRiskQcStandardSecondVos()))
                .collect(Collectors.toList());
        if (qcResultOption.size() != rcResultOption.size()){
            rcResult.setAgree(10);
            return;
        }
        Map<Long, RiskQcStandardVo> idMap = rcResultOption.stream().collect(Collectors.toMap(RiskQcStandardVo::getId, Function.identity(), (oldV, newV) -> newV));
        for (RiskQcStandardVo qc : qcResultOption) {
            RiskQcStandardVo rc = idMap.get(qc.getId());
            if (rc == null){
                rcResult.setAgree(10);
                return;
            }
            List<RiskQcStandardDetailVo> qcSecondVos = qc.getRiskQcStandardSecondVos();
            List<RiskQcStandardDetailVo> rcSecondVos = rc.getRiskQcStandardSecondVos();
            if (CollectionUtils.isEmpty(qcSecondVos)
                    || CollectionUtils.isEmpty(rcSecondVos)
                    || qcSecondVos.size() != rcSecondVos.size()) {
                rcResult.setAgree(10);
                return;
            }
            boolean agree = contrastCheck(qcSecondVos, rcSecondVos);
            if (!agree) {
                rcResult.setAgree(10);
                return;
            }
        }
        rcResult.setAgree(9);
    }

    private boolean contrastCheck(List<RiskQcStandardDetailVo> qcVos, List<RiskQcStandardDetailVo> rcVos) {
        for (int i = 0; i < qcVos.size(); i++) {
            RiskQcStandardDetailVo qc = qcVos.get(i);
            RiskQcStandardDetailVo rc = rcVos.get(i);
            if (qc == null || rc == null) {
                return false;
            }
        }
        return true;
    }

}
