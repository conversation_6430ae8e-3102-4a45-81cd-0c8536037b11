package com.shuidihuzhu.cf.risk.admin.biz.impl.quality.sampling;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQualitySpotStrategyTypeRelBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQualitySpotTypeBiz;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotStrategyTypeRelDao;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType;
import com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/12
 */
@Service
public class RiskQualitySpotStrategyTypeRelBizImpl implements RiskQualitySpotStrategyTypeRelBiz {

    @Resource
    private RiskQualitySpotStrategyTypeRelDao qualitySpotStrategyTypeRelDao;
    @Resource
    private RiskQualitySpotTypeBiz spotTypeBiz;


    @Override
    public RiskQualitySpotTypeRel save(long strategyId, long typeId, String sceneInfo) {
        RiskQualitySpotTypeRel riskQualitySpotTypeRel = new RiskQualitySpotTypeRel();
        riskQualitySpotTypeRel.setStrategyId(strategyId);
        riskQualitySpotTypeRel.setTypeId(typeId);
        riskQualitySpotTypeRel.setSceneInfo(StringUtils.isBlank(sceneInfo) ? getSceneInfo(typeId) : sceneInfo);
        qualitySpotStrategyTypeRelDao.save(riskQualitySpotTypeRel);
        return riskQualitySpotTypeRel;
    }

    @Override
    public List<RiskQualitySpotTypeRel> findByStrategyId(long strategyId) {
        if (strategyId <= 0){
            return null;
        }
        return qualitySpotStrategyTypeRelDao.findByStrategyId(strategyId);
    }

    @Override
    public List<RiskQualitySpotTypeRel> findByStrategyIdList(List<Long> strategyIdList) {
        if (CollectionUtils.isEmpty(strategyIdList)){
            return null;
        }
        return qualitySpotStrategyTypeRelDao.findByStrategyIdList(strategyIdList);
    }

    @Override
    public List<RiskQualitySpotTypeRel> findByTypeId(long typeId) {
        if (typeId <= 0){
            return null;
        }
        return qualitySpotStrategyTypeRelDao.findByTypeId(typeId);
    }

    @Override
    public int deleteByStrategyId(long strategyId) {
        if (strategyId <= 0){
            return 0;
        }
        return qualitySpotStrategyTypeRelDao.deleteByStrategyId(strategyId);
    }

    @Override
    public List<RiskQualitySpotTypeRel> findByTypeIdList(List<Long> typeIdList) {
        if (CollectionUtils.isEmpty(typeIdList)){
            return Lists.newArrayList();
        }
        return qualitySpotStrategyTypeRelDao.findByTypeIdList(typeIdList);
    }

    private String getSceneInfo(long typeId) {
        List<RiskQualitySpotType> riskQualitySpotTypes  = spotTypeBiz.findAllParentById(typeId);
        if (CollectionUtils.isEmpty(riskQualitySpotTypes)){
            return "";
        }
        return riskQualitySpotTypes.stream().map(RiskQualitySpotType::getTypeName).collect(Collectors.joining("-"));
    }
}
