package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class RiskPsProgress implements PageHasId {
    private long id;
    private long psId;//舆情id
    private String operator; //操作人
    private int action; //事件动作
    private String fermentationCondition;//舆情信息
    private Timestamp bizTime;//业务时间
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public RiskPsProgress(){

    }

    public RiskPsProgress(long psId, String operator, int action, String fermentationCondition, Timestamp bizTime) {
        this.psId = psId;
        this.operator = operator;
        this.action = action;
        this.fermentationCondition = fermentationCondition;
        this.bizTime = bizTime;
    }

}
