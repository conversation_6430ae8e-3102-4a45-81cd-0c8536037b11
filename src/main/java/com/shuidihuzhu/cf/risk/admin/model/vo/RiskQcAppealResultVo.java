package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealResultModel;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealInfoModel;
import lombok.Data;

import java.util.Date;

/**
 * @Auther: subing
 * @Date: 2020/11/16
 */
@Data
public class RiskQcAppealResultVo {
    private int appealResult;
    private RiskQcAppealInfoModel appealInfo;
    private long workOrderId;
    /**
     * 第一申诉工单id
     */
    private long firstAppealWorkOrderId;
    /**
     * 工单提交时间
     */
    private Date submitTime;
    /**
     * 工单处理时间
     */
    private Date handleTime;
    /**
     * 操作人
     */
    private String operationName;

    public static RiskQcAppealResultVo buildVo(RiskQcAppealResultModel riskQcAppealResultModel){
        if (riskQcAppealResultModel == null){
            return null;
        }
        RiskQcAppealResultVo riskQcAppealResultVo = new RiskQcAppealResultVo();
        riskQcAppealResultVo.setAppealResult(riskQcAppealResultModel.getAppealResult());
        RiskQcAppealInfoModel riskQcAppealInfoModel =
        JSON.parseObject(riskQcAppealResultModel.getAppealInfo(), new TypeReference<RiskQcAppealInfoModel>(){});
        riskQcAppealResultVo.setAppealInfo(riskQcAppealInfoModel);
        riskQcAppealResultVo.setWorkOrderId(riskQcAppealResultModel.getWorkOrderId());
        return riskQcAppealResultVo;
    }

}

