package com.shuidihuzhu.cf.risk.admin.dao.hit;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskStrategyHitLogDao {
    int insertSelective(RiskStrategyHitLog record);

    RiskStrategyHitLog selectByPrimaryKey(Long id);

    List<RiskStrategyHitLog> listByRecordId(Long recordId);
}