package com.shuidihuzhu.cf.risk.admin.dao.blacklist;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataTypeRef;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskBlacklistDataTypeRefDao {
    int insertSelective(RiskBlacklistDataTypeRef record);

    RiskBlacklistDataTypeRef selectByPrimaryKey(Long id);

    List<RiskBlacklistDataTypeRef> listValidByDataIds(Collection<Long> dataIds);

    List<RiskBlacklistDataTypeRef> listByTypeIdLimit(@Param("typeId") Long typeId, @Param("previousId") Long previousId, @Param("limit") int limit);

    List<RiskBlacklistDataTypeRef> listByDataIds(@Param("dataIds") List<Long> dataIds);

    int saveBatch(List<RiskBlacklistDataTypeRef> typeRefs);

    int deleteByIds(List<Long> ids);

    int updateActionIdsByIds(@Param("ids") Collection<Long> ids, @Param("actionIds") String actionIds);
}