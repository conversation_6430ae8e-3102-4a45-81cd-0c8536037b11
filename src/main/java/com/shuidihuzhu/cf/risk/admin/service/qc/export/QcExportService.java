package com.shuidihuzhu.cf.risk.admin.service.qc.export;

import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderParam;
import com.shuidihuzhu.cf.risk.admin.model.vo.excel.ExcelExportDataVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface QcExportService {

    void getDetailListV2(QcWorkOrderParam workOrderParam,long adminUserId);

    List<?> getWx1v1ExcelVos(QcWorkOrderParam workOrderParam,long adminUserId);

    ExcelExportDataVO getList(String startTime, String endTime, int orderType,long adminUserId);
}
