package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "抽检量级更新实体")
public class RiskQualitySpotLevelConfUpdateVo {

  /*  @ApiModelProperty("质检对象")
    @NotNull(message = "质检对象不能为空")
    @Min(value = 1, message = "质检对象不能小于1")
    private Integer firstScene;*/

    @ApiModelProperty("二级工单类型")
    @NotNull(message = "二级工单类型不能为空")
    @Min(value = 1, message = "二级工单类型不能小于1")
    private Long secondScene;

    @NotNull(message = "抽检量级不能为空")
    @Min(value = 0, message = "抽检量级不能小于0")
    @ApiModelProperty(value = "抽检量级，条/天", required = true)
    private Integer samplingLevel;

    @ApiModelProperty(value = "修改原因备注", required = true)
    @NotBlank(message = "原因备注不能为空")
    @Length(min = 6, message = "备注内容长度不能小于6")
    private String modifyReason;

}