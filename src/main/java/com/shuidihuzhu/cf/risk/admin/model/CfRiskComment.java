package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 评论总表
 *
 * id	BIGINT	主键
 * case_id	VARCHAR	案例id
 * biz_id	BIGINT	业务对应的id
 * user_id  BIGINT	用户id
 * content	VARCHAR（200）	用户评论内容
 * sensitive_word	VARCHAR（30）	命中的敏感词列表
 * sensitive_status	boolean	是否包含敏感词:
 * 0表示无敏感词
 * 1表示有敏感词
 * type	TINYINT	1：评议评论
 */
@Data
public class CfRiskComment implements PageHasId {
    private long id;
    private long caseId;
    private long bizId;
    private long userId;
    private String content;
    private String sensitiveWord;
    private boolean sensitiveStatus;
    private int type;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;
    //vo字段
    private int praiseCount;
}
