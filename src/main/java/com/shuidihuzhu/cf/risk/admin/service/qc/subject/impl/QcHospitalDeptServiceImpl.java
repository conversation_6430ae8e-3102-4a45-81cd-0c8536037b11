package com.shuidihuzhu.cf.risk.admin.service.qc.subject.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcLogBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.delegate.ClewDelegate;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.constant.WonAction;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.param.DeptUpdateParam;
import com.shuidihuzhu.cf.risk.admin.model.qc.QcWorkOrderParam;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcLog;
import com.shuidihuzhu.cf.risk.admin.model.qc.subject.QcHospitalDeptDetailVO;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcDetailService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcWorkOrderService;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.qc.subject.QcHospitalDeptService;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeItemModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentEditor;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentNameChangeMqModel;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QcHospitalDeptServiceImpl implements QcHospitalDeptService {

    @Autowired
    private RiskQcDetailService riskQcDetailService;

    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;

    @Autowired
    private WonRecordClient wonRecordClient;

    @Autowired
    private ClewDelegate clewDelegate;

    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;

    @Autowired
    private SeaAccountService seaAccountService;

    @Autowired
    private RiskQcLogBiz riskQcLogBiz;

    @Autowired
    private RiskQcLogService riskQcLogService;

    @Autowired
    private RiskQcWorkOrderService riskQcWorkOrderService;

    @Autowired
    private CfSearchClient cfSearchClient;

    private static final String OTHER_DEPT = "其他科室";

    @Override
    public Response<QcHospitalDeptDetailVO> getDetail(long workOrderId) {
        QcHospitalDeptDetailVO view = new QcHospitalDeptDetailVO();
        // 不查快照，直接调接口
        final BasicWorkOrder order = Von.read().getOrderBasicInfoById(workOrderId).getData();
        view.setOrderInfo(order);
//        OperationResult<WonRecord> lastRecordResp = wonRecordClient.getLastByBizId(workOrderId,
//                WonAction.HOSPITAL_DEPT_SNAPSHOT);
//        if (lastRecordResp == null || lastRecordResp.isFail()) {
//            return NewResponseUtil.makeFail("请求失败");
//        }
//        WonRecord lastRecord = lastRecordResp.getData();
//        if (lastRecord != null) {
//            DepartmentChangeDetailModel snapshot = lastRecord.getExt("snapshot", DepartmentChangeDetailModel.class);
//            return transModelToShow(view, snapshot);
//        }
//        long qcId = riskQcDetailService.getQcId(workOrderId);
        DepartmentChangeDetailModel data = selectDepartmentChangeDetailByWorkOrderId(workOrderId);
        if(data == null){
            return NewResponseUtil.makeFail("请求失败");
        }
        view.setInfo(data);
        return NewResponseUtil.makeSuccess(view);
    }

    private DepartmentChangeDetailModel selectDepartmentChangeDetailByWorkOrderId(long workOrderId) {
        int deptId = getBuildingId(workOrderId);
        Response<DepartmentChangeDetailModel> detailResp = clewDelegate.getHospitalDeptInfo(deptId);

        if (detailResp.notOk()) {
            return null;
        }
        return detailResp.getData();
    }

    private int getBuildingId(long workOrderId) {
        RiskQcSearchIndex searchIndex = riskQcSearchIndexBiz.getByWorkOrderId(workOrderId);
        return (int) searchIndex.getHospitalDeptId();
    }

    @Override
    public void saveSnapshot(Long workOrderId, DepartmentChangeDetailModel clewInfo) {
        wonRecordClient.create()
                .buildBizId(String.valueOf(workOrderId))
                .buildActionId(WonAction.HOSPITAL_DEPT_SNAPSHOT)
                .buildExtValue("snapshot", clewInfo)
                .save();
    }

    @Override
    public Response<Boolean> updateBuildingInfo(long workOrderId, String buildingName, long userId) {

        final int buildingId = getBuildingId(workOrderId);

        // 更新楼宇信息
        // 修改前数据获取
        final DepartmentChangeDetailModel data = clewDelegate.getHospitalDeptInfoData(buildingId);
        if(data == null){
            log.info("workOrderId:{} 未查询到医院信息",workOrderId);
            return NewResponseUtil.makeFail("请求失败");
        }

        String buildingNameBefore = data.getChangedBuildingDetail().getBuildName();


        if(StringUtils.equals(buildingNameBefore, buildingName)){
            return NewResponseUtil.makeSuccess(false);
        }

        String updateBuildingName = "操作类型：修改楼宇信息\n" + "操作内容："+ "楼名：由" + buildingNameBefore + "修改为" + buildingName;

        String operator = "";
        if (userId > 0) {
            operator = seaAccountService.getOrganization(userId) + seaAccountService.getName(userId);
        }

        // 给小鲸鱼传修改后信息
        final Response<Integer> changeResp = clewDelegate.editBuildingName(operator, userId, buildingId, buildingName);
        if (NewResponseUtil.isNotOk(changeResp)) {
            return NewResponseUtil.makeFail(changeResp.getMsg());
        }

        return NewResponseUtil.makeSuccess(riskQcLogBiz.addLog(new RiskQcLog(userId, operator, updateBuildingName,
                RiskQcOperationTypeEnum.UPDATE_BUILDING_INFO.getType(), workOrderId)) > 0);
    }

    @Override
    public Response<Boolean> updateDepartmentInfo(DeptUpdateParam param) {
        final long workOrderId = param.getWorkOrderId();
        final int buildingId = getBuildingId(workOrderId);

        final DepartmentChangeDetailModel preInfo = clewDelegate.getHospitalDeptInfoData(buildingId);
        if (preInfo == null) {
            return NewResponseUtil.makeFail("信息查询失败");
        }
        if (null == preInfo.getDepartmentDetails()) {
            preInfo.setDepartmentDetails(Lists.newArrayList());
        }
        final Map<Integer, String> preDeptIdNameMap = preInfo.getDepartmentDetails().stream()
                .collect(Collectors.toMap(
                        DepartmentChangeDetailModel.BuildingDepartmentDetail::getId,
                        DepartmentChangeDetailModel.BuildingDepartmentDetail::getDepartmentName,
                        (o1, o2) -> o2)
                );

        final List<DepartmentChangeItemModel> changeList = param.getDepartmentDetails();

        // 更新科室信息 增、删、改
        final String name = seaAccountService.getName(param.getOperatorId());

        final Response<Integer> changeResp = clewDelegate.editDeptInfo(name, param.getOperatorId(),
                buildingId, param.getDepartmentDetails());
        if (NewResponseUtil.isNotOk(changeResp)) {
            log.error("修改科室信息失败 resp {}", changeResp);
            return NewResponseUtil.makeFail(changeResp.getMsg());
        }

        StringBuilder content = new StringBuilder(
                "操作类型：修改住院楼科室信息\n" +
                "操作内容：");
        boolean hasChange = false;
        for (DepartmentChangeItemModel change : changeList) {
            final String editType = change.getEditType();
            final String departmentName = change.getDepartmentName();
            if (StringUtils.equals(editType, "remove")) {
                content.append("         " + "删除科室 ").append(departmentName).append("\n");
                hasChange = true;
            }
            if (StringUtils.equals(editType, "add")) {
                content.append("         " + "新增科室 ").append(departmentName).append("\n");
                hasChange = true;
            }
            if (StringUtils.equals(editType, "update")) {
                final String preName = preDeptIdNameMap.get(change.getId());
                content.append("         " + "修改科室 由【").append(preName).append("】修改为【")
                        .append(departmentName).append("】").append("\n");
                hasChange = true;
            }
        }
        if (!hasChange) {
            return NewResponseUtil.makeFail("没有任何改动");
        }
        riskQcLogService.addLog(RiskQcOperationTypeEnum.HOSPITAL_DEPT_MODIFY, workOrderId, content.toString());

        return NewResponseUtil.makeSuccess();
    }

    @Override
    public boolean getDeptClassifySuccess(DepartmentChangeDetailModel clewInfo) {
        final List<DepartmentChangeDetailModel.BuildingDepartmentDetail> departmentDetails = clewInfo.getDepartmentDetails();
        if (CollectionUtils.isEmpty(departmentDetails)) {
            return true;
        }
        for (DepartmentChangeDetailModel.BuildingDepartmentDetail departmentDetail : departmentDetails) {
            if (StringUtils.equals(departmentDetail.getClassifyDepartmentName(), OTHER_DEPT)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 科室信息变更 同步修改所有对应科室工单数据
     * @param payload
     */
    @Override
    public void onDeptChange(DepartmentNameChangeMqModel payload) {
        final int buildingId = payload.getBuildingId();
        final DepartmentChangeDetailModel hospitalDeptInfoData = clewDelegate.getHospitalDeptInfoData(buildingId);
        if (hospitalDeptInfoData == null) {
            log.error("楼宇信息查询失败 科室修改同步 {}", payload);
            return;
        }
        final QcWorkOrderParam param = new QcWorkOrderParam();
        param.setPageNum(1);
        param.setPageSize(200);
        param.setOrderType(WorkOrderType.qc_hospital_dept.getType());
        param.setHandleResult(-1);
        param.setCaseId(buildingId);
        CfWorkOrderV2IndexSearchParam searchParam = riskQcWorkOrderService.buildRiskQcSearchParam(param, null);

        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        log.debug("查询该楼所有工单 {}", searchRpcResult);
        final CfWorkOrderIndexSearchResult result = searchRpcResult.getData();
        if (result == null) {
            return;
        }
        final List<CfWorkOrderModel> models = result.getModels();
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        final List<Long> orderIdList = models.stream().map(CfWorkOrderModel::getWorkOrderId).collect(Collectors.toList());

        final boolean deptClassifySuccess = getDeptClassifySuccess(hospitalDeptInfoData);
        for (Long orderId : orderIdList) {
            Von.extUpdate().saveByList(orderId, Lists.newArrayList(
                    WorkOrderExt.create(orderId, QcConst.OrderExt.deptHospitalName, hospitalDeptInfoData.getHospitalName()),
                    WorkOrderExt.create(orderId, QcConst.OrderExt.deptClassifySuccess, deptClassifySuccess)
                    )
            );
        }
    }

    @Override
    public void refreshData(int page, int size) {
        final long total = refreshDataByPage(page, size);
        final long maxPage = total / size + 1;
        log.info("洗数据 total {}, maxPage {}, size {}, page {}", total, maxPage, size, page);
        for (int i = page + 1; i<= maxPage; i++) {
            refreshDataByPage(i, size);
        }
    }

    private long refreshDataByPage(int page, int size) {
        final QcWorkOrderParam param = new QcWorkOrderParam();
        param.setPageNum(page);
        param.setPageSize(size);
        param.setOrderType(WorkOrderType.qc_hospital_dept.getType());
        param.setHandleResult(-1);
        CfWorkOrderV2IndexSearchParam searchParam = riskQcWorkOrderService.buildRiskQcSearchParam(param, null);

        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        log.debug("查询所有工单 page {}, size {}, {}", page, size, searchRpcResult);
        final CfWorkOrderIndexSearchResult result = searchRpcResult.getData();
        if (result == null) {
            return 0;
        }
        final List<CfWorkOrderModel> models = result.getModels();
        if (CollectionUtils.isEmpty(models)) {
            return result.getTotal();
        }
        final List<Long> orderIdList = models.stream().map(CfWorkOrderModel::getWorkOrderId).collect(Collectors.toList());
        for (Long orderId : orderIdList) {
            final int buildingId = getBuildingId(orderId);
            final DepartmentChangeDetailModel hospitalDeptInfoData = clewDelegate.getHospitalDeptInfoData(buildingId);
            if (hospitalDeptInfoData == null) {
                continue;
            }
            final boolean deptClassifySuccess = getDeptClassifySuccess(hospitalDeptInfoData);
            riskQcSearchIndexBiz.updateOrgByWorkOrderId(orderId, getOrg(hospitalDeptInfoData));
            Von.extUpdate().saveByList(orderId, Lists.newArrayList(
                    WorkOrderExt.create(orderId, QcConst.OrderExt.deptHospitalName, hospitalDeptInfoData.getHospitalName()),
                    WorkOrderExt.create(orderId, QcConst.OrderExt.deptClassifySuccess, deptClassifySuccess)
                    )
            );
            log.info("洗数据 orderId {}, buildingId {}, data {}", orderId, buildingId, hospitalDeptInfoData);
        }
        log.info("洗数据按页 page {}", page);
        return result.getTotal();
    }

    private String getOrg(DepartmentChangeDetailModel clewInfo) {
        final List<DepartmentEditor> departmentEditors = clewInfo.getDepartmentEditors();
        if (CollectionUtils.isEmpty(departmentEditors)) {
            return "";
        }
        final String res = departmentEditors.stream()
                .map(DepartmentEditor::getOrgInfo)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("#"));

        return StringUtils.trimToEmpty(res);
    }

}
