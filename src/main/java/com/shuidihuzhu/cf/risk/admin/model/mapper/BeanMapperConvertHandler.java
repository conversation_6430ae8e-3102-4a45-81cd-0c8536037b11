package com.shuidihuzhu.cf.risk.admin.model.mapper;

import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-11-09 4:52 PM
 **/
@Component
public class BeanMapperConvertHandler {

    @Resource
    private ShuidiCipher shuidiCipher;

    @Named("decrypt")
    public String decrypt(String str) {
        if (StringUtils.isBlank(str)) {
            return StringUtils.EMPTY;
        }
        return Optional.ofNullable(shuidiCipher.decrypt(str)).orElse(StringUtils.EMPTY);
    }
}
