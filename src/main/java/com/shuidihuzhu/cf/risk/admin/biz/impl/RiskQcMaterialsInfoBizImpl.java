package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcMaterialsInfoDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-17 14:47
 **/
@Service
public class RiskQcMaterialsInfoBizImpl implements RiskQcMaterialsInfoBiz {

    @Autowired
    private RiskQcMaterialsInfoDao riskQcMaterialsInfoDao;

    @Override
    public int addMaterials(RiskQcMaterialsInfo riskQcMaterialsInfo) {
        return riskQcMaterialsInfoDao.insertOne(riskQcMaterialsInfo);
    }

    @Override
    public int addMaterials(List<RiskQcMaterialsInfo> riskQcMaterialsInfos) {
        return riskQcMaterialsInfoDao.insertBatch(riskQcMaterialsInfos);
    }

    @Override
    public List<RiskQcMaterialsInfo> getMaterials(long qcId, String materialsKey) {
        return riskQcMaterialsInfoDao.getByQcIdAndMaterialsKey(qcId, materialsKey);
    }

    @Override
    public RiskQcMaterialsInfo getById(long id) {
        return riskQcMaterialsInfoDao.getById(id);
    }

    @Override
    public int updateById(long id, String value) {
        return riskQcMaterialsInfoDao.updateById(id, value);
    }

    @Override
    public int updateSnapshot(long qcId, String materialsKey, String materialsValue) {
        return riskQcMaterialsInfoDao.updateSnapshot(qcId, materialsKey, materialsValue);
    }
}
