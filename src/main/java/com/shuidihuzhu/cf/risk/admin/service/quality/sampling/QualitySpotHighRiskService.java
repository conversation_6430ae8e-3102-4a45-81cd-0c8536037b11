package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.QualitySpotLevelConfBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling.RiskQualitySpotLevelConfVo;
import com.shuidihuzhu.cf.risk.client.highrisk.HighRiskClient;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotHighRiskDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotJobConfDto;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotRuleInfo;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotExecuteModelEnum;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.cf.risk.model.highrisk.HighRiskRecordDto;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeConst;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.AssignTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QualitySpotHighRiskService {

    @Resource
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Resource
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;
    @Resource
    private QualitySpotStrategyService qualitySpotStrategyService;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private QualitySpotLevelConfBiz qualitySpotLevelConfBiz;
    @Resource
    private HighRiskClient highRiskClient;

    @Async
    public void spotHighRisk(Long qcWorkOrderId, RiskQcBaseInfo riskQcBaseInfo, WorkOrderVO workOrderVO,int callStatus) {
        //获取工单规则
        List<QualitySpotJobConfDto> qualitySpotJobConfDtoList = qualitySpotStrategyService.listJobScopeConf(QualitySpotSceneEnum.HIGH_RISK_QC.getCode());
        if (CollectionUtils.isEmpty(qualitySpotJobConfDtoList)) {
            return;
        }
        log.info("spotMaterial qcWorkOrderId:{} riskQcBaseInfo:{} workOrderVO:{}", qcWorkOrderId, JSON.toJSONString(riskQcBaseInfo),
                JSON.toJSONString(workOrderVO));
        QualitySpotJobConfDto qualitySpotJobConfDto = qualitySpotJobConfDtoList.get(0);
        //检查当天的质检数量是否已经到达上限
        if (checkStrategyCount(qualitySpotJobConfDto)) {
            return;
        }
        QualitySpotHighRiskDto qualitySpotHighRiskDto = buildQualitySpotHighRiskDto(qcWorkOrderId, riskQcBaseInfo, workOrderVO,callStatus);

        List<QualitySpotRuleInfo> ruleInfoList = qualitySpotJobConfDto.getRuleInfoList();
        if (CollectionUtils.isEmpty(ruleInfoList)) {
            return;
        }
        ruleInfoList = ruleInfoList.stream().sorted(Comparator.comparing(QualitySpotRuleInfo::getPriority)).collect(Collectors.toList());

        for (QualitySpotRuleInfo ruleInfo : ruleInfoList) {
            if (checkRuleSampling(ruleInfo, qualitySpotJobConfDto)){
                continue;
            }
            qualitySpotHighRiskDto =
                    qualitySpotStrategyService.doQualitySpotHighRiskDtoStrategy(ruleInfo.getRuleId(), qualitySpotHighRiskDto);
            //分配工单
            if (!qualitySpotHighRiskDto.isHit()) {
                continue;
            }
            //记录命中规则名称,命中规则id
            riskQcSearchIndexBiz.updateRuleNameByWorkOrderId(qualitySpotHighRiskDto.getWorkOrderId(), ruleInfo.getRuleName());
            cfQcWorkOrderClient.addExtValue(qualitySpotHighRiskDto.getWorkOrderId(),
                    OrderExtName.ruleId.getName(), Long.toString(ruleInfo.getRuleId()));
            //更新工单分配类型
            cfQcWorkOrderClient.updateAssignType(Lists.newArrayList(qualitySpotHighRiskDto.getWorkOrderId()), AssignTypeEnum.MUST_ASSIGN.getCode());
            //统计工单信息
            updateCountRedis(qualitySpotHighRiskDto, ruleInfo);
            break;
        }

    }

    private void updateCountRedis(QualitySpotHighRiskDto qualitySpotHighRiskDto, QualitySpotRuleInfo ruleInfo) {
        long scene = QualitySpotSceneEnum.HIGH_RISK_QC.getCode();
        // 被质检人当天必须分配工单数量
        redissonHandler.incrAndSetTimeWhenNotExists(getCheckerDayCountKey(scene, qualitySpotHighRiskDto.getUniqueCode()),
                RedissonHandler.ONE_DAY);
        // 最近1h内必须分配的工单量（自然1h）
        redissonHandler.incrAndSetTimeWhenNotExists(getAllHourCountKey(scene), RedissonHandler.ONE_HOUR);
        // 总规则统计
        redissonHandler.incrAndSetTimeWhenNotExists(getRuleCountKey(ruleInfo.getRuleId()),
                RedissonHandler.ONE_DAY);
        // 总策略统计
        //查询数量
        StrategyCount strategyCount = redissonHandler.get(getStrategyCountKey(scene) , StrategyCount.class);
        if (strategyCount == null) {
            List<RiskQualitySpotLevelConfVo> riskQualitySpotLevelConfVos =
                    qualitySpotLevelConfBiz.listWithAllCurrentValidScene(scene);
            if (CollectionUtils.isEmpty(riskQualitySpotLevelConfVos)) {
                log.error("riskQualitySpotLevelConfVos is empty, scene:{}", scene);
                return;
            }
            strategyCount = new StrategyCount(0, riskQualitySpotLevelConfVos.get(0).getSamplingLevel(),
                    DateUtil.getYMDStringByDate(new Date()));
        }
        strategyCount.setCurrentCount(strategyCount.getCurrentCount() + 1);
        redissonHandler.setEX(getStrategyCountKey(scene) ,
                strategyCount, RedissonHandler.ONE_DAY);

    }

    private boolean checkRuleSampling(QualitySpotRuleInfo ruleInfo, QualitySpotJobConfDto qualitySpotJobConfDto) {
        if (qualitySpotJobConfDto.getExecuteMode() == QualitySpotExecuteModelEnum.PRIORITY.getCode()) {
            return false;
        }
        int count =  getCountByKey(getRuleCountKey(ruleInfo.getRuleId()));
        QualitySpotExecuteModelEnum executeModelEnum = QualitySpotExecuteModelEnum.fromCode(qualitySpotJobConfDto.getExecuteMode());
        switch (executeModelEnum) {
            case PRIORITY:
                return false;
            case PERCENTAGE:
            case FINAL_COUNT:
                return count >= ruleInfo.getRuleSamplingLevel();
            default:
                return true;
        }

    }

    private QualitySpotHighRiskDto buildQualitySpotHighRiskDto(Long qcWorkOrderId, RiskQcBaseInfo riskQcBaseInfo, WorkOrderVO workOrderVO,int callStatus) {
        QualitySpotHighRiskDto qualitySpotHighRiskDto = new QualitySpotHighRiskDto();
        qualitySpotHighRiskDto.setWorkOrderId(qcWorkOrderId);
        qualitySpotHighRiskDto.setCheckerName(riskQcBaseInfo.getQcByName());
        qualitySpotHighRiskDto.setUniqueCode(riskQcBaseInfo.getQcUniqueCode());

        int handleResult = 0;
        if (workOrderVO.getHandleResult() == HandleResultEnum.audit_pass.getType()) {
            handleResult = 2;
        } else if (workOrderVO.getHandleResult() == HandleResultEnum.audit_reject.getType()) {
            handleResult = 1;
        } else if (workOrderVO.getHandleResult() == HandleResultEnum.stop_case.getType()) {
            handleResult = 3;
        }
        qualitySpotHighRiskDto.setTargetHandleResult(handleResult);

        long scene = QualitySpotSceneEnum.HIGH_RISK_QC.getCode();
        //被质检人当天必须分配工单数量
        qualitySpotHighRiskDto.setDayAllotWorkOrderCount(getCountByKey(
                getCheckerDayCountKey(scene, qualitySpotHighRiskDto.getUniqueCode())));
        // 最近1h内必须分配的工单量（自然1h）
        qualitySpotHighRiskDto.setHourAllotWorkOrderCount(getCountByKey(getAllHourCountKey(scene)));
        //是否有录音
        qualitySpotHighRiskDto.setHavingRecord(callStatus);
        //高风险案例手动调用规则码
        Response<List<HighRiskRecordDto>> response = highRiskClient.getListByCaseId(workOrderVO.getCaseId());
        List<HighRiskRecordDto> highRiskRecordDtoList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        qualitySpotHighRiskDto.setRuleCode(StringUtils.EMPTY);
        if (CollectionUtils.isNotEmpty(highRiskRecordDtoList)) {
            Optional<String> codes = highRiskRecordDtoList.stream().filter(v -> v.getTriggerSource() == HighRiskJudgeConst.Source.MANUAL).map(HighRiskRecordDto::getHitCodes).findFirst();
            codes.ifPresent(qualitySpotHighRiskDto::setRuleCode);
        }
        return qualitySpotHighRiskDto;
    }

    private int getCountByKey(String key) {
        if (log.isDebugEnabled()){
            log.debug("key:{}", key);
        }
        Integer count = redissonHandler.get(key, Integer.class);
        if (count == null) {
            return 0;
        }
        return count;
    }

    private boolean checkStrategyCount(QualitySpotJobConfDto confDto) {
        StrategyCount strategyCount = redissonHandler.get(getStrategyCountKey(confDto.getScene()), StrategyCount.class);
        if (strategyCount == null) {
            List<RiskQualitySpotLevelConfVo> riskQualitySpotLevelConfVos =
                    qualitySpotLevelConfBiz.listWithAllCurrentValidScene(confDto.getScene());
            if (CollectionUtils.isEmpty(riskQualitySpotLevelConfVos)) {
                log.error("riskQualitySpotLevelConfVos is empty, scene:{}", confDto.getScene());
                return true;
            }
            //查询数量
            strategyCount = new StrategyCount(0, riskQualitySpotLevelConfVos.get(0).getSamplingLevel(),
                    DateUtil.getYMDStringByDate(new Date()));
            redissonHandler.setEX(getStrategyCountKey(confDto.getScene()), strategyCount, RedissonHandler.ONE_DAY);
        }
        return strategyCount.getCurrentCount() >= strategyCount.getTargetCount();
    }

    /**
     * 任务策略的key
     */
    private String getStrategyCountKey(long scene) {
        return DateUtil.getYMDStringByDate(new Date()) + "_" + scene;
    }


    /**
     * 规则每天的工单的key
     */
    private String getRuleCountKey(long ruleId) {
        return DateUtil.getYMDStringByDate(new Date()) + "_" + ruleId;
    }

    /**
     * 被质检人当天质检工单量 key
     * @param scene
     */
    private String getCheckerDayCountKey(long scene, String userKey) {
        return DateUtil.getYMDStringByDate(new Date()) + "_" + scene + "_" + userKey;
    }

    /**
     * 1小时内质检工单量 key
     */
    private String getAllHourCountKey(long scene) {
        return com.shuidihuzhu.common.util.DateUtil.getCurFormatDate("yyyyMMddHH") + "_" + scene;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class StrategyCount {
        private int currentCount;
        private int targetCount;
        private String currentDate;
    }
}
