package com.shuidihuzhu.cf.risk.admin.delegate.ai.textvalidforaudio;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.cf.risk.admin.model.vo.AsrSentenceVO;
import com.shuidihuzhu.cf.risk.admin.model.vo.RiskQcVideoVo;
import com.shuidihuzhu.common.web.model.Response;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=932950052
 */
@Service
@Slf4j
public class AiTextValidForAudioDelegate {

    @Autowired
    private OceanApiClient oceanApiClient;

    @Resource
    private MeterRegistry meterRegistry;

    public static String TAG = "classif-chou-wk-order-zy-asr";
    public static String USER_ID = "10007";
    public static String TOKEN = "976d6c2ca00fbc1f";

    public AiTextValidForAudioResp analyse(long workOrderId, List<RiskQcVideoVo> videos) {
        log.info("AiTextValidForAudioDelegate in workOrderId {}, videos {}", workOrderId, videos);
        if (CollectionUtils.isEmpty(videos)) {
            log.warn("录音文件为空");
            return null;
        }

        OceanApiRequest req = new OceanApiRequest();
        req.setTag(TAG);
        req.setUserId(USER_ID);
        req.setToken(TOKEN);

        AiTextValidForAudioParam param = new AiTextValidForAudioParam();
        param.setWorkOrderId(workOrderId);
        param.setType("class");
        final List<AiTextValidForAudioParam.Video> views = videos.stream()
                .map(v -> {
                    final AiTextValidForAudioParam.Video r = new AiTextValidForAudioParam.Video();
                    r.setId(v.getId());
                    r.setVideoUrl(v.getVideoUrl());
                    r.setCreateTime(v.getCreatTime());
                    r.setEndTime(v.getEndTime());
                    r.setSentences(v.getSentenceInfoList());
                    return r;
                })
                .sorted(Comparator.comparing(AiTextValidForAudioParam.Video::getCreateTime))
                .collect(Collectors.toList());
        param.setVideos(views);
        req.setBody(JSON.toJSONString(param));
        Response<OceanApiResponse> resp = oceanApiClient.agent(req);
        log.info("AiTextValidForAudioDelegate analyse req {}, resp {}", req, resp);
        final boolean fail = resp == null || resp.notOk() || resp.getData() == null;
        if (fail) {
            meterRegistry.gauge("AiTextValidForAudioDelegate", Tags.of("business", "cf", "success", "false"), 0);
            log.warn("AiTextValidForAudioDelegate ai error {}", resp);
            return null;
        }
        OceanApiResponse data = resp.getData();
        String body = data.getBody();
        final AiTextValidForAudioResp res = JSON.parseObject(body, AiTextValidForAudioResp.class);

        // 打点
        meterRegistry.gauge("AiTextValidForAudioDelegate", Tags.of("business", "cf", "success", "true"), res.getPreb());
        return res;
    }

}
