package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.risk.admin.cache.list.ListDepartmentCache;
import com.shuidihuzhu.cf.risk.admin.service.list.ListDepartmentCaseService;
import com.shuidihuzhu.cf.risk.client.admin.list.RiskListDepartmentClient;
import com.shuidihuzhu.cf.risk.model.admin.list.HospitalAuditPassDto;
import com.shuidihuzhu.cf.risk.model.admin.list.ListDepartmentDto;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/31 15:43
 */
@RestController
@Slf4j
public class RiskListDepartmentInnerController implements RiskListDepartmentClient {

    @Resource
    private ListDepartmentCache departmentCache;
    @Resource
    private ListDepartmentCaseService departmentCaseService;

    @Override
    public Response<List<ListDepartmentDto>> getListHospital(String areaCode, String landline) {
        log.info("查询医院核实名单，areaCode:{}, landline:{}", areaCode, landline);
        return NewResponseUtil.makeSuccess(departmentCache.listDepartmentDtos(areaCode, landline));
    }

    @Override
    public Response<Void> hospitalAuditPass(HospitalAuditPassDto hospitalAuditPassDto) {
        log.info("下发医院核实通过更新名单信息:{}", hospitalAuditPassDto);
        departmentCaseService.hospitalAuditPassHandle(hospitalAuditPassDto);
        return NewResponseUtil.makeSuccess(null);
    }
}
