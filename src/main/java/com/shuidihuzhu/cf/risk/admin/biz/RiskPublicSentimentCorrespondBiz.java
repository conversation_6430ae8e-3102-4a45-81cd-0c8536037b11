package com.shuidihuzhu.cf.risk.admin.biz;

import com.shuidihuzhu.cf.risk.admin.model.vo.RiskSourceCorrespondVo;
import com.shuidihuzhu.cf.risk.admin.model.RiskSourceCorrespond;

import java.util.List;

public interface RiskPublicSentimentCorrespondBiz {
    List<RiskSourceCorrespondVo> getByInfoSource(int infoSource, boolean isShowOther);


    List<RiskSourceCorrespond> getByInfoSources(List<Integer> infoSources);

    RiskSourceCorrespond getByInfoFeedBack(int infoFeedback);


}
