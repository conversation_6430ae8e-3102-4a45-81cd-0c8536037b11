package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataLog;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@NoArgsConstructor
@ApiModel(description = "黑名单信息日志")
public class BlacklistDataLogVo {

    public BlacklistDataLogVo(RiskBlacklistDataLog riskBlacklistDataLog) {
        this.modifyContent = riskBlacklistDataLog.getModifyContent();
        this.operateName = riskBlacklistDataLog.getOperateName();
        this.operateReason = riskBlacklistDataLog.getOperateReason();
        this.updateTime = DateUtil.getDate2LStr(riskBlacklistDataLog.getUpdateTime());
    }

    @ApiModelProperty("修改内容")
    private String modifyContent;

    @ApiModelProperty("操作原因")
    private String operateReason;

    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("操作时间")
    private String updateTime;

}
