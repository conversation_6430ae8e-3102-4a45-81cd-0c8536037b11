package com.shuidihuzhu.cf.risk.admin.model.qcAppeal;

import lombok.Data;

import java.util.Date;

/**
 * @Auther: subing
 * @Date: 2020/11/16
 */
@Data
public class RiskQcAppealResultModel {
    /**
     * 申诉结果
     */
    private int appealResult;
    /**
     * 申诉信息
     * @see RiskQcAppealInfoModel
     */
    private String appealInfo;
    /**
     * 工单id
     */
    private long workOrderId;

    private Date createTime;
    private long id;

}
