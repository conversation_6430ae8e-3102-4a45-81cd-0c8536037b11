package com.shuidihuzhu.cf.risk.admin.model.po.blacklist;

import java.util.Date;

public class RiskBlacklistDataTypeRef {
    /**
     * 主键
     */
    private Long id;

    /**
     * 黑名单数据id
     */
    private Long dataId;

    /**
     * 黑名单类型id
     */
    private Long typeId;

    /**
     * 冗余类型动作ids
     */
    private String actionIds;

    /**
     * 冗余类型名称
     */
    private String typeName;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 主键
     * @return id 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 黑名单数据id
     * @return data_id 黑名单数据id
     */
    public Long getDataId() {
        return dataId;
    }

    /**
     * 黑名单数据id
     * @param dataId 黑名单数据id
     */
    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    /**
     * 黑名单类型id
     * @return type_id 黑名单类型id
     */
    public Long getTypeId() {
        return typeId;
    }

    /**
     * 黑名单类型id
     * @param typeId 黑名单类型id
     */
    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    /**
     * 冗余类型动作ids
     * @return action_ids 冗余类型动作ids
     */
    public String getActionIds() {
        return actionIds;
    }

    /**
     * 冗余类型动作ids
     * @param actionIds 冗余类型动作ids
     */
    public void setActionIds(String actionIds) {
        this.actionIds = actionIds == null ? null : actionIds.trim();
    }

    /**
     * 冗余类型名称
     * @return type_name 冗余类型名称
     */
    public String getTypeName() {
        return typeName;
    }

    /**
     * 冗余类型名称
     * @param typeName 冗余类型名称
     */
    public void setTypeName(String typeName) {
        this.typeName = typeName == null ? null : typeName.trim();
    }

    /**
     * 是否删除，0 否，1 是
     * @return is_delete 是否删除，0 否，1 是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 是否删除，0 否，1 是
     * @param isDelete 是否删除，0 否，1 是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}