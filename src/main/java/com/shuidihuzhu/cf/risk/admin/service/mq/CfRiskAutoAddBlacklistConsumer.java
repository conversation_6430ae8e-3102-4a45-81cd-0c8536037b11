package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.risk.admin.model.vo.hit.RiskHitOperateVo;
import com.shuidihuzhu.cf.risk.admin.service.blacklist.BlackListDataService;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2022/3/16 11:23
 * @Description:
 */
@Component
@RocketMQListener(id = CfRiskMQTagCons.AUTO_ADD_BLACKLIST, tags = CfRiskMQTagCons.AUTO_ADD_BLACKLIST, topic = CfRiskMQTopicCons.CF_RISK_TOPIC)
@Slf4j
public class CfRiskAutoAddBlacklistConsumer implements MessageListener<RiskHitOperateVo> {

    @Resource
    private BlackListDataService blackListDataService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<RiskHitOperateVo> mqMessage) {
        log.info("CfRiskAutoAddBlacklistConsumer is begin {}", mqMessage);
        RiskHitOperateVo payload = mqMessage.getPayload();
        if (Objects.isNull(payload)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        blackListDataService.autoAddBlacklist(payload);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
