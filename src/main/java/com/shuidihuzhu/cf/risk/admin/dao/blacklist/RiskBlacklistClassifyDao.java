package com.shuidihuzhu.cf.risk.admin.dao.blacklist;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistClassify;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskBlacklistClassifyDao {
    int insertSelective(RiskBlacklistClassify record);

    int saveBatch(List<RiskBlacklistClassify> riskBlacklistClassifies);

    RiskBlacklistClassify selectByPrimaryKey(Long id);

    int updateLevelPathAndParentId(@Param("id") Long id, @Param("levelPath") String levelPath, @Param("parentId") Long parentId);

    List<RiskBlacklistClassify> listByIds(List<Long> ids);

    List<RiskBlacklistClassify> listParentIdAndOptional(@Param("parentId") Long parentId, @Param("status") Byte status, @Param("name") String name);

    int updateStatusById(@Param("id") Long id, @Param("status") Byte status);
}