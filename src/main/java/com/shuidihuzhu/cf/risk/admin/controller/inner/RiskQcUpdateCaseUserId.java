package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.dao.RiskQcSearchIndexDao;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-09-06 23:17
 **/
@RestController
@RequestMapping(value = "/innerapi/cf-risk-admin/risk/qc")
public class RiskQcUpdateCaseUserId {

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private RiskQcSearchIndexDao riskQcSearchIndexDao;

    @RequestMapping("update-user-id")
    public Response updateUserId() {
        int size = 200;
        long id = 0;
        while (true) {
            List<RiskQcSearchIndex> riskQcSearchIndices = riskQcSearchIndexDao.selectbyIdAndSize(id, size);
            if (CollectionUtils.isEmpty(riskQcSearchIndices)) {
                break;
            }
            id = riskQcSearchIndices.get(riskQcSearchIndices.size() - 1).getId();
            List<Integer> caseIds = riskQcSearchIndices.stream().map(RiskQcSearchIndex::getCaseId).map(Long::intValue).collect(Collectors.toList());
            FeignResponse<List<CrowdfundingInfo>> crowdfundingListById = crowdfundingFeignClient.getCrowdfundingListById(caseIds);
            if (crowdfundingListById.ok() && CollectionUtils.isNotEmpty(crowdfundingListById.getData())) {
                List<CrowdfundingInfo> crowdfundingInfos = crowdfundingListById.getData();
                var crowdfundingInfoMap = crowdfundingInfos.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (k1, k2) -> k2));
                for (RiskQcSearchIndex riskQcSearchIndex : riskQcSearchIndices) {
                    var crowdfundingInfo = crowdfundingInfoMap.get((int)riskQcSearchIndex.getCaseId());
                    if (Objects.nonNull(crowdfundingInfo)) {
                        riskQcSearchIndexDao.updateById(riskQcSearchIndex.getId(), crowdfundingInfo.getUserId());
                    }
                }
            }
        }
        return NewResponseUtil.makeSuccess(null);
    }
}
