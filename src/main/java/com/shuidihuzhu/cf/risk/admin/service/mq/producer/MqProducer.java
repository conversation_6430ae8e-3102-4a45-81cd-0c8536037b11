package com.shuidihuzhu.cf.risk.admin.service.mq.producer;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.risk.admin.constant.QcConst;
import com.shuidihuzhu.cf.risk.admin.model.QcCreateWorkOrderModel;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTopicCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @author: wanghui
 * @create: 2022/4/11 下午4:57
 */
@Service
@Slf4j
public class MqProducer {

    private static final String KEY = "apollo.asr.order-create-for-recoding.delay-seconds";

    public <T> void sendDelayOfQcWorkOrderCreate4RecodingMsg(QcCreateWorkOrderModel payload) {

        final Long delaySeconds = ConfigService.getAppConfig().getLongProperty(KEY, TimeUnit.HOURS.toSeconds(1));

        final Response<MessageResult> send = MaliMQComponent.builder()
                .setTags(QcConst.QC_WORK_ORDER_CREATE_4_RECODING_MSG)
                .setTopic(CfRiskMQTopicCons.CF_RISK_TOPIC)
                .setDelayTime(delaySeconds * 1000L)
                .setPayload(payload)
                .send();
        log.info("sendDelayOfQcWorkOrderCreate4RecodingMsg payload:{} msg:{}", JSON.toJSONString(payload), JSON.toJSONString(send));
    }
}
