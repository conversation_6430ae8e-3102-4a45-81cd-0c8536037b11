package com.shuidihuzhu.cf.risk.admin.dao;


import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderCallRecordingRelModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface WorkOrderCallRecordingRelDao {


    WorkOrderCallRecordingRelModel selectByRecordingUniqueId(@Param("recordingUniqueId") String recordingUniqueId);

    int insert(WorkOrderCallRecordingRelModel relModel);

    List<WorkOrderCallRecordingRelModel> selectByWorkOrderIds(@Param("workOrderIds") List<Long> workOrderIds);

    List<String> selectByWorkOrderId(@Param("workOrderId") Long workOrderId);
}
