package com.shuidihuzhu.cf.risk.admin.util;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-11-25 15:41
 **/
@Slf4j
public class OkHttpClientUtil {

    private static final OkHttpClientUtil INSTANCE = new OkHttpClientUtil();
    private final OkHttpClient okHttpClient;

    private OkHttpClientUtil() {
        okHttpClient = new
                OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(10, 30, TimeUnit.MINUTES))
                .build();
    }

    public static OkHttpClientUtil getInstance() {
        return INSTANCE;
    }


    public Response doGet(String url) {
        Request request = new Request.Builder().url(url).get().build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
        } catch (IOException e) {
            log.error("OkHttpClientUtil.doGet error", e);
        }
        return response;
    }

}
