package com.shuidihuzhu.cf.risk.admin.delegate;

import com.shuidihuzhu.client.cf.growthtool.client.DepartmentQCFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeDetailModel;
import com.shuidihuzhu.client.cf.growthtool.model.DepartmentChangeItemModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ClewDelegate {

    private static final String TAG = "clew delegate";

    @Resource
    private DepartmentQCFeignClient departmentQCFeignClient;

    public Response<DepartmentChangeDetailModel> getHospitalDeptInfo(int buildingId) {
        return l(departmentQCFeignClient.getChangeDetail(buildingId));
    }

    public DepartmentChangeDetailModel getHospitalDeptInfoData(int buildingId) {
        final Response<DepartmentChangeDetailModel> clewInfoResp = l(departmentQCFeignClient.getChangeDetail(buildingId));
        if (clewInfoResp == null || clewInfoResp.notOk()) {
            log.error("楼宇信息查询失败 buildingId {}", buildingId);
            return null;
        }
        DepartmentChangeDetailModel clewInfo = clewInfoResp.getData();
        if (clewInfo == null) {
            log.error("楼宇信息为空 buildingId {}", buildingId);
            return null;
        }
        return clewInfoResp.getData();
    }

    public Response<Void> notifyDepartmentCheck(int buildingId,
                                                long maxChangeId) {
        return l(departmentQCFeignClient.notifyDepartmentCheck(buildingId, maxChangeId));
    }

    public Response<DepartmentChangeDetailModel> getAllBuildingInfo(int deptId) {
        return l(departmentQCFeignClient.getAllBuildingName(deptId));
    }

    public Response<Integer> editBuildingName(String operatorName, long operatorId, int buildingId, String buildingName){
        return l(departmentQCFeignClient.editBuildingName(operatorName, operatorId, buildingId, buildingName));
    }

    public Response<Integer> editDeptInfo(String operatorName, long operatorId, int buildingId, List<DepartmentChangeItemModel> changeItemModelList){
        return l(departmentQCFeignClient.editDepartmentNameAndFloor(operatorName, operatorId, buildingId, changeItemModelList));
    }


    private <T> T l(T t) {
        log.info("{} data {}", TAG, t);
        return t;
    }
}
