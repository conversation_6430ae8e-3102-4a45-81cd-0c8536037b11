package com.shuidihuzhu.cf.risk.admin.rule.utils;

import com.shuidihuzhu.cf.risk.admin.rule.enums.RuleTypeEnum;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020-03-16
 **/
@Slf4j
public class GroovyScriptRunUtil {

    /**
     * 检查数据是否符合条件.
     *
     * @param ruleScript string
     * @return true data is valid, other is false, {@link Boolean}
     *
     */
    public static <T> T executeCommon(RuleTypeEnum ruleTypeEnum, Number refId, String ruleScript, Object[] args) {
        try {
            String identifier = ruleTypeEnum.getRuleType() + refId + MD5Util.getMD5HashValue(ruleScript);
            return (T) GroovyScriptUtil.invokeMethod(identifier, ruleScript, "check", args);
        } catch (Exception e) {
            log.error("rule:" + ruleScript, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 编译groovy脚本
     * @param ruleTypeEnum
     * @param refId
     * @param ruleScript
     * @return
     */
    public static void compileGroovy(RuleTypeEnum ruleTypeEnum, Number refId, String ruleScript) {
        try {
            String identifier = ruleTypeEnum.getRuleType() + refId + MD5Util.getMD5HashValue(ruleScript);
            GroovyScriptUtil.loadGroovyObject(identifier, ruleScript);
        } catch (Exception e) {
            log.error("script compile:" + ruleScript, e);
        }
    }

}
