package com.shuidihuzhu.cf.risk.admin.model.vo.hit;

import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitLog;
import com.shuidihuzhu.common.util.DateUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/8/21 14:41
 */
@Data
public class StrategyHitLogVo {

    public StrategyHitLogVo(RiskStrategyHitLog riskStrategyHitLog){
        this.operateName = riskStrategyHitLog.getOperateName();
        this.createTime = DateUtil.getDate2LStr(riskStrategyHitLog.getCreateTime());
        this.operateContent = riskStrategyHitLog.getOperateContent();
    }

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 创建时间
     */
    private String createTime;

}
