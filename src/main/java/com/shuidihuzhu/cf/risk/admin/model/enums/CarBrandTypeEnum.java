package com.shuidihuzhu.cf.risk.admin.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/8/17 19:50
 */
@Getter
public enum CarBrandTypeEnum {

    DONT_KNOWN(0, "其他"),
    GENERAL(1, "普通品牌"),
    EXPENSIVE(2, "知名豪车"),
    ;

    private byte code;
    private String desc;

    CarBrandTypeEnum(int code, String desc) {
        this.code = (byte) code;
        this.desc = desc;
    }
}
