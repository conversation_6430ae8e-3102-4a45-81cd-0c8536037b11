package com.shuidihuzhu.cf.risk.admin.consumer;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.risk.admin.service.CfRepeatPayOrderService;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/3/11 14:14
 * @Description:
 */
@Slf4j
@Service
@RocketMQListener(id = CfClientMQTagCons.CF_REPEAT_CASE_PAY_ORDER, tags = CfClientMQTagCons.CF_REPEAT_CASE_PAY_ORDER, topic = MQTopicCons.CF)
public class CfRepeatCasePayOrderConsumer implements MessageListener<List<Integer>> {

    @Resource
    private CfRepeatPayOrderService cfRepeatPayOrderService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<List<Integer>> mqMessage) {
        log.info("CfRepeatCasePayOrderConsumer is begin {}", mqMessage);
        if (CollectionUtils.isEmpty(mqMessage.getPayload())) {
            log.info("CfRepeatCasePayOrderConsumer payload is empty {}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        List<Integer> repeatCaseId = mqMessage.getPayload();
        cfRepeatPayOrderService.check(repeatCaseId);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
