package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.GetInternalStaffAndEmergencyMobileDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface GetInternalStaffAndEmergencyMobileDAO {

    int insert(@Param("infos") List<GetInternalStaffAndEmergencyMobileDO> infos);

    List<GetInternalStaffAndEmergencyMobileDO> selectByStaffMobile(@Param("staffMobile") String staffMobile);

    List<GetInternalStaffAndEmergencyMobileDO> selectByEmergencyMobile(@Param("emergencyMobile") String emergencyMobile);

    int update(@Param("isDelete")int isDelete);

    GetInternalStaffAndEmergencyMobileDO getByStaffMobile(@Param("staffMobile") String staffMobile);

    GetInternalStaffAndEmergencyMobileDO getByEmergencyMobile(@Param("emergencyMobile") String emergencyMobile);

}
