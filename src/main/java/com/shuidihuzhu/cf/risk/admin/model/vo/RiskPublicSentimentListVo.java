package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail;
import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo;
import com.shuidihuzhu.cf.risk.admin.model.enums.PublicSentimentInfoClassifyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.PublicSentimentInfoSourceEnum;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/2/19
 */
@Data
public class RiskPublicSentimentListVo implements PageHasId {
    private long id;
    private String infoSource;
    private String infoFeedBack;
    private Timestamp publishTime;
    private String content;
    private String videoUrl;
    private String imgUrl;
    private Timestamp psRecordTime;
    private Map<String, Object> publicSentimentRecord;
    private int disposeStatus;
    private String infoClassify;
    private int caseId;
    private String disposeName;
    private Timestamp disposeTime;
    private String publicSentimentInfoType;

    public static RiskPublicSentimentListVo buildVo(RiskPublicSentimentInfo riskPublicSentimentInfo,
                                                    RiskPublicSentimentDetail riskPublicSentimentDetail,
                                                    String infoFeedBack, Timestamp psRecordTime, String publicSentimentRecord,
                                                    Timestamp disposeTime, String publicSentimentInfoType){
        RiskPublicSentimentListVo riskPublicSentimentListVo = new RiskPublicSentimentListVo();
        riskPublicSentimentListVo.setId(riskPublicSentimentInfo.getId());
        int infoSource = riskPublicSentimentInfo.getInfoSource();
        riskPublicSentimentListVo.setInfoSource(infoSource == PublicSentimentInfoSourceEnum.OTHER.getCode() ?
               riskPublicSentimentInfo.getInfoSourceOther() : PublicSentimentInfoSourceEnum.findOfCode(infoSource));
        riskPublicSentimentListVo.setInfoFeedBack(riskPublicSentimentInfo.getInfoFeedBack() == 0 ? riskPublicSentimentInfo.getInfoFeedBackOther() : infoFeedBack);
        riskPublicSentimentListVo.setPublishTime(riskPublicSentimentInfo.getPublishTime());
        riskPublicSentimentListVo.setContent(riskPublicSentimentDetail.getContent());
        riskPublicSentimentListVo.setImgUrl(riskPublicSentimentDetail.getImgUrl());
        riskPublicSentimentListVo.setVideoUrl(riskPublicSentimentDetail.getVideoUrl());
        riskPublicSentimentListVo.setPublicSentimentRecord(JSONObject.parseObject(publicSentimentRecord));
        riskPublicSentimentListVo.setPsRecordTime(psRecordTime);
        riskPublicSentimentListVo.setDisposeStatus(riskPublicSentimentInfo.getStatus());
        riskPublicSentimentListVo.setInfoClassify(riskPublicSentimentInfo.getInfoClassify() == PublicSentimentInfoClassifyEnum.OTHER.getCode() ? riskPublicSentimentInfo.getInfoClassifyOther() : PublicSentimentInfoClassifyEnum.findOfCode(riskPublicSentimentInfo.getInfoClassify()));
        riskPublicSentimentListVo.setCaseId(riskPublicSentimentInfo.getCaseId());
        riskPublicSentimentListVo.setDisposeName(riskPublicSentimentInfo.getLastOperator());
        riskPublicSentimentListVo.setDisposeTime(disposeTime);
        riskPublicSentimentListVo.setPublicSentimentInfoType(publicSentimentInfoType);
        return riskPublicSentimentListVo;
    }
}
