package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public enum RiskQcResultEnum {
    //qualified
    QUALIFIED(1, "合格"),
    NO_QUALIFIED(2, "不合格");

    int type;
    String description;

    RiskQcResultEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public static List<Integer> getTypeByEnum(List<RiskQcResultEnum> riskQcOperationTypeEnums){
        if (CollectionUtils.isEmpty(riskQcOperationTypeEnums)){
            return Lists.newArrayList();
        }
        List<Integer> types = Lists.newArrayList();
        riskQcOperationTypeEnums.forEach(riskQcOperationTypeEnum -> {
            types.add(riskQcOperationTypeEnum.getType());
        });
        return types;
    }

    public static String findOfCode(int code) {
        for (RiskQcResultEnum value : values()) {
            if (value.getType() == code) {
                return value.getDescription();
            }
        }
        return "";
    }
}
