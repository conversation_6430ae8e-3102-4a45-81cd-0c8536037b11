package com.shuidihuzhu.cf.risk.admin.service.recording;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.admin.delegate.AiAsrDelegate;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2022/3/28 下午4:01
 */
@Slf4j
@Component
public class WorkOrderRecordingHandlerRegister {

    @Autowired
    private List<AbsWorkOrderRecordingHandler> workOrderRecordingBaseHandlers;
    @Autowired
    private WorkOrderRecordingExtHandler workOrderRecordingExtHandler;

    private final Map<WorkOrderType, WorkOrderRecordingHandler> workOrderTypeMap = Maps.newHashMap();
    private final Map<AiAsrDelegate.HandleTypeEnum, WorkOrderRecordingExtHandler> handleTypeMap = Maps.newHashMap();

    @PostConstruct
    public void registerWorkOrderRecordingExtHandler() {
        workOrderRecordingBaseHandlers.forEach(item -> {
            if (item.getWorkOrderType()!=null) workOrderTypeMap.put(item.getWorkOrderType(), item);
        });
        handleTypeMap.put(AiAsrDelegate.HandleTypeEnum.HANDLE_MATERIAL_QC, workOrderRecordingExtHandler);
        handleTypeMap.put(AiAsrDelegate.HandleTypeEnum.HANDLE_MATERIAL_ACTIVE_SERVICE_QC, workOrderRecordingExtHandler);
        handleTypeMap.put(AiAsrDelegate.HandleTypeEnum.HANDLE_INTERNAL_AUDIT_HIGH_RISK_QC, workOrderRecordingExtHandler);
        handleTypeMap.put(AiAsrDelegate.HandleTypeEnum.HANDLE_WX_1V1_QC_RECHECK, workOrderRecordingExtHandler);
        handleTypeMap.put(AiAsrDelegate.HandleTypeEnum.HANDLE_REPORT_RECORD, workOrderRecordingExtHandler);
    }

    public WorkOrderRecordingHandler getBean(WorkOrderType workOrderType) {
        return workOrderTypeMap.get(workOrderType);
    }

    public WorkOrderRecordingExtHandler getBean(AiAsrDelegate.HandleTypeEnum handleTypeEnum) {
        WorkOrderRecordingExtHandler workOrderRecordingExtHandler = handleTypeMap.get(handleTypeEnum);
        if (workOrderRecordingExtHandler == null) {
            log.error("WorkOrderRecordingHandlerRegister getBean null handleTypeEnum:{}", handleTypeEnum);
        }
        return workOrderRecordingExtHandler;
    }
}
