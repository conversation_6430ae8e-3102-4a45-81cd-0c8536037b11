package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-07-10 13:35
 **/
@Data
public class QcWorkOrderWx1v1RepeatExcelVo {

    @ExcelProperty(value = "复检工单处理时间", index = 0)
    private Date updateTime;
    @ExcelProperty(value = "工单id", index = 1)
    private long workOrderId;
    @ExcelProperty(value = "被质检人姓名", index = 2)
    private String byName;
    @ExcelProperty(value = "复检结果", index = 3)
    private String workOrderResult;
    @ExcelProperty(value = "复检勾选问题描述", index = 4)
    private String secondDesc;
    @ExcelProperty(value = "复检时其他备注", index = 5)
    private String repeatOtherNotes;
    @ExcelProperty(value = "原质检工单勾选问题描述", index = 6)
    private String originSecondDesc;
    @ExcelProperty(value = "原质检工单录音备注", index = 7)
    private String originRecordingNotes;
    @ExcelProperty(value = "原质检工单其他备注", index = 8)
    private String originOtherNotes;
}
