package com.shuidihuzhu.cf.risk.admin.configuration;

import com.shuidihuzhu.esdk.EsClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EsConfig {

    public static final String CLUSTER_NAME = "es-common";

    @Bean("es-es-cf-risk-admin")
    @ConfigurationProperties("rms.es.cf-risk-admin.cf-risk-admin")
    public EsClient esClient() {
        return new EsClient();
    }
}
