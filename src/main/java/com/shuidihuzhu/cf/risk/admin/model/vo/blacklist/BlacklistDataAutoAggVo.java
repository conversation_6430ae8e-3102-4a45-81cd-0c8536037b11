package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/27 下午10:21
 */
@Data
@AllArgsConstructor
public class BlacklistDataAutoAggVo {

    @ApiModelProperty("自动加入黑名单-类型")
    private Pair<Integer, String> autoAddType;

    @ApiModelProperty("黑名单自动加入vo")
    private List<BlacklistDataAutoVo> blacklistDataAutoVos;

}
