package com.shuidihuzhu.cf.risk.admin.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AsrRequestParam {
    private String input_url;
    @ApiModelProperty("Int, 0-9 Optional，默认值为0，注意值越大优先级越高，会影响系统对不同任务的资源分配")
    private int priority;
    private Options options;

    @Data
    public static class Options {

        /**
         * 当前必须指定为："dt-kfcv7"
         */
        private String speech_model = "dt-kfcv7";
//        private String speaker_model = "diarization_emb";

    }
}
