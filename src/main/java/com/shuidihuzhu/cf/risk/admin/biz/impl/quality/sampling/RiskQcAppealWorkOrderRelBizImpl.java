package com.shuidihuzhu.cf.risk.admin.biz.impl.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQcAppealWorkOrderRelBiz;
import com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQcAppealWorkOrderRelDao;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcAppealWorkOrderRel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/11/17
 */
@Service
@Slf4j
public class RiskQcAppealWorkOrderRelBizImpl implements RiskQcAppealWorkOrderRelBiz {

    @Autowired
    private RiskQcAppealWorkOrderRelDao appealWorkOrderRelDao;

    @Override
    public int save(long qcWorkOrderId, long appealWorkOrderId) {
        if (qcWorkOrderId <= 0 || appealWorkOrderId <=0 ){
            return 0;
        }
        return appealWorkOrderRelDao.save( qcWorkOrderId, appealWorkOrderId);
    }

    @Override
    public RiskQcAppealWorkOrderRel getByAppealWorkOrderId(long appealWorkOrderId) {
        if (appealWorkOrderId <= 0 ) {
            return null;
        }
        return appealWorkOrderRelDao.getByAppealWorkOrderId(appealWorkOrderId);
    }
}
