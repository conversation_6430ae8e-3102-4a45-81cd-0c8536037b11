package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.model.vo.CarBrandVo;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotResultIssueInnerService;
import com.shuidihuzhu.cf.risk.client.admin.quality.sampling.QualitySpotResultIssueClient;
import com.shuidihuzhu.cf.risk.model.admin.quality.sampling.QualitySpotResultIssueRequest;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/13
 */
@RestController
@Slf4j
public class QualitySpotResultIssueInnerController implements QualitySpotResultIssueClient {

    @Autowired
    private QualitySpotResultIssueInnerService spotResultIssueInnerService;
    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Override
    public Response<List<Long>> offlineSpotResultIssue(QualitySpotResultIssueRequest qualitySpotResultIssueRequest) {
        if (CollectionUtils.isEmpty(qualitySpotResultIssueRequest.getWorkOrderVOS())){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return NewResponseUtil.makeSuccess(spotResultIssueInnerService.offlineIssue(qualitySpotResultIssueRequest));
    }

    @Override
    public Response<List<Long>> offlineQcAppealResultIssue(QualitySpotResultIssueRequest qualitySpotResultIssueRequest) {
        if (CollectionUtils.isEmpty(qualitySpotResultIssueRequest.getWorkOrderVOS())){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return NewResponseUtil.makeSuccess(spotResultIssueInnerService.offlineAppealIssue(qualitySpotResultIssueRequest));
    }

    @ApiOperation(value = "根据名称模糊查询车品牌-列表")
    @PostMapping(path = "/innerapi/cf-risk-admin-test")
    public Response<Void> testIssue(@ApiParam("工单id") long workorderId ,@ApiParam("是否为申诉") boolean isAppeal) {
        log.info("workorderId：{} isAppeal:{}", workorderId, isAppeal);
        Response<WorkOrderVO> response = workOrderClient.getWorkOrderById(workorderId);
        if (response.notOk()){
            return NewResponseUtil.makeFail("工单查询失败");
        }
        Response<List<WorkOrderExt>> listResponse = workOrderClient.listExtInfos(Lists.newArrayList(response.getData().getWorkOrderId()),
                OrderExtName.qcId.getName());
        if (listResponse.notOk()){
            return NewResponseUtil.makeFail("工单ext查询失败");
        }
        WorkOrderVO workOrderVO = response.getData();
        long qcId = Long.parseLong(listResponse.getData().get(0).getExtValue());
        workOrderVO.setQcId(qcId);
        QualitySpotResultIssueRequest issueRequest = new QualitySpotResultIssueRequest();
        issueRequest.setWorkOrderVOS(Lists.newArrayList(workOrderVO));
        if (isAppeal) {
            spotResultIssueInnerService.offlineAppealIssue(issueRequest);
            return NewResponseUtil.makeSuccess(null);
        }
        if (CollectionUtils.isEmpty(spotResultIssueInnerService.offlineIssue(issueRequest))){
            issueRequest.setNormal(true);
            spotResultIssueInnerService.offlineIssue(issueRequest);
        }
        return NewResponseUtil.makeSuccess(null);
    }
}
