package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.risk.admin.biz.DiscussionBiz;
import com.shuidihuzhu.cf.risk.admin.dao.*;
import com.shuidihuzhu.cf.risk.admin.model.*;
import com.shuidihuzhu.cf.risk.admin.model.enums.DiscussionReasonEnum;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFeignClientV2;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFundStateFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceRefundFeignClient;
import com.shuidihuzhu.cf.finance.client.response.model.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDiscussionEnum;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.finance.model.CfDiscussionRefund;
import com.shuidihuzhu.cf.finance.model.vo.DonationAmountInFenVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import com.shuidihuzhu.cf.risk.admin.model.vo.DiscussionInfoVO;
import com.shuidihuzhu.cf.risk.admin.service.EventCenterService;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.client.constant.MQTagCons;
import com.shuidihuzhu.client.model.event.DiscussionEndEvent;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;
import com.shuidihuzhu.pf.common.v2.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DiscussionBizImpl implements DiscussionBiz {

    @Autowired
    private DiscussionDao discussionDao;
    @Autowired
    private DiscussionStatDao discussionStatDao;
    @Autowired
    private RiskOperationRecordDao operationRecordDao;
    @Autowired
    private CfRiskCommentDao riskCommentDao;
    @Autowired
    private DiscussionCheckRecordDao checkRecordDao;
    @Autowired
    private CfFinanceFeignClientV2 cfFinanceFeignClientV2;
    @Autowired
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private CfFinanceFundStateFeignClient cfFinanceFundStateFeignClient;
    @Autowired
    private CfFinanceRefundFeignClient cfFinanceRefundFeignClient;
    @Autowired
    private EventCenterService eventCenterService;
    @Autowired
    private DiscussionContentDAO discussionContentDAO;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler cfRiskRedissonHandler;

    private static final int DISCUSSION_TITLE_LENGTH_LIMIT = 30;
    private static final int DISCUSSION_DESC_LENGTH_LIMIT = 1000;
    private static final int DISCUSSION_IMAGE_SIZE_LIMIT = 9;

    @Override
    public Response openDiscussion(int userId, Discussion discussion) {
        //参数校验
        int caseId = discussion.getCaseId();
        String infoUuid = discussion.getInfoUuid();
        if (caseId <= 0 || StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), ErrorCode.SYSTEM_PARAM_ERROR.getMsg(), null);
        }
        int reason = discussion.getReason();
        if (reason == 8 && StringUtils.isBlank(discussion.getOtherFundReason())) {
            log.info("otherFundReason is blank infoUuid:{}", infoUuid);
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "其他资金原因不能为空", null);
        }
        if (reason == 10 && StringUtils.isBlank(discussion.getOtherReason())) {
            log.info("otherReason is blank infoUuid:{}", infoUuid);
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "其他原因不能为空", null);

        }
        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isNotEmpty(discussions) && discussions.get(0).getStatus() == 1) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "有开启状态的评议存在", null);
        }
        discussion.setOtherReason(StringUtils.trimToEmpty(discussion.getOtherReason()));
        discussion.setOtherFundReason(StringUtils.trimToEmpty(discussion.getOtherFundReason()));
        discussion.setImages("");
        discussion.setInsteadTitle(StringUtils.trimToEmpty(discussion.getInsteadTitle()));
        discussion.setInsteadDescription(StringUtils.trimToEmpty(discussion.getInsteadDescription()));
        discussion.setInsteadImages(StringUtils.trimToEmpty(discussion.getInsteadImages()));
        String organization = seaAccountService.getOrganization(userId);
        String name = seaAccountService.getName(userId);
        if(discussion.getInsteadStatus() == 1){
            //a.标题输入限制30字，必填
            if (StringUtils.isBlank(discussion.getInsteadTitle()) || discussion.getInsteadTitle().length() > DISCUSSION_TITLE_LENGTH_LIMIT){
                return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "标题为空或超出30个字", null);
            }
            //b.描述限制1000字，必填
            if (StringUtils.isBlank(discussion.getInsteadDescription()) || discussion.getInsteadDescription().length() > DISCUSSION_DESC_LENGTH_LIMIT) {
                return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "描述为空或超出1000个字", null);
            }
            //c.图片限制9张，非必填
            List<String> discussionImgList = Splitter.on(",").splitToList(discussion.getInsteadImages());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(discussionImgList) && discussionImgList.size() > DISCUSSION_IMAGE_SIZE_LIMIT) {
                return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "图片超过9张限制", null);
            }
        }
        //案例金额是否大于0
        boolean donationAmountCheck = checkDonationAmount(infoUuid);
        if (!donationAmountCheck) {
            log.info("案例金额不大于0 infoUuid:{}", infoUuid);
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "案例金额不大于0", null);
        }

        //案例资金状态是否合法
        com.shuidihuzhu.cf.finance.client.response.FeignResponse fundStatusCheck = checkFundStatus(caseId);
        if (fundStatusCheck != null && fundStatusCheck.notOk()) {
            log.info("案例资金状态不合法 infoUuid:{}", infoUuid);
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), fundStatusCheck.getMsg(), null);
        }
        try {
            log.info("案例暂停打款 infoUuid:{}, caseId:{}", infoUuid, caseId);
            //暂停打款
            com.shuidihuzhu.cf.finance.client.response.FeignResponse response =
                    cfFinancePauseFeignClient.addPause(userId, infoUuid, caseId, 11, Collections.singletonList(112), CfOperatingRecordEnum.Role.OPERATOR.getCode(), name, "评议通道开启", true);
            log.info("案例暂停打款 responseCode:{}, infoUuid:{}, caseId:{}", response == null ? -1 : response.getCode(), infoUuid, caseId);
            if (response != null && response.notOk()) {
                return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "案例暂停打款失败", null);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        //评议入库
        discussion.setStatus(1);
        discussionDao.save(discussion);
        Discussion saveDiscussion = discussionDao.getInfos();
        if(discussion.getInsteadStatus() == 1) {
            DiscussionContentVO discussionContentVO = new DiscussionContentVO(saveDiscussion.getId(), 1, organization + "-" + name);
            discussionContentVO.buildInfo(StringUtils.trimToEmpty(discussion.getInsteadTitle()),
                    StringUtils.trimToEmpty(discussion.getInsteadDescription()),
                    StringUtils.trimToEmpty(discussion.getInsteadImages()));
            discussionContentDAO.save(discussionContentVO);
        }
        //添加操作记录
        CrowdfundingApprove crowdfundingApprove = new CrowdfundingApprove();
        crowdfundingApprove.setCrowdfundingId(discussion.getCaseId());
        crowdfundingApprove.setComment(String.format("开启评议通道,原因：“%s”,通道关闭时长：%d个小时", DiscussionReasonEnum.findOfCode(discussion.getReason()), discussion.getCloseHour()));
        crowdfundingApprove.setOprid(userId);
        crowdfundingApprove.setOprtime(new Date());
        crowdfundingApprove.setStatus(0);
        try {
            log.info("添加操作备注 crowdfundingApprove :{}", crowdfundingApprove);
            com.shuidihuzhu.cf.client.response.FeignResponse<Integer> response = crowdfundingFeignClient.insertCrowdfundingApprove(crowdfundingApprove);
            log.info("添加操作备注 responseCode:{}", response == null ? -1 : response.getCode());
        } catch (Exception e) {
            log.error("", e);
        }

        //消息发送
        try {
            if (caseId == 4374464 || caseId == 4394786) {
                //特殊案例不发消息
            } else {
                eventCenterService.eventSend(userId, UserOperationTypeEnum.DISCUSSION_OPEN.getCode(), caseId, infoUuid);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        DiscussionCheckRecord records = new DiscussionCheckRecord(discussion.getId(), 0, "", organization + name);
        records.buildInfo(StringUtils.trimToEmpty(discussion.getTitle()),
                StringUtils.trimToEmpty(discussion.getDescription()),
                StringUtils.trimToEmpty(discussion.getImages()));
        //评议审核记录表
        checkRecordDao.save(records);
        //延时mq
        DiscussionEndEvent discussionEndEvent = DiscussionEndEvent.builder()
                .caseId(discussion.getCaseId())
                .discussionId(discussion.getId())
                .build();
        MaliMQComponent.builder()
                .setTags(MQTagCons.DISCUSSION_END_MSG)
                .addKey(MQTagCons.DISCUSSION_END_MSG, System.currentTimeMillis())
                .setPayload(discussionEndEvent)
                .setDelayTime(discussion.getCloseHour() * 60 * 60 * 1000L)
                .send();
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response closeDiscussion(int userId, int caseId, String infoUuid) {
        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isEmpty(discussions)) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "案例没有相关评议", null);
        }
        String name = seaAccountService.getName(userId);
        String organization = seaAccountService.getOrganization(userId);
        Discussion discussion = discussions.get(0);
        if (discussion.getCheckStatus() == 2 && discussion.getStatus() == 1) {
            return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "评议申请已审核通过，无法关闭通道", null);
        }
        try {
            log.info("案例恢复打款 infoUuId:{}, caseId:{}", infoUuid, caseId);
            //恢复打款
            com.shuidihuzhu.cf.finance.client.response.FeignResponse response =
                    cfFinancePauseFeignClient.recoverBySourceType(infoUuid, caseId, 11,
                            CfDrawCashPauseRecordEnum.RecordStatusEnum.RECOVER.getCode(), CfOperatingRecordEnum.Role.OPERATOR.getCode(),
                            userId, name, "评议通道关闭");
            log.info("案例恢复打款 responseCode:{},infoUuId:{}, caseId:{}", response == null ? -1 : response.getCode(), infoUuid, caseId);
            if (response != null && response.notOk()) {
                return NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "案例恢复打款失败", null);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        int result = discussionDao.updateStatus(0, 1, discussion.getId());
        cfRiskRedissonHandler.del(Constant.DISCUSSION_KEY + caseId);
        DiscussionCheckRecord record = new DiscussionCheckRecord(discussion.getId(), 0, "", organization + name);
        record.buildInfo(StringUtils.trimToEmpty(discussion.getTitle()),
                StringUtils.trimToEmpty(discussion.getDescription()),
                StringUtils.trimToEmpty(discussion.getImages()));
        checkRecordDao.save(record);
        DiscussionEndEvent discussionEndEvent = DiscussionEndEvent.builder()
                .caseId(discussion.getCaseId())
                .discussionId(discussion.getId())
                .build();
        MaliMQComponent.builder()
                .setTags(MQTagCons.DISCUSSION_END_MSG)
                .addKey(MQTagCons.DISCUSSION_END_MSG, System.currentTimeMillis())
                .setPayload(discussionEndEvent)
                .send();
        return result > 0 ? NewResponseUtil.makeSuccess(null)
                : NewResponseUtil.makeResponse(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), "案例状态不是开启状态", null);
    }

    @Override
    public String checkCase(int caseId, String infoUuid) {
        boolean donationAmountCheck = checkDonationAmount(infoUuid);
        if (!donationAmountCheck) {
            return "当前案例筹款金额<=0";
        }
        com.shuidihuzhu.cf.finance.client.response.FeignResponse fundStatusCheck = checkFundStatus(caseId);
        if (fundStatusCheck != null && fundStatusCheck.notOk()) {
            return fundStatusCheck.getMsg();
        }
        return null;
    }

    @Override
    public int getStatus(int caseId) {
        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isEmpty(discussions)) {
            return 0;
        }
        return discussions.get(0).getStatus();
    }

    @Override
    public Discussion findByCaseId(int caseId) {
        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isEmpty(discussions)) {
            return null;
        }
        Discussion discussion = discussions.get(0);
        long discussionId = discussion.getId();
        long closeTime = discussion.getCloseTime().getTime();
        long currentTime = System.currentTimeMillis();
        //如果已经过期就关闭评议
        if (discussion.getStatus() == 1
                && discussion.getCheckStatus() == 2
                && closeTime < System.currentTimeMillis()
                && closeTime > 946656000000L) {
            log.info("discussion is invalid discussionId:{}, closeTime:{}, currentTime:{}", discussionId, closeTime, currentTime);
            int result = discussionDao.updateStatus(2, 1, discussionId);
            if (result > 0) {
                discussion.setStatus(2);
            }
            cfRiskRedissonHandler.del(Constant.DISCUSSION_KEY + caseId);
            try {
                log.info("案例恢复打款 infoUuId:{}, caseId:{}", discussion.getInfoUuid(), caseId);
                //恢复打款
                com.shuidihuzhu.cf.finance.client.response.FeignResponse response =
                        cfFinancePauseFeignClient.recoverBySourceType(discussion.getInfoUuid(), caseId, 11,
                                CfDrawCashPauseRecordEnum.RecordStatusEnum.RECOVER.getCode(), CfOperatingRecordEnum.Role.OPERATOR.getCode(),
                                102, "系统", "评议通道时间到关闭");
                log.info("案例恢复打款 responseCode:{},infoUuId:{}, caseId:{}", response == null ? -1 : response.getCode(), discussion.getInfoUuid(), caseId);
                if (response != null && response.notOk()) {
                    return null;
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
        DiscussionCheckRecord lastRecord = checkRecordDao.findLastByDiscussId(discussionId);
        if (lastRecord != null) {
            discussion.setOperator(lastRecord.getOperator());
            //审核状态=拒绝  并且评议审核状态=下发
            if (lastRecord.getStatus() == 3 && discussion.getCheckStatus() != 1) {
                discussion.setRefuseReason(lastRecord.getRefuseReason());
            }
            discussion.setCheckTime(lastRecord.getCreateTime());
        }
        int applyTime = discussionDao.countByCaseId(caseId);
        discussion.setApplyTime(applyTime);
        return discussion;
    }

    @Override
    public boolean hasDiscussion(int caseId) {
        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isEmpty(discussions)) {
            return false;
        }
        Discussion discussion = discussions.get(0);
        return discussion.getStatus() == 1;
    }

    @Override
    public Response<List<DiscussionCompareVO>> listByCaseId(int caseId) {
        List<Discussion> discussions = discussionDao.findByCaseId(caseId);
        if (CollectionUtils.isEmpty(discussions)) {
            return null;
        }
        List<Long> discussionIds = discussions.stream().map(Discussion::getId).collect(Collectors.toList());
        List<DiscussionCompareVO> discussionCompareList = Lists.newArrayList();
        for(Long discussionId : discussionIds){
            DiscussionContentVO insteadContent = discussionContentDAO.getByDiscussionId(discussionId, 1);
            DiscussionContentVO userContent = discussionContentDAO.getByDiscussionId(discussionId, 2);
            DiscussionCompareVO discussionCompareVO = new DiscussionCompareVO();
            if(insteadContent != null) {
                discussionCompareVO.setInsteadTime(insteadContent.getCreateTime());
                discussionCompareVO.setOperator(insteadContent.getOperator());
                discussionCompareVO.setInsteadTitle(insteadContent.getTitle());
                discussionCompareVO.setInsteadDescription(insteadContent.getDescription());
                discussionCompareVO.setInsteadImages(insteadContent.getImages());
            }
            if(userContent != null) {
                discussionCompareVO.setCreateTime(userContent.getCreateTime());
                discussionCompareVO.setTitle(userContent.getTitle());
                discussionCompareVO.setDescription(userContent.getDescription());
                discussionCompareVO.setImages(userContent.getImages());
            }
            discussionCompareList.add(discussionCompareVO);
        }
        return NewResponseUtil.makeSuccess(discussionCompareList);
    }

    @Override
    public DiscussionInfoVO getInfoById(long id) {
        Discussion discussion = discussionDao.findById(id);
        if (discussion == null){
            return null;
        }
        DiscussionStat discussionStat = discussionStatDao.findById(id);
        if (discussionStat == null){
            return new DiscussionInfoVO();
        }
        int approvalCount = discussionStat.getApprovalCount();
        int opposeCount = discussionStat.getOpposeCount();
        int total = approvalCount + opposeCount;
        DiscussionInfoVO infoVO = new DiscussionInfoVO();
        infoVO.setTotal(total);
        infoVO.setApprovalCount(approvalCount);
        if (approvalCount > 0){
            infoVO.setApprovalRate(new BigDecimal(approvalCount).divide(new BigDecimal(total), 2, RoundingMode.HALF_UP).doubleValue() * 100);
        }
        infoVO.setOpposeCount(opposeCount);
        if (opposeCount > 0){
            infoVO.setOpposeRate(new BigDecimal(opposeCount).divide(new BigDecimal(total), 2, RoundingMode.HALF_UP).doubleValue() * 100);
        }
        infoVO.setCommentCount(discussionStat.getCommentCount());
        int caseId = discussion.getCaseId();
        List<CfRiskComment> commentList = riskCommentDao.findByDiscussionId(id);
        if (CollectionUtils.isNotEmpty(commentList)) {
            List<Long> commentIds = commentList.stream().map(CfRiskComment::getId).collect(Collectors.toList());
            List<Long> userIdList = operationRecordDao.listByBizIdAndType(commentIds, 1);
            if (CollectionUtils.isNotEmpty(userIdList)) {
                Set<Long> distinctCount = new HashSet<>(userIdList);
                infoVO.setDistinctPraiseCount(distinctCount.size());
            }
        }
        com.shuidihuzhu.cf.finance.client.response.FeignResponse<CfDiscussionRefund> refundResponse =
                cfFinanceRefundFeignClient.getDiscussionRefund(caseId);
        if (refundResponse.ok() && refundResponse.getData() != null) {
            CfDiscussionRefund refund = refundResponse.getData();
            int realityRefundAmount = refund.getRealityRefundAmount();
            infoVO.setRefundAmount(new BigDecimal(realityRefundAmount).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).doubleValue());
        }
        return infoVO;
    }

    //检查案例当前筹款金额是否为0
    private boolean checkDonationAmount(String infoUuid) {
        FeignResponse<DonationAmountInFenVo> donationAmountResponse = null;
        try {
            log.info("检查案例筹款金额 infoUuid:{}", infoUuid);
            donationAmountResponse = cfFinanceFeignClientV2.getDonationAmountInFen(infoUuid);
            log.info("检查案例筹款金额 responseCode:{}, infoUuid:{}", donationAmountResponse == null ? -1 : donationAmountResponse.getCode(), infoUuid);
        } catch (Exception e) {
            log.error("", e);
        }
        if (donationAmountResponse == null || donationAmountResponse.notOk() || donationAmountResponse.getData() == null) {
            log.error("检查案例筹款金额 请求失败");
            return false;
        }
        DonationAmountInFenVo donationAmount = donationAmountResponse.getData();
        if (donationAmount.getDonationAmountInFen() <= 0) {
            log.info("case donationAmount illegal infoUuid:{}", infoUuid);
            return false;
        }
        return true;
    }

    //检查案例当前资金状态是否合法
    private com.shuidihuzhu.cf.finance.client.response.FeignResponse checkFundStatus(int caseId) {
        com.shuidihuzhu.cf.finance.client.response.FeignResponse cfFundStateFeignResponse = null;
        try {
            log.info("检查案例资金状态 caseId:{}", caseId);
            cfFundStateFeignResponse = cfFinanceFundStateFeignClient.checkDiscussionFinanceState(caseId, CfDiscussionEnum.DiscussionType.START_CHANELL);
            log.info("检查案例资金状态 responseCode:{}, caseId:{}",
                    cfFundStateFeignResponse == null ? -1 : cfFundStateFeignResponse.getCode(), caseId);
        } catch (Exception e) {
            log.error("", e);
        }
        return cfFundStateFeignResponse;
    }

}
