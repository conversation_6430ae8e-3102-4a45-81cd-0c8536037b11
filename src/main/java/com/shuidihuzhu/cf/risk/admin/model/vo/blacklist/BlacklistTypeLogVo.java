package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeLog;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@NoArgsConstructor
@ApiModel(description = "黑名单类型日志")
public class BlacklistTypeLogVo {

    public BlacklistTypeLogVo(RiskBlacklistTypeLog riskBlacklistTypeLog) {
        this.modifyContent = riskBlacklistTypeLog.getModifyContent();
        this.operateName = riskBlacklistTypeLog.getOperateName();
        this.createTime = DateUtil.getDate2LStr(riskBlacklistTypeLog.getCreateTime());
    }

    @ApiModelProperty("修改内容")
    private String modifyContent;

    @ApiModelProperty("操作人")
    private String operateName;
    @ApiModelProperty("操作时间")
    private String createTime;

}
