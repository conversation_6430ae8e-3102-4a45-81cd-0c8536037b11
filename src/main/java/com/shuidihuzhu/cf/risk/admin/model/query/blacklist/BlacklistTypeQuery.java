package com.shuidihuzhu.cf.risk.admin.model.query.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/7/16 17:54
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(description = "黑名单类型查询")
public class BlacklistTypeQuery extends PageQuery {

    @ApiParam("黑名单类型")
    private String typeName;
    @ApiParam("限制动作类型")
    private Long actionId;
    @ApiParam("状态，0 启用 1 弃用 null默认")
    private Integer status;

}
