package com.shuidihuzhu.cf.risk.admin.rule.utils;

import com.google.common.collect.Maps;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ClassUtils;

import java.util.concurrent.ConcurrentMap;

@Slf4j
public class GroovyScriptUtil {

    public static ConcurrentMap<String, GroovyObject> passedClassMap = Maps.newConcurrentMap();

    public static GroovyClassLoader groovyClassLoader;

    // 初始化GroovyClassLoader
    static {
        ClassLoader parent = ClassUtils.getDefaultClassLoader();
        groovyClassLoader = new GroovyClassLoader(parent);
    }

    /**
     * 
     * 加载 groovy script.
     * 
     * @param script string of groovy script
     * @return {@link GroovyObject}
     * <AUTHOR>
     * 2016年8月2日
     */
    public static GroovyObject loadGroovyObject(String identifier, String script) {
        return passedClassMap.computeIfAbsent(identifier, key -> {
            log.info("loadGroovyObject:{}", identifier);
            Class<Script> groovyClass = groovyClassLoader.parseClass(script);
            try {
                return groovyClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                log.error("创建groovy实例异常", e);
                throw new RuntimeException(e);
            }
        });
    }

    public static Object invokeMethod(String identifier, String script, String method, Object[] args) {
        return loadGroovyObject(identifier, script).invokeMethod(method, args);
    }

    public static void main(String[] args) {
        String script = "class RuleG {\n" +
                "\tstatic def check(def data, def lists) {\n" +
                "\t\tfor (def result in data.abstractions.case_alot_ip_users_count.aggResult) {\n" +
                "\t\t\tif (result.aggValue.longValue() >= 5) {\n" +
                "\t\t\t\treturn true\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\n" +
                "\t\treturn false\n" +
                "\t}\n" +
                "}";
        invokeMethod("123", script, "check", new Object[]{null, null});
    }

}
