package com.shuidihuzhu.cf.risk.admin.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-15 14:20
 **/
@Data
@ApiModel
public class RiskQcBaseInfo {

    @ApiModelProperty("id")
    private long id;
    @ApiModelProperty("案例id")
    private long caseId;
    @ApiModelProperty("质检类型")
    private int qcType;
    @ApiModelProperty("工单类型")
    private int orderType;
    @ApiModelProperty("被质检人姓名")
    private String qcByName;
    @ApiModelProperty("被质检人标识")
    private String qcUniqueCode;

    @ApiModelProperty("微信1v1任务id")
    private long taskId;
}
