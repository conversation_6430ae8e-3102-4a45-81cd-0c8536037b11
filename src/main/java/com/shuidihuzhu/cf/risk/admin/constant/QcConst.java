package com.shuidihuzhu.cf.risk.admin.constant;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
public interface QcConst {

    interface OrderExt {
        @ApiModelProperty("疑似无效质检工单 {0: 未确定, 1: 无效, 2: 有效}")
        String seenInvalidOrder = "seenInvalidOrder";

        @ApiModelProperty("是否远程发起 {1: 是, 0: 不是}")
        String remoteRaise = "remoteRaise";

        @ApiModelProperty("是否有科室归一结果")
        String deptClassifySuccess = "deptClassifySuccess";

        @ApiModelProperty("科室质检-院区搜索")
        String deptHospitalName = "deptHospitalName";
    }

    String QC_WORK_ORDER_CREATE_4_RECODING_MSG = "cf-risk-admin_qc_work_order_create_4_recording_msg";
}
