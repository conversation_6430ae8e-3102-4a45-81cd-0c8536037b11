package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@NoArgsConstructor
@ApiModel(description = "黑名单数据-黑名单类型详情")
public class BlacklistDataTypeDetailVo {

    @ApiModelProperty("黑名单数据id")
    private Long id;

    @ApiModelProperty("修改后的类型ids")
    private List<DataTypeBaseInfo> types;

    @ApiModel(description = "黑名单类型基础数据")
    @Data
    public static final class DataTypeBaseInfo {
        @ApiModelProperty("id")
        private Long id;
        @ApiModelProperty("类型")
        private String typeName;
        @ApiModelProperty("类型关联的动作")
        private List<String> actions;
        @ApiModelProperty("类型关联的动作详情")
        private List<BlacklistTypeActionRefDto> actionList;
    }

}
