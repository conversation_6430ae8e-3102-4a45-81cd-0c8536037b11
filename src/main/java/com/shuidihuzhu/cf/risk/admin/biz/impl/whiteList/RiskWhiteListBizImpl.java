package com.shuidihuzhu.cf.risk.admin.biz.impl.whiteList;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.risk.admin.biz.whiteList.RiskWhiteListBiz;
import com.shuidihuzhu.cf.risk.admin.dao.whiteList.RiskWhiteListDao;
import com.shuidihuzhu.cf.risk.admin.model.query.whiteList.WhiteListQuery;
import com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@Service
public class RiskWhiteListBizImpl implements RiskWhiteListBiz {

    @Autowired
    private RiskWhiteListDao riskWhiteListDao;
    @Resource
    private ShuidiCipher shuidiCipher;


    @Override
    public int save(RiskWhiteListDto riskWhiteListDto) {
        return riskWhiteListDao.save(riskWhiteListDto);
    }

    @Override
    public int update(RiskWhiteListDto riskWhiteListDto) {
        riskWhiteListDto.setLastOperateTime(DateUtil.nowTime());
        return riskWhiteListDao.update(riskWhiteListDto);
    }

    @Override
    public int updateExpireTime(Date expireTime, long id, String operator) {
        if (id <= 0 ) {
            return 0;
        }
        return riskWhiteListDao.updateExpireTime(expireTime, id, operator);
    }

    @Override
    public RiskWhiteListDto getById(long id) {
        if (id <= 0) {
            return null;
        }
        return riskWhiteListDao.getById(id);
    }

    @Override
    public Page<RiskWhiteListDto>  getList(WhiteListQuery whiteListQuery) {
        return PageHelper.startPage(whiteListQuery.getPageNo(), whiteListQuery.getPageSize())
                .doSelectPage(() -> riskWhiteListDao.listByConditions(whiteListQuery));
    }

    @Override
    public RiskWhiteListDto getByCipherMobileAndType(String mobile, byte type) {
        return riskWhiteListDao.getByCipherMobile(shuidiCipher.encrypt(mobile), type, new Date());
    }

    @Override
    public RiskWhiteListDto getByCipherIdCardAndType(String idCard, byte type) {
        return riskWhiteListDao.getByCipherIdCard(shuidiCipher.encrypt(idCard.toUpperCase()), type, new Date());
    }
}
