package com.shuidihuzhu.cf.risk.admin.model.query.list;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/7/28 16:46
 */
@Data
public class ListDepartmentCaseQuery {

    @NotBlank(message = "区号不能为空")
    @ApiModelProperty("座机区号")
    private String areaCode;

    @NotBlank(message = "座机号不能为空")
    @ApiModelProperty("座机号")
    private String landline;

    @ApiModelProperty("座机分机号")
    private String extension;

}
