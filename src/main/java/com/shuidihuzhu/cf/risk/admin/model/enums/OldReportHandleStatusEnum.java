package com.shuidihuzhu.cf.risk.admin.model.enums;

public enum OldReportHandleStatusEnum {

    DEFAULT(0,"默认"),
    REPORT_FOLLOW(1,"举报跟进中"),
    DEAL_COMPLETE(2,"处理完成"),
    NO_DEAL(3,"不需要处理"),
    LOST_CONTACT(4,"已失联");

    int code;
    String description;

    OldReportHandleStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code){
        for (OldReportHandleStatusEnum value : values()) {
            if (value.getCode() == code){
                return value.getDescription();
            }
        }
        return "";
    }
}
