package com.shuidihuzhu.cf.risk.admin.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2022/3/25 下午4:18
 */
/**
    * 质检工单&举报工单录音表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderRecordingDO {
    /**
    * id
    */
    private Long id;

    /**
    * 工单id
    */
    private Long workOrderId;

    /**
    * 手机号
    */
    private String mobile;

    /**
    * 录音文件地址
    */
    private String videoUrl;

    /**
    * 接通状态
    */
    private Integer phoneStatus;

    /**
    * 录音的扩展信息 WorkOrderRecordingModel
    */
    private WorkOrderRecordingModel recordingExt;

    /**
    * 是否逻辑删除
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    public WorkOrderRecordingDO(Long workOrderId, WorkOrderRecordingModel recordingExt) {
        this.workOrderId = workOrderId;
        this.mobile = recordingExt.getMobile();
        this.videoUrl = recordingExt.getVideoUrl();
        this.phoneStatus = recordingExt.getPhoneStatus();
        this.recordingExt = recordingExt;
    }

    public String getVideoUrl() {
        return Optional.ofNullable(videoUrl).orElse("");
    }

    public String getMobile() {
        return Optional.ofNullable(mobile).orElse("");
    }
}
