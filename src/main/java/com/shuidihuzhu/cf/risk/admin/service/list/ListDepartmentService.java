package com.shuidihuzhu.cf.risk.admin.service.list;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.cache.CacheRefreshService;
import com.shuidihuzhu.cf.risk.admin.cache.constant.CacheRefreshTopic;
import com.shuidihuzhu.cf.risk.admin.dao.list.RiskListDepartmentDao;
import com.shuidihuzhu.cf.risk.admin.dao.list.RiskListDepartmentLogDao;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartment;
import com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartmentLog;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.list.ListDepartmentQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.list.ListDepartmentBase;
import com.shuidihuzhu.cf.risk.admin.model.vo.list.ListDepartmentEditVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.list.ListDepartmentLogVo;
import com.shuidihuzhu.cf.risk.admin.model.vo.list.ListDepartmentVo;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.model.admin.list.ListDepartmentDto;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/28 20:00
 */
@Validated
@Service
@Slf4j
public class ListDepartmentService {

    private static final String UPDATE_LEVEL_CONF_KEY = "cf_risk_admin_list_department_save";
    private static final long UPDATE_LEVEL_CONF_KEY_LEAVE_TIME = 10 * 1000;

    @Resource
    private RiskListDepartmentDao riskListDepartmentDao;
    @Resource
    private RiskListDepartmentLogDao listDepartmentLogDao;
    @Resource
    private SeaAccountService seaAccountService;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private CacheRefreshService<List<String>> cacheRefreshService;

    public List<ListDepartmentDto> listByAreaLandline(@NotBlank String areaCode, @NotBlank String landline) {
        return riskListDepartmentDao.listByAreaCodeLandline(areaCode, landline).stream().map(listDepartment -> {
            ListDepartmentDto listDepartmentDto = new ListDepartmentDto();
            BeanUtils.copyProperties(listDepartment, listDepartmentDto);
            return listDepartmentDto;
        }).collect(Collectors.toList());
    }

    public PageResult<ListDepartmentVo> queryByPage(ListDepartmentQuery query) {
        Page<RiskListDepartment> pages = PageHelper.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPage(() -> riskListDepartmentDao.listByOptional(query));
        return new PageResult<>(pages, pages.getResult().stream().map(listDepartment -> {
            ListDepartmentVo listDepartmentVo = new ListDepartmentVo();
            BeanUtils.copyProperties(listDepartment, listDepartmentVo);
            return listDepartmentVo;
        }).collect(Collectors.toList()));
    }

    public ListDepartmentEditVo getDetail(Long id) {
        RiskListDepartment riskListDepartment = riskListDepartmentDao.selectByPrimaryKey(id);
        if (riskListDepartment == null) {
            return null;
        }
        ListDepartmentEditVo listDepartmentEditVo = new ListDepartmentEditVo();
        BeanUtils.copyProperties(riskListDepartment, listDepartmentEditVo);
        return listDepartmentEditVo;
    }

    public void del(Long departmentId, long adminUserId) {
        riskListDepartmentDao.delById(departmentId);
        saveLog(departmentId, "删除科室电话", adminUserId);
        RiskListDepartment riskListDepartment = riskListDepartmentDao.selectByPrimaryKey(departmentId);
        cacheRefreshService.publishTopic(CacheRefreshTopic.LIST_DEPARTMENT_MODIFY,
                Lists.newArrayList(riskListDepartment.getAreaCode(), riskListDepartment.getLandline()));
    }

    public void save(ListDepartmentBase department) {
        doOperate(department, riskListDepartment -> riskListDepartmentDao.insertSelective(riskListDepartment),
                "新增科室电话", null, null);
    }

    public void save(ListDepartmentBase department, Long operateId, String operateName) {
        doOperate(department, riskListDepartment -> riskListDepartmentDao.insertSelective(riskListDepartment),
                "新增科室电话", operateId, operateName);
    }

    public void edit(ListDepartmentEditVo editVo) {
        RiskListDepartment oldDepartment = riskListDepartmentDao.selectByPrimaryKey(editVo.getId());
        doOperate(editVo, riskListDepartment -> riskListDepartmentDao.updateById(riskListDepartment),
                "编辑科室电话", null, null);
        cacheRefreshService.publishTopic(CacheRefreshTopic.LIST_DEPARTMENT_MODIFY,
                Lists.newArrayList(oldDepartment.getAreaCode(), oldDepartment.getLandline()));
    }

    private void doOperate(ListDepartmentBase department, Consumer<RiskListDepartment> consumer, String operateContent,
                           Long operateId, String operateName){
        String extension = Strings.nullToEmpty(department.getExtension()).trim();
        String key = UPDATE_LEVEL_CONF_KEY;
        String identify = null;
        Long adminUserId = operateId;
        String adminName = operateName;
        if (adminUserId == null || adminName == null) {
            SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(ContextUtil.getAdminLongUserId());
            adminUserId = userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId();
            adminName = userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg();
        }
        Long departmentId = 0L;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_LEVEL_CONF_KEY_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请稍后重试");
            }
            RiskListDepartment listDepartment = riskListDepartmentDao.getByUniqueTel(
                    department.getAreaCode(), department.getLandline(), extension);
            if ((department.getClass() == ListDepartmentBase.class && listDepartment != null) ||
                    (department.getClass() == ListDepartmentEditVo.class && listDepartment != null &&
                            !Objects.equals(listDepartment.getId(), ((ListDepartmentEditVo)department).getId()))) {
                throw new IllegalArgumentException("该座机号已经存在，不允许重复添加");
            }
            RiskListDepartment riskListDepartment = new RiskListDepartment();
            BeanUtils.copyProperties(department, riskListDepartment);
            riskListDepartment.setOperatorId(adminUserId);
            riskListDepartment.setOperatorName(adminName);
            //处理其他非英文逗号
            riskListDepartment.setDepartments(riskListDepartment.getDepartments().replaceAll("[︐﹐，]", ","));
            consumer.accept(riskListDepartment);
            departmentId = riskListDepartment.getId();
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        saveLog(departmentId, operateContent, adminUserId, adminName);
    }

    public List<ListDepartmentLogVo> queryListDepartmentLog(Long listDepartmentId){
        return listDepartmentLogDao.listByListDepartmentId(listDepartmentId).stream()
                .map(ListDepartmentLogVo::new).collect(Collectors.toList());
    }

    private void saveLog(Long dataId, String modifyContent, long adminUserId) {
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        saveLog(dataId, modifyContent, userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId(),
                userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg());
    }

    private void saveLog(Long dataId, String modifyContent, Long operateId, String operateName) {
        RiskListDepartmentLog riskListDepartmentLog = new RiskListDepartmentLog();
        riskListDepartmentLog.setListDepartmentId(dataId);
        riskListDepartmentLog.setModifyContent(modifyContent);
        riskListDepartmentLog.setOperateId(operateId);
        riskListDepartmentLog.setOperateName(operateName);
        listDepartmentLogDao.insertSelective(riskListDepartmentLog);
    }

}
