package com.shuidihuzhu.cf.risk.admin.model.qc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RiskQcCheckedVideoInfo {
    private long id;
    private long workOrderId;
    private String checkedId;

    private String extInfo;


    @Data
    public static class ExtInfo{
        @ApiModelProperty("语音转译准确的录音id")
        List<Long> asrCorrectIds;
        @ApiModelProperty("语音转译不准确的录音id")
        List<Long> asrIncorrectIds;
    }
}
