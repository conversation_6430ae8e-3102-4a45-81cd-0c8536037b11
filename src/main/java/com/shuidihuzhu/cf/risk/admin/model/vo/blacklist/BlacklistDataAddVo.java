package com.shuidihuzhu.cf.risk.admin.model.vo.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(description = "黑名单数据-新增")
public class BlacklistDataAddVo extends BlackListDataBaseInfo{

    @NotNull(message = "黑名单类型不能为空")
    @Size(min = 1, message = "黑名单类型至少选择一个")
    @ApiModelProperty("选择的类型ids")
    private List<Long> typeIds;

    @NotBlank(message = "操作原因不能为空")
    @Length(min = 5, max = 200, message = "字数要求在5~200字之间")
    @ApiModelProperty("操作原因")
    private String operateReason;

    @NotNull(message = "限制动作不能为空")
    @ApiModelProperty("限制动作集合")
    private List<BlacklistTypeActionRefDto> actionList;
}
