package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskDiseaseTreatmentProjectBiz;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseTreatmentProjectDao;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Service
@Slf4j
public class RiskDiseaseTreatmentProjectBizImpl implements RiskDiseaseTreatmentProjectBiz {

    @Autowired
    private RiskDiseaseTreatmentProjectDao riskDiseaseTreatmentProjectDao;


    @Override
    public int deleteByDiseaseId(long diseaseId) {
        if (diseaseId <= 0) {
            return 0;
        }
        return riskDiseaseTreatmentProjectDao.deleteByDiseaseId(diseaseId);
    }

    @Override
    public int saveList(List<RiskDiseaseTreatmentProject> treatmentProjects) {
        if (CollectionUtils.isEmpty(treatmentProjects)){
            return 0;
        }
        return riskDiseaseTreatmentProjectDao.saveList(treatmentProjects);
    }

    @Override
    public List<RiskDiseaseTreatmentProject> findByDiseaseId(long diseaseId) {
        if (diseaseId <= 0) {
            return Lists.newArrayList();
        }
        return riskDiseaseTreatmentProjectDao.findByDiseaseId(diseaseId);
    }

    @Override
    public int deleteByIdList(List<Long> needDeleteIdList) {
        if (CollectionUtils.isEmpty(needDeleteIdList)) {
            return 0;
        }
        return riskDiseaseTreatmentProjectDao.deleteByIdList(needDeleteIdList);
    }

    @Override
    public int updateList(List<RiskDiseaseTreatmentProject> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        //此处循环 是因为
        // 1.不允许用复杂的更新语句
        // 2.每次更新条数不多
        log.info("updateList size:{}", updateList.size());
        int result = 0;
        for (RiskDiseaseTreatmentProject treatmentProject : updateList){
            result += riskDiseaseTreatmentProjectDao.update(treatmentProject);
        }
        return result;
    }

    @Override
    public List<RiskDiseaseTreatmentProject> findByProjectName(String projectName) {
        if(StringUtils.isEmpty(projectName)){
            return null;
        }
        return riskDiseaseTreatmentProjectDao.findByProjectName(projectName);
    }
}
