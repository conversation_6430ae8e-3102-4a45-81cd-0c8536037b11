package com.shuidihuzhu.cf.risk.admin.dao.hit;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyCallResult;
import com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * @description:
 * @author: zhengqiu
 * @date: 2021-04-11 17:52
 **/
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskStrategyCallResultDao {

    int insertCallResult(RiskStrategyCallResult record);

}
