package com.shuidihuzhu.cf.risk.admin.model;

import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class RiskPsOperationLog implements PageHasId {
    private long id;
    private long psId; //舆情id
    private String operator; //操作人
    private String action; //事件
    private Timestamp createTime; //操作时间
    private Timestamp updateTime;
    private int isDelete;

    public RiskPsOperationLog(long psId, String operator, String action) {
        this.psId = psId;
        this.operator = operator;
        this.action = action;
    }
}
