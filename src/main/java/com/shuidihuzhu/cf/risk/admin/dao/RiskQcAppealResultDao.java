package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealResultModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/11/16
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcAppealResultDao {
    int addInfo(@Param("workOrderId") long workOrderId, @Param("appealInfo") String appealInfo,
                @Param("appealResult") int appealResult, @Param("qcId")long qcId);



    RiskQcAppealResultModel getByWorkOrderId(@Param("workOrderId") long workOrderId);


    List<RiskQcAppealResultModel> findByWorkOrderIds(@Param("workOrderIdList") List<Long> workOrderIdList);
}
