package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConfLog;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQualitySpotLevelConfLogDao {
    int insertSelective(RiskQualitySpotLevelConfLog record);

    RiskQualitySpotLevelConfLog selectByPrimaryKey(Long id);

    List<RiskQualitySpotLevelConfLog> listByScene(@Param("scene") Integer scene);

}