package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/9/25
 */
@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_standard", columnName = "standard_type")},
descName = "description")
@AllArgsConstructor
public enum RiskQcSecondStandardTypeEnum {

    //
    PHOTO_INFO(1, "图文信息"),
    PATIENT_INFO(2, "患者信息"),
    RECEIVER_INFO(3,"收款人信息"),
    DIAGNOSE_INFO(4,"诊断信息"),
    CAPITAL_INFO(5,"资金用途"),
    COMMUNICATE_INFO(6, "通话录音"),
    OTHER_INFO(7, "其他信息"),
    FRONT_INFO(8, "前置信息"),
    SUBSISTENCE_ALLOWANCE_INFO(9, "低保信息"),
    ADD_LETTERS_INFO(10, "增信信息");




    public static final List<Integer> CODE_LIST =
            Arrays.stream(values()).map(RiskQcSecondStandardTypeEnum::getCode).collect(Collectors.toList());

    public static final Map<Integer, RiskQcSecondStandardTypeEnum> ENUM_LIST =
            Arrays.stream(values()).collect(Collectors.toMap(RiskQcSecondStandardTypeEnum::getCode, Function.identity()));

    @Getter
    int code;
    @Getter
    String description;

    public static String findOfCode(int code) {
        for (RiskQcSecondStandardTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return "";
    }
}
