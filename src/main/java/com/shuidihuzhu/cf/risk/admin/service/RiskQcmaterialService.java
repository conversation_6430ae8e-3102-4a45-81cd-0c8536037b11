package com.shuidihuzhu.cf.risk.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.rholder.retry.*;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.UploadBiz;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.serviceexception.ServiceException;
import com.shuidihuzhu.cf.risk.admin.serviceexception.ServiceExceptionUtils;
import com.shuidihuzhu.cf.risk.admin.util.CosUploadUtil;
import com.shuidihuzhu.cf.risk.admin.util.OkHttpClientUtil;
import com.shuidihuzhu.client.cf.admin.client.CfRecordClient;
import com.shuidihuzhu.client.cf.workorder.config.WorkOrderHelper;
import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @Auther: subing
 * @Date: 2020/10/14
 */
@Service
@Slf4j
public class RiskQcmaterialService {

    public static final String PREFIX = "https://cf-risk-img.shuidichou.com/cf-risk-admin";

    @Autowired
    private RiskQcDetailService riskQcDetailService;
    @Autowired
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;

    @Autowired
    private CfRecordClient cfRecordClient;

    @Autowired
    private CosUploadUtil cosUploadUtil;

    @Autowired
    private WorkOrderStorageService workOrderStorageService;

    @Autowired
    private WonRecordClient wonRecordClient;

    @Autowired
    private UploadBiz uploadBiz;

    public String getJsonString(long workOrderId) throws ServiceException {

        // 新版查询
        String snapshot = getSnapshotV2(workOrderId);
        if (StringUtils.isNotBlank(snapshot)) {
            return snapshot;
        }

        return getSnapshotV1(workOrderId);
    }

    private String getSnapshotV1(long workOrderId) {
        String snapshot;
        long qcId = riskQcDetailService.getQcId(workOrderId);
        if (qcId < 0) {
            return null;
        }
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos =
                riskQcMaterialsInfoBiz.getMaterials(qcId, QcMaterialsKeyEnum.DETAIL_SNAPSHOT.getKey());
        if (CollectionUtils.isEmpty(riskQcMaterialsInfos)) {
            return null;
        }
        RiskQcMaterialsInfo riskQcMaterialsInfo =
                Optional.ofNullable(riskQcMaterialsInfos.get(CollectionUtils.size(riskQcMaterialsInfos) - 1)).orElseGet(RiskQcMaterialsInfo::new);
        snapshot = riskQcMaterialsInfo.getMaterialsValue();
        snapshot = uploadBiz.getTemporalUrlByUrl(snapshot);
        return url2data(snapshot);
    }

    private String getSnapshotV2(long workOrderId) {
        Response<VonStorageVO> storageResp = workOrderStorageService.getLastByType(workOrderId, WorkOrderHelper.Storage.HANDLE_RECORD_ID);
        Optional<Long> handleRecordOption = VonStorageVO.getByData(storageResp.getData(), Long.class);
        if (handleRecordOption.isEmpty()) {
            return null;
        }
        Long handleRecordId = handleRecordOption.get();
        if (handleRecordId <= 0) {
            return null;
        }
        OperationResult<WonRecord> recordResp = wonRecordClient.getById(handleRecordId);
        WonRecord record = recordResp.getData();
        String snapshot = record.getExt("snapshot", String.class);
        if (StringUtils.isBlank(snapshot)) {
            return null;
        }
        snapshot = uploadBiz.getTemporalUrlByUrl(snapshot);
        return url2data(snapshot);
    }

    @Nullable
    private String url2data(String snapshot) {
        if (snapshot.startsWith(PREFIX)) {
            try {
                okhttp3.Response response = OkHttpClientUtil.getInstance().doGet(snapshot);
                log.info("OkHttpClientUtil.doGet response:{}", JSON.toJSONString(response));
                if (Objects.isNull(response)) {
                    throw ServiceExceptionUtils.createViewException("网络请求失败，请重试");
                }
                ResponseBody body = response.body();
                if (body == null) {
                    throw ServiceExceptionUtils.createViewException("网络请求失败，请重试");
                }
                return body.string();
            } catch (IOException e) {
                log.error("error getting snapshot", e);
            }
        }
        return null;
    }

    @Async("asyncSaveSnapshot")
    public void saveSnapshotWithRetry(Long qcOrderId, long qcId, int caseId, long workOrderId) {
        saveNeedQcTypes(qcOrderId, caseId, workOrderId);
        try {
            RetryerBuilder.<Boolean>newBuilder()
//                    .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS,2,TimeUnit.SECONDS))
                    .withWaitStrategy(WaitStrategies.fibonacciWait(1000, 12, TimeUnit.SECONDS))
                    .withStopStrategy(StopStrategies.stopAfterDelay(1,TimeUnit.MINUTES))
                    .retryIfException()
                    .retryIfResult(success -> !success)
                    .build()
                    .call(() -> saveSnapshot(qcId, caseId, workOrderId));
        } catch (Exception e) {
            log.error("saveSnapshotWithRetry error qcId {}, caseId {}, workOrderId {}", qcId, caseId, workOrderId, e);
        }
    }

    /**
     * 因材审工单会重复审核
     * 所以将需质检模块types保存到质检工单上
     */
    private void saveNeedQcTypes(Long qcOrderId, int caseId, long workOrderId) {
        Response<VonStorageVO> lastByTypeResp = workOrderStorageService.getLastByType(workOrderId, WorkOrderHelper.Storage.CAI_LIAO_NEED_QC_TYPES);
        if (lastByTypeResp.notOk()) {
            log.error("saveSnapshotWithRetry error {} {}", caseId, workOrderId);
            return;
        }
        Set<Integer> needQcTypes = VonStorageVO.getByData(lastByTypeResp.getData(), new TypeReference<Set<Integer>>() {
        }).orElse(null);
        Response<VonStorageVO> saveResp = workOrderStorageService.addByTypeOfJson(qcOrderId, WorkOrderHelper.Storage.CAI_LIAO_NEED_QC_TYPES, needQcTypes);
        if (saveResp.notOk()) {
            log.error("");
        }
    }

    public boolean saveSnapshot(long qcId, int caseId, long workOrderId) {
        String snapshot = "";
        Response<String> stringResponse = cfRecordClient.selectHandleSnapshot(caseId, workOrderId);
        if (stringResponse.ok()) {
            snapshot = stringResponse.getData();
        }
        if (StringUtils.isEmpty(snapshot)) {
            log.info("get snapshot empty !!!");
            return false;
        }
        snapshot = cosUploadUtil.uploadText(snapshot, null);
        //记录组织结构id、材审工单id
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos = List.of(
                new RiskQcMaterialsInfo(qcId, QcMaterialsKeyEnum.DETAIL_SNAPSHOT.getKey(), snapshot));
        riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfos);
        return true;
    }

    public static void main(String[] args) throws ExecutionException, RetryException {
        RetryerBuilder.<Boolean>newBuilder()

//                .withWaitStrategy(WaitStrategies.fixedWait(2, TimeUnit.SECONDS))
//                .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS,2,TimeUnit.SECONDS))
                .withWaitStrategy(WaitStrategies.fibonacciWait(1000, 15, TimeUnit.SECONDS))

                .withStopStrategy(StopStrategies.stopAfterAttempt(20))
//                .withStopStrategy(StopStrategies.stopAfterDelay(1,TimeUnit.MINUTES))

                .retryIfException()
                .retryIfResult(success -> !success)
                .build()
                .call(RiskQcmaterialService::r);
    }

    public static boolean r(){
        try {
            String time = new Date().toLocaleString();
            System.out.println("RiskQcmaterialService.r start" + time);
            Thread.sleep(5000);
            System.out.println("RiskQcmaterialService.r end" + time);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean saveSnapshotWithRetryV2(Long workOrderId, long qcId, long sourceWorkOrderId, long handleRecordId) {

        OperationResult<WonRecord> recordResp;
        if (handleRecordId > 0) {
            recordResp = wonRecordClient.getById(handleRecordId);
        } else {
            recordResp = wonRecordClient.getLastByBizId(sourceWorkOrderId, 103L);
        }
        WonRecord record = recordResp.getData();
        if (record == null) {
            log.error("record null {} {} {} {}", workOrderId, handleRecordId, qcId, sourceWorkOrderId);
            return false;
        }
        handleRecordId = record.getId();
        String snapshot = record.getExt("snapshot", String.class);
        if (StringUtils.isBlank(snapshot)) {
            log.info("empty snapshot");
            return false;
        }
        // 老数据存储
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos = List.of(
                new RiskQcMaterialsInfo(qcId, QcMaterialsKeyEnum.DETAIL_SNAPSHOT.getKey(), snapshot));
        riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfos);

        // 新数据存储
        Set<Integer> types2NeedQc = record.getExt("types2NeedQc", new TypeReference<>() {
        });
        workOrderStorageService.addByTypeOfJson(workOrderId, WorkOrderHelper.Storage.HANDLE_RECORD_ID, handleRecordId);
        workOrderStorageService.addByTypeOfJson(workOrderId, WorkOrderHelper.Storage.CAI_LIAO_NEED_QC_TYPES, types2NeedQc);
        return true;
    }
}
