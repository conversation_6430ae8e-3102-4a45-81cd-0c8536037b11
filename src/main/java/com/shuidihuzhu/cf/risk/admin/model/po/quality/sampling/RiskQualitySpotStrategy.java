package com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class RiskQualitySpotStrategy {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略名称
     */
    private String strategyName;

    private String sceneInfo;

    /**
     * 等用于type的secondScene
     */
    private long scene;

    /**
     * 规则使用范围
     */
    private String strategyScope;

    /**
     * 策略生效时间
     */
    private Date strategyParseTime;

    /**
     * 策略失效时间
     */
    private Date strategyExpireTime;

    /**
     * 启用停用状态：0 启用，1 停用
     */
    private Byte status;



    /**
     * 最新操作人id
     */
    private Long operateId;

    /**
     * 最新操作人
     */
    private String operateName;

    /**
     * 是否删除0 否，1 是
     */
    private Byte isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 规则定义
     */
    private String ruleDef;

    /**
     * 执行方式
     * @see  com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotExecuteModelEnum
     */
    private int executeMode;



}