package com.shuidihuzhu.cf.risk.admin.controller.disease;

import com.shuidihuzhu.cf.enhancer.subject.env.EnvHelper;
import com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseDataDao;
import com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseTreatmentProjectDao;
import com.shuidihuzhu.cf.risk.admin.feign.ApiUtils;
import com.shuidihuzhu.cf.risk.admin.feign.DiseaseSyncFeignClient;
import com.shuidihuzhu.cf.risk.admin.feign.DiseaseSyncRetrofitClient;
import com.shuidihuzhu.cf.risk.admin.model.disease.DiseaseSyncDTO;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import retrofit2.Call;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("api/cf-risk-admin/disease-sync")
public class DiseaseSyncController {

    @Autowired
    private RiskDiseaseDataDao riskDiseaseDataDao;

    @Autowired
    private RiskDiseaseTreatmentProjectDao riskDiseaseTreatmentProjectDao;

    @Autowired
    private DiseaseSyncFeignClient diseaseSyncFeignClient;

    @Autowired
    private DiseaseSyncRetrofitClient diseaseSyncRetrofitClient;

    private static final String TAG = " diseaseAsync ";
    private static final String CODE = "48rNlT091WjM";

    @RequiresPermission("disease:sync-trigger")
    @PostMapping("trigger")
    public Response<DiseaseSyncDTO> trigger(){
        if(EnvHelper.isDevelopment()){
            return NewResponseUtil.makeFail("测试环境不允许使用");
        }
        // 同步疾病表
        final DiseaseSyncDTO diseaseData = new DiseaseSyncDTO();
        final List<RiskDiseaseData> diseaseList = riskDiseaseDataDao.getAll();
        diseaseData.setClear(true);
        diseaseData.setDiseaseList(diseaseList);
        final Response<DiseaseSyncDTO> diseaseResp = promoteSync(diseaseData);

        // 同步方案表
        final List<RiskDiseaseTreatmentProject> treatmentProjectList = riskDiseaseTreatmentProjectDao.getAll();
        final List<List<RiskDiseaseTreatmentProject>> partition = ListUtils.partition(treatmentProjectList, 200);
        int i = 0;
        for (List<RiskDiseaseTreatmentProject> p : partition) {
            final DiseaseSyncDTO treatmentData = new DiseaseSyncDTO();
            if (i == 0) {
                treatmentData.setClear(true);
            }
            treatmentData.setTreatmentProjectList(p);
            final Response<DiseaseSyncDTO> treatmentResp = promoteSync(treatmentData);
            i += treatmentResp.getData().getTreatmentCount();
        }

        final DiseaseSyncDTO result = new DiseaseSyncDTO();
        result.setDiseaseCount(diseaseResp.getData().getDiseaseCount());
        result.setTreatmentCount(i);
        return NewResponseUtil.makeSuccess(result);
    }

    private Response<DiseaseSyncDTO> promoteSync(DiseaseSyncDTO data) {
        log.info("{} 同步请求准备 数据 {} 疾病数量 {}, 方案数量 {}", TAG, data, CollectionUtils.size(data.getDiseaseList()),
                CollectionUtils.size(data.getTreatmentProjectList()));
        data.setCode(CODE);
        final Call<Response<DiseaseSyncDTO>> call = diseaseSyncRetrofitClient.accept(data);
        final Optional<Response<DiseaseSyncDTO>> respOptional = ApiUtils.execute(call);
//        final Response<DiseaseSyncDTO> accept = diseaseSyncFeignClient.accept(data);
        final Response<DiseaseSyncDTO> accept = respOptional.orElse(null);
        log.info("{} 同步请求结束 结果 {}", TAG, accept);
        return accept;
    }

    @PostMapping("accept")
    public Response<DiseaseSyncDTO> accept(@RequestBody DiseaseSyncDTO data){
        if(EnvHelper.isProduction()){
            return NewResponseUtil.makeFail("线上环境不允许同步");
        }
        if (!StringUtils.equals(data.getCode(), CODE)) {
            return NewResponseUtil.makeFail("请求被拒绝");
        }
        final DiseaseSyncDTO result = new DiseaseSyncDTO();
        final List<RiskDiseaseData> diseaseList = data.getDiseaseList();
        if (CollectionUtils.isNotEmpty(diseaseList)) {
            if (data.isClear()) {
                riskDiseaseDataDao.clearAll();
            }
            final int syncCount = riskDiseaseDataDao.sync(diseaseList);
            result.setDiseaseCount(syncCount);
            log.info("{} 疾病数据写入数量{}", TAG, syncCount);
        }

        final List<RiskDiseaseTreatmentProject> treatmentProjectList = data.getTreatmentProjectList();
        if (CollectionUtils.isNotEmpty(treatmentProjectList)) {
            if (data.isClear()) {
                riskDiseaseTreatmentProjectDao.clearAll();
            }
            final int syncCount = riskDiseaseTreatmentProjectDao.sync(treatmentProjectList);
            result.setTreatmentCount(syncCount);
            log.info("{} 方案数据写入数量{}", TAG, syncCount);
        }

        return NewResponseUtil.makeSuccess(result);
    }
}
