package com.shuidihuzhu.cf.risk.admin.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.admin.dao.GetInternalStaffAndEmergencyMobileDAO;
import com.shuidihuzhu.cf.risk.admin.model.GetInternalStaffAndEmergencyMobileDO;
import com.shuidihuzhu.cf.risk.admin.util.SDEncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;


@Slf4j
public class GetInternalStaffAndEmergencyMobileListener extends AnalysisEventListener<GetInternalStaffAndEmergencyMobileDO> {

    private GetInternalStaffAndEmergencyMobileDAO getInternalStaffAndEmergencyMobileDAO;

    public GetInternalStaffAndEmergencyMobileListener(GetInternalStaffAndEmergencyMobileDAO getInternalStaffAndEmergencyMobileDAO) {
        this.getInternalStaffAndEmergencyMobileDAO = getInternalStaffAndEmergencyMobileDAO;
        getInternalStaffAndEmergencyMobileDAO.update(1);
    }


    @Override
    public void invoke(GetInternalStaffAndEmergencyMobileDO getInternalStaffAndEmergencyMobileDO, AnalysisContext analysisContext) {

        log.info("解析到一条数据:{}", JSON.toJSONString(getInternalStaffAndEmergencyMobileDO));
        if (getInternalStaffAndEmergencyMobileDO.getSelfMobile() == null) {

        } else {
            List<GetInternalStaffAndEmergencyMobileDO> list = new ArrayList<>();
            if (getInternalStaffAndEmergencyMobileDO.getSelfName() == null) {
                getInternalStaffAndEmergencyMobileDO.setSelfName("");
            }
            if (getInternalStaffAndEmergencyMobileDO.getEmergencyName() == null) {
                getInternalStaffAndEmergencyMobileDO.setEmergencyName("");
            }
            if (getInternalStaffAndEmergencyMobileDO.getEmergencyMobile() == null) {
                getInternalStaffAndEmergencyMobileDO.setEmergencyMobile("");
            }
            String encryptMobile = SDEncryptUtils.encrypt(getInternalStaffAndEmergencyMobileDO.getEmergencyMobile());
            getInternalStaffAndEmergencyMobileDO.setEmergencyMobile(StringUtils.trimToEmpty(encryptMobile));
            String encryptM = SDEncryptUtils.encrypt(getInternalStaffAndEmergencyMobileDO.getSelfMobile());
            getInternalStaffAndEmergencyMobileDO.setSelfMobile(StringUtils.trimToEmpty(encryptM));
            list.add(getInternalStaffAndEmergencyMobileDO);
            getInternalStaffAndEmergencyMobileDAO.insert(list);
            log.info("存储数据库成功！");
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {


    }
}
