package com.shuidihuzhu.cf.risk.admin.dao.whiteList;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.query.whiteList.WhiteListQuery;
import com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskWhiteListDao {


    int save(RiskWhiteListDto riskWhiteListDto);

    int update(RiskWhiteListDto riskWhiteListDto);

    int updateExpireTime(@Param("expireTime") Date expireTime, @Param("id") long id, @Param("operator") String operator);


    RiskWhiteListDto getById(@Param("id") long id);


    List<RiskWhiteListDto> listByConditions(WhiteListQuery whiteListQuery);


    RiskWhiteListDto getByCipherMobile(@Param("cipherMobile") String cipherMobile, @Param("type") Byte type, @Param("currTime") Date currTime);

    RiskWhiteListDto getByCipherIdCard(@Param("cipherIdCard") String cipherIdCard, @Param("type") Byte type, @Param("currTime") Date currTime);
}
