package com.shuidihuzhu.cf.risk.admin.model.po.list;

import java.util.Date;

public class RiskCityArea {
    /**
     * 主键
     */
    private Long id;

    /**
     * 省份id
     */
    private Integer provinceId;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 城市区号
     */
    private String areaCode;

    /**
     * 城市曾用区号
     */
    private String areaCodeFormer;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 主键
     * @return id 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 省份id
     * @return province_id 省份id
     */
    public Integer getProvinceId() {
        return provinceId;
    }

    /**
     * 省份id
     * @param provinceId 省份id
     */
    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    /**
     * 省份
     * @return province 省份
     */
    public String getProvince() {
        return province;
    }

    /**
     * 省份
     * @param province 省份
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 城市id
     * @return city_id 城市id
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 城市id
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 城市
     * @return city 城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 城市
     * @param city 城市
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 城市区号
     * @return area_code 城市区号
     */
    public String getAreaCode() {
        return areaCode;
    }

    /**
     * 城市区号
     * @param areaCode 城市区号
     */
    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode == null ? null : areaCode.trim();
    }

    /**
     * 城市曾用区号
     * @return area_code_former 城市曾用区号
     */
    public String getAreaCodeFormer() {
        return areaCodeFormer;
    }

    /**
     * 城市曾用区号
     * @param areaCodeFormer 城市曾用区号
     */
    public void setAreaCodeFormer(String areaCodeFormer) {
        this.areaCodeFormer = areaCodeFormer == null ? null : areaCodeFormer.trim();
    }

    /**
     * 是否删除，0 否，1 是
     * @return is_delete 是否删除，0 否，1 是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 是否删除，0 否，1 是
     * @param isDelete 是否删除，0 否，1 是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}