package com.shuidihuzhu.cf.risk.admin.service.quality.sampling;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.clinet.event.center.constants.MQTopicCons;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcLogBiz;
import com.shuidihuzhu.cf.risk.admin.biz.quality.sampling.RiskQcAppealWorkOrderRelBiz;
import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcAppealWorkOrderRel;
import com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcLog;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestNoticeModel;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/11/16
 */
@Service
@Slf4j
public class QcAppealInnerService {


    @Autowired
    private RiskQcLogBiz riskQcLogBiz;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private RiskQcAppealWorkOrderRelBiz workOrderRelBiz;


    public void normalQualitySpotAppeal(CfGwReplaceInputQualityTestFeedbackModel feedbackModel) {
        if (feedbackModel.getIssueType() != CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE){
            return;
        }
        //首次不认可生成工单
        if (feedbackModel.getNoticeType()  == CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.FIRST_NOTICE) {
            dealNormalOneAppeal(feedbackModel);
            return;
        } else if (feedbackModel.getNoticeType()  == CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.SECOND_NOTICE) {
            dealNormalTwoAppeal(feedbackModel);
            return;
        }
    }

    private void dealNormalOneAppeal(CfGwReplaceInputQualityTestFeedbackModel feedbackModel) {
        if (feedbackModel!= null && feedbackModel.getExpired()) {
            //工单过期
            saveAppealExpireLog(feedbackModel.getWorkOrderId());
            return;
        }
        //判断顾问认可结果
        if (feedbackModel.isFeedback()) {
            saveAgreeQcResultLog(feedbackModel.getMis(), feedbackModel.getWorkOrderId(),
                    RiskQcOperationTypeEnum.ADVISER_AGREE_QC_RESULT);
            return;
        }
        //生成工单
        sendCreateMQ(feedbackModel.getWorkOrderId(), feedbackModel);
        return;
    }

    private void dealNormalTwoAppeal(CfGwReplaceInputQualityTestFeedbackModel feedbackModel) {
        if (feedbackModel!= null && feedbackModel.getExpired()) {
            //工单过期
            saveAppealExpireLog(feedbackModel.getWorkOrderId());
            return;
        }
        //顾问不认可申诉判定结果
        if (feedbackModel.isFeedback()) {
            saveAgreeQcResultLog(feedbackModel.getMis(), getQcWorkOrderId(feedbackModel.getWorkOrderId()),
                    RiskQcOperationTypeEnum.ADVISER_AGREE_APPEAL_RESULT);
            return;
        }
    }

    public void managerQualitySpotAppeal(CfGwReplaceInputQualityTestFeedbackModel feedbackModel) {
        //首次不认可生成工单
        if (feedbackModel.getNoticeType() == CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.FIRST_NOTICE) {
            dealManageOneAppeal(feedbackModel);
            return;
        } else if (feedbackModel.getNoticeType() == CfGwReplaceInputQualityTestNoticeModel.NoticeTypeEnum.SECOND_NOTICE) {
            dealManageTwoAppeal(feedbackModel);
            return;
        }
    }

    private void dealManageOneAppeal(CfGwReplaceInputQualityTestFeedbackModel feedbackModel) {
        if (feedbackModel.getExpired() != null && feedbackModel.getExpired()) {
            //工单过期
            saveAppealExpireLog(getQcWorkOrderId(feedbackModel.getWorkOrderId()));
            return;
        }
        //判断业务经理认可质检结果
        if (feedbackModel.isFeedback()) {
            saveManageAgreeLog(feedbackModel.getMis(), feedbackModel.getWorkOrderId(), RiskQcOperationTypeEnum.MANAGER_AGREE_QC_RESULT);
            return;
        }
        //生成工单
        sendCreateMQ(feedbackModel.getWorkOrderId(), feedbackModel);
        return;
    }

    private void dealManageTwoAppeal(CfGwReplaceInputQualityTestFeedbackModel feedbackModel) {
        //顾问不支持二次申诉判定结果
        if (feedbackModel.getLeaderAction().equals(CfGwReplaceInputQualityTestFeedbackModel.LeaderActionEnum.DISAGREE.getDesc())) {
            saveManageNoSupportLog(feedbackModel.getLeaderMis(), getQcWorkOrderId(feedbackModel.getWorkOrderId()),
                    RiskQcOperationTypeEnum.MANAGER_NO_SUPPORT_AGAIN_APPEAL, "");
        } else if (feedbackModel.getLeaderAction().equals(CfGwReplaceInputQualityTestFeedbackModel.LeaderActionEnum.AGREE.getDesc())) {
            sendCreateMQ(feedbackModel.getWorkOrderId(), feedbackModel);
        }
    }

    private long getQcWorkOrderId(long appealWorkOrderId) {
        RiskQcAppealWorkOrderRel riskQcAppealWorkOrderRel = workOrderRelBiz.getByAppealWorkOrderId(appealWorkOrderId);
        if (riskQcAppealWorkOrderRel == null){
            return 0;
        }
        return riskQcAppealWorkOrderRel.getQcWorkOrderId();
    }

    public void sendCreateMQ(long workOrderId, CfGwReplaceInputQualityTestFeedbackModel  feedbackModel) {
        log.info("sendCreateMQ workOrderId:{} feedbackModel:{}", workOrderId, JSON.toJSONString(feedbackModel));
        producer.send(new Message<>(MQTopicCons.CF, RiskMQTagCons.QC_COMPLAINT_WORK_ORDER_CREATE,
                RiskMQTagCons.QC_COMPLAINT_WORK_ORDER_CREATE + workOrderId, feedbackModel));
    }


    /**
     * 生成工单log
     * @param workOrderId      新工单id
     * @param mis              操作人mis
     * @param issueTypeEnum    问题类型
     * @param beforeWorkOrderId    上一个质检工单id
     * @param workOrderTypeMsg     工单类型
     */
    //@Async
    public void saveBuildLog(long workOrderId, String mis,
                             CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum issueTypeEnum,
                             long beforeWorkOrderId, String workOrderTypeMsg) {
        /**
         * 保存工单关联关系
         */
        long qcWorkOrderId = saveRel(beforeWorkOrderId, workOrderId);
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getInfoModelByMis(mis);
        String content = DateUtil.getCurrentDateTimeStr() + "\n" + "生成" + workOrderTypeMsg + "，" + "工单Id 【" + workOrderId + "】 \n" +
                "操作人：" + (issueTypeEnum == CfGwReplaceInputQualityTestNoticeModel.IssueTypeEnum.DEFAULT_ISSUE ? "顾问" : "业务经理")
                + userNameWithOrg.getUserNameWithOrg();
        riskQcLogBiz.addLog(new RiskQcLog(userNameWithOrg.getAdminUserId(), userNameWithOrg.getAdminUserName(), content,
                RiskQcOperationTypeEnum.CREATE_APPEAL_WORK_ORDER.getType(), qcWorkOrderId));
    }

    private long saveRel(long beforeWorkOrderId, long workOrderId) {
        // 1. 查询之前是否有工单关联
        RiskQcAppealWorkOrderRel riskQcAppealWorkOrderRel = workOrderRelBiz.getByAppealWorkOrderId(beforeWorkOrderId);
        if (riskQcAppealWorkOrderRel == null) {
            workOrderRelBiz.save(beforeWorkOrderId, workOrderId);
            return beforeWorkOrderId;
        }
        workOrderRelBiz.save(riskQcAppealWorkOrderRel.getQcWorkOrderId(), workOrderId);
        return riskQcAppealWorkOrderRel.getQcWorkOrderId();
    }

    /**
     * 申诉过期log
     * @param qcWorkOrderId    原始工单id
     */
    public void saveAppealExpireLog(long qcWorkOrderId) {
        riskQcLogBiz.addLog(new RiskQcLog(0, "", DateUtil.getCurrentDateStr() + "\n" + "申诉过期",
                RiskQcOperationTypeEnum.APPEAL_WORK_ORDER_EXPIRE.getType(), qcWorkOrderId));
    }

    /**
     * 顾问认可通用log
     * @param qcWorkOrderId    原始工单id
     * @param mis              操作人mis
     */
    public void saveAgreeQcResultLog(String mis, long qcWorkOrderId, RiskQcOperationTypeEnum operationTypeEnum) {
        if (qcWorkOrderId <= 0) {
            log.error("qcWorkOrderId is 0!");
            return;
        }
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getInfoModelByMis(mis);
        String content = DateUtil.getCurrentDateTimeStr() + "\n" + "顾问姓名：" + userNameWithOrg.getUserNameWithOrg() + "\n" +
                operationTypeEnum.getDescription();
        riskQcLogBiz.addLog(new RiskQcLog(userNameWithOrg.getAdminUserId(),
                userNameWithOrg.getUserNameWithOrg(), content, operationTypeEnum.getType(), qcWorkOrderId));
    }


    /**
     * 业务经理不支持二次申诉
     * @param qcWorkOrderId    原始工单id
     * @param mis              操作人mis
     */
    public void saveManageNoSupportLog(String mis, long qcWorkOrderId, RiskQcOperationTypeEnum operationTypeEnum, String reason) {
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getInfoModelByMis(mis);
        String content = DateUtil.getCurrentDateTimeStr() + "\n" + "业务经理姓名：" + userNameWithOrg.getUserNameWithOrg() + "\n" +
                operationTypeEnum.getDescription() +
                reason;
        riskQcLogBiz.addLog(new RiskQcLog(userNameWithOrg.getAdminUserId(),
                userNameWithOrg.getUserNameWithOrg(), content, operationTypeEnum.getType(), qcWorkOrderId));
    }



    /**
     * 业务经理认可质检结果
     * @param qcWorkOrderId    原始工单id
     * @param mis              操作人mis
     */
    public void saveManageAgreeLog(String mis, long qcWorkOrderId, RiskQcOperationTypeEnum operationTypeEnum) {
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getInfoModelByMis(mis);
        String content = DateUtil.getCurrentDateTimeStr() + "\n" + "业务经理姓名：" + userNameWithOrg.getUserNameWithOrg() + "\n" +
                operationTypeEnum.getDescription();
        riskQcLogBiz.addLog(new RiskQcLog(userNameWithOrg.getAdminUserId(),
                userNameWithOrg.getUserNameWithOrg(), content, operationTypeEnum.getType(), qcWorkOrderId));
    }
}
