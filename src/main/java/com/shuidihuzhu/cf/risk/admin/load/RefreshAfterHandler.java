package com.shuidihuzhu.cf.risk.admin.load;

import com.shuidihuzhu.common.web.aop.ShuidiExceptionHandler;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/7/22 19:59
 */
@Component
public class Refresh<PERSON><PERSON><PERSON><PERSON><PERSON> implements SmartLifecycle {
    @Override
    public void start() {
        ShuidiExceptionHandler.allowIllegalArgumentExceptionErrMsg();
    }

    @Override
    public void stop() {}

    @Override
    public boolean isRunning() {
        return false;
    }
}
