package com.shuidihuzhu.cf.risk.admin.rule.compile.value;

import com.shuidihuzhu.cf.risk.admin.rule.model.CallMethod;
import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionData;
import com.shuidihuzhu.cf.risk.admin.rule.model.Parameter;
import com.shuidihuzhu.cf.risk.admin.rule.enums.ValueType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 注意：该处理为末尾节点，如有新增处理节点，加到该节点前
 * <AUTHOR>
 * @date 2020/6/18 22:35
 */
@Component("callMethodValueCompile")
@Slf4j
public class CallMethodValueCompile extends AbstractCompileChain<CriterionData> {

    @Resource
    private AbstractCompileChain valueCompile;

    @Override
    public String compileValue(CriterionData criterionData) {
        if (criterionData.getValueType() == ValueType.CALL_METHOD) {
            // TODO: houys 2020/6/18 函数处理未实现
            CallMethod callMethod = criterionData.getCallMethod();
            StringBuilder callMethodResult = new StringBuilder();
            callMethodResult.append("(");
            for (Parameter parameter : callMethod.getParameters()) {
                callMethodResult.append(valueCompile.compileValue(parameter.getValue())).append(",");
            }
            callMethodResult.append(")");
            return callMethodResult.toString();
        }
        throw new UnsupportedOperationException();
    }

    @Override
    public void setValueCompile(AbstractCompileChain valueCompile) {}

}
