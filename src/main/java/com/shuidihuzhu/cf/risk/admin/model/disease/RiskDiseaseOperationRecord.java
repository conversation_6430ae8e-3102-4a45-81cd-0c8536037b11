package com.shuidihuzhu.cf.risk.admin.model.disease;

import com.shuidihuzhu.cf.risk.admin.model.enums.RiskDiseaseOperationTypeEnum;
import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Data
@Slf4j
public class RiskDiseaseOperationRecord implements PageHasId {

    private long id;
    private int userId;
    private long diseaseId;
    private String operatorName;
    private int type;
    private Timestamp createTime;
    private String deleteReason;
    private String operateBefore;
    private String operateAfter;

    private RiskDiseaseOperationRecord() {
    }

    public static RiskDiseaseOperationRecord buildDelete(int adminUserId, String name, long diseaseId, String deleteReason) {
       return build(adminUserId, name, RiskDiseaseOperationTypeEnum.DELETE, diseaseId, deleteReason, "", "");
    }

    public static RiskDiseaseOperationRecord build(int adminUserId,
                                                   String name,
                                                   RiskDiseaseOperationTypeEnum typeEnum,
                                                   long diseaseId,
                                                   String deleteReason,
                                                   String operateBefore,
                                                   String operateAfter){
        if (adminUserId <= 0 || diseaseId <=0 ) {
            log.info("diseaseId:{}");
            return null;
        }
        RiskDiseaseOperationRecord operationRecord = new RiskDiseaseOperationRecord();
        operationRecord.setUserId(adminUserId);
        operationRecord.setType(typeEnum.getCode());
        operationRecord.setOperatorName(StringUtils.trimToEmpty(name));
        operationRecord.setDiseaseId(diseaseId);;
        operationRecord.setDeleteReason(deleteReason);
        operationRecord.setOperateBefore(operateBefore);
        operationRecord.setOperateAfter(operateAfter);
        return operationRecord;
    }

    public static RiskDiseaseOperationRecord buildSave(int adminUserId, String name, long diseaseId, String operateBefore, String operateAfter) {
        return build(adminUserId, name, RiskDiseaseOperationTypeEnum.SAVE, diseaseId, "", operateBefore, operateAfter);
    }

    public static RiskDiseaseOperationRecord buildUpdate(int adminUserId, String name, long diseaseId, String operateBefore, String operateAfter) {
        return build(adminUserId, name, RiskDiseaseOperationTypeEnum.UPDATE, diseaseId, "", operateBefore, operateAfter);
    }
}
