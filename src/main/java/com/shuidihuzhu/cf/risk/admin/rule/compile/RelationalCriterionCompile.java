package com.shuidihuzhu.cf.risk.admin.rule.compile;

import com.shuidihuzhu.cf.risk.admin.rule.compile.value.ValueCompileContext;
import com.shuidihuzhu.cf.risk.admin.rule.enums.CriterionTypeEnum;
import com.shuidihuzhu.cf.risk.admin.rule.enums.FieldType;
import com.shuidihuzhu.cf.risk.admin.rule.enums.RlatOp;
import com.shuidihuzhu.cf.risk.admin.rule.enums.ValueType;
import com.shuidihuzhu.cf.risk.admin.rule.function.rlat.RlatStrategyContext;
import com.shuidihuzhu.cf.risk.admin.rule.model.Criterion;
import com.shuidihuzhu.cf.risk.admin.rule.model.CriterionData;
import com.shuidihuzhu.cf.risk.admin.rule.model.Relational;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/6/18 23:21
 */
@Service
@Slf4j
public class RelationalCriterionCompile implements ICriterionCompile<Criterion>{

    @Resource
    private ValueCompileContext valueCompile;
    @Resource
    private LogicalCriterionCompile logicalCriterionCompile;
    @Resource
    private RlatStrategyContext RlatStrategyContext;

    @Override
    public String compileCriterion(Criterion criterionGroup) {
        String relationalResult = "";
        if (criterionGroup.getCriterionType() == CriterionTypeEnum.RELATIONAL) {
            Relational relational = criterionGroup.getRelational();
            RlatOp rlatOp = relational.getOp();
            String leftArith = valueCompile.spliceArithmetic(relational.getLeftValue());
            String rightArith = conversionDataType(valueCompile.spliceArithmetic(relational.getRightValue()),
                    relational.getLeftValue(), relational.getRightValue());
            if (StringUtils.isBlank(rlatOp.getScript())) {
                relationalResult = RlatStrategyContext.spliceRlat(rlatOp, leftArith, rightArith);
            } else {
                relationalResult = leftArith + rlatOp.getScript() + rightArith;
            }
        }
        if (criterionGroup.getCriterionType() == CriterionTypeEnum.CRITERION_GROUP) {
            relationalResult = logicalCriterionCompile.compileCriterion(criterionGroup.getCriterionGroup());
        }
        return relationalResult;
    }

    private String conversionDataType(String originResult, CriterionData leftValue, CriterionData rightValue) {
        ValueType rightValueType = rightValue.getValueType();
        if (rightValueType == ValueType.CONSTANT) {
            FieldType leftFieldType = leftValue.getVariable().getFieldType();
            if (leftFieldType == FieldType.String) {
                return "\"" + originResult + "\"";
            }
        }
        return originResult;
    }
}
