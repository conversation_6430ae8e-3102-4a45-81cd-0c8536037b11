package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.AuthenticityIndicator;
import com.shuidihuzhu.cf.client.material.model.materialField.*;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.admin.util.MaskUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.verify.v1.enums.UserRelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.shuidihuzhu.financial.utils.ShuidiCipherUtil.shuidiCipher;

/**
 * @Auther: subing
 * @Date: 2020/6/18
 */
@Data
public class CreditInfoVo {
    private String patientName;
    private int patientIdCardType;
    private String patientIdCard;
    private NumberMaskVo patientIdCardMask;
    private String houseSellingAmount;
    private Integer houseSellingCount;
    private Integer carNum;
    private Integer carSellingCount;
    private Integer carHasSell;
    private String carAmountArea;
    private String carSellingAmount;
    private String homeIncome;
    private String homeIncomeArea;
    private Integer hasFinancialAssets;
    private boolean homeOwningAmountStatus;
    private String homeOwningAmount;
    private Integer livingAllowance;
    private Integer hasPoverty;
    private Integer hasRaise;
    private String raiseAmount;
    private String remainAmount;
    private Integer useForMedical;
    private Integer medicalInsurance;
    private Integer hasPersonalInsurance;
    private Integer hasCarInsurance;
    private String title;
    private String content;
    private String pictureUrl;
    private String raiseMobile;
    private NumberMaskVo raiseMobileMask;
    private int raisePatientRelation;
    private String raiseName;
    private String selfCryptoIdcard;
    private NumberMaskVo selfCryptoIdcardMask;
    private String preAuditImageUrl;
    private List<Integer> riskLabels;
    private String targetAmount;
    private String financialAssetsAmount;
    private Integer homeOwningAmountArea;
    private String povertyImg;
    private String allowanceImg;
    private int caseVersion;

    //其他房
    private Integer houseNum;
    private String houseAmountArea;
    @ApiModelProperty("其他房产总净值")
    private String houseNetWorthArea;
    private Integer houseHasSell;
    private Integer houseValue;


    //自建房
    /**
     * {@link PreposeMaterialModel.HouseNumEnum}
     */
    @ApiModelProperty("房屋数量")
    private Integer selfHouseNum;
    /**
     * {@link PreposeMaterialModel.HouseSellingAmountAreaEnum}
     */
    @ApiModelProperty("房产总价值")
    private String selfHouseAmountArea;

    @ApiModelProperty("手动填写的房屋价值")
    private Integer selfHouseValue;
    /**
     * {@link PreposeMaterialModel.HasSellEnum}
     */
    @ApiModelProperty("是否变卖")
    private Integer selfHouseHasSell;
    /**
     * {@link PreposeMaterialModel.HouseNumEnum}
     */
    @ApiModelProperty("变卖中房产数量")
    private Integer selfHouseSellingCount;
    /**
     * {@link PreposeMaterialModel.HouseSellingAmountAreaEnum}
     */
    @ApiModelProperty("变卖中房产价值区间")
    private String selfHouseSellingAmountArea;

    @ApiModelProperty("手动填写的房屋价值")
    private Integer selfHouseSellingAmount;

    @ApiModelProperty("是否有自建房")
    private boolean isSelfHouse;

    @ApiModelProperty("患者婚姻状况 PatientMarriedEnum")
    private Integer patientMaritalStatus;
    @ApiModelProperty("患者有几位子女已结婚")
    private Integer marriedChildrenCount;
    @ApiModelProperty("患者已婚子女状况 EconomicSituationEnum")
    private List<Integer> marriedChildrenStatus;
    @ApiModelProperty("患者父母状况 EconomicSituationEnum")
    private List<Integer> patientParentStatus;
    @ApiModelProperty("净值阈值 单位：万")
    private Integer netWorthThreshold;

    /**
     * 新增增信信息
     * https://wdh.feishu.cn/wiki/wikcnuHeQVT98sFvo3dF9mllOEb
     */
    @ApiModelProperty("增信信息(新)")
    private AuthenticityIndicator authenticityIndicator;

    public static CreditInfoVo buildCreditInfo(CfFirsApproveMaterial cfFirsApproveMaterial,
                                               CfPropertyInsuranceInfoModel cfPropertyInsuranceInfoModel,
                                               CfBasicLivingGuardModel cfBasicLivingGuardModel,
                                               ShuidiCipher shuidiCipher, CrowdfundingInfo crowdfundingInfo,
                                               String mobile, com.shuidihuzhu.cf.enhancer.utils.MaskUtil maskUtil) {
        CreditInfoVo creditInfoVo = new CreditInfoVo();
        creditInfoVo.setRaiseMobileMask(maskUtil.buildByDecryptPhone(mobile));
        creditInfoVo.setRaiseMobile(null);

        if (crowdfundingInfo != null) {
            creditInfoVo.setTargetAmount(PreposeMaterialVo.buildValue(crowdfundingInfo.getTargetAmount() / 100));
        }
        if (cfFirsApproveMaterial != null) {
            creditInfoVo.setPatientName(StringUtils.trimToNull(cfFirsApproveMaterial.getPatientRealName()));
            creditInfoVo.setPatientIdCardType(cfFirsApproveMaterial.getPatientIdType());
            if (cfFirsApproveMaterial.getPatientIdType() == 1) {
                creditInfoVo.setPatientIdCardMask(maskUtil.buildByEncryptStrAndType(cfFirsApproveMaterial.getPatientCryptoIdcard(), DesensitizeEnum.IDCARD));
                creditInfoVo.setPatientIdCard(null);
            } else if (cfFirsApproveMaterial.getPatientIdType() == 2) {
                // 出生证不掩码
                creditInfoVo.setPatientIdCardMask(maskUtil.buildByEncryptStrAndType(cfFirsApproveMaterial.getPatientBornCard(), DesensitizeEnum.IDCARD));
                creditInfoVo.getPatientIdCardMask().setMaskNumber(cfFirsApproveMaterial.getPatientBornCard());
            }
            creditInfoVo.setRaisePatientRelation(cfFirsApproveMaterial.getUserRelationTypeForC());
            creditInfoVo.setRaiseName(StringUtils.trimToNull(cfFirsApproveMaterial.getSelfRealName()));
            creditInfoVo.setSelfCryptoIdcardMask(maskUtil.buildByEncryptStrAndType(cfFirsApproveMaterial.getSelfCryptoIdcard(), DesensitizeEnum.IDCARD));
            // 出生证情况
            if(creditInfoVo.getSelfCryptoIdcardMask() != null) {
                if (StringUtils.isBlank(creditInfoVo.getSelfCryptoIdcardMask().getMaskNumber())) {
                    creditInfoVo.getSelfCryptoIdcardMask().setMaskNumber(shuidiCipher.decrypt(cfFirsApproveMaterial.getSelfCryptoIdcard()));
                }
            }
            creditInfoVo.setSelfCryptoIdcard(null);
            creditInfoVo.setPreAuditImageUrl(StringUtils.trimToNull(cfFirsApproveMaterial.getImageUrl()));
        }
        if (cfPropertyInsuranceInfoModel != null) {
            buildProperty(creditInfoVo, cfPropertyInsuranceInfoModel);
        }
        if (cfBasicLivingGuardModel != null) {
            creditInfoVo.setLivingAllowance(cfBasicLivingGuardModel.getLivingAllowance());
            creditInfoVo.setHasPoverty(cfBasicLivingGuardModel.getHasPoverty());
            creditInfoVo.setPovertyImg(cfBasicLivingGuardModel.getPovertyImg());
            creditInfoVo.setAllowanceImg(cfBasicLivingGuardModel.getAllowanceImg());
        }
        setIdentity(creditInfoVo);
        return creditInfoVo;
    }

    public static CreditInfoVo buildCreditInfoV2(Map<String, String> infoMap, CfPropertyInsuranceInfoModel cfPropertyInsuranceInfoModel,
                                                 com.shuidihuzhu.cf.enhancer.utils.MaskUtil maskUtil) {
        CreditInfoVo creditInfoVo = new CreditInfoVo();
        String mobile = checkNull(infoMap.get(CfRaiseBasicInfoField.raise_basic_self_mobile));
        creditInfoVo.setRaiseMobileMask(maskUtil.buildByEncryptPhone(mobile));
        creditInfoVo.setRaiseMobile(null);

        creditInfoVo.setTargetAmount(PreposeMaterialVo.buildValue(makeStringToInteger(infoMap.get(CfBaseInfoField.target_amount))));

        creditInfoVo.setPatientName(checkNull(infoMap.get("patient_real_name")));
        String patientIdCard = checkNull(infoMap.get("patient_crypto_idcard"));
        if(StringUtils.isNotEmpty(patientIdCard)){
            creditInfoVo.setPatientIdCardType(1);
            creditInfoVo.setPatientIdCardMask(maskUtil.buildByEncryptStrAndType(patientIdCard, DesensitizeEnum.IDCARD));
            creditInfoVo.setPatientIdCard(null);
        }
        String patientBornCard = checkNull(infoMap.get("patient_born_card"));
        if(StringUtils.isNotEmpty(patientBornCard)){
            creditInfoVo.setPatientIdCardType(2);
            if(creditInfoVo.getPatientIdCardMask() != null) {
                creditInfoVo.getPatientIdCardMask().setMaskNumber(patientBornCard);
            }
        }
        creditInfoVo.setRaisePatientRelation(makeStringToInt(infoMap.get(CfRaiseBasicInfoField.raise_basic_user_relation_type_for_c)));
        creditInfoVo.setRaiseName(checkNull(infoMap.get(CfRaiseBasicInfoField.raise_basic_self_real_name)));
        List<String> preAuditImageUrlList = makeStringToStringList(checkNull(infoMap.get(CfBaseInfoField.raise_images)));
        if(CollectionUtils.isNotEmpty(preAuditImageUrlList)) {
            creditInfoVo.setPictureUrl(StringUtils.join(preAuditImageUrlList, ","));
        }
        creditInfoVo.setPreAuditImageUrl(checkNull(infoMap.get("first_approve_image_url")));


        buildPropertyV2(creditInfoVo, infoMap);

        creditInfoVo.setLivingAllowance(makeStringToInteger(infoMap.get(CfBasicLivingGuardField.living_allowance)));
        creditInfoVo.setHasPoverty(makeStringToInteger(infoMap.get(CfBasicLivingGuardField.has_poverty)));
        creditInfoVo.setPovertyImg(checkNull(infoMap.get(CfBasicLivingGuardField.poverty_img)));
        creditInfoVo.setAllowanceImg(checkNull(infoMap.get(CfBasicLivingGuardField.allowance_img)));

        creditInfoVo.setTitle(checkNull(infoMap.get(CfMaterialPreModifyField.baseInfoTitle)));
        creditInfoVo.setContent(checkNull(infoMap.get(CfMaterialPreModifyField.baseInfoContent)));

        setIdentityV2(creditInfoVo);
        // 增信信息（新）
        creditInfoVo.setAuthenticityIndicator(cfPropertyInsuranceInfoModel.getAuthenticityIndicator());
        return creditInfoVo;
    }
    private static void buildPropertyV2(CreditInfoVo creditInfoVo, Map<String, String> infoMap) {

        creditInfoVo.setPatientMaritalStatus(makeStringToInteger(infoMap.get(CfInitialPropertyField.patient_marital_status)));
        creditInfoVo.setMarriedChildrenCount(makeStringToInteger(infoMap.get(CfInitialPropertyField.married_children_count)));
        creditInfoVo.setMarriedChildrenStatus(makeStringToIntegerList(infoMap.get(CfInitialPropertyField.married_children_status)));
        creditInfoVo.setPatientParentStatus(makeStringToIntegerList(infoMap.get(CfInitialPropertyField.patient_parent_status)));
        creditInfoVo.setNetWorthThreshold(makeStringToInteger(infoMap.get(CfInitialPropertyField.net_worth_threshold)));

        creditInfoVo.setHouseNum(makeStringToInteger(infoMap.get(CfInitialPropertyField.house_total_count)));
        Integer houseTotalValueUserDefined = makeStringToInteger(infoMap.get(CfInitialPropertyField.house_total_value_user_defined));
        Integer houseTotalValueRangeType = makeStringToInteger(infoMap.get(CfInitialPropertyField.house_total_value_range_type));
        buildHouseAmountArea(houseTotalValueUserDefined, creditInfoVo, houseTotalValueRangeType);
        Integer pureValueUserDefined = makeStringToInteger(infoMap.get("pure_value_user_defined"));
        Integer pureValueRangeType = makeStringToInteger(infoMap.get("pure_value_range_type"));
        buildHouseNetAssetsArea(pureValueUserDefined, creditInfoVo, pureValueRangeType);
        creditInfoVo.setHouseHasSell(makeStringToInteger(infoMap.get(CfInitialPropertyField.house_sale_status)));
        Integer houseSaleValueUserDefined = makeStringToInteger(infoMap.get(CfInitialPropertyField.house_sale_value_user_defined));
        Integer houseSaleValueRangeType = makeStringToInteger(infoMap.get(CfInitialPropertyField.house_sale_value_range_type));
        if (getInt(houseSaleValueUserDefined) == null) {
            if (getInt(houseSaleValueRangeType) != null) {
                creditInfoVo.setHouseSellingAmount(CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(houseSaleValueRangeType).getDesc());
            }
        } else {
            creditInfoVo.setHouseSellingAmount(PreposeMaterialVo.buildValue(getInt(houseSaleValueUserDefined)));
        }
        creditInfoVo.setHouseSellingCount(getInt(makeStringToInteger(infoMap.get(CfInitialPropertyField.house_sale_count))));
        creditInfoVo.setHouseValue(houseTotalValueUserDefined);


        creditInfoVo.setCarNum(getInt(makeStringToInteger(infoMap.get(CfInitialPropertyField.car_total_count))));
        Integer carTotalValueUserDefined = makeStringToInteger(infoMap.get(CfInitialPropertyField.car_total_value_user_defined));
        Integer carTotalValueRangeType = makeStringToInteger(infoMap.get(CfInitialPropertyField.car_total_value_range_type));
        buildCarAmountArea(carTotalValueUserDefined, creditInfoVo, carTotalValueRangeType);
        creditInfoVo.setCarHasSell(makeStringToInteger(infoMap.get(CfInitialPropertyField.car_sale_status)));
        Integer carSaleValueUserDefined = makeStringToInteger(infoMap.get(CfInitialPropertyField.car_sale_value_user_defined));
        Integer carSaleValueRangeType = makeStringToInteger(infoMap.get(CfInitialPropertyField.car_sale_value_range_type));
        if (getInt(carSaleValueUserDefined) == null) {
            if (carSaleValueRangeType != null) {
                creditInfoVo.setCarSellingAmount(CfPropertyInsuranceInfoModel.CarValueRange.valueOfCode(carSaleValueRangeType).getDesc());
            }
        } else {
            creditInfoVo.setCarSellingAmount(PreposeMaterialVo.buildValue(getInt(carSaleValueUserDefined)));
        }
        creditInfoVo.setCarSellingCount(getInt(makeStringToInteger(infoMap.get(CfInitialPropertyField.car_sale_count))));


        Integer homeIncomeUserDefined = makeStringToInteger(infoMap.get(CfInitialPropertyField.patient_home_income_user_defined));
        Integer homeIncomeRangeType = makeStringToInteger(infoMap.get(CfInitialPropertyField.patient_home_income_range_type));
        creditInfoVo.setHomeIncome(PreposeMaterialVo.buildValue(homeIncomeUserDefined));
        if (homeIncomeUserDefined != null){
            creditInfoVo.setHomeIncomeArea(PreposeMaterialVo.buildValue(homeIncomeUserDefined));
        }else if (homeIncomeRangeType!= null){
            creditInfoVo.setHomeIncomeArea(CfPropertyInsuranceInfoModel.HomeIncomeValueRange.valueOfCode(homeIncomeRangeType).getDesc());
        }


        creditInfoVo.setHasFinancialAssets(makeStringToInteger(infoMap.get(CfInitialPropertyField.patient_has_home_stock)));
        Integer homeStockUserDefined = makeStringToInteger(infoMap.get(CfInitialPropertyField.patient_home_stock_user_defined));
        Integer homeStockRangeType = makeStringToInteger(infoMap.get(CfInitialPropertyField.patient_home_stock_range_type));
        if (homeStockUserDefined != null){
            creditInfoVo.setFinancialAssetsAmount(PreposeMaterialVo.buildValue(homeStockUserDefined));
        }else if (homeStockRangeType != null){
            creditInfoVo.setFinancialAssetsAmount(CfPropertyInsuranceInfoModel.HomeStockValueRange.valueOfCode(homeStockRangeType).getDesc());
        }


        Integer homeDebt = makeStringToInteger(infoMap.get(CfInitialPropertyField.patient_home_debt));
        BigDecimal homeDebtDecimalAmount = makeStringToBigDecimal((infoMap.get(CfInitialPropertyField.patient_home_debt_amount)));
        Integer homeDebtRangeType = makeStringToInteger(infoMap.get(CfInitialPropertyField.patient_home_debt_range_type));
        creditInfoVo.setHomeOwningAmountStatus(homeDebt != null && homeDebt == 1);
        if (homeDebt != null && homeDebt == 1){
            if (homeDebtDecimalAmount != null) {
                creditInfoVo.setHomeOwningAmount(PreposeMaterialVo.buildValue(homeDebtDecimalAmount.intValue()));
            } else if (homeDebtRangeType != null) {
                creditInfoVo.setHomeOwningAmount(PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(homeDebtRangeType).getDesc());
            }
        }
        creditInfoVo.setHomeOwningAmountArea(homeDebtRangeType);


        Integer hasRaiseOnOther = makeStringToInteger(infoMap.get(CfInitialPropertyField.has_raise_on_other));
        creditInfoVo.setHasRaise(hasRaiseOnOther);
        if (hasRaiseOnOther != null && hasRaiseOnOther == 1) {
            creditInfoVo.setRaiseAmount(PreposeMaterialVo.buildValue(makeStringToInteger(infoMap.get(CfInitialPropertyField.raise_amount_on_other))));
            creditInfoVo.setRemainAmount(PreposeMaterialVo.buildValue(makeStringToInteger(infoMap.get(CfInitialPropertyField.remain_amount_on_other))));
            creditInfoVo.setUseForMedical(makeStringToInteger(infoMap.get(CfInitialPropertyField.use_for_medical_on_other)));
        }


        creditInfoVo.setMedicalInsurance(makeStringToInteger(infoMap.get(CfInitialPropertyField.medical_insurance)));
        creditInfoVo.setHasPersonalInsurance(makeStringToInteger(infoMap.get(CfInitialPropertyField.life_insurance)));
        creditInfoVo.setHasCarInsurance(makeStringToInteger(infoMap.get(CfInitialPropertyField.property_insurance)));
        creditInfoVo.setRiskLabels(makeStringToIntegerList(infoMap.get(CfInitialPropertyField.insurance_risk_label)));


        // 自建房字段赋值
        creditInfoVo.setSelfHouse(makeStringToInt(infoMap.get("house_total_count_self")) > 0);
        if(!creditInfoVo.isSelfHouse()){
            return;
        }
        creditInfoVo.setSelfHouseNum(makeStringToInteger(infoMap.get("house_total_count_self")));
        Integer selfHouseTotalValueUserDefined = makeStringToInteger(infoMap.get("house_total_value_user_defined_self"));
        Integer selfHouseTotalValueRangeType = makeStringToInteger(infoMap.get("house_total_value_range_type_self"));
        creditInfoVo.setSelfHouseValue(selfHouseTotalValueUserDefined);
        if (selfHouseTotalValueUserDefined == null) {
            creditInfoVo.setSelfHouseAmountArea(selfHouseTotalValueRangeType == null ? null : CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(selfHouseTotalValueRangeType).getDesc());
        } else {
            creditInfoVo.setSelfHouseAmountArea(PreposeMaterialVo.buildValue(selfHouseTotalValueUserDefined));
        }
        creditInfoVo.setSelfHouseSellingCount(makeStringToInteger(infoMap.get("house_sale_count_self")));
        creditInfoVo.setSelfHouseHasSell(makeStringToInteger(infoMap.get("house_sale_status_self")));
        Integer selfHouseSaleValueUserDefined = makeStringToInteger(infoMap.get("house_sale_value_user_defined_self"));
        Integer selfHouseSaleValueRangeType = makeStringToInteger(infoMap.get("house_sale_value_range_type_self"));
        creditInfoVo.setSelfHouseSellingAmount(selfHouseSaleValueUserDefined);
        if (selfHouseSaleValueUserDefined == null) {
            creditInfoVo.setSelfHouseSellingAmountArea(selfHouseSaleValueRangeType == null ? null : CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(selfHouseSaleValueRangeType).getDesc());
        } else {
            creditInfoVo.setSelfHouseSellingAmountArea(PreposeMaterialVo.buildValue(selfHouseSaleValueUserDefined));
        }

    }
    private static void buildProperty(CreditInfoVo creditInfoVo, CfPropertyInsuranceInfoModel cfPropertyInsuranceInfoModel) {
        CfPropertyInsuranceInfoModel.HousePropertyInfo housePropertyInfo = cfPropertyInsuranceInfoModel.getHouseProperty();
        if (housePropertyInfo != null) {
            creditInfoVo.setHouseNum(getInt(housePropertyInfo.getTotalCount()));
            buildHouseAmountArea(housePropertyInfo.getTotalValueUserDefined(), creditInfoVo, housePropertyInfo.getTotalValueRangeType());
            buildHouseNetAssetsArea(housePropertyInfo.getPureValueUserDefined(), creditInfoVo, housePropertyInfo.getPureValueRangeType());
            creditInfoVo.setHouseHasSell(housePropertyInfo.getSaleStatus());
            if (getInt(housePropertyInfo.getSaleValueUserDefined()) == null) {
                if (housePropertyInfo.getSaleValueRangeType() != null) {
                    creditInfoVo.setHouseSellingAmount(CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(housePropertyInfo.getSaleValueRangeType()).getDesc());
                }
            } else {
                creditInfoVo.setHouseSellingAmount(PreposeMaterialVo.buildValue(getInt(housePropertyInfo.getSaleValueUserDefined())));
            }
            creditInfoVo.setHouseSellingCount(getInt(housePropertyInfo.getSaleCount()));
            creditInfoVo.setHouseValue(housePropertyInfo.getTotalValueUserDefined());
        }
        CfPropertyInsuranceInfoModel.CarPropertyInfo carPropertyInfo = cfPropertyInsuranceInfoModel.getCarProperty();
        if (housePropertyInfo != null) {
            creditInfoVo.setCarNum(getInt(carPropertyInfo.getTotalCount()));
            buildCarAmountArea(carPropertyInfo.getTotalValueUserDefined(), creditInfoVo, carPropertyInfo.getTotalValueRangeType());
            creditInfoVo.setCarHasSell(carPropertyInfo.getSaleStatus());
            if (getInt(carPropertyInfo.getSaleValueUserDefined()) == null) {
                if (carPropertyInfo.getSaleValueRangeType() != null) {
                    creditInfoVo.setCarSellingAmount(CfPropertyInsuranceInfoModel.CarValueRange.valueOfCode(carPropertyInfo.getSaleValueRangeType()).getDesc());
                }
            } else {
                creditInfoVo.setCarSellingAmount(PreposeMaterialVo.buildValue(getInt(carPropertyInfo.getSaleValueUserDefined())));
            }
            creditInfoVo.setCarSellingCount(getInt(carPropertyInfo.getSaleCount()));
        }
        creditInfoVo.setHomeIncome(PreposeMaterialVo.buildValue(cfPropertyInsuranceInfoModel.getHomeIncomeUserDefined()));
        if (cfPropertyInsuranceInfoModel.getHomeIncomeUserDefined() != null){
            creditInfoVo.setHomeIncomeArea(PreposeMaterialVo.buildValue(cfPropertyInsuranceInfoModel.getHomeIncomeUserDefined()));
        }else if (cfPropertyInsuranceInfoModel.getHomeIncomeRangeType()!= null){
            creditInfoVo.setHomeIncomeArea(CfPropertyInsuranceInfoModel.HomeIncomeValueRange.valueOfCode(cfPropertyInsuranceInfoModel.getHomeIncomeRangeType()).getDesc());
        }
        creditInfoVo.setHasFinancialAssets(cfPropertyInsuranceInfoModel.getHasHomeStock());
        if (cfPropertyInsuranceInfoModel.getHomeStockUserDefined() != null){
            creditInfoVo.setFinancialAssetsAmount(PreposeMaterialVo.buildValue(cfPropertyInsuranceInfoModel.getHomeStockUserDefined()));
        }else if (cfPropertyInsuranceInfoModel.getHomeStockRangeType() != null){
            creditInfoVo.setFinancialAssetsAmount(CfPropertyInsuranceInfoModel.HomeStockValueRange.valueOfCode(cfPropertyInsuranceInfoModel.getHomeStockRangeType()).getDesc());
        }
        creditInfoVo.setHomeOwningAmountStatus(cfPropertyInsuranceInfoModel.getHomeDebt() != null && cfPropertyInsuranceInfoModel.getHomeDebt() == 1);
        if (cfPropertyInsuranceInfoModel.getHomeDebt() != null && cfPropertyInsuranceInfoModel.getHomeDebt() == 1){
            if (cfPropertyInsuranceInfoModel.getHomeDebtDecimalAmount() != null) {
                creditInfoVo.setHomeOwningAmount(
                        PreposeMaterialVo.buildValue(cfPropertyInsuranceInfoModel.getHomeDebtDecimalAmount().intValue()));
            } else if (cfPropertyInsuranceInfoModel.getHomeDebtRangeType() != null) {
                creditInfoVo.setHomeOwningAmount(PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(cfPropertyInsuranceInfoModel.getHomeDebtRangeType()).getDesc());
            }
        }
        creditInfoVo.setHomeOwningAmountArea(cfPropertyInsuranceInfoModel.getHomeDebtRangeType());
        CfPropertyInsuranceInfoModel.RaiseOnOtherPlatform raiseOnOtherPlatform = cfPropertyInsuranceInfoModel.getOtherPlatform();
        creditInfoVo.setHasRaise(raiseOnOtherPlatform == null ? null : raiseOnOtherPlatform.getHasRaise());
        if (raiseOnOtherPlatform != null && raiseOnOtherPlatform.getHasRaise() == 1) {
            creditInfoVo.setRaiseAmount(PreposeMaterialVo.buildValue(raiseOnOtherPlatform.getRaiseAmount()));
            creditInfoVo.setRemainAmount(PreposeMaterialVo.buildValue(raiseOnOtherPlatform.getRemainAmount()));
            creditInfoVo.setUseForMedical(raiseOnOtherPlatform.getUseForMedical());
        }
        creditInfoVo.setMedicalInsurance(cfPropertyInsuranceInfoModel.getMedicalInsurance());
        creditInfoVo.setHasPersonalInsurance(cfPropertyInsuranceInfoModel.getLifeInsurance());
        creditInfoVo.setHasCarInsurance(cfPropertyInsuranceInfoModel.getPropertyInsurance());
        creditInfoVo.setRiskLabels(CollectionUtils.isEmpty(cfPropertyInsuranceInfoModel.getRiskLabels()) ? null : cfPropertyInsuranceInfoModel.getRiskLabels());

        // 自建房字段赋值
        CfPropertyInsuranceInfoModel.HousePropertyInfo selfBuiltHouse = cfPropertyInsuranceInfoModel.getSelfBuiltHouse();
        if (Objects.nonNull(selfBuiltHouse)) {
            creditInfoVo.setSelfHouse(true);
            creditInfoVo.setSelfHouseNum(selfBuiltHouse.getTotalCount());
            creditInfoVo.setSelfHouseValue(selfBuiltHouse.getTotalValueUserDefined());
            if (selfBuiltHouse.getTotalValueUserDefined() == null) {
                creditInfoVo.setSelfHouseAmountArea(selfBuiltHouse.getTotalValueRangeType() == null ? null : CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(selfBuiltHouse.getTotalValueRangeType()).getDesc());
            } else {
                creditInfoVo.setSelfHouseAmountArea(PreposeMaterialVo.buildValue(selfBuiltHouse.getTotalValueUserDefined()));
            }
            creditInfoVo.setSelfHouseSellingCount(selfBuiltHouse.getSaleCount());
            creditInfoVo.setSelfHouseHasSell(selfBuiltHouse.getSaleStatus());
            creditInfoVo.setSelfHouseSellingAmount(selfBuiltHouse.getSaleValueUserDefined());
            if (selfBuiltHouse.getSaleValueUserDefined() == null) {
                creditInfoVo.setSelfHouseSellingAmountArea(selfBuiltHouse.getSaleValueRangeType() == null ? null : CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(selfBuiltHouse.getSaleValueRangeType()).getDesc());
            } else {
                creditInfoVo.setSelfHouseSellingAmountArea(PreposeMaterialVo.buildValue(selfBuiltHouse.getSaleValueUserDefined()));
            }
        }

        // 增信信息（新）
        creditInfoVo.setAuthenticityIndicator(cfPropertyInsuranceInfoModel.getAuthenticityIndicator());

        creditInfoVo.setPatientMaritalStatus(cfPropertyInsuranceInfoModel.getPatientMaritalStatus());
        creditInfoVo.setMarriedChildrenCount(cfPropertyInsuranceInfoModel.getMarriedChildrenCount());
        creditInfoVo.setMarriedChildrenStatus(cfPropertyInsuranceInfoModel.getMarriedChildrenStatus());
        creditInfoVo.setPatientParentStatus(cfPropertyInsuranceInfoModel.getPatientParentStatus());
        creditInfoVo.setNetWorthThreshold(cfPropertyInsuranceInfoModel.getNetWorthThreshold());

    }

    private static String checkNull(String s) {
        return "cf_material_value_null".equals(s) ? null : StringUtils.trimToNull(s);
    }

    private static Integer makeStringToInteger(String s) {
        s = checkNull(s);
        return StringUtils.isEmpty(s) ? null : Integer.valueOf(s);
    }

    private static int makeStringToInt(String s) {
        s = checkNull(s);
        return StringUtils.isBlank(s) ? 0 : Integer.parseInt(s);
    }

    private static BigDecimal makeStringToBigDecimal(String s) {
        s = checkNull(s);
        return StringUtils.isEmpty(s) ? null : new BigDecimal(s);
    }

    private static List<Integer> makeStringToIntegerList(String s) {
        s = checkNull(s);
        if(StringUtils.isEmpty(s)){
            return null;
        }
        List<Integer> list = JSONArray.parseArray(s, Integer.class);
        return CollectionUtils.isEmpty(list) ? null : list;
    }

    private static List<String> makeStringToStringList(String s) {
        s = checkNull(s);
        if(StringUtils.isEmpty(s)){
            return null;
        }
        List<String> list = JSONArray.parseArray(s, String.class);
        return CollectionUtils.isEmpty(list) ? null : list;
    }

    private static void setIdentity(CreditInfoVo creditInfoVo) {
        if (creditInfoVo.getRaisePatientRelation() == UserRelTypeEnum.SELF.getValue()) {
            creditInfoVo.setRaiseName(creditInfoVo.getPatientName());
            creditInfoVo.setSelfCryptoIdcardMask(creditInfoVo.getPatientIdCardMask());
        }
    }

    private static void setIdentityV2(CreditInfoVo creditInfoVo) {
        if (creditInfoVo.getRaisePatientRelation() == UserRelTypeEnum.SELF.getValue()) {
            if(StringUtils.isEmpty(creditInfoVo.getPatientName())){
                creditInfoVo.setPatientName(creditInfoVo.getRaiseName());
            }
            creditInfoVo.setRaiseName(creditInfoVo.getPatientName());
            creditInfoVo.setSelfCryptoIdcardMask(creditInfoVo.getPatientIdCardMask());
        }
    }


    private static Integer getInt(Integer integer) {
        return integer;
    }


    private static void buildHouseAmountArea(Integer amount, CreditInfoVo creditInfoVo, Integer type) {
        if (Objects.isNull(amount)) {
            if (type != null) {
                creditInfoVo.setHouseAmountArea(CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(type).getDesc());
            }
        } else {
            creditInfoVo.setHouseAmountArea(PreposeMaterialVo.buildValue(amount));
        }
    }

    private static void buildHouseNetAssetsArea(Integer amount, CreditInfoVo creditInfoVo, Integer type) {
        if (Objects.isNull(amount)) {
            if (type != null) {
                creditInfoVo.setHouseNetWorthArea(CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(type).getDesc());
            }
        } else {
            creditInfoVo.setHouseNetWorthArea(PreposeMaterialVo.buildValue(amount));
        }
    }

    private static void buildCarAmountArea(Integer amount, CreditInfoVo creditInfoVo, Integer type) {
        if (Objects.isNull(amount)) {
            if (type != null) {
                creditInfoVo.setCarAmountArea(CfPropertyInsuranceInfoModel.CarValueRange.valueOfCode(type).getDesc());
            }
        } else {
            creditInfoVo.setCarAmountArea(PreposeMaterialVo.buildValue(amount));
        }
    }
}
