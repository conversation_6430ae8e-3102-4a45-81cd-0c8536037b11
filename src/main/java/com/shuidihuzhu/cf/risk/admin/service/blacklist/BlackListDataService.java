package com.shuidihuzhu.cf.risk.admin.service.blacklist;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.risk.admin.biz.blacklist.BlacklistTypeBiz;
import com.shuidihuzhu.cf.risk.admin.dao.blacklist.*;
import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeActionRefDto;
import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeNameActionDto;
import com.shuidihuzhu.cf.risk.admin.model.enums.BlackActionLimitTimeType;
import com.shuidihuzhu.cf.risk.admin.model.enums.BlacklistAutoAddTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.*;
import com.shuidihuzhu.cf.risk.admin.model.query.PageResult;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.BlacklistDataQuery;
import com.shuidihuzhu.cf.risk.admin.model.vo.blacklist.*;
import com.shuidihuzhu.cf.risk.admin.model.vo.hit.RiskHitOperateVo;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserAccountDelegateService;
import com.shuidihuzhu.cf.risk.admin.service.delegate.UserInfoDelegateService;
import com.shuidihuzhu.cf.risk.admin.util.MixtureUtil;
import com.shuidihuzhu.cf.risk.constant.CfRiskMQTagCons;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlackListAddParam;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistDataDto;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.UpdateBatchDto;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.VerifyAutoAddBlacklistVo;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.cf.risk.model.risk.RiskBlacklistDataTypeRefVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.account.v1.accountservice.MobileUserIdResponse;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/7/15 19:54
 */
@RefreshScope
@Validated
@Service
@Slf4j
public class BlackListDataService {

    private static final String UPDATE_BLACKLIST_DATA_TYPE_KEY = "cf_risk_admin_blacklist_data_type_lock";
    public static final String UPDATE_BLACKLIST_DATA_ADD_KEY = "cf_risk_admin_blacklist_data_add_lock";
    private static final long UPDATE_BLACKLIST_DATA_LEAVE_TIME = 15 * 1000;
    private static final long WAIT_LOCK_TIME_MILLI = 500;

    @Value("${pre.trial.not.enough.type.id}")
    private Long preTrialNotEnoughTypeId;
    @Value("${verify.auto.add.blacklist.type.id}")
    private Long verifyAutoAddBlacklistTypeId;
    @Value("${repeat.pay.order.auto.add.black.list.type.name:实锤库-实锤商业转发/刷单-实锤重复捐单}")
    private String repeatPayOrderAutoAddBlackListTypeName;

    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private RiskBlacklistDataDao riskBlacklistDataDao;
    @Resource
    private RiskBlacklistDataLogDao riskBlacklistDataLogDao;
    @Resource
    private RiskBlacklistDataTypeRefDao dataTypeRefDao;
    @Resource
    private RiskBlacklistDataActionRefDao dataActionRefDao;
    @Resource(name = "cfRiskRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private UserInfoDelegateService userInfoDelegateService;
    @Resource
    private UserAccountDelegateService userAccountDelegateService;
    @Resource
    private SeaAccountService seaAccountService;
    @Resource
    private BlacklistTypeBiz blacklistTypeBiz;
    @Resource
    private AuthorFeignClient authorClient;
    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialClient;
    @Resource(name = "asyncTaskThreadPool")
    private ThreadPoolTaskExecutor poolTaskExecutor;
    @Resource
    private CfFirstApproveClient cfFirstApproveClient;
    @Resource
    private RiskBlacklistTypeDao riskBlacklistTypeDao;

    private Config config;

    @Value("${apollo.black-list.config:}")
    public void setConfig(String configJson){
        config = JSON.parseObject(configJson, Config.class);
    }

    @Data
    public static class Config {

        private Map<String, Long> uuidIdMap;

    }

    public PageResult<BlacklistDataVo> queryPage(BlacklistDataQuery query) {
        setEncryptForQuery(query);
        Page<RiskBlacklistData> page = PageHelper.<RiskBlacklistData>startPage(query.getPageNo(), query.getPageSize())
                .doSelectPage(() -> riskBlacklistDataDao.listByOptions(query));
        List<RiskBlacklistData> result = page.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return new PageResult<>(page, Collections.emptyList());
        }
        List<Long> dataIds = result.stream().map(RiskBlacklistData::getId).collect(Collectors.toList());
        List<RiskBlacklistDataTypeRef> dataTypeRefs = dataTypeRefDao.listValidByDataIds(dataIds);
        Map<Long, List<RiskBlacklistDataTypeRef>> dataIdMap = dataTypeRefs.stream()
                .collect(Collectors.groupingBy(RiskBlacklistDataTypeRef::getDataId));

        List<BlacklistDataVo> blacklistDataVos = Lists.newArrayListWithCapacity(result.size());
        for (RiskBlacklistData blacklistData : result) {
            BlacklistDataVo blacklistDataVo = assembleDataVoBase(blacklistData, true);
            List<RiskBlacklistDataTypeRef> refs = dataIdMap.get(blacklistData.getId());
            Set<String> actions = Sets.newHashSet();
            List<String> typeNames = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(refs)) {
                for (RiskBlacklistDataTypeRef ref : refs) {
                    typeNames.add(ref.getTypeName());
                    actions.addAll(LimitActionEnum.ids2Names(ref.getActionIds()));
                }
            }
            blacklistDataVo.setTypeNames(typeNames);
            blacklistDataVo.setAction(Joiner.on(",").join(actions));

            blacklistDataVos.add(blacklistDataVo);
        }

        return new PageResult<>(page, blacklistDataVos);
    }

    public BlacklistDataTypeDetailVo getTypeDetail(Long dataId){
        List<RiskBlacklistDataTypeRef> refs = dataTypeRefDao.listValidByDataIds(List.of(dataId));
        if (CollectionUtils.isEmpty(refs)) {
            return null;
        }
        List<RiskBlacklistDataActionRef> blacklistDataActionRefList = dataActionRefDao.listByDataId(dataId);
        if (CollectionUtils.isEmpty(blacklistDataActionRefList)) {
            return null;
        }
        Map<Long, RiskBlacklistDataActionRef> riskBlacklistDataActionRefMap = blacklistDataActionRefList.stream()
                .collect(Collectors.toMap(RiskBlacklistDataActionRef::getActionId, Function.identity(), (x, y) -> x));
        BlacklistDataTypeDetailVo typeDetailVo = new BlacklistDataTypeDetailVo();
        typeDetailVo.setId(dataId);

        List<BlacklistDataTypeDetailVo.DataTypeBaseInfo> baseInfos = Lists.newArrayListWithCapacity(refs.size());
        for (RiskBlacklistDataTypeRef ref : refs) {
            BlacklistDataTypeDetailVo.DataTypeBaseInfo baseInfo = new BlacklistDataTypeDetailVo.DataTypeBaseInfo();
            baseInfo.setId(ref.getTypeId());
            baseInfo.setTypeName(ref.getTypeName());
            baseInfo.setActions(LimitActionEnum.ids2Names(ref.getActionIds()));
            baseInfo.setActionList(convert(ref.getActionIds(), riskBlacklistDataActionRefMap));
            baseInfos.add(baseInfo);
        }
        typeDetailVo.setTypes(baseInfos);

        return typeDetailVo;
    }

    private List<BlacklistTypeActionRefDto> convert(String actionIds, Map<Long, RiskBlacklistDataActionRef> riskBlacklistDataActionRefMap) {
        return Splitter.on(",").splitToList(actionIds)
                .stream()
                .map(m -> {
                    long actionId = Long.parseLong(m);
                    RiskBlacklistDataActionRef actionRef = riskBlacklistDataActionRefMap.get(actionId);
                    LimitActionEnum limitActionEnum = LimitActionEnum.fromCode(actionId);
                    BlacklistTypeActionRefDto dto = new BlacklistTypeActionRefDto();
                    dto.setActionId(actionId);
                    dto.setName(Objects.nonNull(limitActionEnum) ? limitActionEnum.getName() : "");
                    dto.setLimitTime(Objects.nonNull(actionRef) ? actionRef.getLimitTime() : 0);
                    dto.setLimitTimeType(Objects.nonNull(actionRef) ? actionRef.getLimitTimeType() : 0);
                    return dto;
                })
                .collect(Collectors.toList());

    }

    public BlacklistDataUserDetailVo getDataDetail(Long dataId){
        RiskBlacklistData blacklistData = riskBlacklistDataDao.selectByPrimaryKey(dataId);
        BlacklistDataVo blacklistDataVo = assembleDataVoBase(blacklistData, false);
        BlacklistDataUserDetailVo typeDetailVo = new BlacklistDataUserDetailVo();
        BeanUtils.copyProperties(blacklistDataVo, typeDetailVo);

        return typeDetailVo;
    }

    public void addData(BlacklistDataAddVo dataAddVo, String optContent, long adminUserId){
        doAddData(dataAddVo, optContent, () -> seaAccountService.getCurrAdminUserNameWithOrg(adminUserId));
    }

    private void doAddData(BlacklistDataAddVo dataAddVo, String optContent, Supplier<SeaAccountService.AdminUserNameWithOrg> adminSupplier){
        autoFillBindInfo(dataAddVo);
        RiskBlacklistData blacklistData = baseInfoConversionByEncrypt(dataAddVo);
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = adminSupplier.get();
        long operateId = userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId();
        String operateName = userNameWithOrg == null || StringUtils.isEmpty(userNameWithOrg.getUserNameWithOrg())
                ? "系统" : userNameWithOrg.getUserNameWithOrg();
        blacklistData.setOperateId(operateId);
        blacklistData.setOperateName(operateName);
        long defaultLimitTime = Optional.ofNullable(dataAddVo.getActionList())
                .orElse(Collections.emptyList())
                .stream()
                .max(Comparator.comparing(BlacklistTypeActionRefDto::getLimitTime))
                .map(BlacklistTypeActionRefDto::getLimitTime)
                .orElse(4102329600000L);
        blacklistData.setLimitTime(defaultLimitTime);

        String key = UPDATE_BLACKLIST_DATA_ADD_KEY;
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_BLACKLIST_DATA_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请刷新后重试");
            }

            checkUserInfoUnicity(dataAddVo, null, true,dataAddVo.getTypeIds());
            riskBlacklistDataDao.insertSelective(blacklistData);
            List<BlacklistTypeNameActionDto> nameActionDtoMap = blacklistTypeBiz.queryTypeActionsByTypeIds(dataAddVo.getTypeIds());
            Map<Long, BlacklistTypeActionRefDto> blacklistTypeActionRefDtoMap = Optional.ofNullable(dataAddVo.getActionList())
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toMap(BlacklistTypeActionRefDto::getActionId, Function.identity(), (x, y) -> x));
            if (MapUtils.isNotEmpty(blacklistTypeActionRefDtoMap)) {
                nameActionDtoMap = nameActionDtoMap.stream()
                        .peek(m -> m.setActions(m.getActions()
                                .stream()
                                .filter(f -> Objects.nonNull(blacklistTypeActionRefDtoMap.get(f)))
                                .collect(Collectors.toList())))
                        .collect(Collectors.toList());
            }
            dataActionRefDao.saveBatch(assembleDataActionRefs(blacklistData.getId(), nameActionDtoMap, dataAddVo, blacklistTypeActionRefDtoMap));
            dataTypeRefDao.saveBatch(assembleRefs(nameActionDtoMap, dataAddVo.getTypeIds(), blacklistData.getId()));
        } catch (InterruptedException e) {
            log.error("", e);
            throw new RuntimeException("服务开小差，请稍后重试");
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }

        //保存日志
        saveLog(blacklistData.getId(), optContent, dataAddVo.getOperateReason(), operateId, operateName);
    }

    private void autoFillBindInfo(BlackListDataBaseInfo baseInfo){
        if (baseInfo.getUserIdAlias() != null && baseInfo.getUserIdAlias() > 0) {
            UserInfoModel userInfo = getUserInfoByUserId(baseInfo.getUserIdAlias());
            baseInfo.setMobileBind(null);
            if (userInfo != null) {
                baseInfo.setMobileBind(shuidiCipher.decrypt(userInfo.getCryptoMobile()));
            }
        }
        baseInfo.setUserIdBind(null);
        if (StringUtils.isNotBlank(baseInfo.getMobile())) {
            baseInfo.setUserIdBind(getUserIdByMobile(baseInfo.getMobile()));
        }
    }

    public void modifyDataType(BlacklistDataTypeEditVo typeEditVo, long adminUserId){
        doModifyDataType(typeEditVo, null, ()->seaAccountService.getCurrAdminUserNameWithOrg(adminUserId));
    }

    private void doModifyDataType(BlacklistDataTypeEditVo typeEditVo, String optContent, Supplier<SeaAccountService.AdminUserNameWithOrg> adminSupplier){
        //去个重，发现前端老错传
        typeEditVo.setTypeIds(new ArrayList<>(new HashSet<>(typeEditVo.getTypeIds() == null
                ? Collections.emptyList() : typeEditVo.getTypeIds())));
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = adminSupplier.get();
        long operateId = userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId();
        String operateName = userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg();
        List<BlacklistTypeNameActionDto> nameActionDtoMap;
        Set<Long> typeIdsFromDb;
        String key = UPDATE_BLACKLIST_DATA_TYPE_KEY;
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_BLACKLIST_DATA_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请刷新后重试");
            }
            List<RiskBlacklistDataTypeRef> refs = dataTypeRefDao.listValidByDataIds(List.of(typeEditVo.getId()));
            Map<Long, Long> typeIdMapFromDb = refs.stream().collect(
                    Collectors.toMap(RiskBlacklistDataTypeRef::getTypeId, RiskBlacklistDataTypeRef::getId));
            typeIdsFromDb = typeIdMapFromDb.keySet();
            List<Long> typeIds = typeEditVo.getTypeIds();

            Set<Long> allOfTypeId = new HashSet<>(typeIds);allOfTypeId.addAll(typeIdsFromDb);
            nameActionDtoMap = blacklistTypeBiz.queryTypeActionsByTypeIds(allOfTypeId);

            Collection<Long> needAddTypeIds = CollectionUtils.subtract(typeIds, typeIdsFromDb);
            Collection<Long> needDelTypeIds = CollectionUtils.subtract(typeIdsFromDb, typeIds);
            if (CollectionUtils.isNotEmpty(needAddTypeIds) || CollectionUtils.isNotEmpty(needDelTypeIds)) {
                //保存数据与类型关系
                List<RiskBlacklistDataTypeRef> addDataTypeRefs = null;
                if (CollectionUtils.isNotEmpty(needAddTypeIds)) {
                    addDataTypeRefs = assembleRefs(nameActionDtoMap, needAddTypeIds, typeEditVo.getId());
                    dataTypeRefDao.saveBatch(addDataTypeRefs);
                }
                if (CollectionUtils.isNotEmpty(needDelTypeIds)) {
                    List<Long> refIds = needDelTypeIds.stream().map(typeIdMapFromDb::get).collect(Collectors.toList());
                    dataTypeRefDao.deleteByIds(refIds);
                }

                //保存数据与动作的关系
                Set<Long> keepActionIds = refs.stream().filter(ref -> !needDelTypeIds.contains(ref.getTypeId())).collect(Collectors.toList())
                        .stream().map(ref -> Lists.newArrayList(Splitter.on(",").split(ref.getActionIds()).iterator()))
                        .flatMap(Collection::stream).map(Long::valueOf).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(addDataTypeRefs)) {
                    keepActionIds.addAll(addDataTypeRefs.stream()
                            .map(ref->Lists.newArrayList(Splitter.on(",").split(ref.getActionIds()).iterator()))
                            .flatMap(Collection::stream).map(Long::valueOf).collect(Collectors.toList()));
                }
                modifyDataActionRefInfo(typeEditVo.getId(), keepActionIds, typeEditVo.getActionList());
            }
            updateActionLimitTime(typeEditVo);
            BlacklistTypeActionRefDto blacklistTypeActionRefDto = typeEditVo.getActionList()
                    .stream()
                    .max(Comparator.comparing(BlacklistTypeActionRefDto::getLimitTime))
                    .orElse(null);
            //更新修改原因
            RiskBlacklistData blacklistData = new RiskBlacklistData();
            blacklistData.setId(typeEditVo.getId());
            blacklistData.setOperateReason(typeEditVo.getOperateReason());
            blacklistData.setOperateId(operateId);
            blacklistData.setOperateName(operateName);
            if (Objects.nonNull(blacklistTypeActionRefDto)) {
                blacklistData.setLimitTime(blacklistTypeActionRefDto.getLimitTime());
            }
            riskBlacklistDataDao.updateOptionsById(blacklistData);
        } catch (InterruptedException e) {
            log.error("", e);
            throw new RuntimeException("服务开小差，请稍后重试");
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }

        //保存日志
        Map<Long, String> typeIdMap = nameActionDtoMap.stream().collect(
                Collectors.toMap(BlacklistTypeNameActionDto::getTypeId, BlacklistTypeNameActionDto::getTypeName));
        String oldTypeNames = Joiner.on(",").join(typeIdsFromDb.stream().map(typeIdMap::get).collect(Collectors.toList()));
        String newTypeNames = Joiner.on(",").join(typeEditVo.getTypeIds().stream().map(typeIdMap::get).collect(Collectors.toList()));
        saveModifyTypeLog(operateId, operateName, typeEditVo.getId(), typeEditVo.getOperateReason(),
                StringUtils.isBlank(optContent) ? "编辑所属黑名单类型：\""+oldTypeNames+"\"修改为\""+newTypeNames+"\"" : optContent);
    }

    private void updateActionLimitTime(BlacklistDataTypeEditVo typeEditVo) {
        List<RiskBlacklistDataActionRef> dataActionRefs = dataActionRefDao.listByDataId(typeEditVo.getId());
        if (CollectionUtils.isEmpty(dataActionRefs)) {
            return;
        }
        List<BlacklistTypeActionRefDto> typeEditVoActionList = typeEditVo.getActionList();
        if (CollectionUtils.isEmpty(typeEditVoActionList)) {
            return;
        }
        Map<Long, BlacklistTypeActionRefDto> actionRefDtoMap = typeEditVoActionList.stream()
                .collect(Collectors.toMap(BlacklistTypeActionRefDto::getActionId, Function.identity(), (x, y) -> x));
        for (RiskBlacklistDataActionRef riskBlacklistDataActionRef : dataActionRefs) {
            BlacklistTypeActionRefDto blacklistTypeActionRefDto = actionRefDtoMap.get(riskBlacklistDataActionRef.getActionId());
            if (Objects.isNull(blacklistTypeActionRefDto)) {
                continue;
            }
            if (blacklistTypeActionRefDto.getLimitTimeType() == riskBlacklistDataActionRef.getLimitTimeType() && blacklistTypeActionRefDto.getLimitTime() == riskBlacklistDataActionRef.getLimitTime()) {
                continue;
            }
            riskBlacklistDataActionRef.setLimitTime(blacklistTypeActionRefDto.getLimitTime());
            riskBlacklistDataActionRef.setLimitTimeType(blacklistTypeActionRefDto.getLimitTimeType());
            dataActionRefDao.updateActionRefById(riskBlacklistDataActionRef);
            log.info("doModifyDataType updateActionLimitTime success : {}", typeEditVo);
        }

    }

    private void modifyDataActionRefInfo(Long dataId, Set<Long> actions, List<BlacklistTypeActionRefDto> actionRefDtoList) {
        Map<Long, BlacklistTypeActionRefDto> typeActionRefDtoMap = actionRefDtoList
                .stream()
                .collect(Collectors.toMap(BlacklistTypeActionRefDto::getActionId, Function.identity(), (x, y) -> x));
        List<RiskBlacklistDataActionRef> dataActionRefs = dataActionRefDao.listByDataId(dataId);
        Map<Long, Long> actionIdMap = dataActionRefs.stream()
                .collect(Collectors.toMap(RiskBlacklistDataActionRef::getActionId, RiskBlacklistDataActionRef::getId));
        Collection<Long> needDelActions = CollectionUtils.subtract(actionIdMap.keySet(), actions);
        Collection<Long> needAddActions = CollectionUtils.subtract(actions, actionIdMap.keySet());
        if (CollectionUtils.isNotEmpty(needAddActions)) {
            dataActionRefDao.saveBatch(needAddActions.stream().map(actionId->{
                BlacklistTypeActionRefDto blacklistTypeActionRefDto = typeActionRefDtoMap.get(actionId);
                RiskBlacklistDataActionRef dataActionRef = new RiskBlacklistDataActionRef();
                dataActionRef.setDataId(dataId);
                dataActionRef.setActionId(actionId);
                if (Objects.nonNull(blacklistTypeActionRefDto)) {
                    dataActionRef.setLimitTime(blacklistTypeActionRefDto.getLimitTime());
                    dataActionRef.setLimitTimeType(blacklistTypeActionRefDto.getLimitTimeType());
                }
                return dataActionRef;
            }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(needDelActions)) {
            dataActionRefDao.deleteByIds(needDelActions.stream().map(actionIdMap::get).collect(Collectors.toList()));
        }
    }

    private List<RiskBlacklistDataTypeRef> assembleRefs(List<BlacklistTypeNameActionDto> nameActionDtoMap,
                                                        Collection<Long> typeIds, Long dataId){
        return nameActionDtoMap.stream().filter(nameActionDto -> typeIds.contains(nameActionDto.getTypeId()))
                .map(nameActionDto -> {
                    RiskBlacklistDataTypeRef ref = new RiskBlacklistDataTypeRef();
                    ref.setTypeId(nameActionDto.getTypeId());
                    ref.setDataId(dataId);
                    ref.setTypeName(nameActionDto.getTypeName());
                    ref.setActionIds(Joiner.on(",").join(nameActionDto.getActions()));
                    return ref;
                }).collect(Collectors.toList());
    }

    private List<RiskBlacklistDataActionRef> assembleDataActionRefs(
            Long dataId,
            List<BlacklistTypeNameActionDto> actionDtos,
            BlacklistDataAddVo dataAddVo,
            Map<Long, BlacklistTypeActionRefDto> blacklistTypeActionRefDtoMap) {
        return actionDtos.stream()
                .map(BlacklistTypeNameActionDto::getActions)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet())
                .stream()
                .map(actionId -> {
                    BlacklistTypeActionRefDto blacklistTypeActionRefDto = blacklistTypeActionRefDtoMap.get(actionId);
                    RiskBlacklistDataActionRef dataActionRef = new RiskBlacklistDataActionRef();
                    dataActionRef.setActionId(actionId);
                    dataActionRef.setDataId(dataId);
                    dataActionRef.setLimitTimeType(Objects.isNull(blacklistTypeActionRefDto) ? BlackActionLimitTimeType.FOREVER.getCode() : blacklistTypeActionRefDto.getLimitTimeType());
                    dataActionRef.setLimitTime(Objects.isNull(blacklistTypeActionRefDto) ? 0L : blacklistTypeActionRefDto.getLimitTime());
                    return dataActionRef;
                }).collect(Collectors.toList());
    }

    public void modifyDataInfo(BlacklistDataUserEditVo userEditVo, long adminUserId){
        SeaAccountService.AdminUserNameWithOrg userNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        long operateId = userNameWithOrg == null ? 0L : (long) userNameWithOrg.getAdminUserId();
        String operateName = userNameWithOrg == null ? "" : userNameWithOrg.getUserNameWithOrg();
        String key = UPDATE_BLACKLIST_DATA_ADD_KEY;
        String identify = null;
        try {
            identify = redissonHandler.tryLock(key, UPDATE_BLACKLIST_DATA_LEAVE_TIME);
            if (StringUtils.isBlank(identify)) {
                throw new RuntimeException("服务开小差，请刷新后重试");
            }
            RiskBlacklistData blacklistData = riskBlacklistDataDao.selectByPrimaryKey(userEditVo.getId());
            BlacklistDataVo blacklistDataVo = assembleDataVoBase(blacklistData, false);
            checkUserInfoUnicity(userEditVo, blacklistDataVo, checkUserInfoChange(blacklistDataVo, userEditVo),Lists.newArrayList());
            autoFillBindInfo(userEditVo);
            RiskBlacklistData blacklistDataEncrypt = baseInfoConversionByEncrypt(userEditVo);
            blacklistDataEncrypt.setOperateId(operateId);
            blacklistDataEncrypt.setOperateName(operateName);
            riskBlacklistDataDao.updateOptionsById(blacklistDataEncrypt);
        } catch (InterruptedException e) {
            log.error("", e);
            throw new RuntimeException("服务开小差，请稍后重试");
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    redissonHandler.unLock(key, identify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        //保存日志
        saveLog(userEditVo.getId(), "编辑用户信息", userEditVo.getOperateReason(), operateId, operateName);
    }

    public List<RiskBlacklistDataTypeRefVo> listByDataIds(List<Long> dataIds) {
        List<RiskBlacklistDataTypeRefVo> riskBlacklistDataTypeRefVos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dataIds)) {
            return riskBlacklistDataTypeRefVos;
        }

        List<RiskBlacklistDataTypeRef> riskBlacklistDataTypeRefs = dataTypeRefDao.listByDataIds(dataIds);
        log.info("riskBlacklistDataTypeRefs riskBlacklistDataTypeRefs:{}", JSON.toJSONString(riskBlacklistDataTypeRefs));
        if (CollectionUtils.isNotEmpty(riskBlacklistDataTypeRefs)) {
            for (RiskBlacklistDataTypeRef r : riskBlacklistDataTypeRefs) {
                RiskBlacklistDataTypeRefVo riskBlacklistDataTypeRefVo = new RiskBlacklistDataTypeRefVo();
                riskBlacklistDataTypeRefVo.setDataId(r.getDataId());
                riskBlacklistDataTypeRefVo.setTypeId(r.getTypeId());
                riskBlacklistDataTypeRefVos.add(riskBlacklistDataTypeRefVo);
            }
        }
        return riskBlacklistDataTypeRefVos;
    }

    public boolean checkUserInfoChange(BlacklistDataVo blacklistDataVo, BlacklistDataUserEditVo userEditVo){
        boolean haveNew = false;
        boolean haveUniqueNew = false;
        //1. 判断是否在修改数据
        Long userIdAlias = userEditVo.getUserIdAlias();
        if (userIdAlias != null && userIdAlias > 0) {
            if (blacklistDataVo.getUserIdAlias() > 0 && !Objects.equals(userIdAlias, blacklistDataVo.getUserIdAlias())) {
                throw new IllegalArgumentException("用户id已填写，不允许修改");
            }
            if (blacklistDataVo.getUserIdAlias() == 0) {
                haveNew = true;
                haveUniqueNew = true;
            }
        }
        String mobile = userEditVo.getMobile();
        String mobileFromDb = blacklistDataVo.getMobile();
        if (StringUtils.isNotBlank(mobile)) {
            if (StringUtils.isNotBlank(mobileFromDb) && !Objects.equals(mobile, mobileFromDb)) {
                throw new IllegalArgumentException("用户手机号已填写，不允许修改");
            }
            if (StringUtils.isBlank(mobileFromDb)) {
                haveNew = true;
                haveUniqueNew = true;
            }
        }
        String idCard = userEditVo.getIdCard();
        String idCardFormDb = blacklistDataVo.getIdCard();
        if (StringUtils.isNotBlank(idCard)) {
            if (StringUtils.isNotBlank(idCardFormDb) && !Objects.equals(idCard, idCardFormDb)) {
                throw new IllegalArgumentException("用户身份证已填写，不允许修改");
            }
            if (StringUtils.isBlank(idCardFormDb)) {
                haveNew = true;
                haveUniqueNew = true;
            }
        }
        String bornCard = userEditVo.getBornCard();
        String bornCardFromDb = blacklistDataVo.getBornCard();
        if (StringUtils.isNotBlank(bornCard)) {
            if (StringUtils.isNotBlank(bornCardFromDb) && !Objects.equals(bornCard, bornCardFromDb)) {
                throw new IllegalArgumentException("用户出生证已填写，不允许修改");
            }
            if (StringUtils.isBlank(bornCardFromDb)) {
                haveNew = true;
                haveUniqueNew = true;
            }
        }
        String userName = userEditVo.getUserName();
        String userNameFromDb = blacklistDataVo.getUserName();
        if (StringUtils.isNotBlank(userName) ) {
            if (StringUtils.isNotBlank(userNameFromDb) && !Objects.equals(userName, userNameFromDb)) {
                throw new IllegalArgumentException("用户姓名已填写，不允许修改");
            }
            if (StringUtils.isBlank(userNameFromDb)) {
                haveNew = true;
            }
        }

        //2. 检查是否有内容变动
        if(!haveNew) {
            throw new IllegalArgumentException("用户信息未发生变更");
        }

        return haveUniqueNew;
    }

    /**
     * 更新类型动作时，实时更新冗余字段的数据局
     * @param typeId
     * @param needAdd
     * @param needDelete
     */
    public void asyncDataActions(Long typeId, Collection<Long> needAdd, Collection<Long> needDelete){
        int limit = 500,size;
        long previousId = 0;
        List<RiskBlacklistDataTypeRef> tmpRefs;
        List<Future<?>> futures = Lists.newArrayList();

        //1. 查找所有引用该typeId的date_type_ref
        do {
            tmpRefs = dataTypeRefDao.listByTypeIdLimit(typeId, previousId, limit);
            size = tmpRefs.size();
            //2. 更新actionIds
            List<RiskBlacklistDataTypeRef> finalTmpRefs = tmpRefs;
            Future<?> future = poolTaskExecutor.submit(() -> doAsyncDataActions(finalTmpRefs, typeId, needAdd, needDelete));
            futures.add(future);
        } while (size >= limit && (previousId = tmpRefs.get(size-1).getId()) > 0);

        futures.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException|ExecutionException e) {
                log.error("", e);
            }
        });
    }

    private void doAsyncDataActions(List<RiskBlacklistDataTypeRef> dataTypeRefs, Long typeId, Collection<Long> needAdd, Collection<Long> needDelete) {
        if (CollectionUtils.isEmpty(dataTypeRefs)) {
            return;
        }
        CompletableFuture<CompletableFuture<Void>> dataActionFuture = CompletableFuture.supplyAsync(() -> {
            //查询所有dataId关联的dataTypeRef记录
            List<Long> dataIds = dataTypeRefs.parallelStream().map(RiskBlacklistDataTypeRef::getDataId).collect(Collectors.toList());
            return obtainConfigTypeRefsExcludeOne(typeId, dataIds);
        }, poolTaskExecutor).thenApplyAsync(pair -> {
            // 更新data_action_ref
            Map<Long, Set<Long>> allDataActionMap = pair.getLeft();
            Map<Long, Set<Long>> otherDataActionMap = pair.getRight();
            CompletableFuture<Void> addDataActionFuture = CompletableFuture.runAsync(() -> {
                List<RiskBlacklistDataActionRef> needAddList = allDataActionMap.entrySet().parallelStream().collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> CollectionUtils.subtract(needAdd, entry.getValue())))
                        .entrySet().parallelStream().filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                        .flatMap(entry -> entry.getValue().stream().map(actionId -> {
                            RiskBlacklistDataActionRef dataActionRef = new RiskBlacklistDataActionRef();
                            dataActionRef.setDataId(entry.getKey());
                            dataActionRef.setActionId(actionId);
                            return dataActionRef;
                        })).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(needAddList)) {
                    dataActionRefDao.saveBatch(needAddList);
                }
            }, poolTaskExecutor);
            CompletableFuture<Void> delDataActionFuture = CompletableFuture.runAsync(() -> {
                Map<Long, Set<Long>> needDeleteMap = otherDataActionMap.entrySet().parallelStream().collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> Sets.newHashSet(CollectionUtils.subtract(needDelete, entry.getValue()))))
                        .entrySet().parallelStream().filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                List<RiskBlacklistDataActionRef> needDeleteList = dataActionRefDao.listByDataIdAndActionIds(needDeleteMap.keySet(),
                        needDeleteMap.values().parallelStream().flatMap(Collection::parallelStream).collect(Collectors.toSet()));
                Map<Long, Map<Long, Long>> dataActionMap = needDeleteList.parallelStream().collect(
                        Collectors.groupingBy(RiskBlacklistDataActionRef::getDataId,
                                Collectors.toMap(RiskBlacklistDataActionRef::getActionId, RiskBlacklistDataActionRef::getId)));
                Set<Long> needDelRefIds = needDeleteMap.entrySet().parallelStream().flatMap(entry -> entry.getValue().parallelStream()
                        .map(actionId -> Optional.ofNullable(dataActionMap.get(entry.getKey()))
                                .map(actionMap->actionMap.get(actionId)).orElse(null))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet()).parallelStream())
                        .collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(needDelRefIds)) {
                    dataActionRefDao.deleteByIds(needDelRefIds);
                }
            }, poolTaskExecutor);
            // 更新data_type_ref
            CompletableFuture<Void> dataTypeFuture = CompletableFuture.runAsync(() -> {
                List<Long> finalTypeActions = Lists.newArrayList(Splitter.on(",").split(dataTypeRefs.get(0).getActionIds()))
                        .stream().map(Long::parseLong).collect(Collectors.toList());
                finalTypeActions.addAll(needAdd);
                finalTypeActions = Lists.newArrayList(CollectionUtils.subtract(finalTypeActions, needDelete));
                finalTypeActions.sort(Long::compareTo);
                dataTypeRefDao.updateActionIdsByIds(dataTypeRefs.parallelStream().map(RiskBlacklistDataTypeRef::getId).collect(Collectors.toList()),
                        Joiner.on(",").join(Sets.newLinkedHashSet(finalTypeActions)));
            });

            return addDataActionFuture.runAfterBoth(delDataActionFuture, () -> {}).runAfterBoth(dataTypeFuture, () -> {});
        }, poolTaskExecutor);

        try {
            dataActionFuture.get();
        } catch (InterruptedException|ExecutionException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }

    private Pair<Map<Long, Set<Long>>, Map<Long, Set<Long>>> obtainConfigTypeRefsExcludeOne(Long excludeTypeId, List<Long> dataIds){
        List<RiskBlacklistDataTypeRef> dataTypeRefByDataList = dataTypeRefDao.listByDataIds(dataIds);
        Collector<RiskBlacklistDataTypeRef, Set<Long>, Set<Long>> collector = Collector.of(HashSet::new,
                (objects, dataActionRef) -> {
                    if (StringUtils.isNotBlank(dataActionRef.getActionIds())) {
                        objects.addAll(Lists.newArrayList(Splitter.on(",").split(dataActionRef.getActionIds()))
                                .stream().map(Long::parseLong).collect(Collectors.toList()));
                    }
                }, (left, right) -> {left.addAll(right);return left;});
        return Pair.of(dataTypeRefByDataList.parallelStream().collect(
                Collectors.groupingBy(RiskBlacklistDataTypeRef::getDataId, collector)), dataTypeRefByDataList.parallelStream()
                .peek(dataTypeRef -> {
                    //将需要排除的类型的动作设为空
                    if (Objects.equals(dataTypeRef.getTypeId(), excludeTypeId)) {
                        dataTypeRef.setActionIds("");
                    }
                })
                .collect(Collectors.groupingBy(RiskBlacklistDataTypeRef::getDataId, collector)));
    }

    public UserInfoModel getUserInfoByUserId(Long userId){
        log.info("call account params, userId:{}", userId);
        UserInfoModel userInfo = userInfoDelegateService.getUserInfoByUserId(userId);
        log.info("call account user info response:{}", userInfo);
        return userInfo;
    }

    public Long getUserIdByMobile(String mobile){
        log.info("call account params, mobile:{}", mobile);
        MobileUserIdModel userIdByMobile = userAccountDelegateService.getUserIdByMobile(mobile);
        log.info("call account user id response:{}", userIdByMobile);
        return userIdByMobile == null ? 0L : userIdByMobile.getUserId();
    }

    public List<BlacklistDataDto> queryNotBoundUid(Long previousId,
                                                   @NotNull(message = "limit不能为空") @Min(value = 1, message = "至少1条") Integer limit) {
        return riskBlacklistDataDao.listNotBoundUidLimit(previousId, limit).stream().map(data -> {
            BlacklistDataDto blacklistDataDto = new BlacklistDataDto();
            blacklistDataDto.setId(data.getId());
            blacklistDataDto.setMobile(shuidiCipher.decrypt(data.getEncryptMobile()));
            return blacklistDataDto;
        }).collect(Collectors.toList());
    }

    public void updateBindUidByDataId(List<UpdateBatchDto> updateBatchDtos) {
        if (CollectionUtils.isEmpty(updateBatchDtos)) {
            return;
        }
        for (UpdateBatchDto updateBatchDto : updateBatchDtos) {
            RiskBlacklistData blacklistData = new RiskBlacklistData();
            blacklistData.setId(updateBatchDto.getDataId());
            blacklistData.setUserIdBind(Long.valueOf(String.valueOf(updateBatchDto.getDestVal())));
            riskBlacklistDataDao.updateOptionsById(blacklistData);
        }
    }

    public List<BlacklistDataDto> queryNotBoundMobile(Long previousId,
                                                      @NotNull(message = "limit不能为空") @Min(value = 1, message = "至少1条") Integer limit) {
        return riskBlacklistDataDao.listNotBoundMobileLimit(previousId, limit).stream().map(data -> {
            BlacklistDataDto blacklistDataDto = new BlacklistDataDto();
            blacklistDataDto.setId(data.getId());
            blacklistDataDto.setUserId(data.getUserId());
            return blacklistDataDto;
        }).collect(Collectors.toList());
    }

    public void updateBindMobileByDataId(List<UpdateBatchDto> updateBatchDtos) {
        if (CollectionUtils.isEmpty(updateBatchDtos)) {
            return;
        }
        for (UpdateBatchDto updateBatchDto : updateBatchDtos) {
            RiskBlacklistData blacklistData = new RiskBlacklistData();
            blacklistData.setId(updateBatchDto.getDataId());
            blacklistData.setEncryptMobileBind((String) updateBatchDto.getDestVal());
            riskBlacklistDataDao.updateOptionsById(blacklistData);
        }
    }

    public List<BlacklistDataLogVo> queryDatLogByDataId(Long dataId){
        return riskBlacklistDataLogDao.listByDataId(dataId).stream().map(BlacklistDataLogVo::new).collect(Collectors.toList());
    }

    public List<BlacklistDataDto> listByOperateNameAndTime(String operateName, long beginTime, long endTime) {
        return riskBlacklistDataDao.listByOperateNameAndTime(operateName, com.shuidihuzhu.msg.util.DateUtil.getLongToDate(beginTime)
                , com.shuidihuzhu.msg.util.DateUtil.getLongToDate(endTime)).stream().map(data -> {
            BlacklistDataDto blacklistDataDto = new BlacklistDataDto();
            blacklistDataDto.setId(data.getId());
            blacklistDataDto.setUserId(data.getUserId());
            blacklistDataDto.setBornCard(data.getEncryptBornCard());
            blacklistDataDto.setCreateTime(data.getCreateTime());
            blacklistDataDto.setIdCard(data.getEncryptIdCard());
            blacklistDataDto.setMobile(data.getEncryptMobile());
            blacklistDataDto.setMobileBind(data.getEncryptMobileBind());
            blacklistDataDto.setUserIdBind(data.getUserIdBind());
            return blacklistDataDto;
        }).collect(Collectors.toList());
    }

    public int countByOperateNameAndTime(String operateName, long beginTime, long endTime) {
        return riskBlacklistDataDao.countByOperateNameAndTime(operateName, com.shuidihuzhu.msg.util.DateUtil.getLongToDate(beginTime)
                , com.shuidihuzhu.msg.util.DateUtil.getLongToDate(endTime));
    }

    private void checkUserInfoUnicity(BlackListDataBaseInfo baseInfo, BlacklistDataVo blacklistDataVo, boolean isUniqueChange,List<Long> typeIds) {
        if (!isUniqueChange) {
            return;
        }
        Long userId = null;
        String cryptoMobile = null;
        String cryptoIdCard = null;
        String cryptoBornCard = null;
        Long userIdAlias = baseInfo.getUserIdAlias();
        if (userIdAlias != null && userIdAlias > 0 &&
                (blacklistDataVo == null || !Objects.equals(userIdAlias, blacklistDataVo.getUserIdAlias()))) {
            userId = userIdAlias;
        }
        String mobile = baseInfo.getMobile();
        if (StringUtils.isNotBlank(mobile) &&
                (blacklistDataVo == null || !Objects.equals(mobile, blacklistDataVo.getMobile()))) {
            cryptoMobile = oldShuidiCipher.aesEncrypt(mobile);
        }
        String idCard = baseInfo.getIdCard();
        if (StringUtils.isNotBlank(idCard) &&
                (blacklistDataVo == null || !Objects.equals(idCard, blacklistDataVo.getIdCard()))) {
            cryptoIdCard = oldShuidiCipher.aesEncrypt(idCard);
        }
        String bornCard = baseInfo.getBornCard();
        if (StringUtils.isNotBlank(bornCard) &&
                (blacklistDataVo == null || !Objects.equals(bornCard, blacklistDataVo.getBornCard()))) {
            cryptoBornCard = oldShuidiCipher.aesEncrypt(bornCard);
        }
        List<RiskBlacklistData> repeatList = riskBlacklistDataDao.getByUserIdOrCryptoIdCardOrMobileOrBornCard(
                Optional.ofNullable(userId).map(List::of).orElse(null),
                Optional.ofNullable(cryptoMobile).filter(StringUtils::isNotBlank).map(List::of).orElse(null),
                Optional.ofNullable(cryptoIdCard).filter(StringUtils::isNotBlank).map(List::of).orElse(null),
                Optional.ofNullable(cryptoBornCard).filter(StringUtils::isNotBlank).map(List::of).orElse(null));
        //去个重
        TreeSet<RiskBlacklistData> repeatSet = new TreeSet<>(Comparator.comparingLong(RiskBlacklistData::getId));
        repeatSet.addAll(repeatList);

        //查询黑名单类型
        Map<Long, Long> riskBlacklistDataTypeRefMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(repeatList)) {
            List<Long> ids = repeatList.stream().map(RiskBlacklistData::getId).collect(Collectors.toList());
            List<RiskBlacklistDataTypeRef> riskBlacklistDataTypeRefList = dataTypeRefDao.listByDataIds(ids);
            riskBlacklistDataTypeRefMap = riskBlacklistDataTypeRefList.stream().collect(Collectors.toMap(RiskBlacklistDataTypeRef::getDataId, RiskBlacklistDataTypeRef::getTypeId, (old, news) -> news));
        }
        Set<String> errorMsg = Sets.newHashSet();
        for (RiskBlacklistData blacklistData : repeatSet) {
            Long typeId = riskBlacklistDataTypeRefMap.get(blacklistData.getId());
            if (userId != null && Objects.equals(blacklistData.getUserId(), userId) && typeIds.contains(typeId)) {
                errorMsg.add("用户id已经存在在黑名单库中,无需重复添加");
            }
            if (cryptoMobile != null && Objects.equals(blacklistData.getEncryptMobile(), cryptoMobile) && typeIds.contains(typeId)) {
                errorMsg.add("手机号已经存在在黑名单库中,无需重复添加");
            }
            if (cryptoIdCard != null && Objects.equals(blacklistData.getEncryptIdCard(), cryptoIdCard) && typeIds.contains(typeId)) {
                errorMsg.add("身份证号已经存在在黑名单库中,无需重复添加");
            }
            if (cryptoBornCard != null && Objects.equals(blacklistData.getEncryptBornCard(), cryptoBornCard) && typeIds.contains(typeId)) {
                errorMsg.add("出生证号已经存在在黑名单库中,无需重复添加");
            }
        }
        if (CollectionUtils.isNotEmpty(errorMsg)) {
            throw new IllegalArgumentException(Joiner.on(";").join(errorMsg));
        }
    }

    private BlacklistDataVo assembleDataVoBase(RiskBlacklistData blacklistData, boolean cover){
        BlacklistDataVo blacklistDataVo = new BlacklistDataVo();
        BeanUtils.copyProperties(blacklistData, blacklistDataVo);
        blacklistDataVo.setUserIdAlias(blacklistData.getUserId());
        blacklistDataVo.setUpdateTime(DateUtil.getDate2LStr(blacklistData.getUpdateTime()));
        String encryptMobile = blacklistData.getEncryptMobile();
        if (StringUtils.isNotBlank(encryptMobile)) {
            String decrypt = shuidiCipher.decrypt(encryptMobile);
            blacklistDataVo.setMobile(cover ? shuidiCipher.decryptAndDesensitize(encryptMobile, DesensitizeEnum.MOBILE) : decrypt);
        }
        String encryptMobileBind = blacklistData.getEncryptMobileBind();
        if (StringUtils.isNotBlank(encryptMobileBind)) {
            String decrypt = shuidiCipher.decrypt(encryptMobileBind);
            blacklistDataVo.setMobileBind(cover ? shuidiCipher.decryptAndDesensitize(encryptMobileBind, DesensitizeEnum.MOBILE) : decrypt);
        }
        String encryptIdCard = blacklistData.getEncryptIdCard();
        if (StringUtils.isNotBlank(encryptIdCard)) {
            String decrypt = shuidiCipher.decrypt(encryptIdCard);
            blacklistDataVo.setIdCard(cover ? shuidiCipher.decryptAndDesensitize(encryptIdCard, DesensitizeEnum.IDCARD) : decrypt);
        }
        String encryptBornCard = blacklistData.getEncryptBornCard();
        if (StringUtils.isNotBlank(encryptBornCard)) {
            blacklistDataVo.setBornCard(shuidiCipher.decrypt(encryptBornCard));
        }
        Byte isDelete = blacklistData.getIsDelete();
        blacklistDataVo.setIsDelete(isDelete == 1);
        return blacklistDataVo;
    }

    private RiskBlacklistData baseInfoConversionByEncrypt(BlackListDataBaseInfo baseInfo){
        RiskBlacklistData blacklistData = new RiskBlacklistData();
        BeanUtils.copyProperties(baseInfo, blacklistData);
        blacklistData.setUserId(baseInfo.getUserIdAlias());
        String mobile = baseInfo.getMobile();
        if (StringUtils.isNotBlank(mobile)) {
            blacklistData.setEncryptMobile(oldShuidiCipher.aesEncrypt(mobile));
        }
        String mobileBind = baseInfo.getMobileBind();
        if (StringUtils.isNotBlank(mobileBind)) {
            blacklistData.setEncryptMobileBind(oldShuidiCipher.aesEncrypt(mobileBind));
        }
        String idCard = baseInfo.getIdCard();
        if (StringUtils.isNotBlank(idCard)) {
            blacklistData.setEncryptIdCard(oldShuidiCipher.aesEncrypt(idCard));
        }
        String bornCard = baseInfo.getBornCard();
        if (StringUtils.isNotBlank(bornCard)) {
            blacklistData.setEncryptBornCard(oldShuidiCipher.aesEncrypt(bornCard));
        }
        return blacklistData;
    }

    private void setEncryptForQuery(BlacklistDataQuery query){
        String mobile = query.getMobile();
        if (StringUtils.isNotBlank(mobile)) {
            query.setMobile(oldShuidiCipher.aesEncrypt(mobile));
        }
        String idCard = query.getIdCard();
        if (StringUtils.isNotBlank(idCard)) {
            query.setIdCard(oldShuidiCipher.aesEncrypt(idCard));
        }
        String bornCard = query.getBornCard();
        if (StringUtils.isNotBlank(bornCard)) {
            query.setBornCard(oldShuidiCipher.aesEncrypt(bornCard));
        }
    }

    /**
     * 预审增加“自动添加黑名单”功能，添加黑名单数据
     */
    public void saveAutoAddPreTrial(BlacklistDataAutoAddVo dataAutoAddVo, long adminUserId) {
        List<BlacklistTypeNameActionDto> typeNameActionDtoList = blacklistTypeBiz.queryTypeActionsByTypeIds(
                List.of(dataAutoAddVo.getBlackListTypeId()));
        if (CollectionUtils.isEmpty(typeNameActionDtoList)) {
            throw new IllegalArgumentException("黑名单类型不存在");
        }
        BlacklistTypeNameActionDto typeNameActionDto = typeNameActionDtoList.get(0);
        BlacklistDataAutoAggVo blacklistDataAutoAggVo = assembleAutoAddBlacklistData(dataAutoAddVo.getCaseId(), typeNameActionDto);
        for (BlacklistDataAutoVo blacklistDataAutoVo : blacklistDataAutoAggVo.getBlacklistDataAutoVos()) {
            BlacklistDataAddVo blacklistDataAddVo = new BlacklistDataAddVo();
            BeanUtils.copyProperties(blacklistDataAutoVo, blacklistDataAddVo);
            blacklistDataAddVo.setTypeIds(blacklistDataAutoVo.getTypeIdNames().stream().map(Pair::getKey)
                    .collect(Collectors.toList()));
            blacklistDataAddVo.setOperateReason(dataAutoAddVo.getOperateReason());
            blacklistDataAddVo.setActionList(dataAutoAddVo.getActionList());
            addData(blacklistDataAddVo, "预审"+blacklistDataAutoAggVo.getAutoAddType().getValue()+"，案例ID为"+dataAutoAddVo.getCaseId(), adminUserId);
        }
    }

    /**
     * 预审增加“自动添加黑名单”功能，黑名单数据拼接
     */
    public BlacklistDataAutoAggVo autoAddPreTrialGetDetail(Integer caseId, long blackListTypeId) {
        BlacklistDataAutoAggVo dataAutoAggVo = assembleAutoAddBlacklistData(caseId, null);
        //设置掩码
        dataAutoAggVo.getBlacklistDataAutoVos().forEach(blacklistDataAutoVo -> {
            if (StringUtils.isNotEmpty(blacklistDataAutoVo.getIdCard()) || StringUtils.isNotEmpty(blacklistDataAutoVo.getBornCard())) {
                BlacklistDataQuery blacklistDataQuery = new BlacklistDataQuery();
                blacklistDataQuery.setTypeId(blackListTypeId);
                if (StringUtils.isNotEmpty(blacklistDataAutoVo.getIdCard())) {
                    blacklistDataQuery.setIdCard(oldShuidiCipher.aesEncrypt(blacklistDataAutoVo.getIdCard()));
                }
                if (StringUtils.isNotEmpty(blacklistDataAutoVo.getBornCard())) {
                    blacklistDataQuery.setBornCard(blacklistDataAutoVo.getBornCard());
                }
                List<RiskBlacklistData> riskBlacklistData = riskBlacklistDataDao.listByOptions(blacklistDataQuery);
                long blackListId = Optional.ofNullable(riskBlacklistData)
                        .orElse(Collections.emptyList())
                        .stream()
                        .findFirst()
                        .map(RiskBlacklistData::getId)
                        .orElse(0L);
                blacklistDataAutoVo.setBlackListId(blackListId);
            }
            Optional.ofNullable(blacklistDataAutoVo.getIdCard()).filter(StringUtils::isNotBlank)
                    .ifPresent(idCard -> blacklistDataAutoVo.setIdCard(MixtureUtil.mixtureIdCard(idCard)));
                });

        return dataAutoAggVo;
    }

    private BlacklistDataAutoAggVo assembleAutoAddBlacklistData(Integer caseId, BlacklistTypeNameActionDto typeNameActionDto) {
        FeignResponse<CfFirsApproveMaterial> response = authorClient.getAuthorInfoByInfoId(caseId);
        log.info("查询预审信息, caseId:{}, resp:{}", caseId, response);
        if (response.notOk()) {
            throw new RuntimeException();
        }
        CfFirsApproveMaterial authorInfo = response.getData();
        if (authorInfo.getUserRelationTypeForC() == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.NO.getCode()) {
            throw new IllegalArgumentException("非3100版本案例");
        }

        //本人：发起人加入黑名单
        if (authorInfo.getUserRelationTypeForC() == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.SELF.getCode()) {
            return new BlacklistDataAutoAggVo(Pair.of(BlacklistAutoAddTypeEnum.INITIATOR.getCode(), BlacklistAutoAddTypeEnum.INITIATOR.getDesc()),
                    List.of(assembleSelfData(typeNameActionDto, authorInfo)));
        }
        //非直系亲属：患者加入黑名单
        if (nonImmediateRelative(authorInfo.getUserRelationTypeForC())) {
            return new BlacklistDataAutoAggVo(Pair.of(BlacklistAutoAddTypeEnum.PATIENT.getCode(), BlacklistAutoAddTypeEnum.PATIENT.getDesc()),
                    List.of(assembleNonImmediateData(typeNameActionDto, authorInfo)));
        }
        //直系亲属：发起人信息&患者信息加入黑名单
        if (immediateRelative(authorInfo.getUserRelationTypeForC())) {
            return new BlacklistDataAutoAggVo(Pair.of(BlacklistAutoAddTypeEnum.INITIATOR_PATIENT.getCode(), BlacklistAutoAddTypeEnum.INITIATOR_PATIENT.getDesc()),
                    List.of(assembleSelfData(typeNameActionDto, authorInfo), assembleNonImmediateData(typeNameActionDto, authorInfo)));
        }

        throw new IllegalArgumentException("未知的发起人和患者关系");
    }

    private BlacklistDataAutoVo assembleSelfData(BlacklistTypeNameActionDto typeNameActionDto, CfFirsApproveMaterial authorInfo){
        UserInfoModel userInfoModel = userInfoDelegateService.getUserInfoByUserId(authorInfo.getUserId());

        BlacklistDataAutoVo blacklistData = new BlacklistDataAutoVo();
        if (Objects.nonNull(typeNameActionDto)) {
            blacklistData.setTypeIdNames(List.of(Pair.of(typeNameActionDto.getTypeId(), typeNameActionDto.getTypeName())));
            blacklistData.setAction(Joiner.on(",").join(LimitActionEnum.ids2Names(typeNameActionDto.getActions())));
            blacklistData.setActionList(typeNameActionDto.getActionList());
        }
        CfCaseSpecialPrePoseDetail prePoseDetail = obtainClewPrePoseMaterial(authorInfo.getInfoId());
        blacklistData.setIdCard(Optional.of(authorInfo.getUserRelationTypeForC() == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.SELF.getCode())
                .filter(Boolean::booleanValue)
                .map(b->shuidiCipher.decrypt(authorInfo.getPatientCryptoIdcard()))
                .orElse(shuidiCipher.decrypt(authorInfo.getSelfCryptoIdcard())));
        blacklistData.setMobile(Optional.ofNullable(prePoseDetail)
                .map(prePose -> Optional.ofNullable(prePose.getMobile()).filter(StringUtils::isNotBlank).orElse(""))
                .orElse(shuidiCipher.decrypt(userInfoModel.getCryptoMobile())));
        Optional.of(blacklistData.getMobile()).filter(StringUtils::isNotBlank)
                .ifPresent(mobile->blacklistData.setUserIdBind(getUserIdByMobile(mobile)));
        blacklistData.setUserName(Optional.of(authorInfo.getUserRelationTypeForC() == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.SELF.getCode())
                .filter(Boolean::booleanValue)
                .map(b->authorInfo.getPatientRealName())
                .orElse(authorInfo.getSelfRealName()));

        return blacklistData;
    }

    private BlacklistDataAutoVo assembleNonImmediateData(BlacklistTypeNameActionDto typeNameActionDto, CfFirsApproveMaterial authorInfo){
        BlacklistDataAutoVo blacklistData = new BlacklistDataAutoVo();
        if (Objects.nonNull(typeNameActionDto)) {
            blacklistData.setTypeIdNames(List.of(Pair.of(typeNameActionDto.getTypeId(), typeNameActionDto.getTypeName())));
            blacklistData.setAction(Joiner.on(",").join(LimitActionEnum.ids2Names(typeNameActionDto.getActions())));
            blacklistData.setActionList(typeNameActionDto.getActionList());
        }

        if (authorInfo.getPatientIdType() == PreposeMaterialModel.PatientIdCardTypeEnum.IDENTITY_CARD.getCode()) {
            blacklistData.setIdCard(shuidiCipher.decrypt(authorInfo.getPatientCryptoIdcard()));
        }
        if (authorInfo.getPatientIdType() == PreposeMaterialModel.PatientIdCardTypeEnum.BIRTH_CERTIFICATE.getCode()) {
            blacklistData.setBornCard(authorInfo.getPatientBornCard());
        }
        blacklistData.setUserName(authorInfo.getPatientRealName());

        return blacklistData;
    }

    private CfCaseSpecialPrePoseDetail obtainClewPrePoseMaterial(Integer caseId){
        Response<List<CfCaseSpecialPrePoseDetail>> response = clewPreproseMaterialClient.getSpecialPrePoseDetail(List.of(caseId));
        log.info("查询案例线索信息, caseId:{}, resp:{}", caseId, response);
        if (response.notOk()) {
            throw new RuntimeException();
        }
        return Optional.ofNullable(response.getData())
                .filter(CollectionUtils::isNotEmpty)
                .map(prePoseDetails->prePoseDetails.get(0))
                .filter(prePoseDetail -> Objects.equals(prePoseDetail.getCaseId(), caseId))
                .orElse(null);
    }

    private boolean nonImmediateRelative(int relationTypeCode){
        return relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.OTHER.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.FAMILY.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.FRIENDS.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.RELATIONS.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.DONATOR.getCode();
    }

    private boolean immediateRelative(int relationTypeCode){
        return relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.FATHER.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.MATHER.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.SON.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.DAUGHTER.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.HUSBAND.getCode() ||
                relationTypeCode == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.WIFE.getCode();
    }

    /**
     * 异常多次证实自动添加UGC黑名单
     */
    public void saveAutoAddVerify(VerifyAutoAddBlacklistVo verifyAutoAddBlacklistVo){
        List<Long> userIds = verifyAutoAddBlacklistVo.getUserIds();
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        String modifyTypeLockKey = UPDATE_BLACKLIST_DATA_TYPE_KEY;
        String modifyTypeLockIdentify = null;
        String addLockKey = UPDATE_BLACKLIST_DATA_ADD_KEY;
        String addLockIdentify = null;
        try {
            modifyTypeLockIdentify = redissonHandler.tryLock(modifyTypeLockKey, WAIT_LOCK_TIME_MILLI, UPDATE_BLACKLIST_DATA_LEAVE_TIME);
            addLockIdentify = redissonHandler.tryLock(addLockKey, WAIT_LOCK_TIME_MILLI, UPDATE_BLACKLIST_DATA_LEAVE_TIME);
            if (StringUtils.isAnyBlank(modifyTypeLockIdentify, addLockIdentify)) {
                throw new RuntimeException("服务开小差，请刷新后重试");
            }
            doVerifyAutoAdd(userIds);
        } catch (InterruptedException e) {
            log.error("", e);
            throw new RuntimeException("服务开小差，请稍后重试");
        } finally {
            if (StringUtils.isNotBlank(modifyTypeLockIdentify)) {
                try {
                    redissonHandler.unLock(modifyTypeLockKey, modifyTypeLockIdentify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            if (StringUtils.isNotBlank(addLockIdentify)) {
                try {
                    redissonHandler.unLock(addLockKey, addLockIdentify);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
    }

    private void doVerifyAutoAdd(List<Long> userIds){
        Map<Long, Long> existUserIdMap = riskBlacklistDataDao.listByUserIds(userIds).stream()
                .collect(Collectors.toMap(RiskBlacklistData::getUserId, RiskBlacklistData::getId));
        Collection<Long> needAddUserIds = CollectionUtils.subtract(userIds, existUserIdMap.keySet());
        if (MapUtils.isNotEmpty(existUserIdMap)) {
            Map<Long, Set<Long>> existDataTypeIdsMap = dataTypeRefDao.listValidByDataIds(existUserIdMap.values()).stream()
                    .collect(Collectors.groupingBy(RiskBlacklistDataTypeRef::getDataId,
                            Collector.of(HashSet::new, (list, o) -> list.add(o.getTypeId()),
                                    (list1, list2) -> {list1.addAll(list2);return list1;})));
            List<Long> needAddTypeDataIds = existUserIdMap.values().stream()
                    .filter(dataId -> Optional.ofNullable(existDataTypeIdsMap.get(dataId))
                            .map(typeIds -> !typeIds.contains(verifyAutoAddBlacklistTypeId))
                            .orElse(true))
                    .collect(Collectors.toList());
            //增加类型
            log.info("需要增加类型的dataIds:{}", needAddTypeDataIds);
            for (Long dataId : needAddTypeDataIds) {
                Set<Long> existTypeIds = Optional.ofNullable(existDataTypeIdsMap.get(dataId)).orElse(Sets.newHashSet());
                existTypeIds.add(verifyAutoAddBlacklistTypeId);
                doModifyDataType(new BlacklistDataTypeEditVo(dataId, Lists.newArrayList(existTypeIds),
                                "命中异常多次证实识别策略，且该证实被处理为仅自己可见", Collections.emptyList()), "自动新增黑名单类型",
                        () -> new SeaAccountService.AdminUserNameWithOrg(0, "系统", ""));
            }
        }
        //增加数据和类型
        log.info("需要增加数据的userIds:{}", needAddUserIds);
        for (Long userId : needAddUserIds) {
            BlacklistDataAddVo blacklistDataAddVo = new BlacklistDataAddVo();
            blacklistDataAddVo.setUserIdAlias(userId);
            blacklistDataAddVo.setOperateReason("命中异常多次证实识别策略，且该证实被处理为仅自己可见");
            blacklistDataAddVo.setTypeIds(List.of(verifyAutoAddBlacklistTypeId));
            doAddData(blacklistDataAddVo, "自动新增黑名单用户",
                    () -> new SeaAccountService.AdminUserNameWithOrg(0, "系统", ""));
        }
    }

    private void saveModifyTypeLog(Long operateId, String operateName, Long dataId, String operateReason, String modifyContent) {
        saveLog(dataId, modifyContent, operateReason, operateId, operateName);
    }

    private void saveLog(Long dataId, String modifyContent, String modifyReason, Long operateId, String operateName) {
        List<RiskBlacklistDataActionRef> riskBlacklistDataActionRefs = dataActionRefDao.listByDataId(dataId);
        List<String> collect = riskBlacklistDataActionRefs.stream()
                .map(m -> Optional.ofNullable(LimitActionEnum.fromCode(m.getActionId()))
                        .map(LimitActionEnum::getName)
                        .orElse("") + "--"
                        + Optional.ofNullable(BlackActionLimitTimeType.fromCode(m.getLimitTimeType()))
                        .map(BlackActionLimitTimeType::getDesc)
                        .orElse(""))
                .collect(Collectors.toList());
        String join = Joiner.on(";").join(collect);
        RiskBlacklistDataLog dataLog = new RiskBlacklistDataLog();
        dataLog.setOperateId(Objects.requireNonNullElse(operateId, 0L));
        dataLog.setOperateName(StringUtils.isEmpty(operateName) ? "系统" : operateName);
        dataLog.setDataId(dataId);
        dataLog.setModifyContent(modifyContent + join);
        dataLog.setOperateReason(modifyReason);
        riskBlacklistDataLogDao.insertSelective(dataLog);

        MaliMQComponent.builder()
                .setTags(CfRiskMQTagCons.BLACK_LIST_UPDATE)
                .setPayload("{}")
                .send();
    }

    public void autoDeleteBlackList(long adminUserId) {
        Date date = new Date();
        date = DateUtil.addDays(date, 1);
        String date2SStr = DateUtil.getDate2SStr(date);
        Date startDate = DateUtil.getStr2SDate(date2SStr);
        List<RiskBlacklistData> byLimitTime = riskBlacklistDataDao.getByLimitTime(startDate.getTime());
        if (CollectionUtils.isNotEmpty(byLimitTime)) {
            byLimitTime.forEach(f -> {
                int deleteBlackListData = deleteBlackListData(f.getId(), "限制时间已到", adminUserId);
                if (deleteBlackListData == 0) {
                    log.info("BlackListDataService autoDeleteBlackList error {}", f.getId());
                }
            });
        }

        List<RiskBlacklistDataActionRef> blacklistDataActionRefList = dataActionRefDao.getByLimitTime(startDate.getTime());
        if (CollectionUtils.isNotEmpty(blacklistDataActionRefList)) {
            List<Long> idList = blacklistDataActionRefList.stream()
                    .map(RiskBlacklistDataActionRef::getId)
                    .collect(Collectors.toList());
            List<Long> dataIdList = blacklistDataActionRefList.stream()
                    .map(RiskBlacklistDataActionRef::getDataId)
                    .collect(Collectors.toList());
            dataActionRefDao.deleteByIds(idList);
            List<RiskBlacklistDataTypeRef> riskBlacklistDataTypeRefs = dataTypeRefDao.listByDataIds(dataIdList);
            for (RiskBlacklistDataTypeRef typeRef : riskBlacklistDataTypeRefs) {
                List<RiskBlacklistDataActionRef> riskBlacklistDataActionRefs = dataActionRefDao.listByDataId(typeRef.getDataId());
                String join = Joiner.on(",").join(riskBlacklistDataActionRefs.stream()
                        .map(RiskBlacklistDataActionRef::getActionId)
                        .collect(Collectors.toList()));
                if (StringUtils.isEmpty(join)) {
                    int deleteBlackListData = deleteBlackListData(typeRef.getDataId(), "限制时间已到", adminUserId);
                    if (deleteBlackListData == 0) {
                        log.info("BlackListDataService autoDeleteBlackList error {}", typeRef.getDataId());
                    }
                    continue;
                }
                dataTypeRefDao.updateActionIdsByIds(Collections.singleton(typeRef.getId()), join);
            }
        }

    }

    public int deleteBlackListData(long id, String reason, long adminUserId) {
        int deleteBlackListData = riskBlacklistDataDao.deleteBlackListData(id);
        if (deleteBlackListData == 0) {
            return 0;
        }
        List<RiskBlacklistDataActionRef> riskBlacklistDataActionRefs = dataActionRefDao.listByDataId(id);
        List<Long> actionIdList = riskBlacklistDataActionRefs.stream()
                .map(RiskBlacklistDataActionRef::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(actionIdList)) {
            dataActionRefDao.deleteByIds(actionIdList);
        }
        List<RiskBlacklistDataTypeRef> riskBlacklistDataTypeRefs = dataTypeRefDao.listByDataIds(Collections.singletonList(id));
        List<Long> dataTypeIdList = riskBlacklistDataTypeRefs.stream()
                .map(RiskBlacklistDataTypeRef::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dataTypeIdList)) {
            dataTypeRefDao.deleteByIds(dataTypeIdList);
        }
        SeaAccountService.AdminUserNameWithOrg currAdminUserNameWithOrg = seaAccountService.getCurrAdminUserNameWithOrg(adminUserId);
        long operateId = currAdminUserNameWithOrg == null ? 0L : (long) currAdminUserNameWithOrg.getAdminUserId();
        String operateName = currAdminUserNameWithOrg == null ? "" : currAdminUserNameWithOrg.getUserNameWithOrg();
        saveLog(id, "删除黑名单用户", reason, operateId, operateName);
        return deleteBlackListData;
    }

    public void autoAddBlacklist(RiskHitOperateVo riskHitOperateVo) {
        Integer caseId = riskHitOperateVo.getCaseId();
        RpcResult<CfFirsApproveMaterial> firsApproveMaterialRpcResult = cfFirstApproveClient.selectFirstApproveByCaseId(caseId);
        CfFirsApproveMaterial cfFirsApproveMaterial = Optional.ofNullable(firsApproveMaterialRpcResult)
                .map(RpcResult::getData)
                .orElse(null);
        if (Objects.isNull(cfFirsApproveMaterial)) {
            log.info("BlackListDataService autoAddBlacklist cfFirsApproveMaterial is null {}", caseId);
            return;
        }
        RiskBlacklistType riskBlacklistType = riskBlacklistTypeDao.listByEnabledTypeNames(Collections.singleton("实锤库-实锤商业转发/刷单-实锤重复捐单"))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(riskBlacklistType)) {
            log.info("BlackListDataService autoAddBlacklist riskBlacklistType is null 实锤库-实锤商业转发/刷单-实锤重复捐单");
            return;
        }
        riskHitOperateVo.setTypeId(riskBlacklistType.getId());
        long oldBlackList = updateOldBlackList(riskHitOperateVo, cfFirsApproveMaterial);
        if (oldBlackList > 0) {
            log.info("BlackListDataService autoAddBlacklist updateOldBlackList success {}", riskHitOperateVo);
            return;
        }
        BlacklistDataAddVo dataAddVo = new BlacklistDataAddVo();
        dataAddVo.setActionList(riskHitOperateVo.getActionList());
        dataAddVo.setOperateReason(riskHitOperateVo.getRemark());
        dataAddVo.setTypeIds(Collections.singletonList(riskBlacklistType.getId()));
        if (StringUtils.isEmpty(cfFirsApproveMaterial.getPatientBornCard()) && StringUtils.isEmpty(cfFirsApproveMaterial.getPatientCryptoIdcard())) {
            dataAddVo.setIdCard(shuidiCipher.decrypt(cfFirsApproveMaterial.getSelfCryptoIdcard()));
            dataAddVo.setUserName(cfFirsApproveMaterial.getSelfRealName());
        } else {
            dataAddVo.setUserName(cfFirsApproveMaterial.getPatientRealName());
            dataAddVo.setBornCard(cfFirsApproveMaterial.getPatientBornCard());
            dataAddVo.setIdCard(shuidiCipher.decrypt(cfFirsApproveMaterial.getPatientCryptoIdcard()));
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        addData(dataAddVo, "案例Id:" + caseId + "," + riskHitOperateVo.getRemark(), adminUserId);
    }

    private long updateOldBlackList(RiskHitOperateVo riskHitOperateVo, CfFirsApproveMaterial cfFirsApproveMaterial) {
        BlacklistDataQuery blacklistDataQuery = new BlacklistDataQuery();
        blacklistDataQuery.setUserName(cfFirsApproveMaterial.getPatientRealName());
        List<RiskBlacklistData> riskBlacklistData = riskBlacklistDataDao.listByOptions(blacklistDataQuery);
        if (CollectionUtils.isEmpty(riskBlacklistData) && StringUtils.isNotEmpty(cfFirsApproveMaterial.getSelfCryptoIdcard())) {
            blacklistDataQuery.setUserName(cfFirsApproveMaterial.getSelfRealName());
            riskBlacklistData = riskBlacklistDataDao.listByOptions(blacklistDataQuery);
        }
        if (CollectionUtils.isEmpty(riskBlacklistData)) {
            return 0L;
        }
        List<Long> blackDataIdList = riskBlacklistData.stream()
                .filter(f -> StringUtils.equals(f.getEncryptIdCard(), cfFirsApproveMaterial.getPatientCryptoIdcard())
                        || StringUtils.equals(shuidiCipher.decrypt(f.getEncryptBornCard()), cfFirsApproveMaterial.getPatientBornCard())
                        || StringUtils.equals(f.getEncryptIdCard(), cfFirsApproveMaterial.getSelfCryptoIdcard()))
                .filter(i -> i.getIsDelete() == 0)
                .map(RiskBlacklistData::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(blackDataIdList)) {
            return 0L;
        }
        List<RiskBlacklistDataTypeRef> riskBlacklistDataTypeRefs = dataTypeRefDao.listByDataIds(blackDataIdList);
        if (CollectionUtils.isEmpty(riskBlacklistDataTypeRefs)) {
            return 0L;
        }
        RiskBlacklistDataTypeRef riskBlacklistDataTypeRef = riskBlacklistDataTypeRefs.stream()
                .filter(f -> f.getTypeId().equals(riskHitOperateVo.getTypeId()))
                .findAny()
                .orElse(null);
        if (Objects.isNull(riskBlacklistDataTypeRef)) {
            return 0L;
        }
        Map<Long, BlacklistTypeActionRefDto> blacklistTypeActionRefDtoMap = riskHitOperateVo.getActionList()
                .stream()
                .collect(Collectors.toMap(BlacklistTypeActionRefDto::getActionId, Function.identity(), (x, y) -> x));
        Map<Long, RiskBlacklistDataActionRef> collect = dataActionRefDao.listByDataId(riskBlacklistDataTypeRef.getDataId()).stream()
                .collect(Collectors.toMap(RiskBlacklistDataActionRef::getActionId, Function.identity(), (x, y) -> x));
        StringBuilder actionIds = new StringBuilder(riskBlacklistDataTypeRef.getActionIds());
        for (Map.Entry<Long, BlacklistTypeActionRefDto> entry : blacklistTypeActionRefDtoMap.entrySet()) {
            RiskBlacklistDataActionRef riskBlacklistDataActionRef = collect.get(entry.getKey());
            BlacklistTypeActionRefDto blacklistTypeActionRefDto = blacklistTypeActionRefDtoMap.get(entry.getKey());
            if (Objects.nonNull(riskBlacklistDataActionRef)) {
                riskBlacklistDataActionRef.setLimitTime(blacklistTypeActionRefDto.getLimitTime());
                riskBlacklistDataActionRef.setLimitTimeType(blacklistTypeActionRefDto.getLimitTimeType());
                // update
                dataActionRefDao.updateActionRefById(riskBlacklistDataActionRef);
                continue;
            }
            RiskBlacklistDataActionRef dataActionRef = new RiskBlacklistDataActionRef();
            dataActionRef.setDataId(riskBlacklistDataTypeRef.getDataId());
            dataActionRef.setActionId(entry.getKey());
            dataActionRef.setLimitTime(blacklistTypeActionRefDto.getLimitTime());
            dataActionRef.setLimitTimeType(blacklistTypeActionRefDto.getLimitTimeType());
            dataActionRefDao.insertSelective(dataActionRef);
            actionIds.append(",").append(entry.getKey());
        }
        String s = actionIds.toString();
        if (!StringUtils.equals(s, riskBlacklistDataTypeRef.getActionIds())) {
            dataTypeRefDao.updateActionIdsByIds(Collections.singleton(riskBlacklistDataTypeRef.getId()), s);
        }
        RiskBlacklistData data = riskBlacklistData.stream()
                .filter(f -> f.getId().equals(riskBlacklistDataTypeRef.getDataId()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(data)) {
            return 0L;
        }
        Long aLong = riskHitOperateVo.getActionList()
                .stream()
                .max(Comparator.comparing(BlacklistTypeActionRefDto::getLimitTime))
                .map(BlacklistTypeActionRefDto::getLimitTime)
                .orElse(0L);
        data.setLimitTime(aLong);
        riskBlacklistDataDao.updateOptionsById(data);
        saveLog(riskBlacklistDataTypeRef.getDataId(), "重复捐单更新黑名单", riskHitOperateVo.getRemark(), 0L, "系统");
        return data.getId();
    }

    public RiskBlacklistData selectByPrimaryKey(Long id) {
        return riskBlacklistDataDao.selectByPrimaryKey(id);
    }

    public void add(BlackListAddParam param, long adminUserId) {
        final BlacklistDataAddVo a = new BlacklistDataAddVo();
        a.setUserIdAlias(param.getUserId());
        a.setMobile(param.getMobile());
        a.setIdCard(param.getIdCard());
        a.setBornCard(param.getBornCard());
        a.setOperateReason(param.getOperateReason());
        final List<Long> typeIdList = param.getTypeUuidList()
                .stream()
                .map(v -> config.getUuidIdMap().get(v))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        a.setTypeIds(typeIdList);
        addData(a, "系统添加黑名单", adminUserId);
    }

}
