package com.shuidihuzhu.cf.risk.admin.model.enums;

import lombok.Getter;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-29 14:07
 **/
@Getter
public enum RiskRuleResult {

    A_0(0, "默认值"),
    A_1(1, "已花费金额"),
    A_2(2, "医疗药品费用"),
    A_3(3, "康复护理费用"),
    A_4(4, "政府补助");

    private int code;

    private String msg;

    RiskRuleResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
