package com.shuidihuzhu.cf.risk.admin.model.po.list;

import lombok.Data;

import java.util.Date;

@Data
public class RiskListDepartment {
    /**
     * 主键
     */
    private Long id;

    /**
     * 名单类型：1 黑名单，2 灰名单，3 白名单
     */
    private Byte listType;

    /**
     * 医院id
     */
    private Integer hospitalId;

    /**
     * 医院code码
     */
    private String hospitalCode;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 座机区号
     */
    private String areaCode;

    /**
     * 座机号
     */
    private String landline;

    /**
     * 座机分机号
     */
    private String extension;

    /**
     * 医院所在省份
     */
    private String province;

    /**
     * 医院所在城市
     */
    private String city;

    /**
     * 最新操作人id
     */
    private Long operatorId;

    /**
     * 最新操作人姓名
     */
    private String operatorName;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 科室
     */
    private String departments;

}