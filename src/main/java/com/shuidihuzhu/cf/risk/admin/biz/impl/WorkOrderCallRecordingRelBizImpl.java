package com.shuidihuzhu.cf.risk.admin.biz.impl;


import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.risk.admin.biz.WorkOrderCallRecordingRelBiz;
import com.shuidihuzhu.cf.risk.admin.dao.WorkOrderCallRecordingRelDao;
import com.shuidihuzhu.cf.risk.admin.model.WorkOrderCallRecordingRelModel;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class WorkOrderCallRecordingRelBizImpl implements WorkOrderCallRecordingRelBiz {



    @Autowired
    private WorkOrderCallRecordingRelDao workOrderCallRecordingRelDao;


    @Override
    public WorkOrderCallRecordingRelModel selectByRecordingUniqueId(String recordingUniqueId) {
        if (StringUtil.isBlank(recordingUniqueId)){
            return null;
        }
        return  workOrderCallRecordingRelDao.selectByRecordingUniqueId(recordingUniqueId);
    }


    @Override
    public int insert(WorkOrderCallRecordingRelModel relModel) {
        if (relModel == null){
            return -1;
        }
        return workOrderCallRecordingRelDao.insert(relModel);
    }

    @Override
    public List<WorkOrderCallRecordingRelModel> selectByWorkOrderIds(List<Long> workOrderIds) {
        if (CollectionUtils.isEmpty(workOrderIds)){
            return Lists.newArrayList();
        }
        return workOrderCallRecordingRelDao.selectByWorkOrderIds(workOrderIds);
    }

    @Override
    public List<String> selectByWorkOrderId(long workOrderId) {
        return workOrderCallRecordingRelDao.selectByWorkOrderId(workOrderId);
    }
}
