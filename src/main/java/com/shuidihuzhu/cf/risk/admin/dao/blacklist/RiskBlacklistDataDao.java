package com.shuidihuzhu.cf.risk.admin.dao.blacklist;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistData;
import com.shuidihuzhu.cf.risk.admin.model.query.blacklist.BlacklistDataQuery;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskBlacklistDataDao {
    int insertSelective(RiskBlacklistData record);

    RiskBlacklistData selectByPrimaryKey(Long id);

    List<RiskBlacklistData> listByOptions(BlacklistDataQuery blacklistDataQuery);


    int updateOptionsById(RiskBlacklistData userEditVo);

    int saveBatch(List<? extends RiskBlacklistData> dataList);

    List<RiskBlacklistData> getByUserIdOrCryptoIdCardOrMobileOrBornCard(@Param("userIds") List<Long> userIds,
                                                                        @Param("cryptoMobiles") List<String> cryptoMobiles, @Param("cryptoIdCards") List<String> cryptoIdCards,
                                                                        @Param("cryptoBornCards") List<String> cryptoBornCards);

    List<RiskBlacklistData> listByUserIds(Collection<Long> userIds);


    List<RiskBlacklistData> listNotBoundUidLimit(@Param("previousId") Long previousId, @Param("limit") Integer limit);


    List<RiskBlacklistData> listNotBoundMobileLimit(@Param("previousId") Long previousId, @Param("limit") Integer limit);

    List<RiskBlacklistData> listByLimit(@Param("previousId") long previousId, @Param("limit") int limit);

    List<RiskBlacklistData> listByOperateNameAndTime(@Param("operateName") String operateName, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    int countByOperateNameAndTime(@Param("operateName") String operateName, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    int deleteBlackListData(@Param("id") Long id);

    List<RiskBlacklistData> getByLimitTime(@Param("limitTime") Long limitTime);


}