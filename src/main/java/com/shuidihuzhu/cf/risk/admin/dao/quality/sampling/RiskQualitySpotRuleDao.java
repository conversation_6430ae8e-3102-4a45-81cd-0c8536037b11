package com.shuidihuzhu.cf.risk.admin.dao.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQualitySpotRuleDao {
    int insertSelective(RiskQualitySpotRule record);

    RiskQualitySpotRule selectByPrimaryKey(Long id);

    List<RiskQualitySpotRule> listByStrategyIdsOptionsStatus(@Param("strategyIds") List<Long> strategyIds, @Param("status") Byte status);

    int saveBatch(List<RiskQualitySpotRule> riskQualitySpotRules);

    int deleteOldRuleByStrategyId(@Param("strategyId") long strategyId);

    int closeOldRuleByStrategyId(@Param("strategyId") long strategyId);

    int openOldRuleByStrategyId(@Param("strategyId") long strategyId);

    List<RiskQualitySpotRule> findByStrategyId(@Param("strategyId") long strategyId);

    List<RiskQualitySpotRule> findById(@Param("ids") List<Long> ids);
}