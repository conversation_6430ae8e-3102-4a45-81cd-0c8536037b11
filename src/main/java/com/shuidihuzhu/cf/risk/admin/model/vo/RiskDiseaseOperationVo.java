package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseOperationRecord;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskDiseaseOperationTypeEnum;
import com.shuidihuzhu.pf.common.v2.biz.PageHasId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Data
@ApiModel("疾病库操作记录")
@Slf4j
public class RiskDiseaseOperationVo{

    @ApiModelProperty("操作人名称")
    private String operatorName;

    @ApiModelProperty("操作人备注")
    private String optDesc;

    @ApiModelProperty("操作人时间")
    private Timestamp operationTime;

    @ApiModelProperty("操作记录序号")
    private long id;

    @ApiModelProperty("操作内容")
    private RiskDiseaseInfoVO operateInfo;

    @ApiModelProperty("删除原因")
    private String deleteReason;

    public static RiskDiseaseOperationVo build(RiskDiseaseOperationRecord operationRecord) {
        if (operationRecord == null) {
            log.info("operationRecord is null!");
            return null;
        }
        RiskDiseaseOperationVo operationVo = new RiskDiseaseOperationVo();
        operationVo.setId(operationRecord.getId());
        operationVo.setOperatorName(operationRecord.getOperatorName());
        operationVo.setOptDesc(RiskDiseaseOperationTypeEnum.findOfCode(operationRecord.getType()));
        operationVo.setOperationTime(operationRecord.getCreateTime());
        operationVo.setOperateInfo(JSONObject.parseObject(operationRecord.getOperateAfter(), RiskDiseaseInfoVO.class));
        if(operationRecord.getType() == 3){
            RiskDiseaseInfoVO operateInfo = new RiskDiseaseInfoVO();
            operateInfo.setDiseaseClassName("");
            operateInfo.setDiseaseMergeRule("");
            operateInfo.setMedicalName("");
            operateInfo.setNormalName("");
            operateInfo.setTreatMethodList("");
            operationVo.setOperateInfo(operateInfo);
        }
        operationVo.setDeleteReason(operationRecord.getDeleteReason());
        return operationVo;
    }
}
