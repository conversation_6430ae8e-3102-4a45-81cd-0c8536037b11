package com.shuidihuzhu.cf.risk.admin.biz.blacklist;

import com.shuidihuzhu.cf.risk.admin.model.dto.blacklist.BlacklistTypeNameActionDto;
import com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeActionRef;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/20 14:32
 */
public interface BlacklistTypeBiz {

    List<RiskBlacklistTypeActionRef> listTypeActionRefsByTypeIds(Collection<Long> typeIds);

    List<BlacklistTypeNameActionDto> queryTypeActionsByTypeIds(Collection<Long> typeIds);

}
