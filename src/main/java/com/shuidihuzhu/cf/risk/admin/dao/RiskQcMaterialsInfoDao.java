package com.shuidihuzhu.cf.risk.admin.dao;


import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskQcMaterialsInfoDao {

    int insertOne(RiskQcMaterialsInfo riskQcMaterialsInfo);

    int insertBatch(@Param("riskQcMaterialsInfos") List<RiskQcMaterialsInfo> riskQcMaterialsInfos);

    List<RiskQcMaterialsInfo> getByQcIdAndMaterialsKey(@Param("qcId") long qcId, @Param("materialsKey") String materialsKey);

    RiskQcMaterialsInfo getById(@Param("id") long id);

    int updateSnapshot(@Param("qcId") long qcId, @Param("materialsKey") String materialsKey, @Param("materialsValue") String materialsValue);

    int updateById(@Param("id") long id, @Param("materialsValue") String materialsValue);
}
