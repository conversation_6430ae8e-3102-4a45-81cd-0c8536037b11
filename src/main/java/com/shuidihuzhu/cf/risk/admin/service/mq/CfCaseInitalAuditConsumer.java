package com.shuidihuzhu.cf.risk.admin.service.mq;

import com.shuidihuzhu.cf.risk.admin.model.constant.RiskMQTagCons;
import com.shuidihuzhu.cf.client.feign.CaseInfoFeignClient;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.risk.admin.service.env.EnvService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-15 12:02
 **/
@Service
@RocketMQListener(id = "qc_work_order",
        group = "qc_work_order_" + com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        tags = com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfCaseInitalAuditConsumer implements MessageListener<InitialAuditItem.InitialAuditOperation> {

    @Autowired
    private Producer producer;
    @Autowired
    private CaseInfoFeignClient caseInfoFeignClient;
    @Autowired
    private EnvService envService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditItem.InitialAuditOperation> mqMessage) {
        InitialAuditItem.InitialAuditOperation payload = mqMessage.getPayload();
        int caseId = payload.getCaseId();
        log.info("CfCaseInitalAuditConsumer caseId:{}", caseId);
        try {
            Response<CfInfoExt> cfInfoExtResponse = caseInfoFeignClient.getCfExt(caseId);
            if (cfInfoExtResponse.ok() && Objects.nonNull(cfInfoExtResponse.getData())){
                CfInfoExt cfInfoExt = cfInfoExtResponse.getData();
                boolean passed = FirstApproveStatusEnum.isPassed(FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus()));
                if (passed){
                    DelayLevel delayLevel = envService.isProduction() ? DelayLevel.H6 : DelayLevel.S5;
                    producer.send(new Message(MQTopicCons.CF, RiskMQTagCons.QC_WORK_ORDER_CREATE,
                            RiskMQTagCons.QC_WORK_ORDER_CREATE + caseId, payload, delayLevel));
                }
            }
        } catch (Exception e) {
            log.error("CfCaseInitalAuditConsumer send msg error", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
