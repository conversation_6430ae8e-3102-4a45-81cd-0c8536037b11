package com.shuidihuzhu.cf.risk.admin.model.po.blacklist;

import lombok.Data;

import java.util.Date;

@Data
public class RiskBlacklistType {
    /**
     * 主键
     */
    private Long id;

    /**
     * 黑名单类型
     */
    private String typeName;

    /**
     * 黑名单三级分类id
     */
    private Long classifyId;

    /**
     * 操作人id
     */
    private Long operateId;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 状态，0 启用 2 弃用
     */
    private Byte status;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}