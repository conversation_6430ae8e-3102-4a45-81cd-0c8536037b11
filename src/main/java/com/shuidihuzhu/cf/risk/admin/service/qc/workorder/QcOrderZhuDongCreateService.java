package com.shuidihuzhu.cf.risk.admin.service.qc.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enhancer.exception.ServiceResponseException;
import com.shuidihuzhu.cf.enhancer.model.response.EnhancerErrorCode;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcBaseInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcMaterialsInfoBiz;
import com.shuidihuzhu.cf.risk.admin.biz.RiskQcSearchIndexBiz;
import com.shuidihuzhu.cf.risk.admin.delegate.WorkOrderDelegate;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo;
import com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcMaterialsKeyEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.QcTypeEnum;
import com.shuidihuzhu.cf.risk.admin.model.enums.RiskQcOperationTypeEnum;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcLogService;
import com.shuidihuzhu.cf.risk.admin.service.RiskQcmaterialService;
import com.shuidihuzhu.cf.risk.admin.service.SeaAccountService;
import com.shuidihuzhu.cf.risk.admin.service.qc.CallService;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QualitySpotMaterialZhuDongService;
import com.shuidihuzhu.cf.risk.model.admin.qc.QcZhuDongCreateParam;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.config.WorkOrderHelper;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.AssignTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageFeignClient;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QcOrderZhuDongCreateService {
    @Resource
    private CfQcWorkOrderClient cfQcWorkOrderClient;
    @Autowired
    private RiskQcBaseInfoBiz riskQcBaseInfoBiz;
    @Autowired
    private RiskQcLogService riskQcLogService;
    @Autowired
    private RiskQcSearchIndexBiz riskQcSearchIndexBiz;

    @Autowired
    private WorkOrderDelegate workOrderDelegate;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private WorkOrderStorageFeignClient workOrderStorageFeignClient;

    @Resource
    private WorkOrderReadFeignClient workOrderReadFeignClient;

    @Resource
    private SeaAccountService seaAccountService;

    @Resource
    private CallService callService;

    @Autowired
    private UserGroupFeignClient userGroupFeignClient;

    @Resource
    private RiskQcMaterialsInfoBiz riskQcMaterialsInfoBiz;

    @Resource
    private RiskQcmaterialService riskQcmaterialService;

    @Resource
    private QualitySpotMaterialZhuDongService qualitySpotMaterialZhuDongService;

    public Response<Void> create(QcZhuDongCreateParam param) {
        long workOrderId = param.getWorkOrderId();
        log.info("QcOrderZhuDongCreateService create  workOrderId {}, param {}", workOrderId, param);

        Response<BasicWorkOrder> currentOrder = workOrderDelegate.getByOrderId(workOrderId);
        if (currentOrder == null || currentOrder.notOk() || currentOrder.getData() == null) {
            throw makeRpcFail(currentOrder);
        }
        BasicWorkOrder sourceOrder = currentOrder.getData();

        // 查询是否有关联的工单
        Response<List<Long>> resp = workOrderDelegate.getAllQcOrderIdListBySourceId(workOrderId);
        if (resp == null || resp.notOk()) {
            throw makeRpcFail(resp);
        }
        List<Long> relateWorkOrderIdList = resp.getData();

        if (CollectionUtils.isEmpty(relateWorkOrderIdList)) {
            log.info("无关联的工单");
            return createQcOrder(sourceOrder, param);
        }
        Response<List<BasicWorkOrder>> orderListResp = workOrderDelegate.getListByOrderIdList(relateWorkOrderIdList);
        if (orderListResp == null || orderListResp.notOk()) {
            throw makeRpcFail(orderListResp);
        }
        List<BasicWorkOrder> orderList = orderListResp.getData();
        // 检查是否所有工单都已处理完成
        List<BasicWorkOrder> unDoneList = orderList.stream()
                .filter(v -> v.getHandleResult() != HandleResultEnum.done.getType())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unDoneList)) {
//            创建工单 触发抽检
            log.info("关联的工单都已处理完成");
            return createQcOrder(sourceOrder, param);
        }

        Optional<BasicWorkOrder> anySameOperatorOrderOptional = unDoneList.stream()
                .filter(v -> getQcTargetOperator(v) == sourceOrder.getOperatorId())
                .findAny();
        if (anySameOperatorOrderOptional.isEmpty()) {
//            创建工单 触发抽检
            log.info("关联的未处理的工单 都不是相同操作人");
            return createQcOrder(sourceOrder, param);
        }

        BasicWorkOrder qcOrder = anySameOperatorOrderOptional.get();

        Long qcId = qcOrder.getLastByKey(WorkOrderHelper.Storage.QC_ID, Long.class).orElse(null);
        if (qcId == null) {
            log.error("qcId null param {}", param);
            throw ServiceResponseException.create("fail qcId null", ErrorCode.SYSTEM_ERROR);
        }
        //更新对应工单详情页
        riskQcmaterialService.saveSnapshotWithRetryV2(qcOrder.getId(), qcId, workOrderId, param.getHandleRecordId());

//      检查是否必须分配
        Response<List<WorkOrderExt>> extResp = cfWorkOrderClient.listExtInfos(Lists.newArrayList(qcOrder.getId()),
                OrderExtName.qcAssignType.getName());
        if (extResp == null || extResp.notOk()) {
            throw makeRpcFail(extResp);
        }
        List<WorkOrderExt> extList = extResp.getData();
        int assignType = Integer.parseInt(extList.get(0).getExtValue());
        if (assignType == AssignTypeEnum.MUST_ASSIGN.getCode()) {
//      是必须分配 结束
            return NewResponseUtil.makeSuccess(null);
        }

//      不是必须分配 走抽检逻辑
        RiskQcBaseInfo qcBaseInfo = riskQcBaseInfoBiz.getById(qcId);
        RiskQcSearchIndex riskQcSearchIndex = riskQcSearchIndexBiz.getByWorkOrderId(qcOrder.getId());
        param.setCallStatus(riskQcSearchIndex.getCallStatus());
        qualitySpotMaterialZhuDongService.spotMaterial(qcOrder.getId(), qcBaseInfo, sourceOrder, param);

        return NewResponseUtil.makeSuccess(null);
    }

    private ServiceResponseException makeRpcFail(Response<?> s) {
        return ServiceResponseException.create("rpc fail resp " + JSON.toJSONString(s), EnhancerErrorCode.RPC_FAIL);
    }

    private Response<Void> createQcOrder(BasicWorkOrder sourceWorkOrder, QcZhuDongCreateParam param) {
        //创建质检基本信息
        RiskQcBaseInfo riskQcBaseInfo = new RiskQcBaseInfo();
        int caseId = sourceWorkOrder.getCaseId();
        riskQcBaseInfo.setCaseId(caseId);
        riskQcBaseInfo.setQcType(QcTypeEnum.MATERIAL.getCode());
        riskQcBaseInfo.setOrderType(WorkOrderType.qc_zhu_dong.getType());
        long sourceOperatorId = sourceWorkOrder.getOperatorId();
        var qcByName = seaAccountService.getName(sourceOperatorId);
        riskQcBaseInfo.setQcByName(qcByName);
        riskQcBaseInfo.setQcUniqueCode(Long.toString(System.currentTimeMillis()));
        riskQcBaseInfoBiz.addQc(riskQcBaseInfo);


        //执行工单创建逻辑
        QcWorkOrder qcWorkOrder = new QcWorkOrder();
        qcWorkOrder.setCaseId(caseId);
        qcWorkOrder.setQcId(riskQcBaseInfo.getId());
        qcWorkOrder.setOrderType(WorkOrderType.qc_zhu_dong.getType());
        qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
        qcWorkOrder.setComment("生成主动服务质检工单");
        qcWorkOrder.setLoginUserId(0);
        Response<Long> clientQcWorkOrder = cfQcWorkOrderClient.createQcWorkOrder(qcWorkOrder);
        log.info("查询工单id信息 clientQcWorkOrder:{}", JSON.toJSONString(clientQcWorkOrder));
        Preconditions.checkNotNull(clientQcWorkOrder);
        Preconditions.checkState(clientQcWorkOrder.ok());
        Long workOrderId = clientQcWorkOrder.getData();
        Preconditions.checkNotNull(workOrderId);
        log.info("createQcWorkOrder id:{}", workOrderId);

        long sourceWorkOrderId = sourceWorkOrder.getId();
        workOrderStorageFeignClient.addByList(Lists.newArrayList(
                createStorage(sourceWorkOrderId, WorkOrderHelper.Storage.QC_ORDER_ID, workOrderId),
                createStorage(workOrderId, WorkOrderHelper.Storage.SOURCE_ORDER_ID, sourceWorkOrderId),
                createStorage(workOrderId, WorkOrderHelper.Storage.QC_ID, riskQcBaseInfo.getId()),
                createStorage(workOrderId, WorkOrderHelper.Storage.TARGET_OPERATOR_ID, sourceOperatorId)
        ));

        boolean snapshotSuccess = riskQcmaterialService.saveSnapshotWithRetryV2(workOrderId, riskQcBaseInfo.getId(), sourceWorkOrder.getId(), param.getHandleRecordId());
//        riskQcmaterialService.saveSnapshotWithRetry(workOrderId, riskQcBaseInfo.getId(), caseId, sourceWorkOrder.getId());
//        qcHospitalDeptService.saveSnapshot(workOrderId, clewInfo);

        //记录组织结构id、材审工单id
        List<RiskQcMaterialsInfo> riskQcMaterialsInfos = List.of(
//                new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.ORG_ID.getKey(), Long.toString(adminOrganization.getId())),
//                new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.DETAIL_SNAPSHOT.getKey(), snapshot),
                new RiskQcMaterialsInfo(riskQcBaseInfo.getId(), QcMaterialsKeyEnum.ORDER_ID.getKey(), Long.toString(workOrderId))
        );
        riskQcMaterialsInfoBiz.addMaterials(riskQcMaterialsInfos);

        var organization = getOrganization(sourceOperatorId);

        // 添加搜索索引字段聚合表
        RiskQcSearchIndex riskQcSearchIndex = new RiskQcSearchIndex();
        riskQcSearchIndex.setCaseId(caseId);
        riskQcSearchIndex.setQcId(riskQcBaseInfo.getId());
        riskQcSearchIndex.setWorkOrderId(workOrderId);
        riskQcSearchIndex.setQcType(QcTypeEnum.MATERIAL.getCode());
        riskQcSearchIndex.setOrganization(organization);
        riskQcSearchIndex.setQcUniqueCode(Long.toString(sourceOperatorId));
        riskQcSearchIndex.setQcByName(qcByName);
        riskQcSearchIndex.setCallStatus(callService.getCallStatus(sourceWorkOrderId));
        riskQcSearchIndex.setHandleResult(sourceWorkOrder.getHandleResult());
        riskQcSearchIndex.setSourceWorkOrderId(sourceWorkOrderId);
        riskQcSearchIndex.setSourceWorkOrderType(sourceWorkOrder.getOrderType());
        riskQcSearchIndex.setRegisterMobileEncrypt("");
        riskQcSearchIndexBiz.addSearchIndex(riskQcSearchIndex, qcWorkOrder.getOrderType());

//            cfQcWorkOrderClient.updateAssignType(Lists.newArrayList(clientQcWorkOrder.getData()), AssignTypeEnum.MUST_ASSIGN.getCode());

        param.setCallStatus(riskQcSearchIndex.getCallStatus());
        if (snapshotSuccess) {
            qualitySpotMaterialZhuDongService.spotMaterial(clientQcWorkOrder.getData(), riskQcBaseInfo, sourceWorkOrder, param);
        }

        //调用质检操作记录接口，记录操作记录
        String content = "生成主动服务质检工单,工单ID【" + workOrderId + "】";
        riskQcLogService.addLog(RiskQcOperationTypeEnum.CREATE_ZHU_DONG_ORDER, workOrderId, content);
        return NewResponseUtil.makeSuccess(null);
    }

    private VonStorageVO createStorage(long workOrderId, int type, Object value) {
        VonStorageVO json = VonStorageVO.createJson(type, value);
        json.setWorkOrderId(workOrderId);
        return json;
    }

    private long getQcTargetOperator(BasicWorkOrder v) {
        return v.getLastByKey(WorkOrderHelper.Storage.TARGET_OPERATOR_ID, Long.class).orElse(null);
    }


    private String getOrganization(long userId) {
        //内部
        Response<String> authRpcResponse = userGroupFeignClient.getGroupNameByUserId(userId);
        if (authRpcResponse.ok()) {
            return authRpcResponse.getData();
        }
        return "";
    }
}
