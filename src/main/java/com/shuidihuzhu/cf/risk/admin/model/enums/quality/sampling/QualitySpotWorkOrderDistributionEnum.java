package com.shuidihuzhu.cf.risk.admin.model.enums.quality.sampling;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/6/16 10:56
 */
@Getter
public enum QualitySpotWorkOrderDistributionEnum {

    RANDOM(1, "随机分单"),
    CONFIG(2, "按照配置分单"),
    ;

    public static QualitySpotWorkOrderDistributionEnum fromCode(int code){
        for (QualitySpotWorkOrderDistributionEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }

        return null;
    }

    private int code;
    private String desc;

    QualitySpotWorkOrderDistributionEnum(int code, String desc) {
        this.code = (byte)code;
        this.desc = desc;
    }
}
