package com.shuidihuzhu.cf.risk.admin.dao;

import com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo;
import com.shuidihuzhu.cf.risk.admin.configuration.RiskAdminDS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/2/20
 */
@DataSource(RiskAdminDS.CF_RISK_RW)
public interface RiskPublicSentimentInfoDao {
    int add(RiskPublicSentimentInfo riskPublicSentimentInfo);

    int updateById(RiskPublicSentimentInfo riskPublicSentimentInfo);

    int updateHandleById(RiskPublicSentimentInfo riskPublicSentimentInfo);


    RiskPublicSentimentInfo  getInfoById(@Param("id")long id);


    List<RiskPublicSentimentInfo> getInfoByCaseId(@Param("caseId")int caseId);




    List<RiskPublicSentimentInfo> getInfoList(@Param("infoSource")int infoSource, @Param("infoFeedBack")int infoFeedBack,
                                              @Param("infoClassify")int infoClassify, @Param("startTime")String startTime,
                                              @Param("endTime")String endTime,
                                              @Param("publicSentimentInfoType")String publicSentimentInfoType,
                                              @Param("solution")String solution, @Param("caseId")int caseId,
                                              @Param("lastOperator")String lastOperator, @Param("disposeStatus")String disposeStatus);


    int countPsByCaseId(int caseId);

    int updateReportIdById(@Param("reportId")int reportId, @Param("caseId")int caseId, @Param("id") long id);

    int updateStatusById(@Param("status") int status, @Param("id") long id);



    List<String> getByInfoFeedBack();

}
