package com.shuidihuzhu.cf.risk.admin.model.vo;

import com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseRaiseTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Data
@ApiModel("疾病治疗方案VO")
public class RiskDiseaseTreatmentProjectVO {

    @ApiModelProperty("治疗方案编号")
    private long id;

    @ApiModelProperty("方案名称")
    private String projectName ;

    @ApiModelProperty("方案归一规则")
    private String projectMergeRule = "";

    @ApiModelProperty("最小治疗费用")
    private double minTreatmentFee;

    @ApiModelProperty("最大治疗费用")
    private double maxTreatmentFee;

    @ApiModelProperty("自定义治疗方案")
    private String customTreatment = "";

    @ApiModelProperty("发起类型")
    private int raiseType;

    public static RiskDiseaseTreatmentProjectVO build(RiskDiseaseTreatmentProject treatmentProject) {
        if (treatmentProject == null) {
            return null;
        }
        RiskDiseaseTreatmentProjectVO treatmentProjectVO = new RiskDiseaseTreatmentProjectVO();
        treatmentProjectVO.setId(treatmentProject.getId());
        treatmentProjectVO.setProjectName(treatmentProject.getProjectName());
        treatmentProjectVO.setProjectMergeRule(treatmentProject.getProjectMergeRule());
        treatmentProjectVO.setMinTreatmentFee(treatmentProject.getMinTreatmentFee() / 100.0);
        treatmentProjectVO.setMaxTreatmentFee(treatmentProject.getMaxTreatmentFee() / 100.0);
        treatmentProjectVO.setCustomTreatment(treatmentProject.getCustomTreatment());
        return treatmentProjectVO;
    }

    public void buildRaiseType(int diseaseType, int treatmentType){
        this.setRaiseType(diseaseType==DiseaseRaiseTypeEnum.SPECIAL_RAISE.getCode() ? treatmentType : diseaseType == 1 ? 1 : 0);
    }
}
