package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConf;
import com.shuidihuzhu.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@Data
@ApiModel(description = "抽检量级配置")
public class RiskQualitySpotLevelConfVo {

    public RiskQualitySpotLevelConfVo(RiskQualitySpotLevelConf riskQualitySpotLevelConf){
        this.setId(riskQualitySpotLevelConf.getId());
        this.setParseTime(DateUtil.getDate2LStr(riskQualitySpotLevelConf.getParseTime()));
        this.setSamplingLevel(riskQualitySpotLevelConf.getSamplingLevel());
        this.setScene(riskQualitySpotLevelConf.getScene());
        this.setSpotCheck(riskQualitySpotLevelConf.getIsSampling() != 0);
        this.setUpdateTime(DateUtil.getDate2LStr(riskQualitySpotLevelConf.getUpdateTime()));
    }

    private Long id;

    @ApiModelProperty("1v1新增  质检对象 仅展示")
    private String firstScene;

    @ApiModelProperty("1v1新增 二级工单类型 仅展示")
    private String secondScene;

    @ApiModelProperty("二级工单类型 用2.0版本改造的scene")
    private Long scene;

    @ApiModelProperty("抽检量级，条/天")
    private Integer samplingLevel;

    @ApiModelProperty("是否抽检")
    private boolean isSpotCheck;

    @ApiModelProperty("开始生效时间")
    private String parseTime;

    @ApiModelProperty("修改时间")
    private String updateTime;

}