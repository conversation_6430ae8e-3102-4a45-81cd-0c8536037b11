package com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling;

import lombok.Data;

import java.util.Date;

@Data
public class RiskQualitySpotLevelConfLog {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 二级工单类型
     */
    private Long scene;

    /**
     * 修改原因备注
     */
    private String modifyReason;

    /**
     * 修改情况
     */
    private String modifyContent;

    /**
     * 开始生效时间
     */
    private Date parseTime;

    /**
     * 修改人id
     */
    private Long operateId;

    /**
     * 修改人
     */
    private String operateName;

    /**
     * 是否删除0 否，1 是
     */
    private Byte isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}