package com.shuidihuzhu.cf.risk.admin.biz.quality.sampling;

import com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15 16:33
 */
@Validated
public interface QualitySpotRuleBiz {

    List<RiskQualitySpotRule> queryEnableRulesByStrategyIds(@NotNull @Size(min = 1) List<Long> strategyIds);

    RiskQualitySpotRule getById(@NotNull @Min(value = 1) Long id);

    int saveBatch(@NotNull @Size(min = 1) List<RiskQualitySpotRule> riskQualitySpotRules);

    int deleteOldRuleByStrategyId(@NotNull @Min(value = 1)long strategyId);

    int closeOldRuleByStrategyId(@NotNull @Min(value = 1)long strategyId);

    int openOldRuleByStrategyId(@NotNull @Min(value = 1)long strategyId);

    List<RiskQualitySpotRule> findByStrategyId(@NotNull @Min(value = 1)long strategyId);

    List<RiskQualitySpotRule> findById(@NotNull List<Long> ids);
}
