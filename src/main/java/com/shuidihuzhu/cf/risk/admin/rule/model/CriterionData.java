package com.shuidihuzhu.cf.risk.admin.rule.model;

import com.shuidihuzhu.cf.risk.admin.rule.enums.ValueType;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020-02-26
 **/
@Data
@NoArgsConstructor
public class CriterionData {

    public CriterionData(ValueType valueType, CallMethod callMethod){
        this.valueType = valueType;
        this.callMethod = callMethod;
    }

    private ValueType valueType;

    private String content;
    private Variable variable;
    private CallMethod callMethod;
    /**
     * 算数运算配置
     */
    private Arithmetic arithmetic;

}
