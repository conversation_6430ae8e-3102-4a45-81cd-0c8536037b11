package com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/8/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class RiskQualitySpotType {

    @ApiModelProperty("id")
    private long id;
    @ApiModelProperty("场景名称")
    private String typeName;
    @ApiModelProperty("父类id")
    private long parentId;
}
