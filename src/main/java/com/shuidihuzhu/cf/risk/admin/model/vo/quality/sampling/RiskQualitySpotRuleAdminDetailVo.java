package com.shuidihuzhu.cf.risk.admin.model.vo.quality.sampling;

import com.shuidihuzhu.client.cf.workorder.model.vo.RiskQualitySpotRuleDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.checkerframework.checker.units.qual.A;

/**
 * <AUTHOR>
 * @date 2020/9/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class RiskQualitySpotRuleAdminDetailVo extends RiskQualitySpotRuleDetailVo {

    /**
     * 启用停用状态：0 启用，1 停用
     */
    @ApiModelProperty(" 启用停用状态：0 启用，1 停用")
    private byte status = 0;
}
