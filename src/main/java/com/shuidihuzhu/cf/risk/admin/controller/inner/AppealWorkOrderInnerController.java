package com.shuidihuzhu.cf.risk.admin.controller.inner;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.admin.model.vo.CarBrandVo;
import com.shuidihuzhu.cf.risk.admin.service.quality.sampling.QcAppealInnerService;
import com.shuidihuzhu.client.cf.riskadmin.AppealWorkOrderClient;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/16
 */
@RestController
@Slf4j
public class AppealWorkOrderInnerController implements AppealWorkOrderClient {

    @Autowired
    private QcAppealInnerService qcAppealInnerService;

    @Override
    public Response<Void> normalQualitySpotAppeal(CfGwReplaceInputQualityTestFeedbackModel cfGwReplaceInputQualityTestFeedbackModel) {
        log.info("normalQualitySpotAppeal cfGwReplaceInputQualityTestFeedbackModel:{}", JSON.toJSONString(cfGwReplaceInputQualityTestFeedbackModel));
        qcAppealInnerService.normalQualitySpotAppeal(cfGwReplaceInputQualityTestFeedbackModel);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> managerQualitySpotAppeal(CfGwReplaceInputQualityTestFeedbackModel cfGwReplaceInputQualityTestFeedbackModel) {
        log.info("managerQualitySpotAppeal cfGwReplaceInputQualityTestFeedbackModel:{}", JSON.toJSONString(cfGwReplaceInputQualityTestFeedbackModel));
        qcAppealInnerService.managerQualitySpotAppeal(cfGwReplaceInputQualityTestFeedbackModel);
        return NewResponseUtil.makeSuccess(null);
    }
}
