package com.shuidihuzhu.cf.risk.admin.cache;

import com.google.common.base.Ticker;
import com.google.common.cache.*;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Author: lixuan
 * Date: 2018-12-20 20:15
 */
@Slf4j
public abstract class AbstractCache<K, V> {

	private static final int CONCURRENCY_LEVEL = Runtime.getRuntime().availableProcessors();

	@Resource(name = "asyncTaskThreadPool")
	private ThreadPoolTaskExecutor taskExecutor;
	private LoadingCache<K, V> cache;

	/**
	 * preloading
	 */
	@PostConstruct
	protected void preloading() {
		if (needPreLoading()) {
			try {
				getValue((K) getName());
			} catch (ExecutionException e) {
				log.error("", e);
			}
		}
	}

	private LoadingCache<K, V> getCache() {
		if(cache != null) {
			return cache;
		}
		synchronized (this) {
			if(cache != null) {
				return cache;
			}
			String name = getName();
			log.info("init AbstractCache {}...", name);
			cache = CacheBuilder.newBuilder().concurrencyLevel(CONCURRENCY_LEVEL).maximumSize(1000)
					.expireAfterWrite(getDefaultExpireAfterWriteSecond(), TimeUnit.SECONDS)
					.refreshAfterWrite(getDefaultRefreshAfterWriteSecond(), TimeUnit.SECONDS)
					.ticker(Ticker.systemTicker()).recordStats()
					.removalListener(new RemovalListener<Object, Object>() {
						@Override
						public void onRemoval(RemovalNotification<Object, Object> notification) {
							log.warn("{} key {} was removed, cause is {} ", name, notification.getKey(), notification.getCause());
						}
					})
					.build(new CacheLoader<K, V>() {
						@Override
						public V load(K key) {
							log.info("load:{}, key:{}", name, key);
							log.info("cache[{}][{}] stats[{}]", cache, name, getCacheStat().toString());
							return queryData(key);
						}

						@Override
						public ListenableFuture<V> reload(K key, V oldValue) throws Exception {
							ListenableFutureTask<V> task = ListenableFutureTask.create(() -> {
								try {
									return load(key);
								} catch (Exception e) {
									log.error("load error", e);
								}
								return oldValue;
							});
							taskExecutor.execute(task);//这里将这个task放到自定义的线程池中去执行，返回一个futrue，可以通过future获取线程执行获取的值
							return task;
						}
					});

			return cache;
		}
	}

	protected abstract V queryData(@NotNull final K key);

	/**
	 * 建议设置为0
	 * @return int
	 */
	protected abstract int getExpireAfterWriteSecond();

	protected abstract int getRefreshAfterWriteSecond();

	private int getDefaultExpireAfterWriteSecond() {
		int expireAfterWriteSecond = getExpireAfterWriteSecond();
		if(expireAfterWriteSecond <= 0) {
			return 0;
		}
		return expireAfterWriteSecond;
	}

	private int getDefaultRefreshAfterWriteSecond() {
		int refreshAfterWriteSecond = getRefreshAfterWriteSecond();
		if(refreshAfterWriteSecond <= 0) {
			return 5;
		}
		return refreshAfterWriteSecond;
	}

	protected V getValue(@NotNull final K key) throws ExecutionException {
		return getCache().get(key);
	}

	protected void put(@NotNull final K key, @NotNull final V value) {
		getCache().put(key, value);
	}

	protected void invalidateAll(List<K> keys) {
		getCache().invalidateAll(keys);
	}

	protected void invalidateAll() {
		getCache().invalidateAll();
	}

	protected void refreshCache(K key) {
		getCache().refresh(key);
	}

	protected CacheStats getCacheStat() { return cache.stats(); }

	public abstract String getName();

	protected boolean needPreLoading() {return false;}
}
