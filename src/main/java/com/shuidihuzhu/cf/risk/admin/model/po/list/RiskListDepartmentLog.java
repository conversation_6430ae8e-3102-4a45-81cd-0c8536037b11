package com.shuidihuzhu.cf.risk.admin.model.po.list;

import java.util.Date;

public class RiskListDepartmentLog {
    /**
     * 主键
     */
    private Long id;

    /**
     * 科室电话名单id
     */
    private Long listDepartmentId;

    /**
     * 修改内容
     */
    private String modifyContent;

    /**
     * 操作人id
     */
    private Long operateId;

    /**
     * 操作人
     */
    private String operateName;

    /**
     * 是否删除，0 否，1 是
     */
    private Byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 主键
     * @return id 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 科室电话名单id
     * @return list_department_id 科室电话名单id
     */
    public Long getListDepartmentId() {
        return listDepartmentId;
    }

    /**
     * 科室电话名单id
     * @param listDepartmentId 科室电话名单id
     */
    public void setListDepartmentId(Long listDepartmentId) {
        this.listDepartmentId = listDepartmentId;
    }

    /**
     * 修改内容
     * @return modify_content 修改内容
     */
    public String getModifyContent() {
        return modifyContent;
    }

    /**
     * 修改内容
     * @param modifyContent 修改内容
     */
    public void setModifyContent(String modifyContent) {
        this.modifyContent = modifyContent == null ? null : modifyContent.trim();
    }

    /**
     * 操作人id
     * @return operate_id 操作人id
     */
    public Long getOperateId() {
        return operateId;
    }

    /**
     * 操作人id
     * @param operateId 操作人id
     */
    public void setOperateId(Long operateId) {
        this.operateId = operateId;
    }

    /**
     * 操作人
     * @return operate_name 操作人
     */
    public String getOperateName() {
        return operateName;
    }

    /**
     * 操作人
     * @param operateName 操作人
     */
    public void setOperateName(String operateName) {
        this.operateName = operateName == null ? null : operateName.trim();
    }

    /**
     * 是否删除，0 否，1 是
     * @return is_delete 是否删除，0 否，1 是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 是否删除，0 否，1 是
     * @param isDelete 是否删除，0 否，1 是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}