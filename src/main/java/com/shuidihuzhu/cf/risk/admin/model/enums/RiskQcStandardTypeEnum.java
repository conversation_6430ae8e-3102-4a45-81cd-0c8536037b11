package com.shuidihuzhu.cf.risk.admin.model.enums;

import com.shuidihuzhu.pf.tools.dbenum.ColumnEnum;
import com.shuidihuzhu.pf.tools.dbenum.DbInfo;

@ColumnEnum(colInfo = {@DbInfo(dbName = "shuidi_cf_risk", tableName = "risk_qc_standard", columnName = "standard_type")}
, descName = "description")
public enum RiskQcStandardTypeEnum {
    //
    RECORDING(1, "录音质检"),

    MATERIAL(2, "审核质检"),

    HOSPITAL_DEPT(3, "科室质检"),

    ;

    int code;
    String description;

    RiskQcStandardTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String findOfCode(int code) {
        for (RiskQcStandardTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return "";
    }
}
