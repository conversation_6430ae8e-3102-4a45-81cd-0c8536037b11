package com.shuidihuzhu.cf.risk.admin.biz.impl;

import com.shuidihuzhu.cf.risk.admin.biz.RiskCommentBiz;
import com.shuidihuzhu.cf.risk.admin.dao.CfRiskCommentDao;
import com.shuidihuzhu.cf.risk.admin.dao.CfRiskCommentExtDao;
import com.shuidihuzhu.cf.risk.admin.model.CfRiskComment;
import com.shuidihuzhu.cf.risk.admin.model.CfRiskCommentExt;
import com.shuidihuzhu.cf.risk.admin.service.BlacklistService;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.pf.common.v2.model.PageRequest;
import com.shuidihuzhu.pf.common.v2.model.PageResponse;
import com.shuidihuzhu.pf.common.v2.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RiskCommentBizImpl implements RiskCommentBiz {

    @Autowired
    private CfRiskCommentDao cfRiskCommentDao;
    @Autowired
    private CfRiskCommentExtDao cfRiskCommentExtDao;
    @Resource
    private BlacklistService blacklistService;

    @Override
    public PageResponse<CfRiskComment> listByParam(int caseId, String keyword, Timestamp startTime, Timestamp endTime, String pageJson) {
        PageRequest pageRequest = PageUtil.parseJsonString(pageJson);
        Map<String, Object> param = new HashMap<>();
        param.put("caseId", caseId);
        if (StringUtils.isNotBlank(keyword)){
            param.put("keyword", keyword);
        }
        if (startTime != null){
            param.put("startTime", startTime);
        }
        if (endTime != null){
            param.put("endTime", endTime);
        }
        List<CfRiskComment> data = cfRiskCommentDao.findByParam(param, pageRequest);
        if (CollectionUtils.isNotEmpty(data)){
            List<Long> idList = data.stream().map(CfRiskComment::getId).collect(Collectors.toList());
            List<CfRiskCommentExt> cfRiskCommentExtList = cfRiskCommentExtDao.listByCommentIdsIn(idList);
            if (CollectionUtils.isNotEmpty(cfRiskCommentExtList)){
                //组装commentId Map
                Map<Long, CfRiskCommentExt> idMap =
                        cfRiskCommentExtList.stream().collect(Collectors.toMap(CfRiskCommentExt::getCommentId, Function.identity()));
                //循环设置点赞数
                data.forEach(c -> {
                    CfRiskCommentExt ext = idMap.get(c.getId());
                    if (ext != null) {
                        c.setPraiseCount(ext.getPraiseCount());
                    }
                });
            }
        }
        blacklistFilter(data);
        return PageUtil.buildPageResponse
                (data, pageRequest);
    }

    private void blacklistFilter(List<CfRiskComment> commentVos){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(commentVos)) {
            return;
        }
        List<BlacklistVerifyDto> params = commentVos.parallelStream()
                .map(commentVO -> new BlacklistVerifyDto(commentVO.getUserId()+"", BlacklistVerifyTypeEnum.USER_ID.getCode()))
                .collect(Collectors.toList());

        Map<Long, BlacklistVerifyDto> collect = blacklistService.verifyDataList(params).parallelStream().collect(Collectors.toMap(
                blacklistVerifyDto -> Long.valueOf(blacklistVerifyDto.getVerifyData()),
                Function.identity(), (b1, b2) -> b2));

        for (CfRiskComment commentVo : commentVos) {
            BlacklistVerifyDto blacklistVerifyDto = collect.get(commentVo.getUserId());
            if (blacklistVerifyDto.isHit() && blacklistVerifyDto.getLimitActionIds().contains(LimitActionEnum.UGC.getId())) {
                commentVo.setSensitiveStatus(true);
            }
        }
    }

    @Override
    public int delById(long id) {
        cfRiskCommentExtDao.updateDelByCommentId(id);
        return cfRiskCommentDao.updateDelById(id);
    }
}
