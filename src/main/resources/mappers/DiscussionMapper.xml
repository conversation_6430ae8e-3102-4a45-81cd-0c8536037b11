<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.DiscussionDao">

    <sql id="TABLE">
        `discussion`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.Discussion" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="TABLE"/>
        (case_id, info_uuid, reason, other_reason, other_fund_reason, images,
        status, close_hour, instead_status, instead_title, instead_description, instead_images)
        values (#{caseId}, #{infoUuid},#{reason}, #{otherReason},#{otherFundReason},
        #{images}, #{status},#{closeHour},#{insteadStatus},#{insteadTitle},#{insteadDescription},#{insteadImages})
    </insert>

    <select id="findByCaseId" resultType="com.shuidihuzhu.cf.risk.admin.model.Discussion">
        select * from <include refid="TABLE"/>
        where case_id = #{caseId} and is_delete = 0 order by create_time desc
    </select>


    <select id="listByCaseId" resultType="com.shuidihuzhu.cf.risk.admin.model.Discussion">
        select * from <include refid="TABLE"/> as page
        where case_id = #{caseId}
    </select>

    <update id="updateStatus">
        update <include refid="TABLE"/>
        set status = #{newStatus}
        where id = #{id} and status = #{oldStatus}
    </update>

    <update id="updateCheckStatus">
        update <include refid="TABLE"/>
        set check_status = #{newStatus}
        where id = #{discussionId} and check_status = #{oldStatus}
    </update>

    <select id="findById" resultType="com.shuidihuzhu.cf.risk.admin.model.Discussion">
        select * from <include refid="TABLE"/>
        where id = #{id} and is_delete = 0
    </select>

    <update id="updateDelById">
        update <include refid="TABLE"/>
        set is_delete = 1
        where id = #{id} and is_delete = 0
    </update>

    <update id="updateCloseTime">
        update <include refid="TABLE"/>
        set close_time =#{closeTime}
        where id = #{id}
    </update>

    <select id="findByIds" resultType="com.shuidihuzhu.cf.risk.admin.model.Discussion">
        select * from <include refid="TABLE"/>
        where id in
        <foreach collection="list" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="countByCaseId" resultType="java.lang.Integer">
        select count(*) from <include refid="TABLE"/>
        where case_id = #{caseId}
    </select>
    <select id="getInfos" resultType="com.shuidihuzhu.cf.risk.admin.model.Discussion">
        select * from <include refid="TABLE"/>
        where is_delete = 0
        order by id desc limit 1
    </select>


</mapper>