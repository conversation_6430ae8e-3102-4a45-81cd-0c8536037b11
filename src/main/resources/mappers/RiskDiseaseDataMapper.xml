<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseDataDao">

    <sql id="tableName">
        risk_disease_data
    </sql>

    <sql id="selectFields">
        `id` ,
        `disease_class_name` ,
        `medical_name` ,
        `normal_name`,
        `raise_type` ,
        `disease_merge_rule`,
        `choice_type`,
        `create_time`,
        `is_delete`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>
        (`disease_class_name` ,`medical_name` ,`normal_name`,
        `raise_type` , `disease_merge_rule`,`choice_type` )
        values (#{diseaseClassName}, #{medicalName}, #{normalName},
        #{raiseType}, #{diseaseMergeRule}, #{choiceType})
    </insert>

    <update id="delete">
        update <include refid="tableName"/>
        set `is_delete` = 1
        where `id` = #{diseaseId}

    </update>

    <update id="update" parameterType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData">
        update <include refid="tableName"/>
        set `disease_class_name` = #{diseaseClassName},
        `medical_name` = #{medicalName},
        `normal_name` = #{normalName},
        `raise_type` = #{raiseType},
        `disease_merge_rule` = #{diseaseMergeRule},
        `choice_type` = #{choiceType}
        where `id` = #{id}
    </update>

    <select id="getByClassName" resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_class_name` = #{diseaseClassName}
        AND `is_delete` = 0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `id` = #{diseaseId}
        AND `is_delete` = 0
    </select>

    <select id="findList" resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `is_delete` = 0
        <if test="diseaseClassName != null">
            AND `disease_class_name` LIKE concat('%', #{diseaseClassName}, '%')
        </if>
        <if test="medicalName != null">
            AND `medical_name` LIKE concat('%', #{medicalName}, '%')
        </if>
        <if test="normalName != null">
            AND `normal_name` LIKE concat('%', #{normalName}, '%')
        </if>
        <if test="raiseType != 0">
            AND `raise_type` = #{raiseType}
        </if>
    </select>
    <select id="findListByDiseaseIds" resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where id  IN
        <foreach collection="diseaseIds" open="(" close=")" separator="," item="diseaseId">
            #{diseaseId}
        </foreach>
        <if test="diseaseClassName != null">
            AND `disease_class_name` LIKE concat('%', #{diseaseClassName}, '%')
        </if>
        <if test="raiseType != 0">
            AND `raise_type` = #{raiseType}
        </if>
        <if test="isDelete != 2">
            AND `is_delete` = #{isDelete}
        </if>
        <if test="startCreateTime != null and endCreateTime !=null">
            and `create_time` between #{startCreateTime} and #{endCreateTime}
        </if>
        order by `id` desc
    </select>
    <select id="findListV2" resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `id` > 0
        <if test="diseaseClassName != null">
            AND `disease_class_name` LIKE concat('%', #{diseaseClassName}, '%')
        </if>
        <if test="raiseType != 0">
            AND `raise_type` = #{raiseType}
        </if>
        <if test="isDelete != 2">
            AND `is_delete` = #{isDelete}
        </if>
        <if test="startCreateTime != null and endCreateTime !=null">
            and `create_time` between #{startCreateTime} and #{endCreateTime}
        </if>
        order by `id` desc
    </select>

    <select id="getAllDiseaseRule"
            resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseDataPortion">
        select
        disease_class_name as disease,
        disease_merge_rule as rule
        from <include refid="tableName"/>
        where `is_delete` = 0
    </select>

    <delete id="clearAll">
        delete from <include refid="tableName"/>
        where id > 0
    </delete>

    <insert id="sync">
        insert into <include refid="tableName"/>
        (id, `disease_class_name` ,`medical_name` ,`normal_name`,
        `raise_type` , `disease_merge_rule`,`choice_type` )
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id}, #{item.diseaseClassName}, #{item.medicalName}, #{item.normalName},
            #{item.raiseType}, #{item.diseaseMergeRule}, #{item.choiceType})
        </foreach>
    </insert>

    <select id="getAll" resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where is_delete = 0
    </select>

    <select id="findDiseaseNormList" resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseData">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where is_delete = 0
        and disease_class_name LIKE concat('%', #{diseaseClassName}, '%')
    </select>

</mapper>