<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.DiscussionContentDAO">

    <sql id="TABLE">
        `discussion_content_record`
    </sql>
    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.DiscussionContentVO">
        insert into <include refid="TABLE"/>
        (discussion_id, source_num, operator, title, description, images)
        values (#{discussionId}, #{sourceNum}, #{operator}, #{title}, #{description}, #{images})
    </insert>
    <select id="getByDiscussionId" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionContentVO">
        select * from <include refid="TABLE"/>
        where discussion_id = #{discussionId}
        and is_delete = 0
        and source_num = #{sourceNum}
        order by id asc limit 1
    </select>


</mapper>