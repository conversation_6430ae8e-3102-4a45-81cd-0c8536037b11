<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcSearchIndexDao">

    <sql id="tableName">
       risk_qc_search_index
    </sql>

    <sql id="selectFields">
        id,
        case_id,
        work_order_id,
        qc_type,
        organization,
        qc_unique_code,
        qc_by_name,
        qc_result,
        qc_result_second,
        question_type,
        register_mobile_encrypt,
        service_stage,
        job_content,
        task_id,
        user_id,
        rule_name,
        material_id,
        first_level_label,
        two_level_label,
        call_clues_channel,
        call_task_status,
        call_status,
        handle_result,
        hospital_dept_id,
        source_work_order_id,
        source_work_order_type
    </sql>

    <insert id="insertOne">
        insert into
        <include refid="tableName"/>
        (qc_id, case_id,work_order_id,qc_type,organization,qc_unique_code,qc_by_name,material_id, register_mobile_encrypt, service_stage
        ,job_content,task_id,user_id,hospital_dept_id, source_work_order_id, source_work_order_type, handle_result, call_status)
        values
        (#{qcId},#{caseId},#{workOrderId},#{qcType},#{organization},#{qcUniqueCode},#{qcByName},#{materialId}, #{registerMobileEncrypt}
        ,#{serviceStage},#{jobContent},#{taskId},#{userId},#{hospitalDeptId},#{sourceWorkOrderId},#{sourceWorkOrderType},
        #{handleResult},#{callStatus})
    </insert>

    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where work_order_id = #{workOrderId}
        and is_delete = 0
    </select>

    <update id="updateByWorkOrderId">
        update
        <include refid="tableName"/>
        set qc_result = #{qcResult},question_type = #{questionType}, first_property_ids = #{firstPropertyIds},qc_result_second = #{qcResultSecond}
        where work_order_id = #{workOrderId}
    </update>

    <select id="getByWorkOrderIds" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where work_order_id in
        <foreach collection="workOrderIds" open="(" close=")" separator="," item="workOrderId">
            #{workOrderId}
        </foreach>
        and is_delete = 0
    </select>


    <update id="updateMaterialId">
        update
        <include refid="tableName"/>
        set material_id = #{materialId}
        where id = #{id}
    </update>

    <update id="updateServiceStageByTaskId">
        update
        <include refid="tableName"/>
        set service_stage = #{serviceStage}
        where work_order_id = #{workOrderId}
        and task_id = #{taskId}
        and is_delete = 0
    </update>

    <select id="getAllByPage" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex">
        select * from
        <include refid="tableName"/>
        where   <![CDATA[  `id` > #{id} ]]>
        and material_id = 0
        and is_delete = 0
        ORDER BY `id`
        LIMIT 500
    </select>

    <update id="updateMaterialIdByTaskId">
        update
        <include refid="tableName"/>
        set material_id = #{materialId}
        where task_id = #{taskId}
        and qc_type = #{qcType}
    </update>

    <update id="updateCaseIdAndUserId">
        update
        <include refid="tableName"/>
        set case_id = #{caseId},user_id = #{userId}
        where task_id = #{taskId}
        and qc_type = #{qcType}
    </update>

    <update id="updateRuleNameByWorkOrderId">
        update
        <include refid="tableName"/>
        set rule_name = #{ruleName}
        where work_order_id = #{workOrderId}
        and is_delete = 0
    </update>

    <select id="selectbyIdAndSize" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcSearchIndex">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where qc_type = 1
        <if test="id > 0">
            and id > #{id}
        </if>
        order by id
        limit #{size}
    </select>

    <update id="updateById">
        update
        <include refid="tableName"/>
        set user_id = #{userId}
        where id = #{id}
    </update>

    <update id="updateCallTaskStatusByTaskId">
        update
        <include refid="tableName"/>
        set call_task_status = #{callTaskStatus}
        where task_id = #{taskId}
        and qc_type = #{qcType}
    </update>

    <update id="updateLabelByTaskId">
        update
        <include refid="tableName"/>
        set first_level_label = #{firstLevelLabel},two_level_label = #{twoLevelLabel}
        where task_id = #{taskId}
        and qc_type = #{qcType}
    </update>

    <insert id="addCallRiskQcSearchIndex">
        insert into
        <include refid="tableName"/>
        (qc_id,work_order_id,qc_type,organization,qc_unique_code,qc_by_name,register_mobile_encrypt, job_content,task_id,
        first_level_label,two_level_label,call_clues_channel,call_task_status)
        values
        (#{qcId},#{workOrderId},#{qcType},#{organization},#{qcUniqueCode},#{qcByName}, #{registerMobileEncrypt}, #{jobContent},#{taskId},
        #{firstLevelLabel},#{twoLevelLabel},#{callCluesChannel},#{callTaskStatus})
    </insert>

    <insert id="addMaterialRiskQcSearchIndex">
        insert into
        <include refid="tableName"/>
        (qc_id,case_id,work_order_id,qc_type,organization,qc_unique_code,qc_by_name,call_status,handle_result,source_work_order_type,source_work_order_id,user_id)
        values
        (#{qcId},#{caseId},#{workOrderId},#{qcType},#{organization},#{qcUniqueCode},#{qcByName},#{callStatus},#{handleResult},#{sourceWorkOrderType},#{sourceWorkOrderId},#{userId})
    </insert>

    <update id="updateCallStatus">
        update
        <include refid="tableName"/>
        set call_status = #{callStatus}
        where case_id = #{caseId}
        and work_order_id = #{workOrderId}
    </update>

    <update id="updateOrgByWorkOrderId">
        update
        <include refid="tableName"/>
        set organization = #{organization}
        where work_order_id = #{workOrderId}
    </update>
</mapper>