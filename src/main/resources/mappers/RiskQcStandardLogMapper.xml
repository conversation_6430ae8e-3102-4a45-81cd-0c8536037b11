<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcStandardLogDao">

    <sql id="TABLE">
        `risk_qc_standard_log`
    </sql>

    <insert id="addInfo">
        insert into <include refid="TABLE"/>
        (`operation_id`, `operation_name`, `operation_log`, `qc_standard_id`,`operation_type`)
        values
        (#{operationId}, #{operationName}, #{operationLog}, #{qcStandardId}, #{operationType})
    </insert>

    <select id="getLogByStandardId" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardLog">
        select * from
        <include refid="TABLE"/>
        where qc_standard_id = #{standardId}
        and is_delete = 0
    </select>

</mapper>