<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.CfRiskQcCorrectRefuseRecordDao">

    <sql id="TABLE">
        risk_qc_correct_refuse_record
    </sql>

    <insert id="insert">
        insert into <include refid="TABLE" />
        (work_order_id,correct_refuse,operation_type)
        values
        (#{workOrderId},#{correctRefuse},#{operationType})
    </insert>

    <select id="getCorrectRefuseByWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcCorrectRefuseRecord">
        select work_order_id,correct_refuse,operation_type
        from
        <include refid="TABLE"/>
        where
        work_order_id = #{workOrderId}
    </select>

</mapper>