<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcStandardPropertyDao">

    <sql id="TABLE">
        `risk_qc_standard_property`
    </sql>


     <select id="getAll" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty">
         select * from
         <include refid="TABLE"/>
         where is_delete = 0
     </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty">
        select * from
        <include refid="TABLE"/>
        where id = #{id} and is_delete = 0
    </select>

    <select id="getByIds" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty">
        select * from
        <include refid="TABLE"/>
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByNameAndLevel" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardProperty">
        select * from
        <include refid="TABLE"/>
        where property in
        <foreach collection="firstPropertyList" item="property" separator="," open="(" close=")">
            #{property}
        </foreach>
        AND level = #{level}
        and is_delete = 0
    </select>


</mapper>