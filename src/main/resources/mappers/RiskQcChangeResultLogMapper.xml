<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcChangeResultLogDao">

    <sql id="TABLE">
        `risk_qc_change_result_log`
    </sql>

    <insert id="addLog">
        insert into
        <include refid="TABLE"/>
        (`qc_name`, `operation_name`, `operation_time`, `reason`, `work_order_id`)
        values
        (#{qcName}, #{operationName}, now(), #{reason}, #{workOrderId})
    </insert>

</mapper>