<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.DiscussionRecordDao">
    <sql id="TABLE">
        `discussion_record`
    </sql>

    <sql id="selectFields">
        `id`,
        `user_id`,
        `case_id`,
        `discussion_id`,
        `discussion_result`
    </sql>
    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.DiscussionRecord">
        insert into <include refid="TABLE"/>
        (`user_id`,`case_id`,`discussion_id`,`discussion_result`)
        values (#{userId},#{caseId},#{discussionId},#{discussionResult})
    </insert>

    <select id="getByDiscussionIdAndUserId" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionRecord">
        select <include refid="selectFields"/>
        from <include refid="TABLE"/>
        where `user_id` = #{userId}
        AND `discussion_id` = #{discussionId}
        AND `is_delete` = 0
        limit 1
    </select>

    <select id="getByCaseIdAndUserId" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionRecord">
        select <include refid="selectFields"/>
        from <include refid="TABLE"/>
        where `user_id` = #{userId}
        AND `case_id` = #{caseId}
        AND `is_delete` = 0
        limit 1
    </select>


    <select id="getByDiscussionIdAndUserID" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionRecord">
        select <include refid="selectFields"/>
        from <include refid="TABLE"/>
        where `user_id` = #{userId}
        AND `discussion_id` = #{discussionId}
        AND `is_delete` = 0
        limit 1
    </select>
    <select id="getListByDiscussionId" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionRecord">
        select <include refid="selectFields"/>
        from <include refid="TABLE"/>
        where `discussion_id` = #{discussionId}
        AND `is_delete` = 0
    </select>

</mapper>