<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseTreatmentProjectDao">

    <sql id="tableName">
        risk_disease_treatment_project
    </sql>

    <sql id="selectFields">
        `id`,
        `project_name`,
        `disease_id`,
        `project_merge_rule`,
        `min_treatment_fee`,
        `max_treatment_fee`,
        `custom_treatment`,
        `raise_type`,
        `create_time`
    </sql>



    <insert id="saveList">
        insert into <include refid="tableName"/>
        (`project_name`,`disease_id`,`project_merge_rule`,`min_treatment_fee`,`max_treatment_fee`, `custom_treatment`, `raise_type`)
        values
        <foreach collection="collection" separator="," item="treatmentProject">
            (#{treatmentProject.projectName}, #{treatmentProject.diseaseId}, #{treatmentProject.projectMergeRule},
            #{treatmentProject.minTreatmentFee}, #{treatmentProject.maxTreatmentFee}, #{treatmentProject.customTreatment}, #{treatmentProject.raiseType})
        </foreach>
    </insert>

    <update id="deleteByDiseaseId">
        update <include refid="tableName"/>
        set `is_delete` = 1
        where `disease_id` = #{diseaseId}
    </update>


    <update id="update" parameterType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject">
        update <include refid="tableName"/>
        set `project_name` = #{projectName},
        `project_merge_rule` = #{projectMergeRule},
        `min_treatment_fee` = #{minTreatmentFee},
        `max_treatment_fee` = #{maxTreatmentFee},
        `raise_type` = #{raiseType}
        where `id` = #{id}
    </update>


    <update id="deleteByIdList">
        update <include refid="tableName"/>
        set `is_delete` = 1
        where `id` IN
        <foreach collection="collection" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>


    <select id="findByDiseaseId"
            resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `disease_id` = #{diseaseId}
        and `is_delete` = 0
    </select>
    <select id="findByProjectName"
            resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `project_name` = #{projectName}
        and `is_delete` = 0
        order by id desc
    </select>

    <delete id="clearAll">
        delete from <include refid="tableName"/>
        where id > 0
    </delete>

    <insert id="sync">
        insert into <include refid="tableName"/>
        (id, `project_name`,`disease_id`,`project_merge_rule`,`min_treatment_fee`,`max_treatment_fee`, `custom_treatment`, `raise_type`)
        values
        <foreach collection="collection" separator="," item="treatmentProject">
            (#{treatmentProject.id}, #{treatmentProject.projectName}, #{treatmentProject.diseaseId}, #{treatmentProject.projectMergeRule},
            #{treatmentProject.minTreatmentFee}, #{treatmentProject.maxTreatmentFee}, #{treatmentProject.customTreatment}, #{treatmentProject.raiseType})
        </foreach>
    </insert>

    <select id="getAll" resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseTreatmentProject">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where is_delete = 0
    </select>
</mapper>