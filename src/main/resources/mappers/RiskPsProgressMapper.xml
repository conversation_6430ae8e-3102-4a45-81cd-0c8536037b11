<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskPsProgressDao">

    <sql id="TABLE">
        `risk_ps_progress`
    </sql>


    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPsProgress">
        insert into
        <include refid="TABLE"/>
        (`ps_id`, `action`, `operator`, `fermentation_condition`, `biz_time`) values
        (#{psId}, #{action}, #{operator}, #{fermentationCondition}, #{bizTime})
    </insert>

    <insert id="saveList" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPsProgress">
        insert into
        <include refid="TABLE"/>
        (`ps_id`, `action`, `operator`, `fermentation_condition`, `biz_time`) values
        <foreach collection="list" item="item" separator=",">
        (#{item.psId}, #{item.action}, #{item.operator}, #{item.fermentationCondition}, #{item.bizTime})
        </foreach>
    </insert>

    <select id="listByPsId" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsProgress">
        select * from <include refid="TABLE"/>
        where ps_id = #{psId}
    </select>

    <select id="getLastProgress" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsProgress">
        select * from <include refid="TABLE"/>
        where  ps_id = #{psId}
        order by id desc
        limit 1
    </select>

    <select id="getByPsId" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsProgress">
        select * from <include refid="TABLE"/>
        where ps_id = #{psId}
    </select>

</mapper>