<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotLevelConfLogDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConfLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="scene" jdbcType="INTEGER" property="scene"/>
    <result column="modify_reason" jdbcType="VARCHAR" property="modifyReason" />
    <result column="modify_content" jdbcType="VARCHAR" property="modifyContent" />
    <result column="parse_time" jdbcType="TIMESTAMP" property="parseTime" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,scene, modify_reason, modify_content, parse_time, operate_id,
    operate_name, is_delete, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_quality_spot_level_conf_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConfLog" useGeneratedKeys="true">
    insert into risk_quality_spot_level_conf_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scene != null">
        scene,
      </if>
      <if test="modifyReason != null">
        modify_reason,
      </if>
      <if test="modifyContent != null">
        modify_content,
      </if>
      <if test="parseTime != null">
        parse_time,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scene != null">
        #{scene,jdbcType=INTEGER},
      </if>
      <if test="modifyReason != null">
        #{modifyReason,jdbcType=VARCHAR},
      </if>
      <if test="modifyContent != null">
        #{modifyContent,jdbcType=VARCHAR},
      </if>
      <if test="parseTime != null">
        #{parseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="listByScene" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from risk_quality_spot_level_conf_log
    where scene = #{scene,jdbcType=INTEGER}
  </select>

</mapper>