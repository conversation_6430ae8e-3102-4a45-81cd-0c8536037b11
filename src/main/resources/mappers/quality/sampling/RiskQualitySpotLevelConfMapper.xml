<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotLevelConfDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConf">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="scene" jdbcType="INTEGER" property="scene" />
    <result column="is_sampling" jdbcType="TINYINT" property="isSampling" />
    <result column="sampling_level" jdbcType="INTEGER" property="samplingLevel" />
    <result column="parse_time" jdbcType="TIMESTAMP" property="parseTime" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, scene, is_sampling, sampling_level, parse_time, expire_time, is_delete, update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_quality_spot_level_conf
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotLevelConf" useGeneratedKeys="true">
    insert into risk_quality_spot_level_conf
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scene != null">
        scene,
      </if>
      <if test="isSampling != null">
        is_sampling,
      </if>
      <if test="samplingLevel != null">
        sampling_level,
      </if>
      <if test="parseTime != null">
        parse_time,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scene != null">
        #{scene,jdbcType=INTEGER},
      </if>
      <if test="isSampling != null">
        #{isSampling,jdbcType=TINYINT},
      </if>
      <if test="samplingLevel != null">
        #{samplingLevel,jdbcType=INTEGER},
      </if>
      <if test="parseTime != null">
        #{parseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateSamplingLevelById">
    update risk_quality_spot_level_conf
    set sampling_level = #{samplingLevel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateExpireTimeById">
    update risk_quality_spot_level_conf
    set expire_time = #{expireTime,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectBySceneAndExpireTime" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from risk_quality_spot_level_conf
    where expire_time &gt;= #{expireTime,jdbcType=VARCHAR}
    and scene = #{scene,jdbcType=INTEGER}
  </select>

  <select id="listWithAllValidScene" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from risk_quality_spot_level_conf
    where scene in
    <foreach collection="scenes" open="(" item="scene" separator="," close=")">
      #{scene,jdbcType=INTEGER}
    </foreach>
    and expire_time >= #{parseTime,jdbcType=TIMESTAMP}
  </select>


  <select id="listWithAllValid" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from risk_quality_spot_level_conf
    where expire_time >= #{parseTime,jdbcType=TIMESTAMP}
    and is_delete = 0
  </select>

</mapper>