<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotStrategyTypeRelDao">

    <sql id="TABLE">
        `risk_quality_spot_strategy_type_rel`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel">
        insert into <include refid="TABLE"/>
        ( `strategy_id`, `type_id`, `scene_info`)
        values (#{strategyId}, #{typeId}, #{sceneInfo})
    </insert>

    <select id="findByStrategyId" resultType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel">
        select *
        from <include refid="TABLE"/>
        where `strategy_id` = #{strategyId}
        and `is_delete` = 0
    </select>

    <select id="findByTypeId"
            resultType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel">
        select *
        from <include refid="TABLE"/>
        where `type_id` = #{typeId}
        and `is_delete` = 0
    </select>

    <update id="deleteByStrategyId">
        update <include refid="TABLE"/>
        set  `is_delete` = 1
        where strategy_id = #{strategyId}
    </update>

    <select id="findByTypeIdList"
            resultType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel">
        select *
        from <include refid="TABLE"/>
        where `type_id` in
        <foreach collection="typeIds" item="typeId" open="(" close=")" separator=",">
            #{typeId}
        </foreach>
        and `is_delete` = 0
    </select>

    <select id="findByStrategyIdList"
            resultType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotTypeRel">
        select *
        from <include refid="TABLE"/>
        where `strategy_id` in
        <foreach collection="strategyIdList" item="strategyId" open="(" close=")" separator=",">
            #{strategyId}
        </foreach>
        and `is_delete` = 0
    </select>


</mapper>