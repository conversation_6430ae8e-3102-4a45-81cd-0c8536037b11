<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotTypeDao">

    <sql id="TABLE">
        `risk_quality_spot_type`
    </sql>

    <sql id="selectFields">
        `id`,
        `type_name`,
        `parent_id`

    </sql>

    <select id="getById"
            resultType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType">
        select <include refid="selectFields"/>
        from <include refid="TABLE"/>
        where `id` = #{typeId}
        AND `is_delete` = 0
    </select>

    <select id="getByParentId"
            resultType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType">
        select <include refid="selectFields"/>
        from <include refid="TABLE"/>
        where `parent_id` = #{parentId}
        AND `is_delete` = 0
    </select>

    <select id="findById"
            resultType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.RiskQualitySpotType">
        select <include refid="selectFields"/>
        from <include refid="TABLE"/>
        where `id` in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND `is_delete` = 0
    </select>


</mapper>