<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.QualitySpotStrategyLevelConfigTypeRelDao">

    <sql id="TABLE">
            `risk_quality_spot_strategy_level_config_type_rel`
    </sql>

    <sql id="selectFields">
        `id`,
        `level_config_id`,
        `type_id`,
        `scene_info`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.dto.quality.sampling.QualitySpotStrategyLevelConfigTypeRel">
        insert into <include refid="TABLE"/>
        ( `level_config_id`,`type_id`,`scene_info`)
        values (#{levelConfigId}, #{typeId}, #{sceneInfo})
    </insert>


</mapper>