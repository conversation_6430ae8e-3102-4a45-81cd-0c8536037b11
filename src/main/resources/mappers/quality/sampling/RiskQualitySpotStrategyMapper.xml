<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotStrategyDao">
    <resultMap id="BaseResultMap"
               type="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="strategy_scope" jdbcType="VARCHAR" property="strategyScope"/>
        <result column="strategy_parse_time" jdbcType="TIMESTAMP" property="strategyParseTime"/>
        <result column="strategy_expire_time" jdbcType="TIMESTAMP" property="strategyExpireTime"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="operate_id" jdbcType="BIGINT" property="operateId"/>
        <result column="operate_name" jdbcType="VARCHAR" property="operateName"/>
        <result column="execute_mode" jdbcType="TINYINT" property="executeMode"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy">
        <result column="rule_def" jdbcType="LONGVARCHAR" property="ruleDef"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        strategy_name,
        scene,
        strategy_scope,
        strategy_parse_time,
        strategy_expire_time,
        `status`,
        operate_id,
        operate_name,
        is_delete,
        update_time,
        create_time,
        execute_mode
    </sql>
    <sql id="Blob_Column_List">
        rule_def
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from risk_quality_spot_strategy
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy"
            useGeneratedKeys="true">
        insert into risk_quality_spot_strategy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyName != null">
                strategy_name,
            </if>
            <if test="strategyScope != null">
                strategy_scope,
            </if>
            <if test="strategyParseTime != null">
                strategy_parse_time,
            </if>
            <if test="strategyExpireTime != null">
                strategy_expire_time,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="scene != null">
                `scene`,
            </if>
            <if test="operateId != null">
                operate_id,
            </if>
            <if test="operateName != null">
                operate_name,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="ruleDef != null">
                rule_def,
            </if>
            <if test="executeMode != null">
                execute_mode,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyName != null">
                #{strategyName,jdbcType=VARCHAR},
            </if>
            <if test="strategyScope != null">
                #{strategyScope,jdbcType=VARCHAR},
            </if>
            <if test="strategyParseTime != null">
                #{strategyParseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="strategyExpireTime != null">
                #{strategyExpireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="scene != null">
                #{scene},
            </if>
            <if test="operateId != null">
                #{operateId,jdbcType=BIGINT},
            </if>
            <if test="operateName != null">
                #{operateName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ruleDef != null">
                #{ruleDef,jdbcType=LONGVARCHAR},
            </if>
            <if test="executeMode != null">
                #{executeMode,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <select id="listByConditions"
            resultType="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy">
        select rqss.*
        from risk_quality_spot_strategy as rqss
        <where>
            <if test="status != null">
                and rqss.status = #{status,jdbcType=INTEGER}
            </if>
            <if test="operateName != null and operateName != ''">
                and rqss.operate_name like concat('%', #{operateName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="strategyName != null and strategyName != ''">
                and rqss.strategy_name like concat('%', #{strategyName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="sceneList != null and sceneList.size() != 0">
                and scene in <foreach collection="sceneList" item="scene" separator="," close=")" open="("> #{scene} </foreach>
            </if>
        </where>
        order by id desc
    </select>
    <update id="updateStatusById">
        update risk_quality_spot_strategy
        set status               = #{status,jdbcType=INTEGER},
            strategy_expire_time = #{currTime,jdbcType=TIMESTAMP},
            operate_id           = #{operateId,jdbcType=BIGINT},
            operate_name         = #{operateName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="listByScene" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from risk_quality_spot_strategy
        where scene = #{scene,jdbcType=INTEGER}
          and status = #{Status,jdbcType=TINYINT}
    </select>

    <select id="listValidStrategy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from risk_quality_spot_strategy
        <where>
            <if test="startId != null">
                id > #{startId,jdbcType=BIGINT}
            </if>
            and strategy_expire_time &gt;= #{currTime,jdbcType=TIMESTAMP}
        </where>
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from risk_quality_spot_strategy
                where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and is_delete = 0
    </select>

    <update id="updateStrategy"
            parameterType="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotStrategy">
        update risk_quality_spot_strategy
        <set>
            <if test="strategyName != null">
                strategy_name = #{strategyName},
            </if>
            <if test="strategyScope != null">
                strategy_scope = #{strategyScope},
            </if>
            <if test="strategyParseTime != null">
                strategy_parse_time = #{strategyParseTime},
            </if>
            <if test="strategyExpireTime != null">
                strategy_expire_time = #{strategyExpireTime},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="scene != null">
                `scene` = #{scene},
            </if>
            <if test="operateId != null">
                operate_id = #{operateId},
            </if>
            <if test="operateName != null">
                operate_name = #{operateName},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="ruleDef != null">
                rule_def = #{ruleDef},
            </if>
            <if test="executeMode != null">
                execute_mode = #{executeMode},
            </if>
        </set>
        where `id` = #{id}
    </update>
</mapper>