<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQualitySpotRuleDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="data_scope_string" jdbcType="VARCHAR" property="dataScopeString" />
    <result column="execute_mode_value" jdbcType="VARCHAR" property="excuteModeValue" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule">
    <result column="rule_script" jdbcType="LONGVARCHAR" property="ruleScript" />
  </resultMap>
  <sql id="Base_Column_List">
    id, strategy_id, `name`, priority, `status`, is_delete, create_time, update_time, data_scope_string,execute_mode_value
  </sql>
  <sql id="Blob_Column_List">
    rule_script
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from risk_quality_spot_rule
    where id = #{id,jdbcType=BIGINT}
    and status = 0
    and is_delete = 0
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.quality.sampling.RiskQualitySpotRule" useGeneratedKeys="true">
    insert into risk_quality_spot_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="ruleScript != null">
        rule_script,
      </if>
      <if test="excuteModeValue != null">
        execute_mode_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleScript != null">
        #{ruleScript,jdbcType=LONGVARCHAR},
      </if>
      <if test="excuteModeValue != null">
        #{excuteModeValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="listByStrategyIdsOptionsStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from risk_quality_spot_rule
    <where>
        strategy_id in
        <foreach collection="strategyIds" open="(" item="strategyId" separator="," close=")">
            #{strategyId,jdbcType=BIGINT}
        </foreach>
        <if test="status != null">
            and status = #{status,jdbcType=TINYINT}
        </if>
      and is_delete = 0
    </where>
  </select>
  <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id">
    insert into risk_quality_spot_rule (strategy_id, `name`, priority, `status`, `rule_script`, `execute_mode_value`,`data_scope_string`)
    values
    <foreach collection="list" item="rule" separator=",">
        (#{rule.strategyId,jdbcType=BIGINT}, #{rule.name,jdbcType=VARCHAR}, #{rule.priority,jdbcType=INTEGER},
        #{rule.status,jdbcType=TINYINT}, #{rule.ruleScript,jdbcType=VARCHAR}, #{rule.excuteModeValue,jdbcType=VARCHAR},
      #{rule.dataScopeString,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="deleteOldRuleByStrategyId">
    update risk_quality_spot_rule
    set is_delete = 1
    where strategy_id = #{strategyId}
  </update>

  <update id="closeOldRuleByStrategyId">
    update risk_quality_spot_rule
    set status = 1
    where strategy_id = #{strategyId}
  </update>

  <update id="openOldRuleByStrategyId">
    update risk_quality_spot_rule
    set status = 0
    where strategy_id = #{strategyId}
  </update>

  <select id="findByStrategyId"
          resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from risk_quality_spot_rule
    where strategy_id = #{strategyId}
    and status = 0
    and is_delete = 0
  </select>

  <select id="findById" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from risk_quality_spot_rule
    where id in
    <foreach collection="ids" separator="," open="(" close=")" item="id">
      #{id}
    </foreach>
    and status = 0
    and is_delete = 0
  </select>
</mapper>