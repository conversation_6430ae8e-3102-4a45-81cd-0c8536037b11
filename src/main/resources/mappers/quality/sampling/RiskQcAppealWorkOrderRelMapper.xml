<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.quality.sampling.RiskQcAppealWorkOrderRelDao">

    <sql id="TABLE">
        `qc_appeal_work_order_rel`
    </sql>

    <insert id="save">
        insert into <include refid="TABLE"/>
        (`qc_work_order_id`, `appeal_work_order_id`)
        values (#{qcWorkOrderId}, #{appealWorkOrderId})
    </insert>


    <select id="getByAppealWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcAppealWorkOrderRel">
        select id,qc_work_order_id,appeal_work_order_id
        from  <include refid="TABLE"/>
        where appeal_work_order_id = #{appealWorkOrderId}
        AND is_delete = 0
    </select>


</mapper>