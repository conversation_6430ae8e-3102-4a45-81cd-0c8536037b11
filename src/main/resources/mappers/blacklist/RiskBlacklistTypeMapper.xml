<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistTypeDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="classify_id" jdbcType="BIGINT" property="classifyId" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type_name, classify_id, operate_id, operate_name, `status`, is_delete, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistType" useGeneratedKeys="true">
    insert into risk_blacklist_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="typeName != null">
        type_name,
      </if>
      <if test="classifyId != null">
        classify_id,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="classifyId != null">
        #{classifyId,jdbcType=BIGINT},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="listByOptions" resultMap="BaseResultMap">
	select <include refid="Base_Column_List"/>
	from risk_blacklist_type bt
	<where>
		<if test="typeName != null and typeName != ''">
  		 and type_name like concat('%', #{typeName,jdbcType=VARCHAR}, '%')
		</if>
		<if test="actionId != null">
  		 and EXISTS (
			select 1
			from risk_blacklist_type_action_ref tar
			where tar.type_id = bt.id
			and tar.action_id=#{actionId,jdbcType=BIGINT}
			and is_delete = 0
		)
		</if>
        <if test="status != null">
            and `status` = #{status,jdbcType=TINYINT}
        </if>
	</where>
	order by id desc
  </select>
  <update id="updateStatusById">
  	update risk_blacklist_type
  	set `status` = #{status,jdbcType=TINYINT}
  	where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listByClassifyIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
	from risk_blacklist_type
	where classify_id in
	<foreach collection="list" open="(" item="classifyId" separator="," close=")">
		#{classifyId,jdbcType=BIGINT}
	</foreach>
  </select>
  <select id="listByIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_type
  	where id in
  	<foreach collection="collection" open="(" item="id" separator="," close=")">
		#{id,jdbcType=BIGINT}
	</foreach>
  </select>
  <select id="listByEnabledTypeNames" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_type
  	where type_name in
  	<foreach collection="collection" open="(" item="typeName" separator="," close=")">
  		#{typeName,jdbcType=VARCHAR}
    </foreach>
  	and `status` = 0
  </select>
</mapper>