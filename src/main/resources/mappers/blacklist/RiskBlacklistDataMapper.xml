<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistDataDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="encrypt_mobile_bind" jdbcType="VARCHAR" property="encryptMobileBind" />
    <result column="encrypt_id_card" jdbcType="VARCHAR" property="encryptIdCard" />
    <result column="encrypt_mobile" jdbcType="VARCHAR" property="encryptMobile" />
    <result column="user_id_bind" jdbcType="BIGINT" property="userIdBind" />
    <result column="encrypt_born_card" jdbcType="VARCHAR" property="encryptBornCard" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="operate_reason" jdbcType="VARCHAR" property="operateReason" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, encrypt_mobile_bind, encrypt_id_card, encrypt_mobile, user_id_bind, 
    encrypt_born_card, user_name, operate_reason, operate_id, operate_name, is_delete, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistData" useGeneratedKeys="true">
    insert into risk_blacklist_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="encryptMobileBind != null">
        encrypt_mobile_bind,
      </if>
      <if test="encryptIdCard != null">
        encrypt_id_card,
      </if>
      <if test="encryptMobile != null">
        encrypt_mobile,
      </if>
      <if test="userIdBind != null">
        user_id_bind,
      </if>
      <if test="encryptBornCard != null">
        encrypt_born_card,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="operateReason != null">
        operate_reason,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="limitTime != null">
        limit_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="encryptMobileBind != null">
        #{encryptMobileBind,jdbcType=VARCHAR},
      </if>
      <if test="encryptIdCard != null">
        #{encryptIdCard,jdbcType=VARCHAR},
      </if>
      <if test="encryptMobile != null">
        #{encryptMobile,jdbcType=VARCHAR},
      </if>
      <if test="userIdBind != null">
        #{userIdBind,jdbcType=BIGINT},
      </if>
      <if test="encryptBornCard != null">
        #{encryptBornCard,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="operateReason != null">
        #{operateReason,jdbcType=VARCHAR},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="limitTime != null">
        #{limitTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="listByOptions" resultMap="BaseResultMap">
  	select rbd.*
  	from risk_blacklist_data rbd
	  <if test="typeId != null and typeId > 0">
		  join risk_blacklist_data_type_ref rbdtr
			on rbd.id = rbdtr.data_id
			and rbdtr.type_id = #{typeId,jdbcType=BIGINT}
		  	and rbdtr.is_delete = 0
	  </if>
  	<where>
  		<if test="userIdAlias != null">
  		and rbd.user_id = #{userIdAlias,jdbcType=BIGINT}
		</if>
		<if test="mobile != null and mobile != ''">
  		and rbd.encrypt_mobile = #{mobile,jdbcType=VARCHAR}
		</if>
		<if test="idCard != null and idCard != ''">
  		and rbd.encrypt_id_card = #{idCard,jdbcType=VARCHAR}
		</if>
		<if test="userName != null and userName != ''">
  		and rbd.user_name = #{userName,jdbcType=VARCHAR}
		</if>
		<if test="bornCard != null and bornCard != ''">
  		and rbd.encrypt_born_card = #{bornCard,jdbcType=VARCHAR}
		</if>
        <if test="isDelete != null">
            and rbd.is_delete = #{isDelete,jdbcType=TINYINT}
        </if>
        <if test="operateName != null and operateName != ''">
            and rbd.operate_name like concat('%', #{operateName}, '%')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and rbd.create_time <![CDATA[ >= ]]> #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and rbd.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
    </where>
    order by rbd.id desc
  </select>
  <update id="updateOptionsById">
    update risk_blacklist_data
    <set>
      <if test="userId != null">
  		user_id = #{userId,jdbcType=BIGINT},
	  </if>
    <if test="limitTime != null">
        limit_time = #{limitTime,jdbcType=BIGINT},
    </if>
	  <if test="encryptMobileBind != null and encryptMobileBind != ''">
        encrypt_mobile_bind = #{encryptMobileBind,jdbcType=VARCHAR},
      </if>
      <if test="encryptIdCard != null and encryptIdCard != ''">
        encrypt_id_card = #{encryptIdCard,jdbcType=VARCHAR},
      </if>
      <if test="encryptMobile != null and encryptMobile != ''">
        encrypt_mobile = #{encryptMobile,jdbcType=VARCHAR},
      </if>
      <if test="userIdBind != null">
        user_id_bind = #{userIdBind,jdbcType=BIGINT},
      </if>
      <if test="encryptBornCard != null and encryptBornCard != ''">
        encrypt_born_card = #{encryptBornCard,jdbcType=VARCHAR},
      </if>
      <if test="userName != null and userName != ''">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="operateReason != null and operateReason != ''">
       operate_reason = #{operateReason,jdbcType=VARCHAR},
      </if>
      <if test="operateId != null and operateId > 0">
        operate_id = #{operateId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null and operateName != ''">
        operate_name = #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="limitTime != null">
        limit_time = #{limitTime,jdbcType=BIGINT},
      </if>
	</set>
  	where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getByUserIdOrCryptoIdCardOrMobileOrBornCard" resultMap="BaseResultMap">
  	<trim suffixOverrides="union all">
  	  	<if test="userIds != null and userIds.size() != 0">
  	        select <include refid="Base_Column_List"/>
  	        from risk_blacklist_data
  	        where user_id in
  	        <foreach collection="userIds" open="(" item="userId" separator="," close=")">
			   #{userId,jdbcType=BIGINT}
			</foreach>
  	        union all
		</if>
		<if test="cryptoMobiles != null and cryptoMobiles.size() != 0">
  	        select <include refid="Base_Column_List"/>
  	        from risk_blacklist_data
  	        where encrypt_mobile in
  	        <foreach collection="cryptoMobiles" open="(" item="cryptoMobile" separator="," close=")">
			  	#{cryptoMobile,jdbcType=VARCHAR}
			</foreach>
  	        union all
		</if>
		<if test="cryptoIdCards != null and cryptoIdCards.size() != 0">
  	        select <include refid="Base_Column_List"/>
  	        from risk_blacklist_data
  	        where encrypt_id_card in
  	        <foreach collection="cryptoIdCards" open="(" item="cryptoIdCard" separator="," close=")">
			    #{cryptoIdCard,jdbcType=VARCHAR}
			</foreach>
  	        union all
		</if>
		<if test="cryptoBornCards != null and cryptoBornCards.size() != 0">
  	        select <include refid="Base_Column_List"/>
  	        from risk_blacklist_data
  	        where encrypt_born_card in
  	        <foreach collection="cryptoBornCards" open="(" item="cryptoBornCard" separator="," close=")">
			   #{cryptoBornCard,jdbcType=VARCHAR}
			</foreach>
		</if>
	</trim>
  </select>
  <select id="listNotBoundUidLimit" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data
  	where encrypt_mobile > ''
  	and user_id_bind = 0
  	<if test="previousId != null">
  		and id > #{previousId,jdbcType=BIGINT}
    </if>
    limit #{limit,jdbcType=INTEGER}
  </select>
  
  <select id="listNotBoundMobileLimit" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data
  	where user_id > 0
  	and encrypt_mobile_bind = ''
  	<if test="previousId != null">
  		and id > #{previousId,jdbcType=BIGINT}
    </if>
    limit #{limit,jdbcType=INTEGER}
  </select>
  
  <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id">
  	insert into risk_blacklist_data (user_id, encrypt_id_card, encrypt_born_card, encrypt_mobile, user_name, user_id_bind,
  	encrypt_mobile_bind, operate_reason, operate_id, operate_name)
  	values
  	<foreach collection="list" separator="," item="param">
  	(#{param.userId,jdbcType=BIGINT}, #{param.encryptIdCard,jdbcType=VARCHAR}, #{param.encryptBornCard,jdbcType=VARCHAR},
  	 #{param.encryptMobile,jdbcType=VARCHAR}, #{param.userName,jdbcType=VARCHAR}, #{param.userIdBind,jdbcType=BIGINT},
  	 #{param.encryptMobileBind,jdbcType=VARCHAR}, #{param.operateReason,jdbcType=VARCHAR}, #{param.operateId,jdbcType=BIGINT},
  	 #{param.operateName,jdbcType=VARCHAR})
	</foreach>
  </insert>
  
  <select id="listByUserIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data
  	where user_id in
  	<foreach collection="collection" open="(" item="userId" separator="," close=")">
  		#{userId,jdbcType=BIGINT}
	</foreach>
  </select>
  <select id="listByLimit" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data
  	<where>
  		<if test="previousId != null">
	        id > #{previousId}
		</if>
	</where>
	limit #{limit}
  </select>

    <select id="listByOperateNameAndTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from risk_blacklist_data
        where operate_name like CONCAT('%', #{operateName}, '%')
        and create_time <![CDATA[ >= ]]> #{beginTime}
        and create_time <![CDATA[ <= ]]> #{endTime}
        and is_delete = 0
    </select>

    <select id="countByOperateNameAndTime" resultType="java.lang.Integer">
        select count(1)
        from risk_blacklist_data
        where operate_name like CONCAT('%', #{operateName}, '%')
        and create_time <![CDATA[ >= ]]> #{beginTime}
        and create_time <![CDATA[ <= ]]> #{endTime}
        and is_delete = 0
    </select>

    <update id="deleteBlackListData">
        update risk_blacklist_data set is_delete = 1 where id = #{id}
    </update>

    <select id="getByLimitTime" resultMap="BaseResultMap">
        select *
        from risk_blacklist_data
        where limit_time <![CDATA[ <= ]]> #{limitTime} and is_delete = 0 and limit_time <![CDATA[ > ]]> 0
    </select>
</mapper>