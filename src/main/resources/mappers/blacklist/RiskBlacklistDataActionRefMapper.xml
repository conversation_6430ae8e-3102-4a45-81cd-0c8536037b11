<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistDataActionRefDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataActionRef">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="action_id" jdbcType="BIGINT" property="actionId" />
    <result column="limit_time" jdbcType="BIGINT" property="limitTime" />
      <result column="limit_time_type" jdbcType="INTEGER" property="limitTimeType" />
      <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, data_id, action_id, limit_time, limit_time_type, is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_data_action_ref
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataActionRef" useGeneratedKeys="true">
    insert into risk_blacklist_data_action_ref
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        data_id,
      </if>
      <if test="actionId != null">
        action_id,
      </if>
      <if test="limitTime != null">
        limit_time,
      </if>
      <if test="limitTimeType != null">
        limit_time_type,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        #{dataId,jdbcType=BIGINT},
      </if>
      <if test="actionId != null">
        #{actionId,jdbcType=BIGINT},
      </if>
      <if test="limitTime != null">
        #{limitTime,jdbcType=BIGINT},
      </if>
      <if test="limitTimeType != null">
        #{limitTimeType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="saveBatch">
  	insert into risk_blacklist_data_action_ref (data_id, action_id, limit_time, limit_time_type)
  	values
  	<foreach collection="list" separator="," item="param">
  		(#{param.dataId,jdbcType=BIGINT}, #{param.actionId,jdbcType=BIGINT}, #{param.limitTime,jdbcType=BIGINT}, #{param.limitTimeType,jdbcType=INTEGER})
    </foreach>
  </insert>
  <select id="listByDataId" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data_action_ref
  	where data_id = #{dataId,jdbcType=BIGINT}
  	and is_delete = 0
  </select>
  <select id="listByDataIdAndActionIds" resultMap="BaseResultMap">
  	select id, data_id, action_id
  	from risk_blacklist_data_action_ref
  	where data_id in
  	<foreach collection="dataIds" open="(" separator="," item="dataId" close=")">
	    #{dataId,jdbcType=BIGINT}
	</foreach>
	<if test="actionIds != null and actionIds.size() != 0">
		and action_id in
		<foreach collection="actionIds" open="(" item="actionId" separator="," close=")">
			#{actionId,jdbcType=BIGINT}
		</foreach>
	</if>
	and is_delete = 0
  </select>
  <update id="deleteByIds">
   update risk_blacklist_data_action_ref
   set is_delete = 1
   where id in
   <foreach collection="collection" open="(" item="id" separator="," close=")">
  		#{id,jdbcType=BIGINT}
	</foreach>
  </update>

    <update id="updateActionRefById">
        update risk_blacklist_data_action_ref
        <set>
            <if test="dataId != null">
                data_id = #{dataId,jdbcType=BIGINT},
            </if>
            <if test="limitTime != null">
                limit_time = #{limitTime,jdbcType=BIGINT},
            </if>
            <if test="limitTimeType != null">
                limit_time_type = #{limitTimeType,jdbcType=BIGINT},
            </if>
            <if test="actionId != null">
                action_id = #{actionId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByLimitTime" resultMap="BaseResultMap">
        select *
        from risk_blacklist_data_action_ref
        where limit_time <![CDATA[ <= ]]> #{limitTime} and is_delete = 0 and limit_time <![CDATA[ > ]]> 0
    </select>
</mapper>