<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistTypeActionRefDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeActionRef">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="action_id" jdbcType="BIGINT" property="actionId" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type_id, action_id, limit_time_type, is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_type_action_ref
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistTypeActionRef" useGeneratedKeys="true">
    insert into risk_blacklist_type_action_ref
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="typeId != null">
        type_id,
      </if>
      <if test="actionId != null">
        action_id,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="actionId != null">
        #{actionId,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="listByTypeIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_type_action_ref
  	where type_id in
  	<foreach collection="collection" open="(" item="typeId" separator="," close=")">
  		#{typeId,jdbcType=BIGINT}
  	</foreach>
  	and is_delete = 0
  </select>
  <insert id="saveBatch">
  	insert into risk_blacklist_type_action_ref (type_id, action_id, limit_time_type)
  	values
  	<foreach collection="list" separator="," item="param">
  	(#{param.typeId,jdbcType=BIGINT}, #{param.actionId,jdbcType=BIGINT}, #{param.limitTimeType,jdbcType=INTEGER})
	</foreach>
  </insert>
  <update id="deleteByIds">
  	update risk_blacklist_type_action_ref
  	set is_delete = 1
  	where id in
  	<foreach collection="list" open="(" item="id" separator="," close=")">
  	#{id,jdbcType=BIGINT}
    </foreach>
  </update>

    <update id="updateTypeActionRefById">
        update risk_blacklist_type_action_ref
        <set>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=BIGINT},
            </if>
            <if test="limitTimeType != null">
                limit_time_type = #{limitTimeType,jdbcType=INTEGER},
            </if>
            <if test="actionId != null">
                action_id = #{actionId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
  
</mapper>