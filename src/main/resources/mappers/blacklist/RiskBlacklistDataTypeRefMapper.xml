<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistDataTypeRefDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataTypeRef">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="action_ids" jdbcType="VARCHAR" property="actionIds" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, data_id, type_id, action_ids, type_name, is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_data_type_ref
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistDataTypeRef" useGeneratedKeys="true">
    insert into risk_blacklist_data_type_ref
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        data_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="actionIds != null">
        action_ids,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        #{dataId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="actionIds != null">
        #{actionIds,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="listValidByDataIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_data_type_ref
  	where data_id in
  	<foreach collection="collection" open="(" item="dataId" separator="," close=")">
	    #{dataId,jdbcType=BIGINT}
	</foreach>
	and is_delete = 0
  </select>
  <select id="listByTypeIdLimit" resultMap="BaseResultMap">
  	select id, data_id, type_id, action_ids
  	from risk_blacklist_data_type_ref
  	<where>
  	    <if test="typeId != null">
  	    	type_id = #{typeId,jdbcType=BIGINT}
		</if>
  		<if test="previousId != null">
	        and id > #{previousId,jdbcType=BIGINT}
	    </if>
	    and is_delete = 0
    </where>
    order by id
    limit #{limit}
  </select>
  <select id="listByDataIds" resultMap="BaseResultMap">
  	select id, data_id, type_id, action_ids
  	from risk_blacklist_data_type_ref
  	where data_id in
  	<foreach collection="dataIds" open="(" item="dataId" separator="," close=")">
	    #{dataId,jdbcType=BIGINT}
	</foreach>
	and is_delete = 0
  </select>
  <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id">
  	insert into risk_blacklist_data_type_ref (data_id, type_id, action_ids, type_name)
  	values
  	<foreach collection="list" separator="," item="param">
  		(#{param.dataId,jdbcType=BIGINT}, #{param.typeId,jdbcType=BIGINT},
  		#{param.actionIds,jdbcType=VARCHAR}, #{param.typeName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="deleteByIds">
  	update risk_blacklist_data_type_ref
  	set is_delete = 1
  	where id in
  	<foreach collection="list" open="(" item="id" separator="," close=")">
  		#{id,jdbcType=BIGINT}
	</foreach>
  </update>
  <update id="updateActionIdsByIds">
  	update risk_blacklist_data_type_ref
  	set action_ids = #{actionIds,jdbcType=VARCHAR}
  	where id in
  	<foreach collection="ids" item="id" open="(" separator="," close=")">
  		#{id,jdbcType=BIGINT}
    </foreach>
  </update>

</mapper>