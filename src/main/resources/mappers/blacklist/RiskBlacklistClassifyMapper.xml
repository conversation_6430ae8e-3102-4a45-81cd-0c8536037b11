<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.blacklist.RiskBlacklistClassifyDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistClassify">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="level_path" jdbcType="VARCHAR" property="levelPath" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, parent_id, `level`, level_path, `status`, is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_blacklist_classify
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.blacklist.RiskBlacklistClassify" useGeneratedKeys="true">
    insert into risk_blacklist_classify
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="levelPath != null">
        level_path,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="levelPath != null">
        #{levelPath,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id">
  	insert into risk_blacklist_classify (`name`, `level`, level_path, parent_id) values
  	<foreach collection="list" separator="," item="param">
  	(#{param.name,jdbcType=VARCHAR}, #{param.level,jdbcType=INTEGER}, #{param.levelPath,jdbcType=VARCHAR}, #{param.parentId,jdbcType=BIGINT})
   	</foreach>
  </insert>
  <update id="updateLevelPathAndParentId">
    update risk_blacklist_classify
    set level_path = #{levelPath,jdbcType=VARCHAR},
        parent_id = #{parentId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listParentIdAndOptional" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_classify
  	where parent_id = #{parentId,jdbcType=BIGINT}
  	<if test="name != null and name != ''">
	    and `name` = #{name, jdbcType=VARCHAR}
	</if>
  	<if test="status != null">
	    and status = #{status,jdbcType=TINYINT}
	</if>
  </select>
  <select id="listByIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_blacklist_classify
  	where id in
  	<foreach collection="list" open="(" item="id" separator="," close=")">
  	#{id,jdbcType=BIGINT}
    </foreach>
  </select>
  <update id="updateStatusById">
  	update risk_blacklist_classify
  	set status = #{status,jdbcType=TINYINT}
  	where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>