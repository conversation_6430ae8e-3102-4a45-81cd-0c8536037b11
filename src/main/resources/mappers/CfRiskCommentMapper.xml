<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.CfRiskCommentDao">

    <sql id="TABLE">
        `cf_risk_comment`
    </sql>

    <select id="findByParam" resultType="com.shuidihuzhu.cf.risk.admin.model.CfRiskComment" parameterType="java.util.Map">
        select * from <include refid="TABLE"/> as page
        where case_id = #{param.caseId}
        <if test="param.containsKey('startTime')">
           and create_time &gt;= #{param.startTime}
        </if>
        <if test="param.containsKey('endTime')">
            and create_time &lt; #{param.endTime}
        </if>
        <if test="param.containsKey('keyword')">
            and content like concat(#{param.keyword}, '%')
        </if>
    </select>

    <update id="updateDelById">
        update <include refid="TABLE"/>
        set is_delete = 1
        where id = #{id}
    </update>

    <select id="findByDiscussionId" resultType="com.shuidihuzhu.cf.risk.admin.model.CfRiskComment">
        select * from <include refid="TABLE"/>
        where biz_id = #{discussionId} and is_delete = 0
    </select>

</mapper>