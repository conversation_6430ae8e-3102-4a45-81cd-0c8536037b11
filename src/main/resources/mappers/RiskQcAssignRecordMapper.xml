<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcAssignRecordDao">

    <sql id="tableName">
       risk_qc_assign_record
    </sql>

    <insert id="addList" parameterType="java.util.List">
        insert into <include refid="tableName"/>
        (`unique_code`, `order_type`, `work_order_id`) values
        <foreach collection="list" item="i" separator=",">
            (#{i.uniqueCode}, #{i.orderType}, #{i.workOrderId})
        </foreach>
    </insert>

    <select id="countRangeByCode" resultType="java.lang.Integer">
        select count(*) from <include refid="tableName"/>
        where `unique_code` = #{uniqueCode}
        and `order_type` = #{orderType}
        and `create_time` &gt;= #{startTime}
        and `create_time` &lt; #{endTime}
    </select>


</mapper>