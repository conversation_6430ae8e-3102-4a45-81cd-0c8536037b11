<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.hit.RiskStrategyCallResultDao">

    <insert id="insertCallResult"  parameterType="com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyCallResult">
        INSERT INTO risk_strategy_call_result (case_id, risk_strategy, second_strategy, call_result, call_time, user_name, user_id_card )
        VALUES
            ( #{caseId}, #{riskStrategy}, #{secondStrategy}, #{callResult}, #{callTime}, #{userName}, #{userIdCard} );
    </insert>

</mapper>