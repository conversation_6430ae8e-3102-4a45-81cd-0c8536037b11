<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.hit.CfRiskRepeatPayOrderCaseRecordDao">
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.model.admin.hit.StrategyHitDto">
        <result column="case_id" jdbcType="INTEGER" property="caseId" />
        <result column="repeat_case_id" jdbcType="INTEGER" property="repeatCaseId" />
        <result column="hit_phase" jdbcType="INTEGER" property="hitPhase" />
        <result column="risk_strategy" jdbcType="INTEGER" property="riskStrategy" />
        <result column="second_strategy" jdbcType="INTEGER" property="secondStrategy" />
        <result column="risk_type" jdbcType="INTEGER" property="riskType" />
        <result column="hit_info" jdbcType="LONGVARCHAR" property="hitInfo" />
        <result column="action" jdbcType="VARCHAR" property="action" />
    </resultMap>

    <sql id="Base_Column_List">
        repeat_case_id, case_id, hit_phase, risk_strategy, second_strategy, risk_type, `hit_info`, `action`
    </sql>

    <insert id="insertRepeatPayOrder" parameterType="com.shuidihuzhu.cf.risk.model.admin.hit.StrategyHitDto">
        insert into cf_risk_repeat_pay_order_case_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseId != null">
                case_id,
            </if>
            <if test="hitPhase != null">
                hit_phase,
            </if>
            <if test="riskStrategy != null">
                risk_strategy,
            </if>
            <if test="secondStrategy != null">
                second_strategy,
            </if>
            <if test="riskType != null">
                risk_type,
            </if>
            <if test="hitInfo != null">
                `hit_info`,
            </if>
            <if test="repeatCaseId != null">
                repeat_case_id,
            </if>
            <if test="action != null">
                `action`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="caseId != null">
                #{caseId,jdbcType=INTEGER},
            </if>
            <if test="hitPhase != null">
                #{hitPhase,jdbcType=INTEGER},
            </if>
            <if test="riskStrategy != null">
                #{riskStrategy,jdbcType=INTEGER},
            </if>
            <if test="secondStrategy != null">
                #{secondStrategy,jdbcType=INTEGER},
            </if>
            <if test="riskType != null">
                #{riskType,jdbcType=INTEGER},
            </if>
            <if test="hitInfo != null">
                #{hitInfo,jdbcType=INTEGER},
            </if>
            <if test="repeatCaseId != null">
                #{repeatCaseId,jdbcType=INTEGER},
            </if>
            <if test="action != null">
                #{action,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="listByCaseId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from cf_risk_repeat_pay_order_case_record
        where case_id = #{caseId}
    </select>

</mapper>