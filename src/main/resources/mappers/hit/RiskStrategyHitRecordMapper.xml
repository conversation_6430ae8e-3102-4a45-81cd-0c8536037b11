<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.hit.RiskStrategyHitRecordDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="INTEGER" property="caseId" />
    <result column="launch_time" jdbcType="TIMESTAMP" property="launchTime" />
    <result column="hit_phase" jdbcType="INTEGER" property="hitPhase" />
    <result column="risk_strategy" jdbcType="INTEGER" property="riskStrategy" />
    <result column="second_strategy" jdbcType="INTEGER" property="secondStrategy" />
    <result column="risk_type" jdbcType="INTEGER" property="riskType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="result" jdbcType="TINYINT" property="result" />
    <result column="action" jdbcType="VARCHAR" property="action" />
    <result column="lifting" jdbcType="TINYINT" property="lifting" />
    <result column="handle_count" jdbcType="INTEGER" property="handleCount" />
    <result column="update_count" jdbcType="TINYINT" property="updateCount"/>
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord">
    <result column="hit_info" jdbcType="LONGVARCHAR" property="hitInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, case_id, launch_time, hit_phase, risk_strategy, second_strategy, risk_type, `status`,
    `result`, `action`, lifting, handle_count, update_count, operate_id, operate_name, is_delete, create_time,
    update_time
  </sql>
  <sql id="Blob_Column_List">
    hit_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from risk_strategy_hit_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitRecord" useGeneratedKeys="true">
    insert into risk_strategy_hit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>
      <if test="launchTime != null">
        launch_time,
      </if>
      <if test="hitPhase != null">
        hit_phase,
      </if>
      <if test="riskStrategy != null">
        risk_strategy,
      </if>
      <if test="secondStrategy != null">
        second_strategy,
      </if>
      <if test="riskType != null">
        risk_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="result != null">
        `result`,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="lifting != null">
        lifting,
      </if>
      <if test="handleCount != null">
        handle_count,
      </if>
      <if test="updateCount != null">
		update_count,
	  </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="hitInfo != null">
        hit_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=INTEGER},
      </if>
      <if test="launchTime != null">
        #{launchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hitPhase != null">
        #{hitPhase,jdbcType=INTEGER},
      </if>
      <if test="riskStrategy != null">
        #{riskStrategy,jdbcType=INTEGER},
      </if>
      <if test="secondStrategy != null">
        #{secondStrategy,jdbcType=INTEGER},
      </if>
      <if test="riskType != null">
        #{riskType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        #{result,jdbcType=TINYINT},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="lifting != null">
        #{lifting,jdbcType=TINYINT},
      </if>
      <if test="handleCount != null">
        #{handleCount,jdbcType=INTEGER},
      </if>
      <if test="updateCount != null">
        #{updateCount,jdbcType=TINYINT},
	  </if>
      <if test="operateId != null">
        #{operateId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hitInfo != null">
        #{hitInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="listByIds" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
  	from risk_strategy_hit_record
  	where id in
  	<foreach collection="collection" open="(" item="id" separator="," close=")">
  		#{id,jdbcType=BIGINT}
	</foreach>
	order by id desc
  </select>
  <update id="updateHandleInfo">
	update risk_strategy_hit_record
	<set>
		<if test="status != null">
				status = #{status,jdbcType=INTEGER},
		</if>
		<if test="result != null">
				result = #{result,jdbcType=TINYINT},
		</if>
		<if test="action != null and action != ''">
				`action` = #{action,jdbcType=VARCHAR},
		</if>
		<if test="lifting != null">
				lifting = #{lifting,jdbcType=TINYINT},
		</if>
		<if test="incrementUpdateCount">
				update_count = update_count + 1,
		</if>
		handle_count = handle_count + 1,
		operate_id = #{operateId,jdbcType=BIGINT},
		operate_name = #{operateName,jdbcType=VARCHAR}
	</set>
	where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="listByCaseIdPhase" resultMap="ResultMapWithBLOBs">
  	select <include refid="Base_Column_List"/>, <include refid="Blob_Column_List"/>
    from risk_strategy_hit_record
    where case_id = #{caseId,jdbcType=INTEGER}
    and hit_phase = #{hitPhase,jdbcType=INTEGER}
  </select>
  <select id="tempWash" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>, <include refid="Blob_Column_List"/>
  	from risk_strategy_hit_record
  	where second_strategy = 6
  	and result = 0
  	and `action` = ''
  	and lifting = 0
  </select>
  <select id="oneByCaseStrategy" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_strategy_hit_record
  	where case_id = #{caseId,jdbcType=INTEGER}
  	and hit_phase in
  	<foreach collection="hitPhases" open="(" item="hitPhase" separator="," close=")">
  		#{hitPhase,jdbcType=INTEGER}
	</foreach>
	and lifting = 0
  	limit 1
  </select>
  
</mapper>