<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.hit.RiskStrategyHitOperateDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitOperate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="hit_record_id" jdbcType="BIGINT" property="hitRecordId" />
    <result column="result" jdbcType="TINYINT" property="result" />
    <result column="action" jdbcType="VARCHAR" property="action" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, hit_record_id, `result`, `action`, operate_id, operate_name, remark, is_delete, 
    create_time, update_time, other_reason, active_stop, repeat_case_id, radio
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_strategy_hit_operate
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.hit.RiskStrategyHitOperate" useGeneratedKeys="true">
    insert into risk_strategy_hit_operate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hitRecordId != null">
        hit_record_id,
      </if>
      <if test="result != null">
        `result`,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="radio != null">
        radio,
      </if>
      <if test="repeatCaseId != null">
        repeat_case_id,
      </if>
      <if test="activeStop != null">
        active_stop,
      </if>
      <if test="otherReason != null">
        other_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hitRecordId != null">
        #{hitRecordId,jdbcType=BIGINT},
      </if>
      <if test="result != null">
        #{result,jdbcType=TINYINT},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="radio != null">
        #{radio,jdbcType=VARCHAR},
      </if>
      <if test="repeatCaseId != null">
        #{repeatCaseId,jdbcType=INTEGER},
      </if>
      <if test="activeStop != null">
        #{activeStop,jdbcType=VARCHAR},
      </if>
      <if test="otherReason != null">
        #{otherReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="listByRecordId" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_strategy_hit_operate
  	where hit_record_id = #{recordId,jdbcType=BIGINT}
  	order by id desc
  </select>
</mapper>