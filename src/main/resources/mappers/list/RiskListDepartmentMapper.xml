<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.list.RiskListDepartmentDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="list_type" jdbcType="TINYINT" property="listType" />
    <result column="hospital_id" jdbcType="INTEGER" property="hospitalId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="landline" jdbcType="VARCHAR" property="landline" />
    <result column="extension" jdbcType="VARCHAR" property="extension" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartment">
    <result column="departments" jdbcType="LONGVARCHAR" property="departments" />
  </resultMap>
  <sql id="Base_Column_List">
    id, list_type, hospital_id, hospital_code, hospital_name, area_code, landline, extension,
    province, city, operator_id, operator_name, is_delete, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    departments
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from risk_list_department
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartment" useGeneratedKeys="true">
    insert into risk_list_department
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="listType != null">
        list_type,
      </if>
      <if test="hospitalId != null">
        hospital_id,
      </if>
      <if test="hospitalCode != null">
        hospital_code,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="landline != null">
        landline,
      </if>
      <if test="extension != null">
        extension,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="departments != null">
        departments,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="listType != null">
        #{listType,jdbcType=TINYINT},
      </if>
      <if test="hospitalId != null">
        #{hospitalId,jdbcType=INTEGER},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="landline != null">
        #{landline,jdbcType=VARCHAR},
      </if>
      <if test="extension != null">
        #{extension,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="departments != null">
        #{departments,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getByUniqueTel" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_list_department
  	where area_code = #{areaCode,jdbcType=VARCHAR}
  	and landline = #{landline,jdbcType=VARCHAR}
  	and extension = #{extension,jdbcType=VARCHAR}
  	and is_delete = 0
  </select>
  <update id="updateById">
  	update risk_list_department
    <set>
	  list_type = #{listType,jdbcType=TINYINT},
	  <if test="hospitalId != null">
	  hospital_id = #{hospitalId,jdbcType=INTEGER},
	  </if>
      <if test="hospitalCode != null and hospitalCode != ''">
	  hospital_code = #{hospitalCode,jdbcType=VARCHAR},
	  </if>
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      area_code = #{areaCode,jdbcType=VARCHAR},
      landline = #{landline,jdbcType=VARCHAR},
      extension = #{extension,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      departments = #{departments,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=BIGINT},
      operator_name = #{operatorName,jdbcType=VARCHAR}
	</set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listByOptional" resultMap="BaseResultMap">
  	select
  	<include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from risk_list_department
    <where>
        <if test="province != null and province != ''">
			and province = #{province,jdbcType=VARCHAR}
		</if>
    	<if test="city != null and city != ''">
    	    and city = #{city,jdbcType=VARCHAR}
		</if>
		<if test="hospitalName != null and hospitalName != ''">
			and hospital_name like concat('%', #{hospitalName,jdbcType=VARCHAR}, '%')
		</if>
		<if test="hospitalCode != null and hospitalCode != ''">
			and hospital_code = #{hospitalCode,jdbcType=VARCHAR}
		</if>
		<if test="operatorName != null and operatorName != ''">
			and operator_name like concat('%',#{operatorName,jdbcType=VARCHAR},'%')
		</if>
		<if test="landline != null and landline != ''">
			and landline like concat(#{landline,jdbcType=VARCHAR}, '%')
		</if>
		<if test="department != null and department != ''">
			and departments like concat('%', #{department,jdbcType=VARCHAR}, '%')
		</if>
		<if test="listType != null">
			and list_type = #{listType,jdbcType=TINYINT}
		</if>
		and is_delete = 0
	</where>
	order by id desc
  </select>
  <update id="delById">
  	update risk_list_department
  	set is_delete = 1
  	where id = #{departmentId,jdbcType=BIGINT}
  </update>
  
  <select id="listByAreaCodeLandline" resultMap="BaseResultMap">
	select <include refid="Base_Column_List"/>
  	from risk_list_department
  	where area_code = #{areaCode,jdbcType=VARCHAR}
  	and landline = #{landline,jdbcType=VARCHAR}
  	and is_delete = 0
  </select>
</mapper>