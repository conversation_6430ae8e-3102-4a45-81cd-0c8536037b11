<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.list.RiskCityAreaDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskCityArea">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="province_id" jdbcType="INTEGER" property="provinceId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="area_code_former" jdbcType="VARCHAR" property="areaCodeFormer" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, province_id, province, city_id, city, area_code, area_code_former, is_delete, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_city_area
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskCityArea" useGeneratedKeys="true">
    insert into risk_city_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="areaCodeFormer != null">
        area_code_former,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="provinceId != null">
        #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="areaCodeFormer != null">
        #{areaCodeFormer,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="getByProvinceCity" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_city_area
  	where province = #{province,jdbcType=VARCHAR}
  	and city = #{city,jdbcType=VARCHAR}
  	limit 1
  </select>
  <select id="listAll" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_city_area
  </select>
  <select id="getByProvinceCityId" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_city_area
  	where province_id = #{provinceId,jdbcType=INTEGER}
  	and city_id = #{cityId,jdbcType=INTEGER}
  	limit 1
  </select>
</mapper>