<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.list.RiskListUpdateRecordDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListUpdateRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="event_no" jdbcType="VARCHAR" property="eventNo" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="landline" jdbcType="VARCHAR" property="landline" />
    <result column="extension" jdbcType="VARCHAR" property="extension" />
    <result column="case_id" jdbcType="INTEGER" property="caseId" />
    <result column="work_order_id" jdbcType="BIGINT" property="workOrderId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="departments" jdbcType="VARCHAR" property="departments" />
    <result column="city_area_code" jdbcType="VARCHAR" property="cityAreaCode" />
    <result column="city_area_code_former" jdbcType="VARCHAR" property="cityAreaCodeFormer" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, event_no, area_code, landline, extension, case_id, work_order_id, hospital_code,
    hospital_name, province, city, departments, city_area_code, city_area_code_former,
    patient_name, `result`, is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_list_update_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListUpdateRecord" useGeneratedKeys="true">
    insert into risk_list_update_record (event_no, area_code, landline,
      extension, case_id, work_order_id,
      hospital_code, hospital_name, province,
      city, departments, city_area_code,
      city_area_code_former, patient_name, `result`,
      is_delete, create_time, update_time
      )
    values (#{eventNo,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR}, #{landline,jdbcType=VARCHAR},
      #{extension,jdbcType=VARCHAR}, #{caseId,jdbcType=INTEGER}, #{workOrderId,jdbcType=BIGINT},
      #{hospitalCode,jdbcType=VARCHAR}, #{hospitalName,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR},
      #{city,jdbcType=VARCHAR}, #{departments,jdbcType=VARCHAR}, #{cityAreaCode,jdbcType=VARCHAR},
      #{cityAreaCodeFormer,jdbcType=VARCHAR}, #{patientName,jdbcType=VARCHAR}, #{result,jdbcType=VARCHAR},
      #{isDelete,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListUpdateRecord" useGeneratedKeys="true">
    insert into risk_list_update_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eventNo != null">
        event_no,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="landline != null">
        landline,
      </if>
      <if test="extension != null">
        extension,
      </if>
      <if test="caseId != null">
        case_id,
      </if>
      <if test="workOrderId != null">
        work_order_id,
      </if>
      <if test="hospitalCode != null">
        hospital_code,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="departments != null">
        departments,
      </if>
      <if test="cityAreaCode != null">
        city_area_code,
      </if>
      <if test="cityAreaCodeFormer != null">
        city_area_code_former,
      </if>
      <if test="patientName != null">
        patient_name,
      </if>
      <if test="result != null">
        `result`,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eventNo != null">
        #{eventNo,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="landline != null">
        #{landline,jdbcType=VARCHAR},
      </if>
      <if test="extension != null">
        #{extension,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        #{caseId,jdbcType=INTEGER},
      </if>
      <if test="workOrderId != null">
        #{workOrderId,jdbcType=BIGINT},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="departments != null">
        #{departments,jdbcType=VARCHAR},
      </if>
      <if test="cityAreaCode != null">
        #{cityAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="cityAreaCodeFormer != null">
        #{cityAreaCodeFormer,jdbcType=VARCHAR},
      </if>
      <if test="patientName != null">
        #{patientName,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>