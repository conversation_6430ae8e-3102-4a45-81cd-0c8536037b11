<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.list.RiskListDepartmentCaseDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartmentCase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="landline" jdbcType="VARCHAR" property="landline" />
    <result column="extension" jdbcType="VARCHAR" property="extension" />
    <result column="case_id" jdbcType="INTEGER" property="caseId" />
    <result column="launch_time" jdbcType="TIMESTAMP" property="launchTime" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="departments" jdbcType="VARCHAR" property="departments" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="patient_id_type" jdbcType="INTEGER" property="patientIdType" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="patient_id_number" jdbcType="VARCHAR" property="patientIdNumber" />
    <result column="payee_phone" jdbcType="VARCHAR" property="payeePhone" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="payee_id_card" jdbcType="VARCHAR" property="payeeIdCard" />
    <result column="relation_type" jdbcType="TINYINT" property="relationType" />
    <result column="hospital_bank_branch" jdbcType="VARCHAR" property="hospitalBankBranch" />
    <result column="hospital_bank_card" jdbcType="VARCHAR" property="hospitalBankCard" />
    <result column="hospital_account_name" jdbcType="VARCHAR" property="hospitalAccountName" />
    <result column="hospital_num" jdbcType="VARCHAR" property="hospitalNum" />
    <result column="bed_num" jdbcType="VARCHAR" property="bedNum" />
    <result column="payee_department" jdbcType="VARCHAR" property="payeeDepartment" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, area_code, landline, extension, case_id, launch_time, hospital_code, hospital_name,
    province, city, departments, mobile, `name`, id_card, patient_id_type, patient_name,
    patient_id_number, payee_phone, payee_name, payee_id_card, relation_type, hospital_bank_branch,
    hospital_bank_card, hospital_account_name, hospital_num, bed_num, payee_department,
    is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_list_department_case
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartmentCase" useGeneratedKeys="true">
    insert into risk_list_department_case (area_code, landline, extension,
      case_id, launch_time, hospital_code,
      hospital_name, province, city,
      departments, mobile, `name`,
      id_card, patient_id_type, patient_name,
      patient_id_number, payee_phone, payee_name,
      payee_id_card, relation_type, hospital_bank_branch,
      hospital_bank_card, hospital_account_name,
      hospital_num, bed_num, payee_department,
      is_delete, create_time, update_time
      )
    values (#{areaCode,jdbcType=VARCHAR}, #{landline,jdbcType=VARCHAR}, #{extension,jdbcType=VARCHAR},
      #{caseId,jdbcType=INTEGER}, #{launchTime,jdbcType=TIMESTAMP}, #{hospitalCode,jdbcType=VARCHAR},
      #{hospitalName,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
      #{departments,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{idCard,jdbcType=VARCHAR}, #{patientIdType,jdbcType=INTEGER}, #{patientName,jdbcType=VARCHAR},
      #{patientIdNumber,jdbcType=VARCHAR}, #{payeePhone,jdbcType=VARCHAR}, #{payeeName,jdbcType=VARCHAR},
      #{payeeIdCard,jdbcType=VARCHAR}, #{relationType,jdbcType=TINYINT}, #{hospitalBankBranch,jdbcType=VARCHAR},
      #{hospitalBankCard,jdbcType=VARCHAR}, #{hospitalAccountName,jdbcType=VARCHAR},
      #{hospitalNum,jdbcType=VARCHAR}, #{bedNum,jdbcType=VARCHAR}, #{payeeDepartment,jdbcType=VARCHAR},
      #{isDelete,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.list.RiskListDepartmentCase" useGeneratedKeys="true">
    insert into risk_list_department_case
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="landline != null">
        landline,
      </if>
      <if test="extension != null">
        extension,
      </if>
      <if test="caseId != null">
        case_id,
      </if>
      <if test="launchTime != null">
        launch_time,
      </if>
      <if test="hospitalCode != null">
        hospital_code,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="departments != null">
        departments,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="idCard != null">
        id_card,
      </if>
      <if test="patientIdType != null">
        patient_id_type,
      </if>
      <if test="patientName != null">
        patient_name,
      </if>
      <if test="patientIdNumber != null">
        patient_id_number,
      </if>
      <if test="payeePhone != null">
        payee_phone,
      </if>
      <if test="payeeName != null">
        payee_name,
      </if>
      <if test="payeeIdCard != null">
        payee_id_card,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="hospitalBankBranch != null">
        hospital_bank_branch,
      </if>
      <if test="hospitalBankCard != null">
        hospital_bank_card,
      </if>
      <if test="hospitalAccountName != null">
        hospital_account_name,
      </if>
      <if test="hospitalNum != null">
        hospital_num,
      </if>
      <if test="bedNum != null">
        bed_num,
      </if>
      <if test="payeeDepartment != null">
        payee_department,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="landline != null">
        #{landline,jdbcType=VARCHAR},
      </if>
      <if test="extension != null">
        #{extension,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        #{caseId,jdbcType=INTEGER},
      </if>
      <if test="launchTime != null">
        #{launchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="departments != null">
        #{departments,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="patientIdType != null">
        #{patientIdType,jdbcType=INTEGER},
      </if>
      <if test="patientName != null">
        #{patientName,jdbcType=VARCHAR},
      </if>
      <if test="patientIdNumber != null">
        #{patientIdNumber,jdbcType=VARCHAR},
      </if>
      <if test="payeePhone != null">
        #{payeePhone,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeIdCard != null">
        #{payeeIdCard,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=TINYINT},
      </if>
      <if test="hospitalBankBranch != null">
        #{hospitalBankBranch,jdbcType=VARCHAR},
      </if>
      <if test="hospitalBankCard != null">
        #{hospitalBankCard,jdbcType=VARCHAR},
      </if>
      <if test="hospitalAccountName != null">
        #{hospitalAccountName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalNum != null">
        #{hospitalNum,jdbcType=VARCHAR},
      </if>
      <if test="bedNum != null">
        #{bedNum,jdbcType=VARCHAR},
      </if>
      <if test="payeeDepartment != null">
        #{payeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="getByUniqueTelAndCaseId" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_list_department_case
  	where case_id = #{caseId, jdbcType=INTEGER}
  	  and area_code = #{areaCode,jdbcType=VARCHAR}
      and landline = #{landline,jdbcType=VARCHAR}
      and extension = #{extension,jdbcType=VARCHAR}
  </select>
  <select id="listByUniqueTelLimit100" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_list_department_case
  	where area_code = #{areaCode,jdbcType=VARCHAR}
      and landline = #{landline,jdbcType=VARCHAR}
      and extension = #{extension,jdbcType=VARCHAR}
    limit 100
  </select>
</mapper>