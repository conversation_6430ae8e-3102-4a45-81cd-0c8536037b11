<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.DiscussionStatDao">

    <sql id="TABLE">
        `discussion_stat`
    </sql>

    <insert id="save">
        insert into
        <include refid="TABLE"/>
        (discussion_id, case_id) values (#{discussionId}, #{caseId})
    </insert>

    <select id="findById" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionStat">
        select * from <include refid="TABLE"/>
        where discussion_id = #{discussionId}
    </select>


</mapper>