<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskPublicSentimentCorrespondDao">

    <sql id="TABLE">
        `risk_source_correspond`
    </sql>

    <select id="getByInfoSource" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskSourceCorrespond">
      select info_feed_back, info_feed_back_content
      from <include refid="TABLE"/>
      where info_source = #{infoSource} and is_delete = 0
    </select>

    <select id="getByInfoSources" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskSourceCorrespond">
       select id, info_feed_back, info_feed_back_content
       from <include refid="TABLE"/>
       where info_source in
        <foreach collection="infoSources" item="infoSource" open="(" close=")" separator=",">
            #{infoSource}
        </foreach>
        and is_delete = 0
        order by id
    </select>

    <select id="getByInfoFeedBack" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskSourceCorrespond">
        select id, info_feed_back, info_feed_back_content
        from <include refid="TABLE"/>
        where info_feed_back = #{infoFeedback}  and is_delete = 0
    </select>


</mapper>