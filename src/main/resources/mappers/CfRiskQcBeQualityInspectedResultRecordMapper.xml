<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.CfRiskQcBeQualityInspectedResultRecordDao">

    <sql id="TABLE">
        risk_qc_be_quality_inspected_result_record
    </sql>

    <insert id="insert">
        insert ignore into <include refid="TABLE" />
        (work_order_id,inspected_result)
        values
        (#{workOrderId},#{inspectedResult})
    </insert>

    <select id="getInspectedResult" resultType="java.lang.Integer">
        select inspected_result
        from
        <include refid="TABLE"/>
        where
        work_order_id = #{workOrderId}
    </select>

</mapper>