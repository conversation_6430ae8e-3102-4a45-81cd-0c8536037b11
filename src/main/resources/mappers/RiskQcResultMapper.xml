<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcResultDao">

    <sql id="TABLE">
        `risk_qc_result`
    </sql>

    <select id="getQcResultByTime" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult">
        select * from
        <include refid="TABLE"/>
        where qc_unique_code = #{qcUniqueCode}
        and create_time between #{startTime} and #{endTime}
    </select>

    
    <insert id="add">
        insert ignore into <include refid="TABLE"/>
        (`first_qc_result_id`, `second_qc_result_id`, `problem_describe`, `work_order_id`, `qc_unique_code`,`result_type`, `qc_id`)
        values (#{firstQcResultId}, #{secondQcResultId}, #{problemDescribe}, #{wordOrderId}, #{qcUniqueCode}, #{resultType}, #{qcId})
    </insert>

    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult">
        select * from
        <include refid="TABLE"/>
        where work_order_id = #{wordOrderId} and is_delete = 0
    </select>

    <update id="updateInfo">
        update <include refid="TABLE"/>
        set first_qc_result_id = #{firstQcResultId}, second_qc_result_id = #{secondQcResultId},
        problem_describe = #{problemDescribe}
        where work_order_id = #{wordOrderId} and is_delete = 0
    </update>

    <select id="findByWorkOrderIds" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResult">
        select * from
        <include refid="TABLE"/>
        where work_order_id in
        <foreach collection="wordOrderIds" item="workOrderId" open="(" close=")" separator=",">
            #{workOrderId}
        </foreach>
        and is_delete = 0
    </select>
</mapper>