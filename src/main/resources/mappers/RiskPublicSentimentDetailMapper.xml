<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskPublicSentimentDetailDao">

    <sql id="TABLE">
        `risk_public_sentiment_detail`
    </sql>

    <sql id="select">
        title,url,content,special_forwarding,video_url,img_url,supplement_info,fermentation_condition
    </sql>



    <insert id="add" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail">
      insert  into <include refid="TABLE"/>
      (title,url,content,special_forwarding,video_url,img_url,supplement_info,fermentation_condition,public_sentiment_id)
      values
      (#{title},#{url},#{content},#{specialForwarding},#{videoUrl},#{imgUrl}
      ,#{supplementInfo},#{fermentationCondition}, #{publicSentimentId})
    </insert>

    <update id="updateByPublicSentimentId" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail">
     update <include refid="TABLE"/>
     set title = #{title}, url = #{url}, content = #{content},special_forwarding = #{specialForwarding},
        video_url = #{videoUrl},img_url = #{imgUrl}, supplement_info = #{supplementInfo}
     where public_sentiment_id = #{publicSentimentId}
    </update>

    <select id="getByPublicSentimentId" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail">
       select <include refid="select"/>
       from <include refid="TABLE"/>
       where public_sentiment_id = #{publicSentimentId} and is_delete = 0
    </select>

    <select id="getByPublicSentimentIds" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentDetail">
        select *
        from <include refid="TABLE"/>
        where public_sentiment_id in
        <foreach collection="publicSentimentIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
         and is_delete = 0
    </select>


</mapper>