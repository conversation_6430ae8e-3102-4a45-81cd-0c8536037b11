<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.DiscussionCheckRecordDao">

    <sql id="TABLE">
        `discussion_check_record`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.DiscussionCheckRecord">
        insert into <include refid="TABLE"/>
        (discussion_id, status, refuse_reason, operator, title, description, images)
        values (#{discussionId}, #{status}, #{refuseReason}, #{operator}, #{title}, #{description}, #{images})
    </insert>
    
    <select id="findListByDiscussionId" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionCheckRecord">
        select * from <include refid="TABLE"/> as page
        where discussion_id in
        <foreach collection="discussionIds" open="(" close=")" item="discussionId" separator=",">
            #{discussionId}
        </foreach>
        and is_delete = 0  and status = #{status}
    </select>

    <select id="findLastByDiscussId" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionCheckRecord">
        select * from <include refid="TABLE"/>
        where discussion_id = #{discussionId} and is_delete = 0
        order by id desc limit 1
    </select>

    <select id="listByDiscussionIdsIn" resultType="com.shuidihuzhu.cf.risk.admin.model.DiscussionCheckRecord">
        select * from <include refid="TABLE"/>
        where discussion_id in
        <foreach collection="list" open="(" close=")" item="discussionId" separator=",">
            #{discussionId}
        </foreach>
    </select>

</mapper>