<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.CfRiskRecordingProblemsRecordDao">

    <sql id="TABLE">
        cf_risk_recording_problems_record
    </sql>

    <sql id="selectFields">
        `id`,
        `work_order_id`,
        `taping_id`,
        `problem_ids`,
        `comment`
    </sql>

    <insert id="insertList">
        insert into
        <include refid="TABLE"/>
        (work_order_id,taping_id,problem_ids,comment)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.workOrderId},#{item.tapingId},#{item.problemIds},#{item.comment})
        </foreach>
    </insert>

    <select id="getListByWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.vo.QcRecordingProblemsVo">
        select <include refid="selectFields"/>
        from
        <include refid="TABLE"/>
        where
        work_order_id = #{workOrderId}
    </select>

    <select id="getListByWorkOrderIds" resultType="com.shuidihuzhu.cf.risk.admin.model.vo.QcRecordingProblemsVo">
        select
        <include refid="selectFields"/>
        from
        <include refid="TABLE"/>
        where work_order_id in
        <foreach collection="workOrderIds" open="(" close=")" item="workOrderId" separator=",">
            #{workOrderId}
        </foreach>
    </select>

</mapper>