<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcOperationRecordDao">

    <sql id="tableName">
        risk_qc_operation_record
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskQcOperationRecord">
        insert into
        <include refid="tableName"/>
        (user_name,user_id,operation,download_number)
        values
        (#{userName},#{userId},#{operation},#{downloadNumber})
    </insert>


</mapper>