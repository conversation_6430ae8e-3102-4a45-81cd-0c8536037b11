<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.AdminRiskOperatingRecordDao">

    <sql id="TABLE">
        `admin_risk_operating_record`
    </sql>

    <insert id="insert">
        insert into <include refid="TABLE" />
            (`info_id`,`work_order_id`,`admin_user_id`,`biz_type`,`risk`,`risk_level`)
        values
            (#{infoId},#{workOrderId},#{adminUserId},#{bizType},#{risk}, #{riskLevel})
    </insert>

</mapper>