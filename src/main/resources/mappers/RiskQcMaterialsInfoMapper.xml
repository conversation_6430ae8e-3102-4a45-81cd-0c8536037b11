<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcMaterialsInfoDao">

    <sql id="tableName">
       risk_qc_materials_info
    </sql>

    <sql id="selectFields">
        id,
        qc_id,
        materials_key,
        materials_value
    </sql>

    <insert id="insertOne">
        insert into
        <include refid="tableName"/>
        (qc_id,materials_key,materials_value)
        values (#{qcId},#{materialsKey},#{materialsValue})
    </insert>

    <insert id="insertBatch">
        insert into
        <include refid="tableName"/>
        (qc_id,materials_key,materials_value)
        values
        <foreach collection="riskQcMaterialsInfos" separator="," item="riskQcMaterialsInfo">
            (#{riskQcMaterialsInfo.qcId},#{riskQcMaterialsInfo.materialsKey},#{riskQcMaterialsInfo.materialsValue})
        </foreach>
    </insert>

    <select id="getByQcIdAndMaterialsKey" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where qc_id = #{qcId}
        and materials_key = #{materialsKey}
        and is_delete = 0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcMaterialsInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where id = #{id}
        and is_delete = 0
    </select>
    <update id="updateSnapshot">
        update
        <include refid="tableName"/>
        set materials_value = #{materialsValue}
        where qc_id = #{qcId}
        and materials_key = #{materialsKey}
    </update>

    <update id="updateById">
        update
        <include refid="tableName"/>
        set materials_value = #{materialsValue}
        where id = #{id}
    </update>
</mapper>