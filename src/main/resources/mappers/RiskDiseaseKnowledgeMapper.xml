<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseKnowledgeDao">
    <sql id="TABLE">
        `risk_disease_knowledge`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseKnowledge"
    useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="TABLE"/>
        (`number`, `search_name`, `disease_name` , `cure_office`, `disease_alias`, `disease_intro`, `cure_plan`,
        `simple_cure_plan`, `cure_cost`, `simple_cure_cost`, `prognosis`,`diagnose_image`,`diagnose_text`, `disease_file_address_str`, `knowledge_version`)
        values
        (#{number}, #{searchName}, #{diseaseName}, #{cureOffice}, #{diseaseAlias}, #{diseaseIntro}, #{curePlan},
        #{simpleCurePlan}, #{cureCost}, #{simpleCureCost}, #{prognosis}, #{diagnoseImage}, #{diagnoseText}, #{diseaseFileAddressStr}, #{knowledgeVersion})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseKnowledge">
        update
        <include refid="TABLE"/>
        set
        `search_name` = #{searchName},
        `disease_name` = #{diseaseName},
        `cure_office` = #{cureOffice},
        `disease_alias` = #{diseaseAlias},
        `disease_intro` = #{diseaseIntro},
        `cure_plan` = #{curePlan},
        `simple_cure_plan` = #{simpleCurePlan},
        `cure_cost` = #{cureCost},
        `simple_cure_cost` = #{simpleCureCost},
        `prognosis` = #{prognosis},
        `diagnose_image` = #{diagnoseImage},
        `diagnose_text` = #{diagnoseText},
        `disease_file_address_str` = #{diseaseFileAddressStr},
        `knowledge_version` = #{knowledgeVersion}
        where `number` = #{number}
    </update>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseKnowledge">
        update
        <include refid="TABLE"/>
        set
        `search_name` = #{searchName},
        `disease_name` = #{diseaseName},
        `cure_office` = #{cureOffice},
        `disease_alias` = #{diseaseAlias},
        `disease_intro` = #{diseaseIntro},
        `cure_plan` = #{curePlan},
        `simple_cure_plan` = #{simpleCurePlan},
        `cure_cost` = #{cureCost},
        `simple_cure_cost` = #{simpleCureCost},
        `prognosis` = #{prognosis},
        `diagnose_image` = #{diagnoseImage},
        `diagnose_text` = #{diagnoseText},
        `disease_file_address_str` = #{diseaseFileAddressStr},
        `knowledge_version` = #{knowledgeVersion}
        where `id` = #{id}
    </update>

    <select id="existByNumber" resultType="java.lang.Long">
        select `id` from
        <include refid="TABLE"/>
        where `number` = #{number}
    </select>

    <select id="findById" resultType="com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseKnowledgeDto">
        select * from
        <include refid="TABLE"/>
        where `is_delete` = 0 and `id` = #{id}
    </select>

    <select id="findByNumber" resultType="com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseKnowledgeDto">
        select * from
        <include refid="TABLE"/>
        where `is_delete` = 0 and `number` = #{number}
    </select>

    <update id="updateDelById">
        update
        <include refid="TABLE"/>
        set `is_delete` = 1
        where `id` = #{id}
    </update>

    <select id="listByParam" resultType="com.shuidihuzhu.cf.risk.model.admin.disease.DiseaseKnowledgeDto">
        select * from <include refid="TABLE"/>
        where is_delete = 0
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(diseaseName)">
            and `disease_name` like concat('%', #{diseaseName},'%')
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(startTime)">
            and `create_time` &gt;= #{startTime}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(endTime)">
            and `create_time` &lt; #{endTime}
        </if>
        order by convert(`number`, decimal) desc
        limit #{offset}, #{limit}
    </select>

    <select id="countByParam" resultType="java.lang.Integer">
        select count(*) from <include refid="TABLE"/>
        where is_delete = 0
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(diseaseName)">
            and `disease_name` like concat('%', #{diseaseName},'%')
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(startTime)">
            and `create_time` &gt;= #{startTime}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(endTime)">
            and `create_time` &lt; #{endTime}
        </if>
    </select>

</mapper>