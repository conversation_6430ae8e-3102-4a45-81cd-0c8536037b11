<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.GetInternalStaffAndEmergencyMobileDAO">

    <sql id="TABLE">
        `select_by_internal_staff_and_emergency_mobile`
    </sql>

    <insert id="insert">
        insert into <include refid="TABLE" />
        (`self_name`,`self_mobile`,`emergency_name`,`emergency_mobile`,`type`)
        values
        <foreach collection="infos" item="info" separator=",">
            (#{info.selfName},#{info.selfMobile},#{info.emergencyName},#{info.emergencyMobile},#{info.type})
        </foreach>
    </insert>

    <select id="selectByStaffMobile" resultType="com.shuidihuzhu.cf.risk.admin.model.GetInternalStaffAndEmergencyMobileDO" >
        select * from <include refid="TABLE"/>
        where
        self_mobile = #{staffMobile}
        and is_delete = 0
    </select>

    <select id="selectByEmergencyMobile" resultType="com.shuidihuzhu.cf.risk.admin.model.GetInternalStaffAndEmergencyMobileDO" >
        select * from <include refid="TABLE"/>
        where
        emergency_mobile = #{emergencyMobile}
        and is_delete = 0
    </select>

    <update id="update">
        update <include refid="TABLE"/>
        set is_delete = #{isDelete}
    </update>

    <select id="getByStaffMobile" resultType="com.shuidihuzhu.cf.risk.admin.model.GetInternalStaffAndEmergencyMobileDO">
        select * from <include refid="TABLE"/>
        where
        self_mobile = #{staffMobile}
        and is_delete = 0
        limit 1
    </select>

    <select id="getByEmergencyMobile" resultType="com.shuidihuzhu.cf.risk.admin.model.GetInternalStaffAndEmergencyMobileDO">
        select * from <include refid="TABLE"/>
        where
        emergency_mobile = #{emergencyMobile}
        and is_delete = 0
        limit 1
    </select>

</mapper>