<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcLogDao">

    <sql id="TABLE">
        `risk_qc_log`
    </sql>

    <insert id="addLog" parameterType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcLog">
        insert into
        <include refid="TABLE"/>
        (`operation_id`, `operation_name`, `operation_log`, `operation_type`, `work_order_id`)
        values (#{operationId}, #{operationName}, #{operationLog}, #{operationType}, #{workOrderId})
    </insert>

    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcLog">
        select * from
        <include refid="TABLE"/>
        where operation_type in
        <foreach collection="types"  open="(" close=")" item="type" separator=",">
        #{type}
        </foreach>
        and work_order_id = #{workOrderId}
        order by id
    </select>

</mapper>