<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskDiseaseOperationRecordDao">


    <sql id="tableName">
        risk_disease_operation_record
    </sql>

    <sql id="selectFields">
        `id`,
        `user_id`,
        `disease_id`,
        `operator_name`,
        `type`,
        `create_time`,
        `delete_reason`,
        `operate_before`,
        `operate_after`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseOperationRecord">
        insert into <include refid="tableName"/>
        (`user_id`, `disease_id`, `operator_name`, `type`, `delete_reason`, `operate_before`, `operate_after`)
        values (#{userId}, #{diseaseId}, #{operatorName}, #{type}, #{deleteReason}, #{operateBefore}, #{operateAfter})
    </insert>

    <select id="findListByDiseaseId"
            resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseOperationRecord">
        select <include refid="selectFields"/>
        from  <include refid="tableName"/>
        where `disease_id` = #{diseaseId}
        and `is_delete` = 0
        order by id desc
    </select>
    <select id="getLastOneByDiseaseId"
            resultType="com.shuidihuzhu.cf.risk.admin.model.disease.RiskDiseaseOperationRecord">
        select <include refid="selectFields"/>
        from  <include refid="tableName"/>
        where `disease_id` = #{diseaseId}
        and `is_delete` = 0
        order by id desc limit 1
    </select>

</mapper>