<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskPsHandleRecordDao">

    <sql id="TABLE">
        `risk_ps_handle_record`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord">
        insert into <include refid="TABLE"/>
        (ps_id, case_id, info_classify, info_classify_other, public_sentiment_info_type,
        solution, solution_other, department, reply_type, reply_time, reply_content, images,
        satisfaction, satisfaction_ext, other_ext, operator,status)
        values
        (#{psId},#{caseId}, #{infoClassify}, #{infoClassifyOther}, #{publicSentimentInfoType},
        #{solution}, #{solutionOther}, #{department}, #{replyType}, #{replyTime}, #{replyContent}, #{images},
        #{satisfaction}, #{satisfactionExt}, #{otherExt}, #{operator},#{status})
    </insert>

    <select id="listByPsId" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord">
        select * from <include refid="TABLE"/>
        where ps_id = #{psId} order by id desc
    </select>

    <select id="listByPsIdOfPage" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord">
        select * from <include refid="TABLE"/> as `page`
        where ps_id = #{psId}
    </select>
    
    
    <select id="getLastByPsId"  resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord">
        select * from <include refid="TABLE"/>
        where ps_id = #{psId} order by id desc
        limit 1
    </select>

    <select id="getByPsIdAndStatus"  resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsHandleRecord">
        select * from <include refid="TABLE"/>
        where ps_id = #{psId}  order by id desc
        limit 1
    </select>

    <select id="countRecordSumByPsId"  resultType="java.lang.Integer">
        select count(*) from <include refid="TABLE"/>
        where ps_id = #{psId} and is_delete = 0
    </select>


</mapper>