<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcCheckedVideoInfoDao">

    <sql id="TABLE">
        `risk_qc_checked_video_info`
    </sql>


    <insert id="addInfo">
        insert into
        <include refid="TABLE"/>
        (work_order_id, checked_id)
        values
        (#{workOrderId}, #{checkedId})
    </insert>


    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcCheckedVideoInfo">
        select *
        from <include refid="TABLE"/>
        where work_order_id = #{workOrderId}
        and is_delete = 0
    </select>

    <update id="updateByWorkOrderId">
        update <include refid="TABLE"/>
        set checked_id = #{checkId}
        where work_order_id = #{workOrderId}
    </update>

    <select id="findByWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcCheckedVideoInfo">
        select work_order_id,checked_id
        from <include refid="TABLE"/>
        where work_order_id in
        <foreach collection="workOrderIdList" item="workOrderId" open="(" separator="," close=")">
            #{workOrderId}
        </foreach>
        and is_delete = 0
    </select>

    <insert id="add">
        insert into
        <include refid="TABLE"/>
        (work_order_id, checked_id, ext_info)
        values
        (#{workOrderId}, #{checkedId}, #{extInfo})
    </insert>

    <update id="update">
        update <include refid="TABLE"/>
        set checked_id = #{checkedId}, ext_info = #{extInfo}
        where work_order_id = #{workOrderId}
    </update>
</mapper>