<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcStandardDao">

    <sql id="TABLE">
        `risk_qc_standard`
    </sql>


    <select id="getAllByType" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard">
        select * from
        <include refid="TABLE"/>
        where is_use = #{isUse}
        and standard_type = #{standardType}
        <if test="secondStandardTypes != null and secondStandardTypes.size() > 0">
            and second_standard_type in
            <foreach collection="secondStandardTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        and is_delete = 0
    </select>

    <insert id="addStandard" parameterType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="TABLE"/>
        (`standard_name`, `standard_type`,`second_standard_type`, `parent_id`, `level`, `sort`, `is_use`)
        values
        (#{standardName}, #{standardType},#{secondStandardType}, #{parentId}, #{level}, #{sort}, #{isUse})
    </insert>

    <update id="updateSort">
        update
        <include refid="TABLE"/>
        set
        sort = #{sort}
        where id = #{id}
    </update>

    <select id="getByIds" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard">
        select * from
        <include refid="TABLE"/>
        where id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        and is_delete = 0
    </select>

    <update id="updateUseById">
        update <include refid="TABLE"/>
        set is_use = #{isUse}
        where id = #{id} and is_delete = 0
    </update>

    <select id="getByParentId" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard">
        select *
        from <include refid="TABLE"/>
        where parent_id = #{parentId}
        and is_use= #{isUse}
        and is_delete = 0
    </select>

    <select id="getLastByLevel" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard">
        select * from
        <include refid="TABLE"/>
        where level  = #{level}
        <if test="parentId != null">
            and parent_id = #{parentId}
        </if>
        and is_delete = 0
        order by sort desc
        limit 1
    </select>

    <update id="deleteInfo">
        update <include refid="TABLE"/>
        set is_delete = 1
        where id = #{id}
    </update>

    <select id="getById" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard">
      select * from
      <include refid="TABLE"/>
      where id = #{id} and is_delete = 0
    </select>


    <update id="updateSecondaryUseStatus">
        update <include refid="TABLE"/>
        set secondary_use_status = #{useStatus}
        where id = #{id}
    </update>

    <select id="getByName" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard">
        select * from
        <include refid="TABLE"/>
        where standard_name = #{standardName}
        and standard_type = #{standardType}
        and second_standard_type = #{secondStandardType}
        and `level`  = #{level} and is_delete = 0
        limit 1
    </select>

    <select id="fuzzyQuery" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandard">
        select * from
        <include refid="TABLE"/>
        where level = #{level}
        and standard_name like concat('%','${standardName}','%')
        and is_delete = 0
    </select>

</mapper>