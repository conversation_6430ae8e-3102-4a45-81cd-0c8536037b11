<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskPsInfoTypeDao">

    <sql id="TABLE">
        `risk_ps_info_type`
    </sql>

    <select id="getAll" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsInfoType">
        select `id`, `parent_id`, `description`, `grade`,`path`
        from <include refid="TABLE"/> where is_delete = 0
    </select>

    <update id="updateById">
        update <include refid="TABLE"/>
        set path = #{path}
        where id = #{id}
    </update>

    <select id="findByIdsIn" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsInfoType">
        select * from <include refid="TABLE"/>
        where id in
        <foreach collection="list" separator="," item="id" open="(" close=")">
            #{id}
        </foreach>
        and is_delete = 0
    </select>



    <select id="getById" resultType="string">
        select path from <include refid="TABLE"/>
        where id = #{id} and is_delete = 0
    </select>


</mapper>