<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.whiteList.RiskWhiteListLogDao">

    <sql id="TABLE">
        `risk_white_list_log`
    </sql>

    <sql id="selectFields">
        id, white_list_id, operate_type, operate_id, operate_name, other_info ,create_time
    </sql>

    <insert id="saveLog" parameterType="com.shuidihuzhu.cf.risk.admin.model.dto.whitelist.RiskWhiteListLog">
        insert into <include refid="TABLE"/>
        (white_list_id, operate_type, operate_id, operate_name, other_info)
        values (#{whiteListId}, #{operateType}, #{operateId}, #{operateName}, #{otherInfo})
    </insert>

    <select id="findById" resultType="com.shuidihuzhu.cf.risk.admin.model.dto.whitelist.RiskWhiteListLog">
        select <include refid="selectFields"/>
        from <include refid="TABLE"/>
        where white_list_id = #{whiteListId}
        AND is_delete = 0
    </select>


</mapper>