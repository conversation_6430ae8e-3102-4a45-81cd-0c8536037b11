<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.whiteList.RiskWhiteListDao">

    <sql id="TABLE">
        `risk_white_list`
    </sql>

    <sql id="selectFilds">
        `id`,`name`, `id_card`, `phone_number`, `operator`, `add_reason`, `expire_time`,`status`,`type`,`last_operate_time`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into <include refid="TABLE"/>
        (`name`, `id_card`, `phone_number`, `operator`, `add_reason`, `expire_time`)
        values (#{name}, #{idCard}, #{phoneNumber}, #{operator}, #{addReason}, #{expireTime})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto">
        update <include refid="TABLE"/>
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="idCard != null">
                id_card = #{idCard},
            </if>
            <if test="phoneNumber != null">
                phone_number = #{phoneNumber},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
            <if test="addReason != null">
                add_reason = #{addReason},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateExpireTime">
        update <include refid="TABLE"/>
        <set>
            <if test="expireTime != null">
                expire_time = #{expireTime},
            </if>
            last_operate_time = now(),
            operator = #{operator}
        </set>
        where id = #{id}
    </update>

    <select id="getById" resultType="com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto">
        select <include refid="selectFilds"/>
        from <include refid="TABLE"/>
        where id = #{id}
        AND is_delete = 0
    </select>

    <select id="listByConditions" resultType="com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto">
        select <include refid="selectFilds"/>
        from <include refid="TABLE"/>
        <where>
            is_delete = 0
            <if test="status != null and  status == true ">
                and <![CDATA[ expire_time >= now()]]>
            </if>
            <if test="status != null and  status == false ">
                and <![CDATA[ expire_time < now()]]>
            </if>
            <if test="name != null and name != ''">
                and name = #{name,jdbcType=INTEGER}
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator,jdbcType=INTEGER}
            </if>
            <if test="idCard != null and idCard != ''">
                and id_card = #{idCard,jdbcType=INTEGER}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                and phone_number = #{phoneNumber,jdbcType=INTEGER}
            </if>
        </where>
        order by id desc
    </select>
    <select id="getByCipherMobile" resultType="com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto">
    	select <include refid="selectFilds"/>
    	from risk_white_list
    	where phone_number = #{cipherMobile,jdbcType=VARCHAR}
    	and expire_time >= #{currTime,jdbcType=TIMESTAMP}
    	and `type` = #{type,jdbcType=TINYINT}
    	and is_delete = 0
    	limit 1
    </select>
    <select id="getByCipherIdCard" resultType="com.shuidihuzhu.cf.risk.model.risk.whiteList.RiskWhiteListDto">
    	select <include refid="selectFilds"/>
    	from risk_white_list
    	where id_card = #{cipherIdCard,jdbcType=VARCHAR}
    	and expire_time >= #{currTime,jdbcType=TIMESTAMP}
    	and `type` = #{type,jdbcType=TINYINT}
    	and is_delete = 0
    	limit 1
    </select>

</mapper>