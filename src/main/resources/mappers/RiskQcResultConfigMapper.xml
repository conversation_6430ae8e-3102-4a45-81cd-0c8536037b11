<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcResultConfigDao">

    <sql id="TABLE">
        `risk_qc_result_config`
    </sql>

    <select id="getAll" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcResultConfig">
        select * from
        <include refid="TABLE"/>
        where is_delete = 0
    </select>



</mapper>