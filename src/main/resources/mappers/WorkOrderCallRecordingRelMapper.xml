<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.WorkOrderCallRecordingRelDao">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, work_order_id, recording_unique_id, operation_id, is_delete,create_time, update_time
    </sql>
    <sql id="Table_Name">work_order_call_recording_rel</sql>

    <select id="selectByRecordingUniqueId" resultType="com.shuidihuzhu.cf.risk.admin.model.WorkOrderCallRecordingRelModel">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name"/>
        where recording_unique_id = #{recordingUniqueId} and is_delete=0
        limit 1
    </select>



    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="Table_Name"/>
        (`work_order_id`, `recording_unique_id`, `operation_id`)
        values (#{workOrderId}, #{recordingUniqueId}, #{operationId})
    </insert>



    <select id="selectByWorkOrderIds" resultType="com.shuidihuzhu.cf.risk.admin.model.WorkOrderCallRecordingRelModel">
        select * from
        <include refid="Table_Name"/>
        where work_order_id in
        <foreach collection="workOrderIds" item="workOrderId" separator="," open="(" close=")">
            #{workOrderId}
        </foreach>
        and is_delete = 0
    </select>

    <select id="selectByWorkOrderId" resultType="java.lang.String">
        select recording_unique_id from
        <include refid="Table_Name"/>
        where work_order_id = #{workOrderId}
        and is_delete = 0
    </select>
</mapper>
