<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskPsOperationLogDao">

    <sql id="TABLE">
        `risk_ps_operation_log`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPsOperationLog">
        insert into
        <include refid="TABLE"/>
        (`ps_id`, `operator`, `action`) values (#{psId}, #{operator}, #{action})
    </insert>

    <select id="listByPsIdOfPage" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPsOperationLog">
        select * from
        <include refid="TABLE"/> as `page`
        where ps_id = #{psId} and is_delete = 0

    </select>


</mapper>