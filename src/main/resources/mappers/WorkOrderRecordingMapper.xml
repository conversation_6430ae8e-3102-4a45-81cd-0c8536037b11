<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.WorkOrderRecordingMapper">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingDO">
    <!--@mbg.generated-->
    <!--@Table <include refid="Table_Name"/>-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="work_order_id" jdbcType="BIGINT" property="workOrderId" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" typeHandler="com.shuidihuzhu.cf.risk.admin.dao.support.typehanders.ColumnCipherTypeHandler"/>
    <result column="video_url" jdbcType="VARCHAR" property="videoUrl" />
    <result column="phone_status" jdbcType="INTEGER" property="phoneStatus" />
    <result column="recording_ext" jdbcType="LONGVARCHAR" property="recordingExt" typeHandler="com.shuidihuzhu.cf.risk.admin.dao.support.typehanders.WorkOrderRecordingModelTypeHandler"/>
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, work_order_id, mobile, video_url, phone_status, recording_ext, is_delete, create_time,
    update_time
  </sql>
  <sql id="Table_Name">work_order_recording</sql>
  <select id="listByWorkOrderId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from <include refid="Table_Name"/>
    where work_order_id = #{workOrderId,jdbcType=BIGINT} and is_delete=0
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.WorkOrderRecordingDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into <include refid="Table_Name"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="workOrderId != null">
        work_order_id,
      </if>
      <if test="mobile != null and mobile != ''">
        mobile,
      </if>
      <if test="videoUrl != null and videoUrl != ''">
        video_url,
      </if>
      <if test="phoneStatus != null">
        phone_status,
      </if>
      <if test="recordingExt != null ">
        recording_ext,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="workOrderId != null">
        #{workOrderId,jdbcType=BIGINT},
      </if>
      <if test="mobile != null and mobile != ''">
        #{mobile,jdbcType=VARCHAR, typeHandler=com.shuidihuzhu.cf.risk.admin.dao.support.typehanders.ColumnCipherTypeHandler},
      </if>
      <if test="videoUrl != null and videoUrl != ''">
        #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="phoneStatus != null">
        #{phoneStatus,jdbcType=INTEGER},
      </if>
      <if test="recordingExt != null ">
        #{recordingExt,jdbcType=LONGVARCHAR,typeHandler=com.shuidihuzhu.cf.risk.admin.dao.support.typehanders.WorkOrderRecordingModelTypeHandler},
      </if>
    </trim>
  </insert>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into <include refid="Table_Name"/> (work_order_id, mobile, video_url, phone_status, recording_ext)
    values
    <foreach collection="records" item="record" separator=",">
    (#{record.workOrderId}, #{record.mobile,typeHandler=com.shuidihuzhu.cf.risk.admin.dao.support.typehanders.ColumnCipherTypeHandler}, #{record.videoUrl},
    #{record.phoneStatus}, #{record.recordingExt, typeHandler=com.shuidihuzhu.cf.risk.admin.dao.support.typehanders.WorkOrderRecordingModelTypeHandler})
    </foreach>
  </insert>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from <include refid="Table_Name"/>
    where id = #{id,jdbcType=BIGINT} and is_delete=0
  </select>
  <update id="updateWorkOrderRecordingExt">
    update <include refid="Table_Name"/> set recording_ext=#{recordingExt, typeHandler=com.shuidihuzhu.cf.risk.admin.dao.support.typehanders.WorkOrderRecordingModelTypeHandler}
    where id = #{id}
  </update>
</mapper>
