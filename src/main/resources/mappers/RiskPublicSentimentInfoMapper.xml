<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskPublicSentimentInfoDao">

    <sql id="TABLE">
        `risk_public_sentiment_info`
    </sql>

    <sql id="select">
      `id`,`publish_time`, `info_source`, `info_source_other`,`info_feed_back`,
      `info_feed_back_other`,`nick_name_type`,`nick_name`,
      `info_classify`, `info_classify_other`, `case_id`,
      `public_sentiment_info_type`,`solution`,`solution_other`,`report_id`,`status`,
      `create_time`,last_operator
    </sql>


    <insert id="add" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo" useGeneratedKeys="true" keyProperty="id">
      insert into <include refid="TABLE"/>
      (`publish_time`, `info_source`, `info_source_other`,`info_feed_back`,`info_feed_back_other`,`nick_name_type`,`nick_name`,
      `info_classify`, `info_classify_other`, `case_id`, `last_operator`)
      values (#{publishTime},#{infoSource},#{infoSourceOther},#{infoFeedBack},#{infoFeedBackOther},#{nickNameType},#{nickName}
      ,#{infoClassify},#{infoClassifyOther},#{caseId},#{lastOperator} )
    </insert>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo">
        update
        <include refid="TABLE"/>
        set publish_time = #{publishTime},info_source = #{infoSource},info_source_other = #{infoSourceOther},
        info_feed_back = #{infoFeedBack}, info_feed_back_other = #{infoFeedBackOther}, nick_name_type = #{nickNameType},
        nick_name = #{nickName}, info_classify = #{infoClassify}, info_classify_other = #{infoClassifyOther}, case_id =
        #{caseId}, last_operator = #{lastOperator}
        where id = #{id}
    </update>

    <update id="updateHandleById" parameterType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo">
        update <include refid="TABLE"/>
        set  status = #{status}, public_sentiment_info_type = #{publicSentimentInfoType},
        solution = #{solution}, last_operator = #{lastOperator}, info_classify = #{infoClassify},info_classify_other = #{infoClassifyOther},
        case_id = #{caseId}, solution_other = #{solutionOther}
        where id = #{id}
    </update>

    <select id="getInfoById" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo">
        select
        <include refid="select"/>
        from
        <include refid="TABLE"/>
        where id = #{id} and is_delete = 0
    </select>

    <select id="getInfoByCaseId" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo">
        select <include refid="select"/>
        from <include refid="TABLE"/>
        where case_id = #{caseId} and is_delete = 0
    </select>

    <select id="getInfoList" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskPublicSentimentInfo">
        select * from
        <include refid="TABLE"/>
        page
        where
        <if test="infoSource != -1">
            page.info_source = #{infoSource} and
        </if>
        <if test="infoFeedBack != -1">
             page.info_feed_back = #{infoFeedBack} and
        </if>
        <if test="infoClassify != -1">
             page.info_classify = #{infoClassify} and
        </if>
        <if test="startTime !=null and endTime != null">
             page.create_time between #{startTime} and #{endTime} and
        </if>
        <if test="publicSentimentInfoType != null">
             page.public_sentiment_info_type like concat(#{publicSentimentInfoType},'%') and
        </if>
        <if test="solution != null">
             page.solution like concat('%',#{solution},'%') and
        </if>
        <if test="caseId != -1">
             page.case_id = #{caseId} and
        </if>
        <if test="lastOperator != null">
             page.last_operator like concat('%',#{lastOperator},'%') and
        </if>
        page.status = #{disposeStatus} and
        page.is_delete = 0
        order by page.id desc
    </select>

    <select id="countPsByCaseId" resultType="java.lang.Integer">
        select count(*) from
        <include refid="TABLE"/>
        where case_id = #{caseId} and is_delete = 0
    </select>

    <update id="updateReportIdById">
        update <include refid="TABLE"/>
        set report_id = #{reportId}
        where id = #{id}
    </update>

    <update id="updateStatusById">
        update <include refid="TABLE"/>
        set status = #{status}
        where id = #{id}
    </update>

    <select id="getByInfoFeedBack" resultType="string">
        select info_feed_back_other from
        <include refid="TABLE"/>
        where info_feed_back = 0 and  info_source = 0
    </select>

</mapper>