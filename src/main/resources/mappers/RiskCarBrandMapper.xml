<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskCarBrandDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.risk.admin.model.po.RiskCarBrand">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="brand_type" jdbcType="TINYINT" property="brandType" />
    <result column="initial" jdbcType="VARCHAR" property="initial" />
    <result column="sign_picture" jdbcType="VARCHAR" property="signPicture" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, brand_name, brand_type, `initial`, sign_picture, is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from risk_car_brand
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.risk.admin.model.po.RiskCarBrand" useGeneratedKeys="true">
    insert into risk_car_brand
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="brandType != null">
        brand_type,
      </if>
      <if test="initial != null">
        `initial`,
      </if>
      <if test="signPicture != null">
        sign_picture,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandType != null">
        #{brandType,jdbcType=TINYINT},
      </if>
      <if test="initial != null">
        #{initial,jdbcType=VARCHAR},
      </if>
      <if test="signPicture != null">
        #{signPicture,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="listByLimit50" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List"/>
  	from risk_car_brand
  	<where>
	    <if test="_parameter != null and _parameter != ''">
			brand_name like concat('%', #{carName,jdbcType=VARCHAR}, '%')
		</if>
	</where>
	limit 50
  </select>
</mapper>