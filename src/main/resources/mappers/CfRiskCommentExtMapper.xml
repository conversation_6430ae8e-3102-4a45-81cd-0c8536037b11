<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.CfRiskCommentExtDao">

    <sql id="TABLE">
        `cf_risk_comment_ext`
    </sql>

    <update id="updateDelByCommentId">
        update
        <include refid="TABLE"/>
        set `is_delete` = 1
        where `comment_id` = #{commentId}
    </update>

    <select id="listByCommentIdsIn" resultType="com.shuidihuzhu.cf.risk.admin.model.CfRiskCommentExt">
        select * from
        <include refid="TABLE"/>
        where comment_id in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>