<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcAppealResultDao">

    <sql id="tableName">
       risk_qc_appeal_result
    </sql>

    <insert id="addInfo">
        insert ignore into
        <include refid="tableName"/>
        (`work_order_id`, `appeal_info`, `appeal_result`, `qc_id`)
        values
        (#{workOrderId}, #{appealInfo}, #{appealResult}, #{qcId})
    </insert>

    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealResultModel">
        select * from
        <include refid="tableName"/>
        where work_order_id = #{workOrderId}
    </select>

    <select id="findByWorkOrderIds" resultType="com.shuidihuzhu.cf.risk.admin.model.qcAppeal.RiskQcAppealResultModel">
        select `id`,`work_order_id`, `appeal_info`, `appeal_result`,`create_time`
        from <include refid="tableName"/>
        where work_order_id in
        <foreach collection="workOrderIdList" item="workOrderId" open="(" close=")" separator=",">
            #{workOrderId}
        </foreach>
    </select>
</mapper>