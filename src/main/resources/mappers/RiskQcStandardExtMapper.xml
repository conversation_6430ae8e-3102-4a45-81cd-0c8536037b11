<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcStandardExtDao">

    <sql id="TABLE">
        `risk_qc_standard_ext`
    </sql>


    <select id="getByStandardIds" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardExt">
     select * from
     <include refid="TABLE"/>
     where  qc_standard_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
     and  is_delete = 0
    </select>

    <insert id="add">
        insert into <include refid="TABLE"/>
       (qc_standard_id, first_property_id, second_property_id, use_scene)
       values
       (#{qcStandardId}, #{firstProperty}, #{secondProperty}, #{useScene});
    </insert>

    <select id="findByFirstPropertyAndScene" resultType="com.shuidihuzhu.cf.risk.admin.model.qc.RiskQcStandardExt">
        select * from
        <include refid="TABLE"/>
        where  first_property_id in
        <foreach collection="firstPropertyIds" item="firstPropertyId" open="(" close=")" separator=",">
            #{firstPropertyId}
        </foreach>
        and use_scene = #{useScene}
        and  is_delete = 0
    </select>
</mapper>