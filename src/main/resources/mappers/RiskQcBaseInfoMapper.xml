<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskQcBaseInfoDao">

    <sql id="tableName">
       risk_qc_base_info
    </sql>

    <sql id="selectFields">
        id,
        case_id,
        qc_type,
        order_type,
        qc_by_name,
        qc_unique_code,
        task_id
    </sql>

    <insert id="insertOne" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="tableName"/>
        (case_id,qc_type,order_type,qc_by_name,qc_unique_code,task_id)
        values (#{caseId},#{qcType},#{orderType},#{qcByName},#{qcUniqueCode},#{taskId})
    </insert>

    <select id="getByCaseIdAndOrderTypeAndQcUniqueCode"
            resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and order_type = #{orderType}
        and qc_unique_code = #{qcUniqueCode}
        and is_delete = 0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where id = #{id}
        and is_delete = 0
    </select>

    <select id="getByCaseIdAndOrderType" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and order_type = #{orderType}
        and is_delete = 0
    </select>


    <select id="getByTaskIdAndOrderType" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where task_id = #{taskId}
        and order_type = #{orderType}
        and is_delete = 0
    </select>


    <select id="getByIds" resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        and is_delete = 0
    </select>

    <select id="getByTaskIdAndOrderTypeAndQcUniqueCode"
            resultType="com.shuidihuzhu.cf.risk.admin.model.RiskQcBaseInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where task_id = #{taskId}
        and order_type = #{orderType}
        and qc_unique_code = #{qcUniqueCode}
        and is_delete = 0
    </select>

    <update id="updateCaseId">
        update
        <include refid="tableName"/>
        set case_id = #{caseId}
        where task_id = #{taskId}
        and order_type in
        <foreach collection="orderTypes" item="orderType" open="(" close=")" separator=",">
            #{orderType}
        </foreach>
    </update>

</mapper>