<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.risk.admin.dao.RiskOperationRecordDao">

    <sql id="TABLE">
        `risk_operation_record`
    </sql>

    <select id="listByCaseIdAndType" resultType="java.lang.Long">
        select `user_id` from <include refid="TABLE"/>
        where `case_id` = #{caseId} and `type` = #{type} and is_delete = 0
    </select>


    <select id="listByBizIdAndType" resultType="java.lang.Long">
        select `user_id` from <include refid="TABLE"/>
        where `biz_id` in
        <foreach collection="commentIds" open="(" close=")" separator="," item="bizId">
            #{bizId}
        </foreach>
        and `type` = #{type} and is_delete = 0
    </select>


</mapper>