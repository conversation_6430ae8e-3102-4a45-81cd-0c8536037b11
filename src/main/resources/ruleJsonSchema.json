{"$schema": "http://json-schema.org/draft-07/schema", "type": "array", "title": "rule定义json格式检测schema", "description": "", "additionalItems": false, "minItems": 1, "definitions": {"variableParams": {"type": "object", "title": "参数定义", "properties": {"fieldName": {"type": "string", "title": "字段名", "description": "", "maxLength": 64, "minLength": 1}, "fieldLabel": {"type": "string", "title": "字段说明", "description": "", "maxLength": 32, "minLength": 1}, "fieldType": {"type": "string", "title": "字段类型", "description": "", "maxLength": 20, "minLength": 1}, "sourcePath": {"type": "string", "title": "字段路径", "description": "", "maxLength": 100, "minLength": 1}}}, "arithmetic": {"type": "object", "title": "算数运算", "description": "对取值的算数运算", "required": ["op", "value"], "additionalProperties": false, "properties": {"op": {"type": "string", "title": "算术运算符", "description": "", "enum": ["ADD", "SUBTRACT", "MULTIPLY", "DIVIDE"]}, "value": {"$ref": "#/definitions/value"}, "arithmetic": {"$ref": "#/definitions/arithmetic"}}}, "callMethod": {"type": "object", "title": "调用函数", "description": "", "required": ["beanId", "bean<PERSON>abel", "methodName", "methodLabel"], "additionalProperties": false, "properties": {"beanId": {"type": "string", "title": "Bean id", "description": "", "minLength": 1, "maxLength": 64}, "beanLabel": {"type": "string", "title": "Bean描述", "description": "", "minLength": 1, "maxLength": 32}, "methodName": {"type": "string", "title": "方法名", "description": "", "minLength": 1, "maxLength": 64}, "methodLabel": {"type": "string", "title": "方法备注", "description": "", "minLength": 1, "maxLength": 32}, "parameters": {"type": "array", "title": "调用函数参数列表", "description": "", "additionalItems": false, "items": {"type": "object", "title": "参数", "description": "", "required": ["name", "type", "value"], "additionalProperties": false, "properties": {"name": {"type": "string", "title": "参数名", "description": "", "minLength": 1, "maxLength": 64}, "type": {"type": "string", "title": "参数类型", "description": "", "minLength": 1, "maxLength": 20}, "value": {"$ref": "#/definitions/value"}}}}}}, "value": {"type": "object", "title": "关系表达式左边的值", "description": "", "anyOf": [{"required": ["valueType", "content"], "additionalProperties": true, "maxProperties": 3, "properties": {"valueType": {"type": "string", "title": "值类型", "description": "需要复杂计算常量", "enum": ["CONSTANT"]}, "content": {"type": ["string", "array"], "title": "常量内容：可以是string或者数组,数组的数据类型必须一致，且是string、boolean、number中一种", "description": "", "minLength": 1, "maxLength": 100, "minItems": 1, "anyOf": [{"items": {"type": ["string"], "title": "集合元素", "description": "集合元素，使用string传达"}}, {"items": {"type": ["number"], "title": "集合元素", "description": "集合元素，使用number传达"}}, {"items": {"type": ["boolean"], "title": "集合元素", "description": "集合元素，使用boolean传达"}}]}, "arithmetic": {"$ref": "#/definitions/arithmetic"}}}, {"required": ["valueType", "variable"], "additionalProperties": false, "properties": {"valueType": {"type": "string", "title": "值类型", "description": "需要输入的变量", "enum": ["VARIABLE"]}, "variable": {"type": "object", "title": "变量", "description": "", "minProperties": 4, "maxProperties": 4, "required": ["fieldName", "<PERSON><PERSON><PERSON><PERSON>", "fieldType", "sourcePath"], "allOf": [{"$ref": "#/definitions/variableParams"}, {"properties": {}}]}, "arithmetic": {"$ref": "#/definitions/arithmetic"}}}, {"required": ["valueType", "callMethod"], "additionalProperties": false, "properties": {"valueType": {"type": "string", "title": "值类型", "description": "需要复杂计算的函数调用", "enum": ["CALL_METHOD"]}, "callMethod": {"$ref": "#/definitions/callMethod"}, "arithmetic": {"$ref": "#/definitions/arithmetic"}}}]}, "criterionGroup": {"type": "object", "title": "逻辑表达式组", "description": "", "required": ["junctionType", "criterions"], "additionalProperties": false, "properties": {"junctionType": {"type": "string", "title": "逻辑标识符", "description": "", "enum": ["AND", "OR"]}, "criterions": {"type": "array", "title": "关系表达式列表", "description": "可以是单个关系表达式，也可以是逻辑表达式组", "minItems": 1, "items": {"anyOf": [{"type": "object", "title": "表达式", "description": "", "required": ["criterionType", "relational"], "additionalProperties": false, "properties": {"criterionType": {"type": "string", "title": "表达式类型", "description": "普通关系表达式", "enum": ["RELATIONAL"]}, "relational": {"type": "object", "title": "普通关系表达式", "description": "", "required": ["op", "leftValue", "rightValue"], "additionalProperties": false, "properties": {"op": {"type": "string", "title": "关系表达式比较符号", "description": "", "enum": ["EQUALS", "NOT_EQUALS", "LESS_THEN", "LESS_THEN_EQUALS", "GREATER_THEN", "GREATER_THEN_EQUALS", "IN", "NOT_IN", "CONTAIN", "NOT_CONTAIN"]}, "leftValue": {"$ref": "#/definitions/value"}, "rightValue": {"$ref": "#/definitions/value"}}}}}, {"type": "object", "title": "表达式", "description": "", "required": ["criterionType", "criterionGroup"], "additionalProperties": false, "properties": {"criterionType": {"type": "string", "title": "表达式类型", "description": "普通关系表达式或逻辑表达式组", "enum": ["CRITERION_GROUP"]}, "criterionGroup": {"$ref": "#/definitions/criterionGroup"}}}]}}}}, "action": {"anyOf": [{"type": "object", "title": "逻辑表达式为true，执行的动作", "description": "", "required": ["actionType", "variableAssign"], "additionalProperties": false, "properties": {"actionType": {"type": "string", "title": "动作类型", "description": "", "enum": ["VARIABLE_ASSIGN"]}, "variableAssign": {"type": "object", "title": "参数赋值", "description": "", "required": ["fieldName", "<PERSON><PERSON><PERSON><PERSON>", "fieldType", "sourcePath", "value"], "minProperties": 5, "maxProperties": 5, "allOf": [{"$ref": "#/definitions/variableParams"}, {"properties": {"value": {"$ref": "#/definitions/value"}}}]}}}, {"type": "object", "title": "逻辑表达式为true，执行的动作", "description": "", "required": ["actionType", "callMethod"], "additionalProperties": false, "properties": {"actionType": {"type": "string", "title": "动作类型", "description": "", "enum": ["CALL_METHOD"]}, "callMethod": {"$ref": "#/definitions/callMethod"}}}, {"type": "object", "title": "逻辑表达式为true，执行的动作", "description": "", "required": ["actionType", "returnVal"], "additionalProperties": false, "properties": {"actionType": {"type": "string", "title": "动作类型", "description": "", "enum": ["RETURN"]}, "returnVal": {"type": "object", "title": "返回值", "description": "具体返回值还没有定义", "additionalProperties": false}}}]}}, "items": {"type": "object", "title": "规则", "description": "规则详情定义&检测", "required": ["name", "priority", "status", "criterionGroup", "thenAction", "executeModelValue", "dataScopeString"], "additionalProperties": false, "properties": {"name": {"type": "string", "title": "规则名称", "description": "规则名称", "maxLength": 50, "minLength": 1}, "priority": {"type": "integer", "title": "规则优先级", "description": "值越小优先级越高", "default": 2147483647, "minimum": -2147483648, "maximum": 2147483647}, "status": {"type": "integer", "title": "规则启用停用状态", "description": "0 启用，1 停用", "default": 0, "minimum": 0, "maximum": 1}, "extData": {"type": "object", "title": "规则扩展数据", "description": "内部结构根据实际需要自行扩展", "maxProperties": 20}, "executeModelValue": {"type": "integer", "title": "规则执行方式的值", "description": "规则执行方式的值", "maxLength": 50, "minLength": 1}, "dataScopeString": {"type": "string", "title": "规则执行日期", "description": "规则执行日期", "maxLength": 50, "minLength": 1}, "criterionGroup": {"$ref": "#/definitions/criterionGroup"}, "thenAction": {"$ref": "#/definitions/action"}, "elseAction": {"$ref": "#/definitions/action"}}}}