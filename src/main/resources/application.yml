server:
  port: 8351

spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
    allow-circular-references: true
  config:
    use-legacy-processing: true

auth:
  saas:
    appCode: sl56s1sw
    login:
      interceptorEnable: true
      required-path:
        - /api/**
      white-list-path:
        - /innerapi/**
        - /api/cf-risk-admin/disease-sync/accept
    permission:
      interceptorEnable: true
      required-path:
        - /**

web:
  wrapper:
    argument-resolver:
      enable: true
