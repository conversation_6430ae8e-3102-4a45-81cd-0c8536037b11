[{"remark": "抽取工单", "rules": [{"name": "rule3", "lhs": {"criterion": {"criterions": [{"op": "Equals", "left": {"leftPart": {"variableName": "bloodLipFalg", "variableLabel": "执行下一步", "variableCategory": "投保人", "datatype": "Boolean"}, "type": "variable"}, "value": {"content": "true", "valueType": "Input"}, "necessaryClassList": []}, {"criterions": [{"op": "Equals", "left": {"leftPart": {"variableName": "age", "variableLabel": "年龄", "variableCategory": "投保人", "datatype": "Integer"}, "type": "variable"}, "value": {"content": "1", "valueType": "Input"}, "necessaryClassList": []}, {"op": "Equals", "left": {"leftPart": {"variableName": "bloodPrePoint", "variableLabel": "血压评分", "variableCategory": "投保人", "datatype": "String"}, "type": "variable"}, "value": {"content": "延期", "valueType": "Input"}, "necessaryClassList": []}, {"op": "Equals", "left": {"leftPart": {"variableName": "queryPoint", "variableLabel": "问卷评分", "variableCategory": "投保人", "datatype": "String"}, "type": "variable"}, "value": {"content": "延期", "valueType": "Input"}, "necessaryClassList": []}, {"op": "Equals", "left": {"leftPart": {"beanId": "urule.dateAction", "beanLabel": "日期", "methodName": "getay", "methodLabel": "取天", "parameters": [{"name": "目标日期", "type": "Date", "value": {"variableName": "birthday", "variableLabel": "出生日期", "variableCategory": "客户", "datatype": "Date", "valueType": "Variable"}}]}, "type": "method", "arithmetic": {"type": "Add", "value": {"content": "1", "valueType": "Input"}}}, "value": {"beanId": "urule.dateAction", "beanLabel": "日期", "methodLabel": "取天", "methodName": "getay", "parameters": [{"name": "目标日期", "type": "Date", "value": {"beanId": "urule.dateAction", "beanLabel": "日期", "methodLabel": "当前日期", "methodName": "getDate", "parameters": [], "valueType": "Method"}}], "valueType": "Method"}, "necessaryClassList": []}], "junctionType": "and"}], "junctionType": "and"}}, "rhs": {"actions": [{"type": "variable", "actionType": "VariableAssign", "datatype": "String", "variableCategory": "投保人", "variableLabel": "总评分", "variableName": "total", "value": {"content": "延期", "valueType": "Input"}, "priority": 0}, {"type": "variable", "actionType": "VariableAssign", "datatype": "Boolean", "variableCategory": "投保人", "variableLabel": "执行下一步", "variableName": "bloodLipFalg", "value": {"content": "false", "valueType": "Input"}, "priority": 0}]}, "other": {}, "loopRule": false, "remark": "三个评分中一个为延期时，总评分是延期", "withElse": false}]}]