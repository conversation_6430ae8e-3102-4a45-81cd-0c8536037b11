[{"name": "质检抽检规则01", "priority": 2, "status": 0, "criterionGroup": {"junctionType": "AND", "criterions": [{"criterionType": "RELATIONAL", "relational": {"op": "GREATER_THEN", "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "recordingTime", "fieldLabel": "录音时长", "fieldType": "<PERSON>", "sourcePath": "recordingTime"}}, "rightValue": {"valueType": "CONSTANT", "content": "3"}}}, {"criterionType": "RELATIONAL", "relational": {"op": "IN", "rightValue": {"valueType": "CONSTANT", "content": "[1,2,3]"}, "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "areaId", "fieldLabel": "所在区域", "fieldType": "<PERSON>", "sourcePath": "areaId"}}}}, {"criterionType": "RELATIONAL", "relational": {"op": "EQUALS", "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "<PERSON><PERSON><PERSON>", "fieldLabel": "顾问姓名", "fieldType": "String", "sourcePath": "<PERSON><PERSON><PERSON>"}}, "rightValue": {"valueType": "CONSTANT", "content": "小米辣"}}}, {"criterionType": "CRITERION_GROUP", "criterionGroup": {"junctionType": "OR", "criterions": [{"criterionType": "RELATIONAL", "relational": {"op": "LESS_THEN", "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "validQualitySpotCount", "fieldLabel": "顾问当月有效质检数", "fieldType": "<PERSON>", "sourcePath": "validQualitySpotCount"}}, "rightValue": {"valueType": "CONSTANT", "content": "2"}}}, {"criterionType": "RELATIONAL", "relational": {"op": "GREATER_THEN", "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "recordingTime", "fieldLabel": "录音时长", "fieldType": "<PERSON>", "sourcePath": "recordingTime"}}, "rightValue": {"valueType": "CONSTANT", "content": "3"}}}]}}]}, "thenAction": {"actionType": "VARIABLE_ASSIGN", "variableAssign": {"fieldName": "isHit", "fieldLabel": "是否命中策略规则", "fieldType": "Boolean", "sourcePath": "isHit", "value": {"valueType": "CONSTANT", "content": "true"}}}}, {"name": "质检抽检规则01", "priority": 2, "status": 0, "criterionGroup": {"junctionType": "AND", "criterions": [{"criterionType": "RELATIONAL", "relational": {"op": "GREATER_THEN", "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "recordingTime", "fieldLabel": "录音时长", "fieldType": "<PERSON>", "sourcePath": "recordingTime"}}, "rightValue": {"valueType": "CONSTANT", "content": "3"}}}, {"criterionType": "RELATIONAL", "relational": {"op": "IN", "rightValue": {"valueType": "CONSTANT", "content": "[1,2,3]"}, "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "areaId", "fieldLabel": "所在区域", "fieldType": "<PERSON>", "sourcePath": "areaId"}}}}, {"criterionType": "RELATIONAL", "relational": {"op": "EQUALS", "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "<PERSON><PERSON><PERSON>", "fieldLabel": "顾问姓名", "fieldType": "String", "sourcePath": "<PERSON><PERSON><PERSON>"}}, "rightValue": {"valueType": "CONSTANT", "content": "小米辣"}}}, {"criterionType": "CRITERION_GROUP", "criterionGroup": {"junctionType": "OR", "criterions": [{"criterionType": "RELATIONAL", "relational": {"op": "LESS_THEN", "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "validQualitySpotCount", "fieldLabel": "顾问当月有效质检数", "fieldType": "<PERSON>", "sourcePath": "validQualitySpotCount"}}, "rightValue": {"valueType": "CONSTANT", "content": "2"}}}, {"criterionType": "RELATIONAL", "relational": {"op": "GREATER_THEN", "leftValue": {"valueType": "VARIABLE", "variable": {"fieldName": "recordingTime", "fieldLabel": "录音时长", "fieldType": "<PERSON>", "sourcePath": "recordingTime"}}, "rightValue": {"valueType": "CONSTANT", "content": "3"}}}]}}]}, "thenAction": {"actionType": "VARIABLE_ASSIGN", "variableAssign": {"fieldName": "isHit", "fieldLabel": "是否命中策略规则", "fieldType": "Boolean", "sourcePath": "isHit", "value": {"valueType": "CONSTANT", "content": "true"}}}}]