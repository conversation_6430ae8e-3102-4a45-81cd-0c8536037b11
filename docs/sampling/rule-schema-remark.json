[
  {
    "name": "质检抽检规则01",
    "priority": 1,//规则优先级，处理时需要按其进行排序
    "status": 1,//状态：0 启用，1 禁用
    "extData": {//规则扩展数据，只用于展示；功能不会解析
      "dataScope": 1
    },
    "preconditionGroup": "$[0].criterionGroup",//规则执行前置条件，前置条件满足，才会执行正常规则
    "criterionGroup": {//逻辑表达式组
      "junctionType": "AND",//逻辑标识符；具体枚举由后端提供
      "criterions": [//关系表达式列表
        {
          "criterionType": "RELATIONAL/CRITERION_GROUP",//关系表达式类型：普通关系表达式/逻辑表达式组
          "relational": {//普通关系表达式
            "op": "GREATER_THEN_EQUALS",//关系表达式比较符号；具体枚举由后端提供
            "leftValue": {//关系表达式左边的值
              "valueType": "CALL_METHOD/CONSTANT/VARIABLE",//值类型：需要复杂计算的值/输入的常量/变量
              "content": "常量内容",
              "variable": {
                "fieldName": "count",
                "fieldLabel": "字段说明",
                "fieldType": "Integer",
                "sourcePath": "entity.count"//变量取值路径，多层用'.'分隔
              },
              "callMethod": {//调用一个函数处理值
                "beanId": "urule.mapAction",
                "beanLabel": "Map集合",
                "methodName": "get",
                "methodLabel": "从Map中取值",
                "parameters": [//需要计算的参数
                  {
                    "name": "MapObject",
                    "type": "Map",//参数数据类型；枚举由后端提供
                    "value": "$[0].criterionGroup.criterions[0].criterion.leftValue"//入参本身也是一个value，结构同leftValue
                  },
                  {
                    "name": "key",
                    "type": "String",
                    "$ref": "$[0].criterionGroup.criterions[0].criterion.leftValue"
                  }
                ]
              },
              "arithmetic": {//对值进行算术运算
                "op": "Add",//运算方式；具体枚举由后端提供
                "value": "$[0].criterionGroup.criterions[0].criterion.leftValue",
                "$ref": "$[0].criterionGroup.criterions[0].criterion.leftValue.arithmetic"//算数运算后的算数运算：'a+b+c'的'+c+b'部分；结构同父级的arithmetic
              }
            },
            "rightValue": "$[0].criterionGroup.criterions[0].criterion.leftValue"//关系表达式右边的值
          },
          "criterionGroup": "$[0].criterionGroup"//逻辑表达式组
        }
      ]
    },
    "thenAction": {//逻辑判断为true动作
      "actionType": "VARIABLE_ASSIGN/CALL_METHOD/RETURN",//动作类型：变量赋值/调用函数/返回值
      "variableAssign": {//变量赋值
        "fieldName": "count",
        "fieldLabel": "字段说明",
        "fieldType": "Integer",
        "sourcePath": "entity.count",
        "value": "$[0].criterionGroup.criterions[0].criterion.leftValue"
      },
      "$ref": "$[0].criterionGroup.criterions[0].criterion.leftValue.callMethod",
      "returnVal": {//返回值
        //todo ...
      }
    },
    "elseAction": "$[0].thenAction"//逻辑判断为false动作
  }
]
