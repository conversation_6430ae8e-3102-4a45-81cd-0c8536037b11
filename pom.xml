<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spring-cloud-shuidi-parent</artifactId>
        <groupId>com.shuidihuzhu.infra</groupId>
        <version>3.1.42</version>
    </parent>

    <groupId>com.shuidihuzhu.cf-risk</groupId>
    <artifactId>cf-risk-admin</artifactId>
    <version>1.0.296-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>cf-risk-admin</name>
    <description>水滴筹风控B端项目</description>

    <scm>
        <connection>scm:git:https://git.shuiditech.com/cf-risk/cf-risk-admin.git</connection>
        <developerConnection>scm:git:**********************:cf-risk/cf-risk-admin.git</developerConnection>
        <url>https://git.shuiditech.com/cf-risk/cf-risk-admin.git</url>
        <tag>1.0.209</tag>
    </scm>

    <properties>

        <metadata-config-starter.version>0.0.5</metadata-config-starter.version>
        <cf-finance-client.version>3.0.35</cf-finance-client.version>
        <cf-finance-feign-client.version>3.0.21</cf-finance-feign-client.version>
        <cf-api-client.version>3.6.191</cf-api-client.version>
        <cf-model.version>3.6.411</cf-model.version>
        <boot-image.tag>2</boot-image.tag>
        <java.version>11</java.version>

        <pf-common-v2.version>1.0.41</pf-common-v2.version>
        <cf-event-senter-client.version>1.0.7</cf-event-senter-client.version>
        <apollo-client.version>1.1.0</apollo-client.version>
        <cf-client.version>1.2.449</cf-client.version>
        <cf-risk-rpc-client.version>1.0.124</cf-risk-rpc-client.version>
        <cf-activity-client.version>9.0.105</cf-activity-client.version>
        <cf-admin-api-pure-client.version>9.0.105</cf-admin-api-pure-client.version>
        <cf-material-client-version>9.0.102</cf-material-client-version>
        <easyexcel.version>2.1.6</easyexcel.version>
        <guava-retrying.version>2.0.0</guava-retrying.version>
        <kratos-client.version>3.0.1</kratos-client.version>
        <cf-ugc-client.version>9.0.105</cf-ugc-client.version>
        <cf-client-base.version>9.0.82</cf-client-base.version>
        <shuidi-wx-provider.version>2.0.95</shuidi-wx-provider.version>
        <weixin-java-mp.version>1.3.68</weixin-java-mp.version>
        <wx-grpc-client.version>1.0.100</wx-grpc-client.version>
        <es.client.version>3.1.5</es.client.version>
        <elasticsearch.client.verion>7.6.2</elasticsearch.client.verion>
        <baseservice.client.version>3.0.1</baseservice.client.version>
        <pf-common-test.version>1.0.1</pf-common-test.version>
        <pf-tools.verion>0.0.6</pf-tools.verion>
        <cf-store.version>1.0.13</cf-store.version>
        <shuidi-auth-saas-client.version>0.0.28</shuidi-auth-saas-client.version>
        <servicelog-meta-cf.version>1.0.40</servicelog-meta-cf.version>
        <mdc-admin-client.version>9.0.54</mdc-admin-client.version>
        <mdc-client-base.version>9.0.99</mdc-client-base.version>
        <account-grpc-client.version>2.2.148</account-grpc-client.version>
        <mapstruct.version>1.5.2.Final</mapstruct.version>
        <org.projectlombok.version>1.18.16</org.projectlombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>

    </properties>

    <dependencies>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-ocean-client</artifactId>
            <version>0.0.9</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-enhancer-starter</artifactId>
            <version>1.0.90</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-client-base</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-data-platform-client</artifactId>
            <version>9.0.90</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-client-base</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.op</groupId>
            <artifactId>shuidi-auth-saas-client</artifactId>
            <version>${shuidi-auth-saas-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-meta-cf</artifactId>
            <version>${servicelog-meta-cf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pf</groupId>
            <artifactId>pf-tools</artifactId>
            <version>${pf-tools.verion}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.baseservice</groupId>
            <artifactId>baseservice-client</artifactId>
            <version>${baseservice.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.pf</groupId>
            <artifactId>pf-common-test</artifactId>
            <version>${pf-common-test.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>${guava-retrying.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client</artifactId>
            <version>${cf-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>baseservice-client</artifactId>
                    <groupId>com.shuidihuzhu.baseservice</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-client-base</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-material-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.thetransactioncompany</groupId>
            <artifactId>cors-filter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-client</artifactId>
            <version>${cf-finance-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>shuidi-wx-provider</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.wx</groupId>
                    <artifactId>wx-grpc-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-enhancer-starter</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-material-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-ugc-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-feign-client</artifactId>
            <version>${cf-finance-feign-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>shuidi-wx-provider</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-finance-common-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-client</artifactId>
            <version>${cf-api-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-material-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-model</artifactId>
            <version>${cf-model.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-core-v2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>shuidi-wx-provider</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>web-model</artifactId>
                    <groupId>com.shuidihuzhu.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-activity-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-ugc-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-risk-rpc-client</artifactId>
                    <groupId>com.shuidihuzhu.cf-risk</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-client-base</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-finance-common-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-material-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.pf</groupId>
            <artifactId>pf-common-v2</artifactId>
            <version>${pf-common-v2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cos_api</artifactId>
                    <groupId>com.qcloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf-risk</groupId>
            <artifactId>cf-risk-rpc-client</artifactId>
            <version>${cf-risk-rpc-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>pf-common-v2</artifactId>
                    <groupId>com.shuidihuzhu.pf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-material-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-event-center-client</artifactId>
            <version>${cf-event-senter-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>aopalliance</groupId>
                    <artifactId>aopalliance</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-activity-client</artifactId>
            <version>${cf-activity-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-api-pure-client</artifactId>
            <version>${cf-admin-api-pure-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-client-base</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-material-client</artifactId>
            <version>${cf-material-client-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-client-base</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.everit.json</groupId>
            <artifactId>org.everit.json.schema</artifactId>
            <version>1.5.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>json</artifactId>
                    <groupId>org.json</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20180130</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.kratos</groupId>
            <artifactId>kratos-client</artifactId>
            <version>${kratos-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>baseservice-client</artifactId>
                    <groupId>com.shuidihuzhu.baseservice</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-ugc-client</artifactId>
            <version>${cf-ugc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client-base</artifactId>
            <version>${cf-client-base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.soundlibs</groupId>
            <artifactId>tritonus-share</artifactId>
            <version>0.3.7-2</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.soundlibs</groupId>
            <artifactId>mp3spi</artifactId>
            <version>1.9.5-1</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.soundlibs</groupId>
            <artifactId>vorbisspi</artifactId>
            <version>1.0.3-1</version>
        </dependency>
        <dependency>
            <groupId>com.mpatric</groupId>
            <artifactId>mp3agic</artifactId>
            <version>0.9.1</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>shuidi-wx-provider</artifactId>
            <version>${shuidi-wx-provider.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-grpc-client</artifactId>
            <version>${account-grpc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>${weixin-java-mp.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.wx</groupId>
            <artifactId>wx-grpc-client</artifactId>
            <version>${wx-grpc-client.version}</version>
        </dependency>

        <!-- other tool -->
        <!-- es -->
        <dependency>
            <groupId>com.shuidihuzhu.datawarehouse</groupId>
            <artifactId>shuidi-es-client</artifactId>
            <version>${es.client.version}</version>
            <exclusions> <!-- 如果项目中没有引入es组件，则将 exclusions 部分去掉 ，防止与项目中已经使用的 es 模块版本冲突-->
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>elasticsearch</artifactId>
            <groupId>org.elasticsearch</groupId>
            <version>${elasticsearch.client.verion}</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>${elasticsearch.client.verion}</version>
        </dependency>
        <dependency>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <groupId>org.elasticsearch.client</groupId>
            <version>${elasticsearch.client.verion}</version>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-store</artifactId>
            <version>${cf-store.version}</version>
        </dependency>
        <dependency>
            <groupId>io.shardingjdbc</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-alarm-center-client</artifactId>
            <version>9.0.19</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.mdc</groupId>
            <artifactId>mdc-admin-client</artifactId>
            <version>${mdc-admin-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.mdc</groupId>
            <artifactId>mdc-client-base</artifactId>
            <version>${mdc-client-base.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-client-base</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>cf-common-jdbc</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-common-dependencies</artifactId>
                <version>RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
                </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                        <version>1.1.1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <rules>
                                <!--强制项目没有重复声明的依赖项-->
                                <banDuplicatePomDependencyVersions />
                                <!--确保所有jar依赖项是同一版本-->
                                <!--<dependencyConvergence />-->
                                <loggerRule implementation="com.shuidihuzhu.customrule.MapperCheck">
                                    <excludePath>/src/main/java</excludePath>
                                    <excludePath>/src/main/resources/mappers/RiskDiseaseDataMapper.xml</excludePath>
                                    <excludePath>/src/main/resources/mappers/RiskDiseaseTreatmentProjectMapper.xml</excludePath>
                                </loggerRule>
                                <cipherRule implementation="com.shuidihuzhu.rule.CipherRule">
                                </cipherRule>
                            </rules>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.0</version>
                    </dependency>
                    <dependency>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>custom-rule</artifactId>
                        <version>1.0.23</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                        <!-- This is needed when using Lombok 1.18.16 and above -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
